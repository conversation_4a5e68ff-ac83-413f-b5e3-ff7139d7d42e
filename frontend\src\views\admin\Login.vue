<template>
  <div class="admin-login-container">
    <div class="login-box">
      <div class="login-header">
        <img src="@/assets/images/logo.png" alt="Logo" class="logo">
        <h2>后台管理系统</h2>
        <p>管理员登录</p>
      </div>
      
      <el-form
        ref="loginForm"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        label-position="top"
        @submit.native.prevent="handleLogin"
      >
        <el-form-item label="用户名" prop="username">
          <el-input
            id="username"
            v-model="loginForm.username"
            prefix-icon="el-icon-user"
            placeholder="请输入管理员用户名"
            @keyup.enter.native="handleLogin"
          />
        </el-form-item>
        
        <el-form-item label="密码" prop="password">
          <el-input
            id="password"
            v-model="loginForm.password"
            prefix-icon="el-icon-lock"
            type="password"
            placeholder="请输入密码"
            show-password
            @keyup.enter.native="handleLogin"
          />
        </el-form-item>
        
        <el-form-item>
          <el-checkbox id="remember" v-model="rememberMe">记住我</el-checkbox>
        </el-form-item>
        
        <el-form-item>
          <el-button
            type="primary"
            size="large"
            :loading="loading"
            class="login-btn"
            @click="handleLogin"
          >
            {{ loading ? '登录中...' : '登录' }}
          </el-button>
        </el-form-item>
      </el-form>
      
      <div class="login-footer">
        <p>© 2024 早鸟论文 - 后台管理系统</p>
      </div>
    </div>
  </div>
</template>

<script>
import { mapActions } from 'vuex'
import Cookies from 'js-cookie'

export default {
  name: 'AdminLogin',
  data() {
    return {
      loading: false,
      loginForm: {
        username: '',
        password: ''
      },
      loginRules: {
        username: [
          { required: true, message: '请输入管理员账号', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
        ]
      },
      rememberMe: false
    }
  },
  mounted() {
    // 检查是否已登录
    if (this.$store.getters['admin/isAdminLoggedIn']) {
      this.$router.push('/admin/dashboard')
    }
  },
  methods: {
    ...mapActions('admin', ['adminLogin']),
    
    // 处理登录
    async handleLogin() {
      try {
        const valid = await this.$refs.loginForm.validate()
        if (!valid) return
        
        this.loading = true
        const result = await this.adminLogin(this.loginForm)
        
        if (result.success) {
          // 保存token到cookie和localStorage
          const token = result.data.token
          if (token) {
            Cookies.set('admin_token', token, { expires: 7 }) // 7天过期
            localStorage.setItem('admin_token', token)
          }
          
          this.$message.success('登录成功')
          
          // 延迟跳转，确保token已保存
          setTimeout(() => {
            this.$router.push('/admin/dashboard')
          }, 100)
        } else {
          this.$message.error(result.message || '登录失败')
        }
      } catch (error) {
        this.$message.error('登录失败，请稍后重试')
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.admin-login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  
  .login-box {
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    padding: 40px;
    width: 100%;
    max-width: 400px;
    position: relative;
    overflow: hidden;
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    }
    
    .login-header {
      text-align: center;
      margin-bottom: 30px;
      
      .logo {
        width: 80px;
        height: 80px;
        object-fit: contain;
        margin-bottom: 20px;
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        background: white;
        padding: 8px;
      }
      
      h2 {
        color: #333;
        margin: 0 0 10px 0;
        font-size: 24px;
        font-weight: 600;
      }
      
      p {
        color: #666;
        margin: 0;
        font-size: 14px;
      }
    }
    
    .login-form {
      .form-item {
        margin-bottom: 24px;
        
        .el-input {
          .el-input__inner {
            height: 48px;
            border-radius: 8px;
            border: 2px solid #f0f0f0;
            transition: all 0.3s;
            
            &:focus {
              border-color: #667eea;
              box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            }
          }
        }
      }
      
      .login-btn {
        width: 100%;
        height: 48px;
        border-radius: 8px;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        border: none;
        font-size: 16px;
        font-weight: 600;
        color: white;
        transition: all 0.3s;
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }
        
        &:active {
          transform: translateY(0);
        }
      }
    }
  }
}

.login-footer {
  text-align: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #eee;
  
  p {
    color: #999;
    font-size: 12px;
    margin: 0;
  }
}

// 响应式设计
@media (max-width: 480px) {
  .login-box {
    padding: 30px 20px;
  }
  
  .login-header {
    .logo {
      width: 100px;
      max-height: 60px;
    }
    
    h2 {
      font-size: 20px;
    }
  }
}
</style> 