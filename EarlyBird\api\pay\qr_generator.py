#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
本地二维码生成器
作者：EarlyBird
邮箱：<EMAIL>
网址：https://blog.zaoniao.vip
"""

import qrcode
import io
import base64
from PIL import Image
import logging

logger = logging.getLogger(__name__)

class QRCodeGenerator:
    """本地二维码生成器"""
    
    @staticmethod
    def generate_qr_base64(data, size=200):
        """
        生成二维码并返回base64编码的图片数据
        
        Args:
            data: 要编码的数据
            size: 二维码尺寸（像素）
            
        Returns:
            base64编码的PNG图片数据URL
        """
        try:
            # 创建二维码实例
            qr = qrcode.QRCode(
                version=1,  # 控制二维码的大小
                error_correction=qrcode.constants.ERROR_CORRECT_L,  # 错误纠正级别
                box_size=10,  # 每个格子的像素大小
                border=4,  # 边框的格子数
            )
            
            # 添加数据
            qr.add_data(data)
            qr.make(fit=True)
            
            # 创建图片
            img = qr.make_image(fill_color="black", back_color="white")
            
            # 调整图片大小
            img = img.resize((size, size), Image.Resampling.LANCZOS)
            
            # 转换为base64
            buffer = io.BytesIO()
            img.save(buffer, format='PNG')
            img_str = base64.b64encode(buffer.getvalue()).decode()
            
            # 返回data URL格式
            return f"data:image/png;base64,{img_str}"
            
        except Exception as e:
            logger.error(f"生成二维码失败: {str(e)}")
            return None
    
    @staticmethod
    def generate_qr_file(data, file_path, size=200):
        """
        生成二维码并保存为文件
        
        Args:
            data: 要编码的数据
            file_path: 保存文件的路径
            size: 二维码尺寸（像素）
            
        Returns:
            成功返回True，失败返回False
        """
        try:
            # 创建二维码实例
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=4,
            )
            
            # 添加数据
            qr.add_data(data)
            qr.make(fit=True)
            
            # 创建图片
            img = qr.make_image(fill_color="black", back_color="white")
            
            # 调整图片大小
            img = img.resize((size, size), Image.Resampling.LANCZOS)
            
            # 保存文件
            img.save(file_path)
            
            logger.info(f"二维码已保存到: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"保存二维码文件失败: {str(e)}")
            return False 