import logging
import json
from flask import g
import shortuuid
from datetime import datetime

from EarlyBird.ExtendRegister.model_register import (
    The<PERSON>, 
    Paragraph, 
    ChatLog, 
    TitleHistory, 
    OutlineHistory,
    User,
    GenerateTask
)
from EarlyBird.ExtendRegister.db_register import db
from EarlyBird.common import (
    TaskStatus,
    Result,
    PageNav,
    ApiResponse,
    purgeJsonPrefix
)
from sqlalchemy import or_
from EarlyBird.common.ai import (
    MODEL_GPT35,
    MODEL_GEMINI10,
    MODEL_QIANWEN,
    MODEL_KIMI,
    getAdapter,
    AiQuery,
    AiQueryResult,
)

from EarlyBird.api.talk2ai.model.base import BaseAiModel, InvokeResult
from EarlyBird.api.talk2ai.utils import travelOutlineDict, getModelByName
from EarlyBird.api.talk2ai.dantic import *
from EarlyBird.api.talk2ai.model.model_proxy import ModelProxy
from EarlyBird.api.talk2ai.model import prompt_factory
from EarlyBird.common.task_queue import pubTask, pubGenerateThesisTask, TaskModel, TaskType
from EarlyBird.config.config import AppConfig
from EarlyBird.common.outline import OutlineKit
from EarlyBird.model.report_history import ReportHistory

LOGGER = logging.getLogger(__name__)


class Service:
    def __init__(self) -> None:
        pass

    def delChat(self, body: ParamDelChat) -> Result:
        try:
            chat: ChatLog = ChatLog.query.get(body.chatId)
            if chat is None:
                return Result()
                
            # 检查是否是当前用户的聊天记录
            if chat.uid != body.userId:
                return Result().error("无权删除其他用户的聊天记录")
                
            db.session.delete(chat)
            db.session.commit()
            return Result()
        except Exception as e:
            LOGGER.exception(e)
            return Result().error(str(e))

    def getChatLog(self, body: ParamGetChatLog) -> Result:
        try:
            if body.pageNo < 1:
                body.pageNo = 1

            if body.pageSize < 1:
                body.pageSize = 1

            offset = body.pageSize * (body.pageNo - 1)
            limit = body.pageSize

            # 检查ChatLog表是否有uid字段
            has_uid_field = False
            try:
                # 尝试查询一条记录，检查是否有uid字段
                test_log = ChatLog.query.first()
                if test_log:
                    # 尝试访问uid字段
                    _ = test_log.uid
                    has_uid_field = True
                    LOGGER.info("ChatLog表存在uid字段，使用用户隔离查询")
            except Exception as e:
                LOGGER.warning(f"检查ChatLog表uid字段失败: {str(e)}")
                LOGGER.warning("ChatLog表可能不存在uid字段，将不进行用户隔离")

            # 根据是否有uid字段决定查询方式
            if has_uid_field:
                # 添加用户ID过滤
                logList = (
                    ChatLog.query
                    .filter(ChatLog.uid == body.userId)  # 只查询当前用户的聊天记录
                    .order_by(ChatLog.id.desc())
                    .offset(offset)
                    .limit(limit)
                    .all()
                )
            else:
                # 不进行用户隔离，查询所有聊天记录
                logList = (
                    ChatLog.query
                    .order_by(ChatLog.id.desc())
                    .offset(offset)
                    .limit(limit)
                    .all()
                )

            logListJson = []
            for log in logList:
                logListJson.append(log.to_json())
            logListJson.reverse()
            return Result().setData(logListJson)
        except Exception as e:
            LOGGER.exception(e)
            return Result().error(str(e))

    def _saveChatLog(self, modelName: str, type: str, msg: str, userId: int = 0):
        try:
            chatLog: ChatLog = ChatLog()
            chatLog.modelName = modelName
            chatLog.type = type
            chatLog.msg = msg
            
            # 尝试设置uid字段
            try:
                chatLog.uid = userId  # 保存用户ID
            except Exception as e:
                LOGGER.warning(f"设置ChatLog的uid字段失败: {str(e)}")
                LOGGER.warning("可能是数据库表结构尚未更新，将继续保存聊天记录")
                
            chatLog.save()
            return chatLog.id
        except Exception as e:
            LOGGER.exception(e)
            return -1

    def getChatReply(self, body: ParamChat) -> Result:
        if body.userMessage is None or body.userMessage == "":
            return Result().error("userMessage missing")
        try:
            # 保存用户发送的消息，带上用户ID
            self._saveChatLog(body.modelName, ChatLog.SEND, body.userMessage, body.userId)
        except Exception as e:
            LOGGER.exception(e)

        try:
            queryResult: AiQueryResult = getAdapter(body.modelName).query(
                AiQuery(userMessage=body.userMessage)
            )
            if not queryResult.isValid:
                # 保存错误消息，带上用户ID
                self._saveChatLog(
                    body.modelName, ChatLog.RECEIVE, queryResult.errMessage, body.userId
                )
                return Result().error(queryResult.errMessage)

            # 保存AI回复，带上用户ID
            chatId = self._saveChatLog(
                body.modelName, ChatLog.RECEIVE, queryResult.text, body.userId
            )

            return Result().setData({"msg": queryResult.text, "chatId": chatId})
        except Exception as e:
            return Result().error(str(e))

    def exportOutline(self, body: ParamSelect4Content) -> Result:
        allParaTitle = {}

        def _getAllParas(node: dict):
            if node["title"] == "":
                return
            allParaTitle[node["id"]] = node["title"]

        try:

            # 限制一下，同样标题，不能重复提交

            outlineDict = body.outline
            outlineDictWithId = travelOutlineDict(outlineDict, _getAllParas)
            print(outlineDictWithId)
            return Result()
        except Exception as e:
            LOGGER.exception(e)

            return Result().error(str(e))

    def stopThesisGenerate(self, body: ParamThesisId) -> Result:
        try:
            thesisId = body.thesisId
            thesis: Thesis = self.getThesisById(thesisId)
            if thesis is None:
                return Result().error("未查询到论文")

            if thesis.uid != g.userid:
                return Result().error("not found in your list")

            thesis.status = TaskStatus.BEFORE_INIT
            thesis.save()

            cnt = (
                Paragraph.query.filter(Paragraph.thesisId == thesisId)
                .filter(
                    or_(
                        Paragraph.status == TaskStatus.INIT,
                        Paragraph.status == TaskStatus.RUNING,
                    )
                )
                .update({"status": TaskStatus.ERROR})
            )
            db.session.commit()
            LOGGER.info(f"update thesis {thesisId} stop generate")
            return Result()
        except Exception as e:
            LOGGER.exception(e)
            return Result().error(str(e))

    def setThesisNeedGenerate(self, thesisId: int, globalSettings: dict = None) -> Result:
        try:
            thesis: Thesis = Thesis.query.filter(Thesis.id == thesisId).first()

            if thesis.status == TaskStatus.INIT or thesis.status == TaskStatus.RUNING:
                LOGGER.info(f"论文{thesisId}正在生成中，请稍等")
                return Result().error(f"论文正在生成中，请稍等")

            thesis.status = TaskStatus.INIT
            thesis.save()
            LOGGER.info(f"update thesis {thesisId} need update")

            ####
            #### 重要：只有从来没生成过的，和之前生成错误的，可以在这里被在此批量生成
            cnt = (
                Paragraph.query.filter(Paragraph.thesisId == thesisId)
                .filter(
                    or_(
                        Paragraph.status == TaskStatus.BEFORE_INIT,
                        Paragraph.status == TaskStatus.ERROR,
                    )
                )
                .update({"status": TaskStatus.INIT})
            )
            db.session.commit()
            LOGGER.info(f"update Paragraph thesisId:{thesisId} cnt: {cnt}")
            
            # 创建任务模型，包含全局设置
            taskModel = TaskModel(thesisId=thesisId, type=TaskType.GEN_THESIS)
            if globalSettings:
                taskModel.globalSettings = globalSettings
                LOGGER.info(f"任务包含全局设置: {globalSettings}")
            
            pubTask(taskModel)
            return Result()
        except Exception as e:
            LOGGER.error(e)
            LOGGER.exception(e)
            return Result().error(str(e))

    def generateDigest(self, param: ParamThesisId):
        try:
            t = self.getThesisById(param.thesisId)
            if t is None:
                return Result().error("thesis not found")

            genDigestParam = ParamRegenDigest(
                thesisId=param.thesisId,
                title=t.title,
                outline=t.outline,
                lang=t.lang,
                userId=param.userId,
                isVipUser=param.isVipUser,
            )
            modelProxy = ModelProxy(param.model)
            res: InvokeResult = modelProxy.generateDigest(genDigestParam)
            if not res.isSuccess:
                return Result().error(res.message)

            digestJson = res.data
            # key都定义在userMessage里了，不能轻易变动
            newDigest = digestJson["digest"]
            keywords = digestJson["keywords"]

            if newDigest is None:
                return Result().error("摘要生成失败")

            if newDigest.startswith("摘要："):
                newDigest = newDigest[3:].strip()
            elif newDigest.startswith("摘要:"):
                newDigest = newDigest[3:].strip()
            elif newDigest.startswith("摘要"):
                newDigest = newDigest[2:].strip()

            Thesis.query.filter(Thesis.id == param.thesisId).update(
                {"digest": newDigest, "keywords": keywords}
            )
            db.session.commit()
            return Result()

        except Exception as e:
            LOGGER.exception(e)
            return Result().error(str(e))

    def generateDigestEnglish(self, param: ParamThesisId):
        try:
            t = self.getThesisById(param.thesisId)
            if t is None:
                return Result().error("thesis not found")

            genDigestParam = ParamRegenDigest(
                thesisId=param.thesisId,
                title=t.title,
                outline=t.outline,
                lang=t.lang,
                userId=param.userId,
                isVipUser=param.isVipUser,
            )

            modelProxy = ModelProxy(param.model)
            res: InvokeResult = modelProxy.generateDigestEnglish(genDigestParam)
            if not res.isSuccess:
                return Result().error(res.message)

            digestJson = res.data
            # key都定义在userMessage里了，不能轻易变动
            newDigest = digestJson["digest"]
            keywords = digestJson["keywords"]

            if newDigest is None:
                return Result().error("摘要生成失败")

            if newDigest.startswith("摘要："):
                newDigest = newDigest[3:].strip()
            elif newDigest.startswith("摘要:"):
                newDigest = newDigest[3:].strip()
            elif newDigest.startswith("摘要"):
                newDigest = newDigest[2:].strip()

            Thesis.query.filter(Thesis.id == param.thesisId).update(
                {"digestEn": newDigest, "keywordsEn": keywords}
            )
            db.session.commit()
            return Result()

        except Exception as e:
            LOGGER.exception(e)
            return Result().error(str(e))

    def getParagraphById(self, thesisId: int, paraId: str):
        return (
            Paragraph.query.filter(Paragraph.thesisId == thesisId)
            .filter(Paragraph.paraId == paraId)
            .first()
        )

    def generateSingleParagraph(self, param: ParamGenerateSingleParagraph):
        try:
            t = self.getThesisById(param.thesisId)
            if t is None:
                return Result().error("论文id未找到")

            paragraph: Paragraph = self.getParagraphById(
                param.thesisId, param.paragraphId
            )
            if paragraph is None:
                return Result().error("段落未找到")

            param.paragraphTitle = paragraph.title
            param.title = t.title
            param.lang = t.lang

            # 收集上下文信息
            context = self._collectContext(param.thesisId, param.paragraphId, t.outline)
            param.context = context

            mp = ModelProxy(param.model)
            res: InvokeResult = mp.generateParagraph(param)
            if not res.isSuccess:
                return Result().error(res.message)

            newParagraph = res.data
            # 删除句首的关联词
            newParagraph = newParagraph.replace("\n总而言之，", "\n")
            newParagraph = newParagraph.replace("\n综上所述，", "\n")
            newParagraph = newParagraph.replace("\n首先，", "\n")
            newParagraph = newParagraph.replace("\n其次，", "\n")
            newParagraph = newParagraph.replace("\n再次，", "\n")
            newParagraph = newParagraph.replace("\n再者，", "\n")
            newParagraph = newParagraph.replace("\n此外，", "\n")
            newParagraph = newParagraph.replace("\n最后，", "\n")
            newParagraph = newParagraph.replace("\n总之，", "\n")

            Paragraph.query.filter(Paragraph.id == paragraph.id).update(
                {"paragraph": newParagraph, "status": TaskStatus.SUCCESS}
            )
            db.session.commit()
            return Result()

        except Exception as e:
            LOGGER.exception(e)
            return Result().error(str(e))

    def _collectContext(self, thesisId: int, currentParaId: str, outline: dict) -> dict:
        """
        收集论文上下文信息
        
        Args:
            thesisId: 论文ID
            currentParaId: 当前段落ID
            outline: 论文提纲
            
        Returns:
            dict: 包含上下文信息的字典
        """
        try:
            context = {
                'outline': self._formatOutline(outline),
                'previous_paragraphs': [],
                'next_paragraphs': []
            }
            
            # 获取所有段落，按顺序排列
            all_paragraphs = Paragraph.query.filter(
                Paragraph.thesisId == thesisId
            ).order_by(Paragraph.paraId).all()
            
            # 找到当前段落的索引
            current_index = -1
            for i, para in enumerate(all_paragraphs):
                if para.paraId == currentParaId:
                    current_index = i
                    break
            
            if current_index >= 0:
                # 收集已生成的段落内容（最多3个）
                start_idx = max(0, current_index - 3)
                for i in range(start_idx, current_index):
                    if all_paragraphs[i].paragraph and all_paragraphs[i].paragraph.strip():
                        context['previous_paragraphs'].append({
                            'title': all_paragraphs[i].title,
                            'content': all_paragraphs[i].paragraph
                        })
                
                # 收集后续段落标题（最多3个）
                end_idx = min(len(all_paragraphs), current_index + 4)
                for i in range(current_index + 1, end_idx):
                    context['next_paragraphs'].append({
                        'title': all_paragraphs[i].title
                    })
            
            return context
            
        except Exception as e:
            LOGGER.exception(f"收集上下文信息失败: {e}")
            return {'outline': '', 'previous_paragraphs': [], 'next_paragraphs': []}

    def _formatOutline(self, outline: dict) -> str:
        """
        格式化提纲为字符串
        
        Args:
            outline: 提纲字典
            
        Returns:
            str: 格式化的提纲字符串
        """
        try:
            if not outline or not isinstance(outline, dict):
                return ""
            
            def _format_node(node, level=0):
                if not isinstance(node, dict):
                    return ""
                
                result = "  " * level + node.get('title', '无标题') + "\n"
                
                if 'subtitle' in node and isinstance(node['subtitle'], list):
                    for child in node['subtitle']:
                        result += _format_node(child, level + 1)
                
                return result
            
            return _format_node(outline)
            
        except Exception as e:
            LOGGER.exception(f"格式化提纲失败: {e}")
            return ""

    def saveThesisInfo(self, thesisId: int, content: str = None):
        try:
            Thesis.query.filter(Thesis.id == thesisId).update({"outline": content})
            db.session.commit()
            return Result()
        except Exception as e:
            LOGGER.exception(e)
            return Result().error(str(e))

    def getThesisById(self, id: int) -> Thesis:
        try:
            return Thesis.query.filter(Thesis.id == id).first()

        except Exception as e:
            LOGGER.exception(e)
            return None

    def select4Content(self, body: ParamSelect4Content) -> Result:
        """
        处理用户选择的提纲，创建论文记录
        
        Args:
            body: 包含提纲数据的请求体
            
        Returns:
            Result: 处理结果
        """
        try:
            # 检查用户是否已有论文
            existing_thesis_count = Thesis.query.filter(Thesis.uid == body.userId).count()
            LOGGER.info(f"用户 {body.userId} 已有 {existing_thesis_count} 篇论文")
            LOGGER.info(f"用户 {body.userId} 的VIP状态: {body.isVipUser}")
            
            # 强制检查g对象中的VIP状态，确保与body中的一致
            from flask import g, session
            g_vip_status = getattr(g, 'isVip', None)
            LOGGER.info(f"用户 {body.userId} 在g对象中的VIP状态: {g_vip_status}")
            
            # 如果g对象中的VIP状态为True，则优先使用g对象的状态
            if g_vip_status is True:
                LOGGER.info(f"用户 {body.userId} 使用g对象中的VIP状态: {g_vip_status}")
                body.isVipUser = True
            
            # 检查是否有激活码，有激活码的用户视为VIP
            activation_key = session.get('activation_key')
            if activation_key:
                LOGGER.info(f"用户 {body.userId} 有激活码 {activation_key}，视为VIP用户")
                body.isVipUser = True
            
            # 检查用户对象的VIP状态
            user = getattr(g, 'user', None)
            if user and hasattr(user, 'isVip'):
                user_vip_status = user.isVip()
                LOGGER.info(f"用户对象 {user.id} 的VIP状态: {user_vip_status}")
                if user_vip_status:
                    LOGGER.info(f"用户对象 {user.id} 是VIP，更新body中的VIP状态")
                    body.isVipUser = True
            
            # 记录最终的VIP状态判断结果
            LOGGER.info(f"最终判断用户 {body.userId} 的VIP状态: {body.isVipUser}")
            
            if not body.isVipUser:
                if existing_thesis_count > 0:
                    LOGGER.warning(f"免费用户 {body.userId} 已有 {existing_thesis_count} 篇论文，不能创建更多")
                    return Result().error("免费用户最多只能创建一篇论文，请先删除已有论文再继续")
            else:
                # VIP用户也有限制，但数量更多
                max_thesis = 5  # VIP用户最多可以创建5篇论文
                if existing_thesis_count >= max_thesis:
                    LOGGER.warning(f"VIP用户 {body.userId} 已有 {existing_thesis_count} 篇论文，达到上限 {max_thesis}")
                    return Result().error(f"您已创建 {existing_thesis_count} 篇论文，达到上限。请先删除部分论文再继续")
        except Exception as e:
            LOGGER.exception(e)
            return Result().error(f"服务器异常 {str(e)}")

        body.title = body.title.strip()
        if body.title == "":
            return Result().error("请补充论文标题")
        
        # 验证 outline 参数
        if not body.outline:
            LOGGER.error(f"提纲数据为空: {body.outline}")
            return Result().error("提纲数据不能为空")
            
        # 记录接收到的提纲数据类型和内容
        LOGGER.info(f"接收到的提纲数据类型: {type(body.outline)}")
        try:
            LOGGER.info(f"提纲数据键: {body.outline.keys() if isinstance(body.outline, dict) else '非字典类型'}")
            LOGGER.info(f"提纲数据内容: {json.dumps(body.outline, ensure_ascii=False)[:200]}...")
        except Exception as e:
            LOGGER.error(f"提纲数据无法序列化为JSON: {str(e)}")
        
        # 尝试修复提纲数据结构
        try:
            if isinstance(body.outline, dict):
                # 确保提纲有title字段
                if "title" not in body.outline:
                    LOGGER.warning("提纲缺少title字段，使用论文标题")
                    body.outline["title"] = body.title
                
                # 确保提纲有subtitle字段
                if "subtitle" not in body.outline:
                    LOGGER.warning("提纲缺少subtitle字段，尝试从其他字段获取")
                    # 尝试从其他常见字段获取subtitle内容
                    for key in ["children", "subheadings", "sections", "chapters", "content"]:
                        if key in body.outline and isinstance(body.outline[key], list):
                            LOGGER.info(f"使用'{key}'字段作为subtitle")
                            body.outline["subtitle"] = body.outline[key]
                            break
                    
                    # 如果仍然没有找到，创建一个空的subtitle字段
                    if "subtitle" not in body.outline:
                        LOGGER.warning("无法找到subtitle内容，创建空subtitle字段")
                        body.outline["subtitle"] = []
            else:
                LOGGER.error(f"提纲不是字典类型: {type(body.outline)}")
                # 尝试转换为字典
                if isinstance(body.outline, str):
                    try:
                        body.outline = json.loads(body.outline)
                        LOGGER.info("成功将字符串转换为字典")
                    except:
                        LOGGER.error("无法将字符串转换为字典")
                        return Result().error("提纲格式错误，请重新生成提纲")
                else:
                    return Result().error("提纲格式错误，请重新生成提纲")
        except Exception as e:
            LOGGER.exception(f"修复提纲数据结构失败: {e}")
            
        allParaTitle = {}

        def _getAllParas(node: dict):
            if not isinstance(node, dict):
                LOGGER.error(f"提纲节点不是字典类型: {type(node)}")
                return
            
            # 确保节点有id字段
            if "id" not in node:
                node["id"] = shortuuid.uuid()
                LOGGER.info(f"为节点生成ID: {node['id']}")
            
            # 确保节点有title字段
            if "title" not in node:
                LOGGER.error(f"提纲节点缺少title字段")
                # 尝试从其他字段获取标题
                for field in ["name", "heading", "text"]:
                    if field in node and node[field]:
                        node["title"] = node[field]
                        LOGGER.info(f"使用'{field}'字段作为标题: {node['title']}")
                        break
                
                # 如果仍然没有找到标题，使用默认值
                if "title" not in node:
                    node["title"] = "无标题章节"
                    LOGGER.warning(f"无法找到标题，使用默认值: {node['title']}")
            
            if node["title"] == "":
                LOGGER.error(f"提纲节点title为空: {node}")
                node["title"] = "无标题章节"
                
            # 保存段落标题
            allParaTitle[node["id"]] = node["title"]
            LOGGER.debug(f"添加段落标题: {node['id']} -> {node['title']}")

        try:
            userId = g.userid

            # 限制一下，同样标题，不能重复提交

            outlineDict = body.outline
            
            # 确保提纲格式正确
            if not isinstance(outlineDict, dict):
                LOGGER.error(f"提纲格式错误，不是字典类型: {type(outlineDict)}")
                return Result().error("提纲格式错误，请重新生成提纲")
            
            # 检查提纲是否包含必要的字段
            if "subtitle" not in outlineDict:
                LOGGER.error(f"提纲缺少subtitle字段: {outlineDict.keys()}")
                return Result().error("提纲格式错误，缺少必要的结构信息")
            
            # 处理提纲
            try:
                outlineDictWithId = travelOutlineDict(outlineDict, _getAllParas)
                LOGGER.info(f"提纲处理完成，ID: {outlineDictWithId.get('id', 'unknown')}")
            except Exception as e:
                LOGGER.exception(f"处理提纲时发生错误: {e}")
                return Result().error(f"处理提纲时发生错误: {str(e)}")
            
            # 检查是否成功解析到段落标题
            if not allParaTitle:
                LOGGER.error(f"无法解析提纲数据，未找到有效段落标题")
                return Result().error("无法解析提纲数据，请重新生成提纲")
                
            LOGGER.info(f"成功解析提纲，找到 {len(allParaTitle)} 个段落标题")

            # 保存提纲，取到论文的id
            try:
                t = Thesis(
                    uid=userId,
                    title=body.title,
                    outline=outlineDictWithId,
                    lang=body.lang,
                    level=body.level,
                    length=body.length,
                )

                db.session.add(t)
                db.session.commit()
                
                LOGGER.info(f"已创建论文记录，ID: {t.id}")
            except Exception as e:
                db.session.rollback()
                LOGGER.exception(f"创建论文记录失败: {e}")
                return Result().error(f"创建论文记录失败: {str(e)}")

            # 逐个保存段落，进入Paragraph表
            try:
                for id, title in allParaTitle.items():
                    db.session.add(
                        Paragraph(uid=userId, thesisId=t.id, paraId=id, title=title)
                    )
                db.session.commit()
                
                LOGGER.info(f"已保存 {len(allParaTitle)} 个段落记录")
            except Exception as e:
                db.session.rollback()
                LOGGER.exception(f"保存段落记录失败: {e}")
                return Result().error(f"保存段落记录失败: {str(e)}")

            return Result().setData({"thesisId": t.id})
        except Exception as e:
            db.session.rollback()
            LOGGER.exception(f"处理提纲选择时发生异常: {e}")
            return Result().error(str(e))

    # 传入的两个参数，self参数用于获得AI模型，param: ParamOutline参数表示，期待传入一个ParamOutline的类的对象作为参数param
    # 前端需要保证，前端通过axios提交Http Request传过来的参数和后端的ParamOutline类定义的格式一致。
    # 这个代码实际上使用了FastAPI,服务器会自动解析HTTP Request类，拿到服务器端处理方法所需要的参数
    # 也就是ParamOutline格式的类param，如果没有这个类似的参数，服务器会直接拒绝服务，处理的方法getOutline也不会被调用

    def getOutline(self, param: ParamOutline) -> Result:
        try:
            userId = param.userId
            
            param.title = param.title.strip()
            if param.title == "":
                return Result().error("请填写论文标题")

            # 非VIP 章节只能到6，vip可以到12。其实页面上写的是9
            if param.isVipUser:
                param.paragraphCount = (
                    12
                    if param.paragraphCount > 12 or param.paragraphCount < 1
                    else param.paragraphCount
                )
            else:
                param.paragraphCount = (
                    6
                    if param.paragraphCount > 6 or param.paragraphCount < 1
                    else param.paragraphCount
                )
                
            # 首先查询用户历史记录，看是否有相同参数的提纲
            existing_outline = OutlineHistory.query.filter(
                OutlineHistory.uid == userId,
                OutlineHistory.title == param.title,
                OutlineHistory.level == param.level,
                OutlineHistory.lang == param.lang,
                OutlineHistory.length == param.length,
                OutlineHistory.paragraph_count == param.paragraphCount,
                OutlineHistory.second_paragraph_count == param.secondParagraphCount
            ).order_by(OutlineHistory.id.desc()).first()
            
            # 如果找到历史记录，直接返回
            if existing_outline:
                LOGGER.info(f"找到用户 {userId} 的提纲历史记录，ID: {existing_outline.id}")
                return Result().setData(existing_outline.raw_outlines)
                
            LOGGER.info(f"request ${param.model} param: {param}")
            m = ModelProxy(param.model)
            res: InvokeResult = m.getOutline(param)
            
            if res.isSuccess:
                # 生成HTML格式的提纲（用于前端显示）
                html_outlines = []
                raw_outlines = res.data
                
                # 这里需要调用前端的generateHtml方法的Python版本
                # 为了简化，我们先保存原始数据，HTML可以在前端生成
                for outline_data in raw_outlines:
                    html_outlines.append(self._generateOutlineHtml(outline_data["subtitle"], 1))
                
                # 保存提纲历史到数据库
                outline_history = OutlineHistory(
                    uid=userId,
                    title=param.title,
                    level=param.level,
                    lang=param.lang,
                    length=param.length,
                    paragraph_count=param.paragraphCount,
                    second_paragraph_count=param.secondParagraphCount,
                    outlines=html_outlines,
                    raw_outlines=raw_outlines,
                    form_data={
                        "title": param.title,
                        "level": param.level,
                        "lang": param.lang,
                        "length": param.length,
                        "paragraphCount": param.paragraphCount,
                        "secondParagraphCount": param.secondParagraphCount,
                        "model": param.model
                    }
                )
                
                db.session.add(outline_history)
                db.session.commit()
                
                LOGGER.info(f"为用户 {userId} 保存提纲历史记录，ID: {outline_history.id}")
                
                return Result().setData(res.data)
            
            return Result().error(res.message)

        except Exception as e:
            LOGGER.exception(e)
            return Result().error(str(e))
            
    def _generateOutlineHtml(self, json_outline, level=1, title_prefix=""):
        """将JSON格式的提纲转换为HTML格式，模拟前端的generateHtml方法"""
        html_result = ""
        for index, json_node in enumerate(json_outline):
            chapter_no = index + 1
            if level == 1:
                html_result += f"<div class='title-level1'><i>{title_prefix}第{chapter_no}章</i> {json_node['title']}</div>"
            else:
                html_result += f"<div class='title-level{level}'><i>{title_prefix}{chapter_no}</i> {json_node['title']}</div>"
            
            if "subtitle" in json_node and len(json_node["subtitle"]) > 0:
                html_result += self._generateOutlineHtml(
                    json_node["subtitle"],
                    level + 1,
                    f"{title_prefix}{chapter_no}."
                )
        return html_result

    def getTitle(self, param: ParamTitle) -> Result:
        try:
            # 获取用户ID
            userId = g.userid
            
            # 检查是否已有相同参数的历史记录
            existing_title = TitleHistory.query.filter_by(
                uid=userId,
                domain=param.domain,
                topic=param.topic,
                level=param.level,
                lang=param.lang,
                keyword=param.keyword
            ).first()
            
            # 如果找到历史记录，直接返回
            if existing_title:
                LOGGER.info(f"找到用户 {userId} 的标题历史记录，ID: {existing_title.id}")
                return Result().setData(existing_title.titles)
            
            LOGGER.info(f"request ${param.model} param: {param}")
            m = ModelProxy(param.model)
            res: InvokeResult = m.getTitle(param)
            
            if res.isSuccess:
                # 保存标题历史到数据库
                title_history = TitleHistory(
                    uid=userId,
                    domain=param.domain,
                    topic=param.topic,
                    level=param.level,
                    lang=param.lang,
                    keyword=param.keyword,
                    titles=res.data,
                    form_data={
                        "domain": param.domain,
                        "topic": param.topic,
                        "level": param.level,
                        "lang": param.lang,
                        "keyword": param.keyword,
                        "model": param.model
                    }
                )
                
                db.session.add(title_history)
                db.session.commit()
                
                LOGGER.info(f"为用户 {userId} 保存标题历史记录，ID: {title_history.id}")
                
                return Result().setData(res.data)
            
            return Result().error(res.message)
        except Exception as e:
            LOGGER.exception(e)
            return Result().error(str(e))

    def getTitleHistory(self, param: ParamGetTitleHistory) -> Result:
        """获取用户的选题历史记录"""
        try:
            userId = param.userId
            LOGGER.info(f"🔍 查询用户 {userId} 的选题历史记录")
            
            if param.pageNo < 1:
                param.pageNo = 1
            if param.pageSize < 1:
                param.pageSize = 10
                
            offset = param.pageSize * (param.pageNo - 1)
            limit = param.pageSize
            
            # 查询用户的选题历史，按时间倒序
            title_list = (
                TitleHistory.query.filter(TitleHistory.uid == userId)
                .order_by(TitleHistory.id.desc())
                .offset(offset)
                .limit(limit)
                .all()
            )
            
            # 获取总数
            total = TitleHistory.query.filter(TitleHistory.uid == userId).count()
            
            LOGGER.info(f"📋 用户 {userId} 的选题历史记录：共 {total} 条，当前页 {len(title_list)} 条")
            
            # 转换为JSON格式
            result_list = []
            for title_history in title_list:
                result_list.append({
                    "id": title_history.id,
                    "domain": title_history.domain,
                    "topic": title_history.topic,
                    "level": title_history.level,
                    "lang": title_history.lang,
                    "keyword": title_history.keyword,
                    "titles": title_history.titles,
                    "createTime": title_history.create_time.strftime("%Y-%m-%d %H:%M:%S") if title_history.create_time else "",
                    "formData": title_history.form_data
                })
            
            return Result().setData({
                "list": result_list,
                "total": total,
                "pageNo": param.pageNo,
                "pageSize": param.pageSize
            })
            
        except Exception as e:
            LOGGER.exception(e)
            return Result().error(str(e))
    
    def getOutlineHistory(self, param: ParamGetOutlineHistory) -> Result:
        """获取用户的提纲历史记录"""
        try:
            userId = param.userId
            LOGGER.info(f"🔍 查询用户 {userId} 的提纲历史记录")
            
            if param.pageNo < 1:
                param.pageNo = 1
            if param.pageSize < 1:
                param.pageSize = 10
                
            offset = param.pageSize * (param.pageNo - 1)
            limit = param.pageSize
            
            # 查询用户的提纲历史，按时间倒序
            outline_list = (
                OutlineHistory.query.filter(OutlineHistory.uid == userId)
                .order_by(OutlineHistory.id.desc())
                .offset(offset)
                .limit(limit)
                .all()
            )
            
            # 获取总数
            total = OutlineHistory.query.filter(OutlineHistory.uid == userId).count()
            
            LOGGER.info(f"📝 用户 {userId} 的提纲历史记录：共 {total} 条，当前页 {len(outline_list)} 条")
            
            # 转换为JSON格式
            result_list = []
            for outline_history in outline_list:
                result_list.append({
                    "id": outline_history.id,
                    "title": outline_history.title,
                    "level": outline_history.level,
                    "lang": outline_history.lang,
                    "length": outline_history.length,
                    "paragraphCount": outline_history.paragraph_count,
                    "secondParagraphCount": outline_history.second_paragraph_count,
                    "outlines": outline_history.outlines,
                    "rawOutlines": outline_history.raw_outlines,
                    "createTime": outline_history.create_time.strftime("%Y-%m-%d %H:%M:%S") if outline_history.create_time else "",
                    "formData": outline_history.form_data
                })
            
            return Result().setData({
                "list": result_list,
                "total": total,
                "pageNo": param.pageNo,
                "pageSize": param.pageSize
            })
            
        except Exception as e:
            LOGGER.exception(e)
            return Result().error(str(e))
