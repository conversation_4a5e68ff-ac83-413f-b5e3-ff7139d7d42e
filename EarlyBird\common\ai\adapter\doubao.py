import logging
import requests
import json

from EarlyBird.common import Result
from .. import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ai<PERSON>ueryResult
from EarlyBird.config.config import AppConfig


LOGGER = logging.getLogger(__name__)


""""
接入文档：
https://console.volcengine.com/ark/region:ark+cn-beijing/openManagement?current=1&pageSize=10&projectName=default

APIKEY:
https://console.volcengine.com/ark/region:ark+cn-beijing/apiKey

"""




class Doubao(AiAdapter):

    def __init__(self) -> None:
        super().__init__()
        self.modelName = "doubao"

    def query(self, query: AiQuery) -> AiQueryResult:
        try:
            API_KEY = AppConfig.DEEPSEEK_API_KEY
            if not API_KEY or API_KEY == "your_deepseek_api_key_here":
                return AiQueryResult(isValid=False, errMessage="请在配置文件中设置 DeepSeek API Key")
            
            LOGGER.info(f"使用 DeepSeek API Key: {API_KEY[:8]}...")
            LOGGER.info(f"发出请求：{query.userMessage}")

            url = f"{AppConfig.DOUBAO_API_URL}/api/v3/chat/completions"
            headers = {
                "Authorization": f"Bearer {API_KEY}",
                "Content-Type": "application/json",
            }
            payload = {
                "model": "ep-20240612025946-99c77",
                "messages": [{"role": "user", "content": query.userMessage}],
            }
            requests.packages.urllib3.disable_warnings()
            r = requests.post(url=url, headers=headers, json=payload, verify=False)
            LOGGER.info(f"豆包返回code:{r.status_code} body:{r.text}")

            if r.status_code == 200:
                jsonResponse = json.loads(r.text)
                return AiQueryResult(
                    text=jsonResponse["choices"][0]["message"]["content"],
                    totalToken=jsonResponse["usage"]["total_tokens"],
                    modelUsed=jsonResponse["model"],
                )
            else:
                return AiQueryResult(isValid=False, errMessage=r.text)
        except Exception as e:
            LOGGER.exception(e)
            return AiQueryResult(isValid=False, errMessage=str(e))
