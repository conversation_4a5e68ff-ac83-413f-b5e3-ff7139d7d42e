import json
from flask import current_app

import logging
from EarlyBird.common import Result
import requests
from .. import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ueryR<PERSON>ult
from EarlyBird.config.config import AppConfig

#
##
# https://platform.moonshot.cn/docs/api/chat#%E5%BF%AB%E9%80%9F%E5%BC%80%E5%A7%8B
#
#

_MODEL_VERSION = "moonshot-v1-8k"

LOGGER = logging.getLogger(__name__)


class Kimi(AiAdapter):

    def query(self, query: AiQuery) -> AiQueryResult:
        try:
            API_KEY = AppConfig.KIMI_API_KEY
            if not API_KEY or API_KEY == "your_kimi_api_key_here":
                return AiQueryResult(isValid=False, errMessage="请在配置文件中设置 Kimi API Key")
            
            LOGGER.info(f"使用 Kimi API Key: {API_KEY[:8]}...")
            LOGGER.info(f"发出请求：{query.userMessage}")

            url = f"{AppConfig.KIMI_API_URL}/v1/chat/completions"
            headers = {
                "Authorization": f"Bearer {API_KEY}",
                "Content-Type": "application/json",
            }
            payload = {
                "model": _MODEL_VERSION,
                "messages": [{"role": "user", "content": query.userMessage}],
            }

            requests.packages.urllib3.disable_warnings()
            r = requests.post(url=url, headers=headers, json=payload, verify=False)
            LOGGER.info(f"接口返回code:{r.status_code} body:{r.text}")
            
            if r.status_code == 200:
                jsonResponse = json.loads(r.text)
                return AiQueryResult(
                    text=jsonResponse["choices"][0]["message"]["content"],
                    totalToken=jsonResponse["usage"]["total_tokens"],
                )
            else:
                jsonResponse = json.loads(r.text)
                if jsonResponse["error"]["type"] == "invalid_authentication_error":
                    return AiQueryResult(
                        isValid=False, errMessage="配置的 API Key 不正确，请检查"
                    )
                return AiQueryResult(isValid=False, errMessage=r.text)
        except Exception as e:
            LOGGER.exception(e)
            return AiQueryResult(isValid=False, errMessage=str(e))
