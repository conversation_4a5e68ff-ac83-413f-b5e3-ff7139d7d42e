<template>
  <section>
    <div v-for="(p, index) in outlineTree['subtitle']" :key="p['id']" class="tree_node">
      <div
        :class="'tree_title ' + (selectedPid == p.id ? 's' : '')"
        @click="handleClick(p)"
        :data-paraid="p.id"
      >
        <div :class="'tree_title_level' + level">
          [{{ chapterTitlePrefix + (index + 1) }}] {{ p.title }}
        </div>

        <i class="el-icon-warning-outline" v-if="p.status == 14" title="生成失败"></i>
        <i class="el-icon-circle-check" v-if="p.status == 13" title="生成完毕"></i>
        <i class="el-icon-time" v-if="p.status == 11" title="等待生成"></i>
        <i class="el-icon-loading" v-if="p.status == 12" title="生成中"></i>
        <i class="el-icon-remove-outline" v-if="p.status == 1" title="从未生成"></i>
      </div>
      <OutlineTree
        :outlineTree="p"
        :level="level + 1"
        :chapterTitlePrefix="chapterTitlePrefix + (index + 1) + '.'"
      >
      </OutlineTree>
    </div>
  </section>
</template>
<script>
import { mapState } from "vuex";
export default {
  name: "OutlineTree",
  props: {
    chapterTitlePrefix: {
      type: String,
      default: "",
    },
    outlineTree: {
      type: Object,
      default: {},
    },
    level: {
      type: Number,
      default: 1,
    },
  },
  computed: {
    ...mapState({
      selectPara: (state) => state.thesis.outlineTreeSelectPara,
    }),
    selectedPid() {
      return this.selectPara["id"];
    },
  },
  // watch: {
  //   selectedPid(n, o) {
  //     console.log(n, o);
  //   },
  // },

  methods: {
    handleClick(p) {
      this.$store.commit("thesis/SET_SELECTED_PARAGRAPH", p);
    },
  },
};
</script>
<style lang="scss" scoped>
.tree_node {
  overflow: hidden;
}

.tree_title {
  color:#4a4b4c;
  cursor: pointer;
  padding: 7px;
  margin-bottom: 2px;
  border-radius: 4px;
  display: flex;
  align-items: center;

  display: flex;


  i {
    font-weight: bolder;
    font-size: 16px;
    margin-left: 5px;
  }

  i.el-icon-circle-check,
  i.el-icon-check {
    color: green;
  }

  i.el-icon-warning-outline,
  i.el-icon-loading {
    color: red;
  }

  i.el-icon-time {
    color: #0083b0;
  }

  i.el-icon-remove-outline {
    color: orange;
  }
}

.tree_title_level1,
.tree_title_level2,
.tree_title_level3,
.tree_title_level4,
.tree_title_level5,
.tree_title_level6{
  white-space: nowrap;
  overflow: hidden;
}

.tree_title:hover {
  background: #ccc;
}

.tree_title.s {
  background: #bbb;
}

.tree_title_level1 {
  font-size: 14px;
}

.tree_title_level2 {
  font-size: 13px;
  padding-left: 1rem;
}

.tree_title_level3 {
  font-size: 12px;
  padding-left: 1.5rem;
}

.tree_title_level4 {
  font-size: 12px;
  padding-left: 2rem;
}
.tree_title_level5,
.tree_title_level6 {
  font-size: 12px;
  padding-left: 2.5rem;
}
</style>
