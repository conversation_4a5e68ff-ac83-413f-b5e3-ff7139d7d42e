<template>
  <div :class="{'hidden': hidden}" class="pagination-container">
    <el-pagination
      :background="background"
      :current-page.sync="currentPage"
      :page-size.sync="pageSize"
      :layout="layout"
      :page-sizes="pageSizes"
      :total="total"
      :pager-count="5"
      v-bind="$attrs"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script>
export default {
  name: 'Pagination',
  props: {
    total: {
      required: true,
      type: Number
    },
    page: {
      type: Number,
      default: 1
    },
    limit: {
      type: Number,
      default: 20
    },
    pageSizes: {
      type: Array,
      default() {
        return [10, 20, 30, 50]
      }
    },
    layout: {
      type: String,
      default: 'total, sizes, prev, pager, next, jumper'
    },
    background: {
      type: Boolean,
      default: true
    },
    autoScroll: {
      type: Boolean,
      default: true
    },
    hidden: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    currentPage: {
      get() {
        return this.page
      },
      set(val) {
        this.$emit('update:page', val)
      }
    },
    pageSize: {
      get() {
        return this.limit
      },
      set(val) {
        this.$emit('update:limit', val)
      }
    }
  },
  mounted() {
    const paginationTexts = document.querySelectorAll('.el-pagination')
    if (paginationTexts.length > 0) {
      const observer = new MutationObserver(() => {
        this.applyTranslations()
      })
      
      paginationTexts.forEach(el => {
        observer.observe(el, { childList: true, subtree: true })
      })
      
      this.applyTranslations()
    }
  },
  methods: {
    handleSizeChange(val) {
      this.$emit('pagination', { page: this.currentPage, limit: val })
    },
    handleCurrentChange(val) {
      this.$emit('pagination', { page: val, limit: this.pageSize })
      if (this.autoScroll) {
        this.scrollToTop()
      }
    },
    scrollToTop() {
      const main = document.querySelector('.main-container')
      if (main) {
        main.scrollTop = 0
      } else {
        window.scrollTo(0, 0)
      }
    },
    applyTranslations() {
      const gotoElements = document.querySelectorAll('.el-pagination__jump')
      gotoElements.forEach(el => {
        const text = el.innerHTML
        if (text.includes('前往') === false) {
          el.innerHTML = text.replace(/Go to/i, '前往')
        }
      })
      
      const sizeElements = document.querySelectorAll('.el-pagination__sizes')
      sizeElements.forEach(el => {
        const text = el.innerHTML
        if (text.includes('条/页') === false) {
          el.innerHTML = text.replace(/(\d+) \/page/i, '$1 条/页')
        }
      })
      
      const jumpElements = document.querySelectorAll('.el-pagination__jump')
      jumpElements.forEach(el => {
        const text = el.innerHTML
        if (text.includes('页') === false) {
          el.innerHTML = text.replace(/page/i, '页')
        }
      })
      
      const totalElements = document.querySelectorAll('.el-pagination__total')
      totalElements.forEach(el => {
        const text = el.innerHTML
        if (text.includes('共') === false) {
          el.innerHTML = text.replace(/Total (\d+)/i, '共 $1 条')
        }
      })
    }
  }
}
</script>

<style scoped>
.pagination-container {
  background: #fff;
  padding: 32px 16px;
}
.pagination-container.hidden {
  display: none;
}
</style> 