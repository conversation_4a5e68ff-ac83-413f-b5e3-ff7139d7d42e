<template>
  <div class="page-nav">
    <div :class="p == pageNo?'s':''" v-for="p in pageList" :key="p" @click.stop="handlePage(p)">{{ p }}</div>
  </div>
</template>
<script>
export default {
  props: {
    totalPage: {
      type: Number,
      default: 0,
    },
    pageNo: {
      type: Number,
      default: 1,
    },
  },
  data() {
    return {
      startPage: this.pageNo - 3,
      pageList: [],
    };
  },
  watch: {
    totalPage(o, n) {
      this.init();
    },
  },

  methods: {
    handlePage(p) {
      this.$emit("onPageChange", p);
    },
    init() {
      console.log(this.totalPage, this.pageNo);
      if (this.startPage < 1) {
        this.startPage = 1;
      }
      let pageList = [];
      for (let i = 0; i < 8; i++) {
        if (this.startPage + i > this.totalPage) {
          break;
        }
        pageList.push(this.startPage + i);
      }
      console.log(pageList);
      this.pageList = pageList;
    },
  },
};
</script>

<style lang="scss">
.page-nav {
  display: flex;
  margin-top: 10px;
  div {
    border: 1px solid #f1f1f1;
    width: 30px;
    height: 30px;
    text-align: center;
    line-height: 28px;
    margin-right: 10px;
    cursor: pointer;
  }
  div:hover,
  div.s {
    background: #444;
    color: #fff;
  }
}
</style>