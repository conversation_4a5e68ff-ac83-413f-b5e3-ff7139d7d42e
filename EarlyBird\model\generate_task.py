from werkzeug.security import generate_password_hash, check_password_hash

from EarlyBird.common.libs.BaseModel import BaseModel
from sqlalchemy import (
    Integer,
    String,
    Boolean,
    Column,
    JSON,
    Enum,
    SmallInteger,
    DateTime,
    Table,
    BIGINT,
    ForeignKey,
    MetaData,
    TEXT,
)
from sqlalchemy.orm import relationship
import json
from . import TABLE_PREFIX


class GenerateTask(BaseModel):

    class Status(Enum):
        INIT = 1
        RUNING = 2
        SUCCESS = 3
        ERROR = 4

    __tablename__ = f"{TABLE_PREFIX}generate_task"
    __table_args__ = {"comment": "论文生成任务"}

    uid = Column(Integer, nullable=False, comment="用户id")
    thesisId = Column(Integer, nullable=False, comment="论文id")
    status = Column(Integer, default=Status.INIT, comment="当前状态")
    msg = Column(TEXT(65535), comment="消息")
    progress = Column(TEXT(65535), comment="进度json结构")

    def __repr__(self):
        return json.dumps(
            {
                "uid": self.uid,
                "thesisId": self.thesisId,
                "status": self.status,
                "msg": self.msg,
            }
        )
