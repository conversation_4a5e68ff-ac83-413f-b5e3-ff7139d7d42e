<template>
  <div class="admin-stats-overview">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>数据概览</h2>
      <div class="header-actions">
        <el-button type="primary" icon="el-icon-refresh" @click="refreshData" :loading="loading">
          刷新数据
        </el-button>
      </div>
    </div>

    <!-- 数据卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <el-card class="stats-card" shadow="hover">
          <div class="card-content">
            <div class="card-icon users-icon">
              <i class="el-icon-user"></i>
            </div>
            <div class="card-info">
              <div class="card-title">总用户数</div>
              <div class="card-value">{{ overview.total_users || 0 }}</div>
              <div class="card-trend">
                <span class="trend-label">今日新增:</span>
                <span class="trend-value positive">+{{ today.new_users || 0 }}</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card" shadow="hover">
          <div class="card-content">
            <div class="card-icon thesis-icon">
              <i class="el-icon-document"></i>
            </div>
            <div class="card-info">
              <div class="card-title">总论文数</div>
              <div class="card-value">{{ overview.total_thesis || 0 }}</div>
              <div class="card-trend">
                <span class="trend-label">今日新增:</span>
                <span class="trend-value positive">+{{ today.new_thesis || 0 }}</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card" shadow="hover">
          <div class="card-content">
            <div class="card-icon chat-icon">
              <i class="el-icon-chat-dot-round"></i>
            </div>
            <div class="card-info">
              <div class="card-title">聊天记录</div>
              <div class="card-value">{{ overview.total_chat_logs || 0 }}</div>
              <div class="card-trend">
                <span class="trend-label">今日新增:</span>
                <span class="trend-value positive">+{{ today.chat_logs || 0 }}</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card" shadow="hover">
          <div class="card-content">
            <div class="card-icon vip-icon">
              <i class="el-icon-medal"></i>
            </div>
            <div class="card-info">
              <div class="card-title">VIP用户</div>
              <div class="card-value">{{ overview.vip_users || 0 }}</div>
              <div class="card-trend">
                <span class="trend-label">活跃用户:</span>
                <span class="trend-value">{{ overview.active_users || 0 }}</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 详细统计 -->
    <el-row :gutter="20" class="stats-details">
      <el-col :span="12">
        <el-card class="detail-card">
          <div slot="header">
            <span>用户统计详情</span>
          </div>
          <div class="detail-content">
            <div class="detail-item">
              <span class="item-label">VIP用户:</span>
              <span class="item-value">{{ overview.vip_users || 0 }}</span>
            </div>
            <div class="detail-item">
              <span class="item-label">过期VIP:</span>
              <span class="item-value">{{ overview.expired_vip_users || 0 }}</span>
            </div>
            <div class="detail-item">
              <span class="item-label">活跃用户(7天):</span>
              <span class="item-value">{{ overview.active_users || 0 }}</span>
            </div>
            <div class="detail-item">
              <span class="item-label">昨日新增用户:</span>
              <span class="item-value">{{ yesterday.new_users || 0 }}</span>
            </div>
            <div class="detail-item">
              <span class="item-label">昨日新增论文:</span>
              <span class="item-value">{{ yesterday.new_thesis || 0 }}</span>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card class="detail-card">
          <div slot="header">
            <span>系统状态</span>
          </div>
          <div class="detail-content">
            <div class="status-item">
              <div class="status-icon success">
                <i class="el-icon-success"></i>
              </div>
              <div class="status-info">
                <div class="status-title">系统运行正常</div>
                <div class="status-desc">所有服务运行正常</div>
              </div>
            </div>
            <div class="status-item">
              <div class="status-icon info">
                <i class="el-icon-info"></i>
              </div>
              <div class="status-info">
                <div class="status-title">数据库连接</div>
                <div class="status-desc">连接正常</div>
              </div>
            </div>
            <div class="status-item">
              <div class="status-icon warning">
                <i class="el-icon-warning"></i>
              </div>
              <div class="status-info">
                <div class="status-title">存储空间</div>
                <div class="status-desc">使用率 75%</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { adminStats } from '@/api/admin'

export default {
  name: 'AdminStatsOverview',
  data() {
    return {
      loading: false,
      overview: {},
      today: {},
      yesterday: {}
    }
  },
  
  mounted() {
    this.loadData()
  },
  
  methods: {
    // 加载数据概览
    async loadData() {
      try {
        this.loading = true
        const response = await adminStats.getOverview()
        
        if (response.success) {
          this.overview = response.data.overview || {}
          this.today = response.data.today || {}
          this.yesterday = response.data.yesterday || {}
        } else {
          this.$message.error(response.message || '获取数据失败')
        }
      } catch (error) {
        console.error('加载数据概览失败:', error)
        this.$message.error('加载数据失败')
      } finally {
        this.loading = false
      }
    },
    
    // 刷新数据
    refreshData() {
      this.loadData()
    }
  }
}
</script>

<style lang="scss" scoped>
.admin-stats-overview {
  padding: 20px;
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    
    h2 {
      margin: 0;
      color: #1e293b;
      font-size: 24px;
      font-weight: 600;
    }
    
    .header-actions {
      display: flex;
      gap: 12px;
    }
  }
  
  .stats-cards {
    margin-bottom: 24px;
    
    .stats-card {
      .card-content {
        display: flex;
        align-items: center;
        padding: 8px 0;
        
        .card-icon {
          width: 60px;
          height: 60px;
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 16px;
          
          i {
            font-size: 24px;
            color: white;
          }
          
          &.users-icon {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          }
          
          &.thesis-icon {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
          }
          
          &.chat-icon {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
          }
          
          &.vip-icon {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
          }
        }
        
        .card-info {
          flex: 1;
          
          .card-title {
            font-size: 14px;
            color: #64748b;
            margin-bottom: 4px;
          }
          
          .card-value {
            font-size: 28px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 4px;
          }
          
          .card-trend {
            font-size: 12px;
            
            .trend-label {
              color: #64748b;
            }
            
            .trend-value {
              margin-left: 4px;
              
              &.positive {
                color: #10b981;
              }
              
              &.negative {
                color: #ef4444;
              }
            }
          }
        }
      }
    }
  }
  
  .stats-details {
    margin-bottom: 24px;
    
    .detail-card {
      .detail-content {
        .detail-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12px 0;
          border-bottom: 1px solid #f1f5f9;
          
          &:last-child {
            border-bottom: none;
          }
          
          .item-label {
            color: #64748b;
            font-size: 14px;
          }
          
          .item-value {
            color: #1e293b;
            font-weight: 600;
            font-size: 14px;
          }
        }
        
        .status-item {
          display: flex;
          align-items: center;
          padding: 16px 0;
          border-bottom: 1px solid #f1f5f9;
          
          &:last-child {
            border-bottom: none;
          }
          
          .status-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            
            i {
              font-size: 18px;
              color: white;
            }
            
            &.success {
              background: #10b981;
            }
            
            &.info {
              background: #3b82f6;
            }
            
            &.warning {
              background: #f59e0b;
            }
            
            &.error {
              background: #ef4444;
            }
          }
          
          .status-info {
            flex: 1;
            
            .status-title {
              font-size: 14px;
              font-weight: 600;
              color: #1e293b;
              margin-bottom: 2px;
            }
            
            .status-desc {
              font-size: 12px;
              color: #64748b;
            }
          }
        }
      }
    }
  }
}
</style> 