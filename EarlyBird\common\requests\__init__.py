"""
Request模型批量引用文件
统一管理所有API模块的request模型和schema定义
"""

# 用户相关请求模型
from EarlyBird.api.user.entity import (
    VipLogListModel,
    ApiLogListModel, 
    LoginModel,
    RegisterModel
)

# 论文相关请求模型
from EarlyBird.api.thesis.schema import (
    ParamThesisId,
    ParamGenerateOutline,
    ParamSaveSingleParagraph,
    ParamSaveDigest,
    SaveNewPara,
    MovePara,
    DeleteParagraph,
    SaveThesisProperty,
    ParamPayDownload,
    ParamPaymentStatus,
    ParamConfirmPayment
)

# AI对话相关请求模型
from EarlyBird.api.talk2ai.dantic import (
    ParamGetChatLog,
    ParamDelChat,
    ParamChat,
    ParamTitle,
    ParamOutline,
    ParamGetContentFromOutline,
    ParamGenerateSingleParagraph,
    ParamRegenDigest
)

# 首页相关请求模型
from EarlyBird.api.home.dantic import (
    ParamSetting
)

# 生成相关请求模型
from EarlyBird.api.generate.apis import (
    ParamTableContent
)

# AI相关基础模型
from EarlyBird.common.ai import (
    AiQuery,
    AiQueryResult
)

# 管理员相关请求模型
from EarlyBird.common.requests.admin_requests import (
    AdminLoginRequest,
    AdminCreateRequest,
    AdminUpdateRequest,
    AdminPasswordResetRequest,
    AdminListRequest,
    UserListRequest,
    UserUpdateRequest,
    UserVipRequest,
    ThesisListRequest,
    PaymentConfigRequest,
    WeChatPayConfigRequest,
    SystemSettingRequest,
    ApiTestRequest,
    StatsQueryRequest
)

# 支付相关请求模型
from EarlyBird.common.requests.payment_requests import (
    PaymentCreateRequest,
    PaymentNotifyRequest,
    PaymentQueryRequest,
    PaymentRefundRequest,
    WeChatPayCreateRequest,
    WeChatPayNotifyRequest,
    V990rPayCreateRequest,
    V990rPayNotifyRequest,
    PaymentListRequest,
    PaymentStatsRequest,
    ThesisDownloadPayRequest,
    PaymentStatusRequest,
    PaymentConfirmRequest
)

# 基础字段模型
from EarlyBird.api.talk2ai.dantic import _BaseFields

# 导出所有请求模型
__all__ = [
    # 用户相关
    'VipLogListModel',
    'ApiLogListModel',
    'LoginModel',
    'RegisterModel',

    # 论文相关
    'ParamThesisId',
    'ParamGenerateOutline',
    'ParamSaveSingleParagraph',
    'ParamSaveDigest',
    'SaveNewPara',
    'MovePara',
    'DeleteParagraph',
    'SaveThesisProperty',
    'ParamPayDownload',
    'ParamPaymentStatus',
    'ParamConfirmPayment',

    # AI对话相关
    'ParamGetChatLog',
    'ParamDelChat',
    'ParamChat',
    'ParamTitle',
    'ParamOutline',
    'ParamGetContentFromOutline',
    'ParamGenerateSingleParagraph',
    'ParamRegenDigest',

    # 首页相关
    'ParamSetting',

    # 生成相关
    'ParamTableContent',

    # AI基础模型
    'AiQuery',
    'AiQueryResult',

    # 管理员相关
    'AdminLoginRequest',
    'AdminCreateRequest',
    'AdminUpdateRequest',
    'AdminPasswordResetRequest',
    'AdminListRequest',
    'UserListRequest',
    'UserUpdateRequest',
    'UserVipRequest',
    'ThesisListRequest',
    'PaymentConfigRequest',
    'WeChatPayConfigRequest',
    'SystemSettingRequest',
    'ApiTestRequest',
    'StatsQueryRequest',

    # 支付相关
    'PaymentCreateRequest',
    'PaymentNotifyRequest',
    'PaymentQueryRequest',
    'PaymentRefundRequest',
    'WeChatPayCreateRequest',
    'WeChatPayNotifyRequest',
    'V990rPayCreateRequest',
    'V990rPayNotifyRequest',
    'PaymentListRequest',
    'PaymentStatsRequest',
    'ThesisDownloadPayRequest',
    'PaymentStatusRequest',
    'PaymentConfirmRequest',

    # 基础模型
    '_BaseFields'
]
