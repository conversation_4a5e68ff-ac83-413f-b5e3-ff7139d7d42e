<template>
  <div class="user-download-rights">
    <div class="rights-content" v-if="rightsData">
      <!-- 当前论文状态 -->
      <div class="rights-text current-thesis" v-if="rightsData.current_thesis_paid">
        ✅ 当前论文已支付，可以随时免费下载
      </div>

      <!-- 免费下载状态 -->
      <div class="rights-text free-downloads" v-else-if="rightsData.remaining_free_downloads !== 0">
        <span v-if="rightsData.remaining_free_downloads === -1">
          🆓 {{ rightsData.is_vip ? 'VIP用户无限下载' : '免费下载' }}
        </span>
        <span v-else>
          🆓 剩余免费下载：{{ rightsData.remaining_free_downloads }} 次
        </span>
        <span v-if="rightsData.first_free && rightsData.user_download_count === 0" class="sub-text">
          （首次下载免费）
        </span>
      </div>

      <!-- 需要付费状态 -->
      <div class="rights-text payment-required" v-else>
        💰 需要付费下载：<span class="price">¥{{ rightsData.download_price.toFixed(2) }}</span>
      </div>

      <!-- VIP状态显示 -->
      <div class="rights-text vip-status" v-if="rightsData.is_vip">
        👑 VIP用户 | 已下载 {{ rightsData.user_download_count }} 篇论文
      </div>
      <div class="rights-text user-status" v-else>
        👤 普通用户 | 已下载 {{ rightsData.user_download_count }} 篇论文
      </div>
    </div>

    <!-- 加载状态 -->
    <div class="rights-text loading" v-else-if="loading">
      ⏳ 正在获取下载权限信息...
    </div>

    <!-- 错误状态 -->
    <div class="rights-text error" v-else-if="error">
      ❌ {{ error }} <button @click="fetchRights" class="retry-btn">重试</button>
    </div>
  </div>
</template>

<script>
import { getUserDownloadRights } from '@/api/thesis'

export default {
  name: 'UserDownloadRights',
  props: {
    thesisId: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      rightsData: null,
      loading: false,
      error: null
    }
  },
  mounted() {
    this.fetchRights()
  },
  watch: {
    thesisId(newId, oldId) {
      if (newId !== oldId) {
        this.fetchRights()
      }
    }
  },
  methods: {
    async fetchRights() {
      if (!this.thesisId) return
      
      this.loading = true
      this.error = null
      
      try {
        const response = await getUserDownloadRights({ thesisId: this.thesisId })
        
        if (response && response.is_success) {
          this.rightsData = response.data
          console.log('获取下载权限信息成功:', this.rightsData)
        } else {
          this.error = response?.message || '获取下载权限信息失败'
        }
      } catch (error) {
        console.error('获取下载权限信息失败:', error)
        this.error = '网络错误，请稍后重试'
      } finally {
        this.loading = false
      }
    },
    
    // 刷新权限信息（供父组件调用）
    refresh() {
      this.fetchRights()
    }
  }
}
</script>

<style scoped>
.user-download-rights {
  margin: 8px 0;
}

.rights-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.rights-text {
  font-size: 13px;
  line-height: 1.4;
  color: #495057;
  padding: 2px 0;
}

.rights-text.current-thesis {
  color: #28a745;
  font-weight: 500;
}

.rights-text.free-downloads {
  color: #17a2b8;
  font-weight: 500;
}

.rights-text.payment-required {
  color: #856404;
  font-weight: 500;
}

.rights-text.vip-status {
  color: #dc3545;
  font-weight: 500;
}

.rights-text.user-status {
  color: #6c757d;
}

.rights-text.loading {
  color: #6c757d;
  font-style: italic;
}

.rights-text.error {
  color: #dc3545;
}

.sub-text {
  color: #6c757d;
  font-size: 12px;
}

.price {
  color: #dc3545;
  font-weight: bold;
}

.retry-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 2px 8px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 11px;
  margin-left: 8px;
}

.retry-btn:hover {
  background: #0056b3;
}
</style>
