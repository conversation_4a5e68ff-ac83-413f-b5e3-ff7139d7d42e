import logging
import time
from threading import Lock
import platform
from .platform_utils import is_windows, is_linux

logger = logging.getLogger(__name__)

# 根据操作系统动态导入相应的SDK
if is_windows():
    from .RkSDK import RkSDK, Rk初始化软件入参类, Rk通讯加密方式枚举类, Rk禁用还是删除枚举类, Rk结果心跳失败类
    import pythoncom
    logger.info("Windows环境，使用原生RkSDK")
else:
    from .RkSDK_linux import RkSDK, Rk初始化软件入参类, Rk通讯加密方式枚举类, Rk禁用还是删除枚举类, Rk结果心跳失败类
    logger.info("非Windows环境，使用Linux兼容版RkSDK")
    # 创建pythoncom模拟，避免Linux环境下导入错误
    class pythoncom:
        @staticmethod
        def CoInitialize():
            pass
        @staticmethod
        def CoUninitialize():
            pass

class RkSDKWrapper:
    """RkSDK 包装类，提供统一的接口和配置管理"""
    
    _instance = None
    _lock = Lock()
    _last_check_time = 0
    _check_interval = 2  # 状态检查最小间隔(秒)
    _last_activation_time = 0  # 最后激活时间
    _activation_grace_period = 10  # 激活后宽限期(秒)，在此期间不进行严格的服务器验证
    
    def __new__(cls):
        """单例模式"""
        if cls._instance is None:
            cls._instance = super(RkSDKWrapper, cls).__new__(cls)
            cls._instance._initialized = False
            cls._instance._sdk_initialized = False
            cls._instance._current_activation_key = None
            cls._instance._init_attempts = 0
            cls._instance._max_init_attempts = 3
        return cls._instance
    
    def __init__(self):
        """初始化 RkSDK"""
        if self._initialized:
            return
            
        self.rksdk = RkSDK()
        self.rk初始化软件入参 = Rk初始化软件入参类()
        self._initialized = True
        self._init_params()
        
    def _init_params(self):
        """初始化参数配置"""
        self.rk初始化软件入参.平台用户编码 = "e4d1116348842475"  # 替换为您的平台用户编码
        self.rk初始化软件入参.软件编码 = "29a106804a7b3a1b"    # 替换为您的软件编码
        self.rk初始化软件入参.通讯加密方式 = Rk通讯加密方式枚举类.RC4加密
        self.rk初始化软件入参.加密Key = "8b35aaa3"
        self.rk初始化软件入参.签名盐 = "25128d48"
        self.rk初始化软件入参.软件版本号 = "v1.0"
        self.rk初始化软件入参.心跳失败的回调函数 = self._handle_heartbeat_failure
        
    def _handle_heartbeat_failure(self, rk结果心跳失败: Rk结果心跳失败类):
        """处理心跳失败的回调函数"""
        error_messages = {
            1053: "此软件已被禁用",
            6005: "卡密被禁用",
            6003: "卡密到期",
            6004: "卡密点数不足",
            4005: "账号被禁用",
            4003: "账号到期",
            4004: "账号点数不足",
            1067: "被其它顶号登录",
            1013: "非绑定电脑上登陆",
            1093: "请求过快，请10分钟后再试"
        }
        
        error_code = rk结果心跳失败.错误编码
        error_message = error_messages.get(error_code, rk结果心跳失败.错误消息)
        
        logger.error(f"心跳失败: {error_code} - {error_message}")
        self._sdk_initialized = False
        self._current_activation_key = None
        
        # 关闭软件
        self.rksdk.关闭当前软件()
    
    def is_initialized(self):
        """检查 SDK 是否已初始化"""
        return self._sdk_initialized
    
    def initialize(self):
        """初始化软件"""
        try:
            # 初始化 COM (仅Windows环境需要)
            pythoncom.CoInitialize()
            
            # 初始化软件
            rk结果初始化软件 = self.rksdk.初始化软件函数(self.rk初始化软件入参)
            if rk结果初始化软件.错误编码 == 0:
                self._sdk_initialized = True
                
                # 如果有保存的激活码，尝试重新激活
                if self._current_activation_key:
                    try:
                        self.activate(self._current_activation_key)
                    except:
                        logger.exception("重新激活失败")
                        self._current_activation_key = None
                
                return rk结果初始化软件
            else:
                logger.error(f"初始化软件失败: {rk结果初始化软件.错误消息}")
                self._sdk_initialized = False
                raise Exception(f"初始化软件失败: {rk结果初始化软件.错误消息}")
        except Exception as e:
            logger.exception("初始化软件失败")
            self._sdk_initialized = False
            raise
        finally:
            # 取消初始化 COM (仅Windows环境需要)
            pythoncom.CoUninitialize()
    
    def ensure_initialized(self):
        """确保 SDK 已初始化,添加重试次数限制"""
        if not self._sdk_initialized:
            with self._lock:
                if self._init_attempts >= self._max_init_attempts:
                    logger.error("SDK初始化尝试次数过多")
                    raise Exception("SDK初始化失败: 尝试次数过多")
                self._init_attempts += 1
                try:
                    self.initialize()
                    # 初始化成功后重置重试计数器
                    self._init_attempts = 0
                except Exception as e:
                    logger.error(f"SDK初始化失败: {str(e)}")
                    raise
    
    def reset_init_attempts(self):
        """手动重置初始化尝试次数"""
        with self._lock:
            self._init_attempts = 0
            logger.info("SDK初始化尝试次数已重置")
    
    def activate(self, activation_key: str):
        """激活卡密"""
        try:
            # 初始化 COM (仅Windows环境需要)
            pythoncom.CoInitialize()
            
            self.ensure_initialized()
            result = self.rksdk.单码登录函数(activation_key)
            if result.错误编码 == 0:
                self._current_activation_key = activation_key
                # 记录激活时间，用于宽限期判断
                self._last_activation_time = time.time()
                logger.info(f"激活成功，设置宽限期 {self._activation_grace_period} 秒")
            return result
        except Exception as e:
            logger.exception("激活卡密失败")
            raise
        finally:
            # 取消初始化 COM (仅Windows环境需要)
            pythoncom.CoUninitialize()
    
    def deactivate(self):
        """注销激活"""
        try:
            # 初始化 COM (仅Windows环境需要)
            pythoncom.CoInitialize()
            
            self.ensure_initialized()
            result = self.rksdk.退出登录函数()
            if result.错误编码 == 0:
                self._sdk_initialized = False
                self._current_activation_key = None
            return result
        except Exception as e:
            logger.exception("注销激活失败")
            raise
        finally:
            # 取消初始化 COM (仅Windows环境需要)
            pythoncom.CoUninitialize()
    
    def check_status(self):
        """检查激活状态"""
        try:
            # 初始化 COM (仅Windows环境需要)
            pythoncom.CoInitialize()
            
            if not self.is_initialized():
                self.initialize()
            
            # 检查是否在激活后的宽限期内
            current_time = time.time()
            in_grace_period = (current_time - self._last_activation_time) < self._activation_grace_period
            
            if in_grace_period:
                logger.info(f"在激活后宽限期内({current_time - self._last_activation_time:.1f}秒)，使用宽松验证")
                # 在宽限期内，如果SDK有登录状态，优先信任本地状态
                if hasattr(self.rksdk, 'is_login') and self.rksdk.is_login():
                    # 创建一个成功的结果对象
                    try:
                        from .RkSDK_linux import Rk结果单码详情类
                        result = Rk结果单码详情类()
                        result.错误编码 = 0
                        result.错误消息 = "激活有效(宽限期)"
                        result.到期时间 = "2025-07-13 09:00:55"  # 使用默认过期时间
                        result.剩余点数 = 999
                        result.是否已开通 = True
                        result.是否已激活 = True
                        result.机器码 = getattr(self.rksdk, '_RkSDK__maccode', 'unknown')
                        logger.info("宽限期内返回本地激活状态")
                        return result
                    except Exception as e:
                        logger.warning(f"创建宽限期结果失败: {str(e)}")
            
            # 直接调用单码详情函数，不依赖is_login方法
            # 因为is_login方法可能不可用或不准确
            result = self.rksdk.单码详情函数()
            logger.info(f"SDK单码详情查询结果: 错误编码={result.错误编码}, 错误消息={getattr(result, '错误消息', '无')}")
            
            # 如果在宽限期内且服务器返回网络错误，使用更宽松的处理
            if in_grace_period and result.错误编码 in [-998, -999, -4]:
                logger.warning(f"宽限期内服务器验证失败({result.错误编码})，但保持激活状态")
                # 尝试从本地状态创建成功结果
                if hasattr(self.rksdk, 'is_login') and self.rksdk.is_login():
                    try:
                        from .RkSDK_linux import Rk结果单码详情类
                        grace_result = Rk结果单码详情类()
                        grace_result.错误编码 = 0
                        grace_result.错误消息 = "激活有效(宽限期-网络错误)"
                        grace_result.到期时间 = "2025-07-13 09:00:55"
                        grace_result.剩余点数 = 999
                        grace_result.是否已开通 = True
                        grace_result.是否已激活 = True
                        grace_result.机器码 = getattr(self.rksdk, '_RkSDK__maccode', 'unknown')
                        return grace_result
                    except:
                        pass
            
            return result
            
        except Exception as e:
            logger.error(f"检查激活状态失败: {str(e)}")
            # 创建未激活状态的结果对象
            try:
                result = self.rksdk.单码详情函数.__annotations__['return']()
                result.错误编码 = 1001
                result.错误消息 = "检查状态异常"
                return result
            except:
                # 如果无法创建结果对象，返回一个简单的对象
                class SimpleResult:
                    def __init__(self):
                        self.错误编码 = 1001
                        self.错误消息 = "检查状态异常"
                return SimpleResult()
        finally:
            # 取消初始化 COM (仅Windows环境需要)
            pythoncom.CoUninitialize()
    
    def get_software_info(self):
        """获取软件信息"""
        if not self.is_initialized():
            try:
                self.initialize()
            except:
                return None
        
        try:
            info = self.rksdk.软件信息函数()
            result = {
                'name': info.软件名称,
                'version': info.软件当前最新版本号,
                'announcement': info.软件公告,
                'website': getattr(info, '咨询官网', ''),
                'sdkVersion': self.rksdk.获取瑞科验证SDK当前版本号()
            }
            return result
        except Exception as e:
            logger.error(f"获取软件信息失败: {str(e)}")
            return None
    
    def is_login(self):
        """检查是否已登录"""
        try:
            # 尝试多种可能的方法名
            if hasattr(self.rksdk, 'is_login'):
                return self.rksdk.is_login()
            elif hasattr(self.rksdk, 'is_logged_in'):
                return self.rksdk.is_logged_in()
            elif hasattr(self.rksdk, '__isLogin'):
                return self.rksdk.__isLogin and not getattr(self.rksdk, '__isLoginOut', False)
            else:
                # 返回False表示未登录
                logger.warning("RkSDK对象没有登录状态检查方法，默认为未登录")
                return False
        except Exception as e:
            logger.error(f"检查登录状态失败: {str(e)}")
            return False
    
    def unbind_machine(self, card_or_account):
        """解绑机器码"""
        try:
            with self._lock:
                if not self._sdk_initialized:
                    logger.warning("SDK未初始化，无法解绑机器码")
                    return {
                        'success': False,
                        'error_code': -2,
                        'message': 'SDK未初始化'
                    }
                
                logger.info(f"开始解绑机器码: {card_or_account}")
                
                # 调用SDK解绑功能
                result = self.rksdk.解绑机器码函数(card_or_account)
                
                if result.错误编码 == 0:
                    logger.info(f"解绑机器码成功: {card_or_account}")
                    return {
                        'success': True,
                        'error_code': 0,
                        'message': '解绑成功',
                        'data': {
                            'expire_time': result.到期时间,
                            'remaining_points': result.剩余点数
                        }
                    }
                else:
                    logger.warning(f"解绑机器码失败: {result.错误编码} - {result.错误消息}")
                    return {
                        'success': False,
                        'error_code': result.错误编码,
                        'message': result.错误消息
                    }
                    
        except Exception as e:
            logger.exception("解绑机器码异常")
            return {
                'success': False,
                'error_code': -1,
                'message': f'解绑异常: {str(e)}'
            } 