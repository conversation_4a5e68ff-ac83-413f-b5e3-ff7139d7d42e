import os
import logging

import threading


logger = logging.getLogger(__name__)


class _ApiServerThread(threading.Thread):
    def __init__(self, server):
        super().__init__()
        self.server = server

    def run(self):
        try:
            logger.info("_ApiServerThread start")
            self.server.run()
            logger.info("_ApiServerThread exit")
        except Exception as e:
            logger.exception(e)


class TaskApiServer(threading.Thread):

    def __init__(self, shutdownEvent: threading.Event, flaskApp):
        super().__init__()
        self.app = flaskApp
        self.shutdownEvent = shutdownEvent
        self.server = None
        logger.info("TaskApiServer start")

    def run(self):
        try:
            from waitress import create_server

            host = self.app.config["RUN_HOST"]
            # 使用不同的端口避免与主web服务器冲突
            port = self.app.config.get("TASK_API_PORT", self.app.config["RUN_PORT"] + 1)  # 默认使用主端口+1

            self.server = create_server(
                self.app,
                host=host,
                port=port,
                threads=10,
            )
            logger.info(f"TaskApiServer listen on {host}:{port}")
            _ApiServerThread(self.server).start()

            while True:
                if self.shutdownEvent.isSet():
                    logger.info("apiServer start close")
                    self.server.close()
                    logger.info("apiServer shutdown")

                    break
                self.shutdownEvent.wait(3)


            logger.info("TaskApiServer exit")


        except Exception as e:
            # logger.exception(e)
            logger.info(f"TaskApiServer exception ({str(e)})")
