<!--
作者名：EarlyBird
作者邮箱：<EMAIL>
官方网址：https://blog.zaoniao.vip

支付订单详情页面
-->
<template>
  <div class="order-detail">
    <div class="page-header">
      <h2>支付订单详情</h2>
      <div class="header-actions">
        <el-button type="primary" @click="loadOrderDetail" icon="el-icon-refresh">刷新</el-button>
        <el-button @click="goBack" icon="el-icon-back">返回</el-button>
      </div>
    </div>

    <div v-loading="loading">
      <!-- 订单基本信息 -->
      <el-card class="detail-card">
        <div slot="header">
          <span>基本信息</span>
        </div>
        
        <el-descriptions :column="2" border>
          <el-descriptions-item label="订单ID">{{ orderDetail.id || '-' }}</el-descriptions-item>
          <el-descriptions-item label="用户ID">{{ orderDetail.user_id || '-' }}</el-descriptions-item>
          <el-descriptions-item label="商品ID">{{ orderDetail.product_id || '-' }}</el-descriptions-item>
          <el-descriptions-item label="支付金额">{{ orderDetail.amount ? `${orderDetail.amount.toFixed(2)} 元` : '-' }}</el-descriptions-item>
          <el-descriptions-item label="商户订单号">{{ orderDetail.out_trade_no || '-' }}</el-descriptions-item>
          <el-descriptions-item label="平台订单号">{{ orderDetail.trade_no || '-' }}</el-descriptions-item>
          <el-descriptions-item label="支付状态">
            <el-tag :type="getStatusType(orderDetail.status)">
              {{ getStatusText(orderDetail.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="支付方式">{{ orderDetail.pay_type || '-' }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ orderDetail.create_time || '-' }}</el-descriptions-item>
          <el-descriptions-item label="支付时间">{{ orderDetail.pay_time || '-' }}</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 关联下载记录 -->
      <el-card class="detail-card" v-if="orderDetail.download_records && orderDetail.download_records.length > 0">
        <div slot="header">
          <span>关联下载记录</span>
        </div>
        
        <el-table :data="orderDetail.download_records" style="width: 100%" border>
          <el-table-column prop="id" label="记录ID" width="80" align="center"></el-table-column>
          <el-table-column prop="thesis_id" label="论文ID" width="100" align="center"></el-table-column>
          <el-table-column prop="is_paid" label="支付状态" width="100" align="center">
            <template slot-scope="scope">
              <el-tag :type="scope.row.is_paid ? 'success' : 'warning'">
                {{ scope.row.is_paid ? '已支付' : '未支付' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="payment_time" label="支付时间" width="180" align="center"></el-table-column>
          <el-table-column prop="create_time" label="创建时间" width="180" align="center"></el-table-column>
        </el-table>
      </el-card>

      <!-- 回调通知数据 -->
      <el-card class="detail-card" v-if="orderDetail.notify_data">
        <div slot="header">
          <span>回调通知数据</span>
        </div>
        
        <div class="json-viewer">
          <pre>{{ formatJSON(orderDetail.notify_data) }}</pre>
        </div>
      </el-card>

      <!-- 解析后的通知数据 -->
      <el-card class="detail-card" v-if="orderDetail.notify_data_parsed">
        <div slot="header">
          <span>解析后的通知数据</span>
        </div>
        
        <div class="json-viewer">
          <pre>{{ formatJSON(orderDetail.notify_data_parsed) }}</pre>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import { adminPayment } from '@/api/admin'

export default {
  name: 'PaymentOrderDetail',
  data() {
    return {
      orderId: null,
      orderDetail: {},
      loading: false
    }
  },
  
  created() {
    this.orderId = this.$route.params.id
    if (this.orderId) {
      this.loadOrderDetail()
    } else {
      this.$message.error('订单ID不能为空')
      this.goBack()
    }
  },
  
  methods: {
    /**
     * 加载订单详情
     */
    async loadOrderDetail() {
      if (!this.orderId) return
      
      try {
        this.loading = true
        const response = await adminPayment.getOrderDetail(this.orderId)
        
        if (response.success && response.data) {
          this.orderDetail = response.data
        } else {
          this.$message.error(response.message || '加载订单详情失败')
        }
      } catch (error) {
        console.error('加载订单详情失败:', error)
        this.$message.error('加载订单详情失败: ' + (error.message || '未知错误'))
      } finally {
        this.loading = false
      }
    },
    
    /**
     * 获取状态类型
     */
    getStatusType(status) {
      switch (status) {
        case 0:
          return 'warning'
        case 1:
          return 'success'
        case 2:
          return 'info'
        default:
          return 'info'
      }
    },
    
    /**
     * 获取状态文本
     */
    getStatusText(status) {
      switch (status) {
        case 0:
          return '未支付'
        case 1:
          return '已支付'
        case 2:
          return '已退款'
        default:
          return '未知'
      }
    },
    
    /**
     * 格式化JSON
     */
    formatJSON(data) {
      if (!data) return ''
      
      try {
        if (typeof data === 'string') {
          return JSON.stringify(JSON.parse(data), null, 2)
        } else {
          return JSON.stringify(data, null, 2)
        }
      } catch (error) {
        return data
      }
    },
    
    /**
     * 返回上一页
     */
    goBack() {
      this.$router.push({ name: 'AdminPaymentOrders' })
    }
  }
}
</script>

<style scoped>
.order-detail {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.detail-card {
  margin-bottom: 20px;
}

.json-viewer {
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 10px;
  overflow-x: auto;
}

.json-viewer pre {
  margin: 0;
  font-family: monospace;
  white-space: pre-wrap;
  word-break: break-all;
}
</style> 