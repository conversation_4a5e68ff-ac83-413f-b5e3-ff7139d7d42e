#!/bin/bash

echo "================================================"
echo "🔧 早鸟论文系统 - 依赖修复脚本"
echo "================================================"
echo

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Python环境
check_python() {
    log_info "检查Python环境..."
    
    if ! command -v python3 &> /dev/null; then
        log_error "未找到python3，请先安装Python 3.8+"
        exit 1
    fi
    
    PYTHON_VERSION=$(python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
    log_info "Python版本: $PYTHON_VERSION"
}

# 安装缺失的依赖
install_missing_deps() {
    log_info "安装缺失的AI服务依赖..."
    
    # 安装dashscope (阿里云千问)
    log_info "安装dashscope..."
    python3 -m pip install dashscope -i https://pypi.tuna.tsinghua.edu.cn/simple
    
    # 安装其他可能缺失的AI相关依赖
    log_info "安装其他AI依赖..."
    python3 -m pip install openai -i https://pypi.tuna.tsinghua.edu.cn/simple
    python3 -m pip install anthropic -i https://pypi.tuna.tsinghua.edu.cn/simple
    
    log_info "依赖安装完成"
}

# 测试导入
test_imports() {
    log_info "测试关键模块导入..."
    
    python3 -c "
import sys
import os
sys.path.insert(0, os.getcwd())

# 测试基础模块
try:
    import flask
    print('✅ Flask')
except ImportError as e:
    print(f'❌ Flask: {e}')

try:
    import sqlalchemy
    print('✅ SQLAlchemy')
except ImportError as e:
    print(f'❌ SQLAlchemy: {e}')

try:
    import dashscope
    print('✅ DashScope (千问)')
except ImportError as e:
    print(f'⚠️  DashScope: {e}')

try:
    import requests
    print('✅ Requests')
except ImportError as e:
    print(f'❌ Requests: {e}')

try:
    import pydantic
    print('✅ Pydantic')
except ImportError as e:
    print(f'❌ Pydantic: {e}')

# 测试项目模块
try:
    from EarlyBird.common.ai import getAdapter
    print('✅ AI模块')
except ImportError as e:
    print(f'⚠️  AI模块: {e}')

try:
    from EarlyBird.api.app import create_app
    print('✅ Flask应用')
except ImportError as e:
    print(f'❌ Flask应用: {e}')
"
}

# 创建启动脚本
create_startup_script() {
    log_info "创建启动脚本..."
    
    cat > start_server.sh << 'EOF'
#!/bin/bash

echo "启动早鸟论文系统..."

# 设置环境变量
export FLASK_ENV=production
export PYTHONPATH=$(pwd):$PYTHONPATH

# 检查端口是否被占用
if lsof -Pi :3301 -sTCP:LISTEN -t >/dev/null ; then
    echo "端口3301已被占用，正在停止现有进程..."
    pkill -f "python.*web_server.py"
    sleep 2
fi

# 启动服务
echo "启动Web服务器..."
cd $(dirname $0)
python3 EarlyBird/web_server.py

EOF

    chmod +x start_server.sh
    log_info "启动脚本已创建: start_server.sh"
}

# 创建停止脚本
create_stop_script() {
    log_info "创建停止脚本..."
    
    cat > stop_server.sh << 'EOF'
#!/bin/bash

echo "停止早鸟论文系统..."

# 查找并停止相关进程
pkill -f "python.*web_server.py"
pkill -f "python.*server.py"

echo "服务已停止"
EOF

    chmod +x stop_server.sh
    log_info "停止脚本已创建: stop_server.sh"
}

# 主函数
main() {
    log_info "开始修复依赖问题..."
    
    check_python
    install_missing_deps
    test_imports
    create_startup_script
    create_stop_script
    
    echo
    log_info "🎉 依赖修复完成！"
    echo
    echo "现在可以尝试启动服务："
    echo "  ./start_server.sh"
    echo
    echo "停止服务："
    echo "  ./stop_server.sh"
    echo
    echo "如果仍有问题，请检查："
    echo "1. 数据库配置: EarlyBird/config/production.ini"
    echo "2. API密钥配置: EarlyBird/config/api_keys.ini"
    echo "3. 查看详细错误日志"
}

main "$@"
