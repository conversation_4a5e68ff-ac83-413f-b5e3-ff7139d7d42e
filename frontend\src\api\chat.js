
import request from '@/utils/request'


export function chat(params) {
    return request({
        url: '/api/talk/chat',
        method: 'post',
        data: params,
        timeout: 500000,
    })
}

export function delChat(params) {
    return request({
        url: '/api/talk/delChat',
        method: 'post',
        data: params,
        timeout: 500000,
    })
}

export function getChatLog(params) {
    return request({
        url: '/api/talk/getChatLog',
        method: 'post',
        data: params,
        timeout: 500000,
    })
}

