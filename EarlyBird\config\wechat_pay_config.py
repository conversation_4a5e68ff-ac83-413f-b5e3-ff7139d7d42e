#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
作者名：EarlyBird
作者邮箱：<EMAIL>
官方网址：https://blog.zaoniao.vip

微信支付V3配置文件
支持环境变量和数据库配置
"""

import os
import logging

logger = logging.getLogger(__name__)

class WeChatPayConfig:
    """微信支付V3配置类"""
    
    # 环境变量配置（备用）
    APPID = os.getenv('WECHAT_APPID', 'your_appid_here')
    MCHID = os.getenv('WECHAT_MCHID', 'your_mchid_here')
    API_V3_KEY = os.getenv('WECHAT_API_V3_KEY', 'your_api_v3_key_here')
    SERIAL_NO = os.getenv('WECHAT_SERIAL_NO', 'your_serial_no_here')
    PRIVATE_KEY_PATH = os.getenv('WECHAT_PRIVATE_KEY_PATH', 'certs/apiclient_key.pem')
    PLATFORM_CERT_PATH = os.getenv('WECHAT_PLATFORM_CERT_PATH', 'certs/platform_cert.pem')
    NOTIFY_URL = os.getenv('WECHAT_NOTIFY_URL', 'https://your-domain.com/api/pay/wechat_notify')
    SANDBOX = os.getenv('WECHAT_SANDBOX', 'false').lower() == 'true'
    
    # 沙箱环境配置
    SANDBOX_APPID = os.getenv('WECHAT_SANDBOX_APPID', 'your_sandbox_appid_here')
    SANDBOX_MCHID = os.getenv('WECHAT_SANDBOX_MCHID', 'your_sandbox_mchid_here')
    SANDBOX_API_V3_KEY = os.getenv('WECHAT_SANDBOX_API_V3_KEY', 'your_sandbox_api_v3_key_here')
    SANDBOX_SERIAL_NO = os.getenv('WECHAT_SANDBOX_SERIAL_NO', 'your_sandbox_serial_no_here')
    SANDBOX_PRIVATE_KEY_PATH = os.getenv('WECHAT_SANDBOX_PRIVATE_KEY_PATH', 'certs/sandbox_apiclient_key.pem')
    
    @classmethod
    def get_config(cls):
        """获取当前环境的配置，优先使用数据库配置"""
        try:
            # 尝试从数据库获取配置
            from EarlyBird.model.wechat_pay_config import WeChatPayConfig as DBConfig
            db_config = DBConfig.get_config_dict()
            
            if db_config and db_config.get('appid') and not db_config['appid'].startswith('your_'):
                logger.info("使用数据库中的微信支付配置")
                return db_config
        except Exception as e:
            logger.warning(f"无法从数据库获取微信支付配置: {str(e)}")
        
        # 使用环境变量配置（备用）
        logger.info("使用环境变量中的微信支付配置")
        if cls.SANDBOX:
            return {
                'appid': cls.SANDBOX_APPID,
                'mchid': cls.SANDBOX_MCHID,
                'api_v3_key': cls.SANDBOX_API_V3_KEY,
                'serial_no': cls.SANDBOX_SERIAL_NO,
                'private_key_path': cls.SANDBOX_PRIVATE_KEY_PATH,
                'platform_cert_path': cls.PLATFORM_CERT_PATH,
                'notify_url': cls.NOTIFY_URL,
                'sandbox': True,
                'is_test_mode': False
            }
        else:
            return {
                'appid': cls.APPID,
                'mchid': cls.MCHID,
                'api_v3_key': cls.API_V3_KEY,
                'serial_no': cls.SERIAL_NO,
                'private_key_path': cls.PRIVATE_KEY_PATH,
                'platform_cert_path': cls.PLATFORM_CERT_PATH,
                'notify_url': cls.NOTIFY_URL,
                'sandbox': False,
                'is_test_mode': False
            }
    
    @classmethod
    def validate_config(cls):
        """验证配置是否完整"""
        config = cls.get_config()
        if not config:
            raise ValueError("微信支付配置为空")
        
        required_fields = ['appid', 'mchid', 'api_v3_key', 'serial_no', 'private_key_path', 'notify_url']
        
        missing_fields = []
        for field in required_fields:
            if not config.get(field) or config[field].startswith('your_'):
                missing_fields.append(field)
        
        if missing_fields:
            raise ValueError(f"微信支付配置不完整，缺少以下字段: {', '.join(missing_fields)}")
        
        # 检查私钥文件是否存在
        if not os.path.exists(config['private_key_path']):
            raise FileNotFoundError(f"商户私钥文件不存在: {config['private_key_path']}")
        
        return True
    
    @classmethod
    def is_enabled(cls):
        """检查微信支付是否启用"""
        try:
            from EarlyBird.model.wechat_pay_config import WeChatPayConfig as DBConfig
            config = DBConfig.get_active_config()
            return config and config.is_enabled
        except:
            return True  # 默认启用 