<template>
  <div class="my-breadcrumb" v-if="false">
    <div class="title">{{ title }}</div>
    <div class="desc">{{ info }}</div>

    <!-- <el-breadcrumb separator-class="el-icon-arrow-right" class="my-breadcrumb">
      <el-breadcrumb-item :to="{ path: '/' }">
        <i class="el-icon-s-home"></i>
        首页</el-breadcrumb-item>
      <el-breadcrumb-item>活动管理</el-breadcrumb-item>
    </el-breadcrumb> -->
  </div>
</template>
<script>
export default {
  name: "BreadCrumb",
  props: {
    title: String,
    info: String,
  },
};
</script>

<style lang="scss" scoped>
.my-breadcrumb {
  padding: 10px 20px;
  border-bottom: 1px solid #eee;
  display: flex;
  align-items: center;
//   background: #c6e2ff !important;
  background:#1890ff;
}
.title {
  font-size: 20px;
  font-weight: bold;
  color: #fff;
  background: #1890ff;
  border-radius: 10px;
  padding: 13px 20px;
  background: linear-gradient(to right, #96c93d, #00b09b);
}
.desc {
  flex: 1;
  color: #fff;
  font-size: 14px;
  line-height: 20px;
  padding-left: 20px;
}
</style>