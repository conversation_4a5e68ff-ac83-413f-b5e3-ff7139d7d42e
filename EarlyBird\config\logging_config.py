# 作者名：EarlyBird
# 作者邮箱：<EMAIL>
# 官方网址：https://blog.zaoniao.vip

import logging
import os
import sys
from datetime import datetime

def setup_logging():
    """设置日志配置"""
    
    # 创建logs目录
    log_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'logs')
    os.makedirs(log_dir, exist_ok=True)
    
    # 生成日志文件名
    log_filename = os.path.join(log_dir, f'app_{datetime.now().strftime("%Y%m%d")}.log')
    
    # 配置根日志记录器
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s  %(filename)s %(lineno)s: %(levelname)s  %(message)s [%(threadName)s]",
        datefmt="%Y-%m-%d %H:%M:%S",
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler(log_filename, encoding='utf-8')
        ]
    )
    
    # 设置第三方库的日志级别，减少不必要的输出
    logging.getLogger('waitress').setLevel(logging.WARNING)
    logging.getLogger('werkzeug').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)
    logging.getLogger('sqlalchemy').setLevel(logging.WARNING)
    
    # 设置应用日志记录器
    app_logger = logging.getLogger('EarlyBird')
    app_logger.setLevel(logging.INFO)
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_formatter = logging.Formatter(
        "%(asctime)s  %(name)s %(lineno)s: %(levelname)s  %(message)s [%(threadName)s]",
        datefmt="%Y-%m-%d %H:%M:%S"
    )
    console_handler.setFormatter(console_formatter)
    
    # 创建文件处理器
    file_handler = logging.FileHandler(log_filename, encoding='utf-8')
    file_handler.setLevel(logging.DEBUG)
    file_formatter = logging.Formatter(
        "%(asctime)s  %(name)s %(lineno)s: %(levelname)s  %(message)s [%(threadName)s]",
        datefmt="%Y-%m-%d %H:%M:%S"
    )
    file_handler.setFormatter(file_formatter)
    
    # 添加处理器到应用日志记录器
    app_logger.addHandler(console_handler)
    app_logger.addHandler(file_handler)
    
    return app_logger

def get_logger(name):
    """获取指定名称的日志记录器"""
    return logging.getLogger(f'EarlyBird.{name}')

# 创建日志记录器实例
logger = setup_logging() 