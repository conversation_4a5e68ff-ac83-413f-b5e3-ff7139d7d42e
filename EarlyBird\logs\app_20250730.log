2025-07-30 14:32:49  init_flask.py 27: INFO  {'CpuCount': 6, 'Mem': '32 GB'} [MainThread]
2025-07-30 14:32:49  web_server.py 32: INFO  Static directory: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\ui [MainThread]
2025-07-30 14:32:49  web_server.py 33: INFO  Static directory exists: True [MainThread]
2025-07-30 14:32:49  web_server.py 37: INFO  设置工作目录: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird [MainThread]
2025-07-30 14:32:49  EarlyBird.config.config 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-30 14:32:49  config.py 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-30 14:32:49  config.py 94: INFO  Database URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-30 14:32:54  EarlyBird.api.admin.app 39: INFO  ✅ 后台管理系统API已注册 [MainThread]
2025-07-30 14:32:54  app.py 39: INFO  ✅ 后台管理系统API已注册 [MainThread]
2025-07-30 14:32:54  EarlyBird 40: INFO  确保数据目录存在: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\data [MainThread]
2025-07-30 14:32:54  server.py 40: INFO  确保数据目录存在: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\data [MainThread]
2025-07-30 14:32:54  EarlyBird 44: INFO  开始执行数据库迁移... [MainThread]
2025-07-30 14:32:54  server.py 44: INFO  开始执行数据库迁移... [MainThread]
2025-07-30 14:32:54  config.py 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-30 14:32:54  config.py 94: INFO  Database URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-30 14:32:54  EarlyBird 53: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-30 14:32:54  server.py 53: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-30 14:32:54  EarlyBird 97: WARNING  ChatLog表不存在，将在应用启动时自动创建 [MainThread]
2025-07-30 14:32:54  server.py 97: WARNING  ChatLog表不存在，将在应用启动时自动创建 [MainThread]
2025-07-30 14:32:54  add_uid_to_chatlog.py 20: INFO  开始执行ChatLog表迁移... [MainThread]
2025-07-30 14:32:54  add_uid_to_chatlog.py 24: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-30 14:32:54  add_uid_to_chatlog.py 38: WARNING  ChatLog表不存在，可能需要先创建表 [MainThread]
2025-07-30 14:32:54  task_api_server.py 31: INFO  TaskApiServer start [MainThread]
2025-07-30 14:32:54  task_paper_generator.py 38: INFO  论文生成任务处理线程初始化 [MainThread]
2025-07-30 14:32:54  task_paper_generator.py 72: INFO  论文生成任务处理线程启动 [Thread-2]
2025-07-30 14:32:54  task_api_server.py 47: INFO  TaskApiServer listen on 127.0.0.1:3302 [Thread-1]
2025-07-30 14:32:54  task_api_server.py 17: INFO  _ApiServerThread start [Thread-3]
2025-07-30 14:33:04  web_server.py 99: INFO  Serving request for path:  [waitress-0]
2025-07-30 14:33:04  web_server.py 117: INFO  Serving index.html for path:  [waitress-0]
2025-07-30 14:33:04  web_server.py 99: INFO  Serving request for path: js/chunk-vendors.04aff60f.js [waitress-1]
2025-07-30 14:33:04  web_server.py 114: INFO  Serving static file: js/chunk-vendors.04aff60f.js [waitress-1]
2025-07-30 14:33:04  web_server.py 99: INFO  Serving request for path: js/app.053c105a.js [waitress-2]
2025-07-30 14:33:04  web_server.py 114: INFO  Serving static file: js/app.053c105a.js [waitress-2]
2025-07-30 14:33:04  web_server.py 99: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-3]
2025-07-30 14:33:04  web_server.py 99: INFO  Serving request for path: css/app.1e68ddae.css [waitress-0]
2025-07-30 14:33:04  web_server.py 114: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-3]
2025-07-30 14:33:04  web_server.py 114: INFO  Serving static file: css/app.1e68ddae.css [waitress-0]
2025-07-30 14:33:04  web_server.py 99: INFO  Serving request for path: js/22.683bc166.js [waitress-1]
2025-07-30 14:33:04  web_server.py 99: INFO  Serving request for path: js/897.717dcd2a.js [waitress-3]
2025-07-30 14:33:04  web_server.py 99: INFO  Serving request for path: css/875.f629b748.css [waitress-2]
2025-07-30 14:33:04  web_server.py 99: INFO  Serving request for path: js/875.729fa994.js [waitress-0]
2025-07-30 14:33:04  web_server.py 114: INFO  Serving static file: js/22.683bc166.js [waitress-1]
2025-07-30 14:33:04  web_server.py 114: INFO  Serving static file: js/897.717dcd2a.js [waitress-3]
2025-07-30 14:33:04  web_server.py 114: INFO  Serving static file: css/875.f629b748.css [waitress-2]
2025-07-30 14:33:04  web_server.py 114: INFO  Serving static file: js/875.729fa994.js [waitress-0]
2025-07-30 14:33:04  web_server.py 99: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-0]
2025-07-30 14:33:04  web_server.py 114: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-0]
2025-07-30 14:33:04  service.py 71: INFO  获取设置成功，包含 8 个配置项 [waitress-2]
2025-07-30 14:33:04  web_server.py 99: INFO  Serving request for path: fonts/element-icons.ff18efd1.woff [waitress-1]
2025-07-30 14:33:04  web_server.py 114: INFO  Serving static file: fonts/element-icons.ff18efd1.woff [waitress-1]
2025-07-30 14:33:11  EarlyBird.api.user.apis 33: INFO  用户登录请求: username=admin [waitress-3]
2025-07-30 14:33:11  apis.py 33: INFO  用户登录请求: username=admin [waitress-3]
2025-07-30 14:33:11  EarlyBird.api.user.apis 41: INFO  已清除之前的session数据和激活相关字段 [waitress-3]
2025-07-30 14:33:11  apis.py 41: INFO  已清除之前的session数据和激活相关字段 [waitress-3]
2025-07-30 14:33:11  EarlyBird.api.user.service 52: WARNING  登录失败: 用户名 admin 不存在 [waitress-3]
2025-07-30 14:33:11  service.py 52: WARNING  登录失败: 用户名 admin 不存在 [waitress-3]
2025-07-30 14:33:11  EarlyBird.api.user.apis 80: WARNING  用户登录失败: 用户名或密码错误 [waitress-3]
2025-07-30 14:33:11  apis.py 80: WARNING  用户登录失败: 用户名或密码错误 [waitress-3]
2025-07-30 14:33:14  EarlyBird.api.user.apis 33: INFO  用户登录请求: username=test1002 [waitress-0]
2025-07-30 14:33:14  apis.py 33: INFO  用户登录请求: username=test1002 [waitress-0]
2025-07-30 14:33:14  EarlyBird.api.user.apis 41: INFO  已清除之前的session数据和激活相关字段 [waitress-0]
2025-07-30 14:33:14  apis.py 41: INFO  已清除之前的session数据和激活相关字段 [waitress-0]
2025-07-30 14:33:15  EarlyBird.api.user.service 81: INFO  用户 test1002(ID=12) 登录成功 [waitress-0]
2025-07-30 14:33:15  service.py 81: INFO  用户 test1002(ID=12) 登录成功 [waitress-0]
2025-07-30 14:33:15  EarlyBird.api.user.apis 57: INFO  用户 12 登录成功，session已设置 [waitress-0]
2025-07-30 14:33:15  apis.py 57: INFO  用户 12 登录成功，session已设置 [waitress-0]
2025-07-30 14:33:15  EarlyBird.api.user.apis 58: INFO  Session内容: {'user_id': 12, 'username': 'test1002', 'login_time': '2025-07-30T14:33:15.118687', '_permanent': True} [waitress-0]
2025-07-30 14:33:15  apis.py 58: INFO  Session内容: {'user_id': 12, 'username': 'test1002', 'login_time': '2025-07-30T14:33:15.118687', '_permanent': True} [waitress-0]
2025-07-30 14:33:15  service.py 71: INFO  获取设置成功，包含 8 个配置项 [waitress-2]
2025-07-30 14:33:18  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getList [waitress-1]
2025-07-30 14:33:18  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getList [waitress-1]
2025-07-30 14:33:18  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-30T14:33:15.118687', 'user_id': 12, 'username': 'test1002'} [waitress-1]
2025-07-30 14:33:18  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-30T14:33:15.118687', 'user_id': 12, 'username': 'test1002'} [waitress-1]
2025-07-30 14:33:18  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 12 [waitress-1]
2025-07-30 14:33:18  hook_register.py 131: INFO  🔍 提取的user_id: 12 [waitress-1]
2025-07-30 14:33:18  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-30 14:33:18  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-30 14:33:18  EarlyBird.api.thesis.apis 92: INFO  用户 12 请求获取论文列表 [waitress-1]
2025-07-30 14:33:18  apis.py 92: INFO  用户 12 请求获取论文列表 [waitress-1]
2025-07-30 14:33:18  EarlyBird.api.thesis.service 867: INFO  查询论文列表，用户ID: 12 [waitress-1]
2025-07-30 14:33:18  service.py 867: INFO  查询论文列表，用户ID: 12 [waitress-1]
2025-07-30 14:33:18  EarlyBird.api.thesis.service 873: INFO  查询到 1 篇论文 [waitress-1]
2025-07-30 14:33:18  service.py 873: INFO  查询到 1 篇论文 [waitress-1]
2025-07-30 14:33:18  EarlyBird.api.thesis.service 883: INFO  论文ID: 28, 标题: 无损检测技术在地质工程中的应用与优化, 用户ID: 12 [waitress-1]
2025-07-30 14:33:18  service.py 883: INFO  论文ID: 28, 标题: 无损检测技术在地质工程中的应用与优化, 用户ID: 12 [waitress-1]
2025-07-30 14:33:18  EarlyBird.api.thesis.service 885: INFO  返回论文列表，共 1 篇 [waitress-1]
2025-07-30 14:33:18  service.py 885: INFO  返回论文列表，共 1 篇 [waitress-1]
2025-07-30 14:33:18  EarlyBird.api.thesis.apis 95: INFO  用户 12 获取论文列表成功，共 1 篇 [waitress-1]
2025-07-30 14:33:18  apis.py 95: INFO  用户 12 获取论文列表成功，共 1 篇 [waitress-1]
2025-07-30 14:33:18  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getDetail [waitress-3]
2025-07-30 14:33:18  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getDetail [waitress-3]
2025-07-30 14:33:18  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-30T14:33:15.118687', 'user_id': 12, 'username': 'test1002'} [waitress-3]
2025-07-30 14:33:18  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-30T14:33:15.118687', 'user_id': 12, 'username': 'test1002'} [waitress-3]
2025-07-30 14:33:18  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 12 [waitress-3]
2025-07-30 14:33:18  hook_register.py 131: INFO  🔍 提取的user_id: 12 [waitress-3]
2025-07-30 14:33:18  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-30 14:33:18  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-30 14:33:18  EarlyBird.api.talk2ai.utils 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-3]
2025-07-30 14:33:18  utils.py 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-3]
2025-07-30 14:33:18  EarlyBird.api.talk2ai.utils 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-3]
2025-07-30 14:33:18  utils.py 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-3]
2025-07-30 14:33:18  EarlyBird.api.talk2ai.utils 161: INFO  提纲处理完成，根节点ID: 8PbNQczE9nc3q3SurUQ9iN [waitress-3]
2025-07-30 14:33:18  utils.py 161: INFO  提纲处理完成，根节点ID: 8PbNQczE9nc3q3SurUQ9iN [waitress-3]
2025-07-30 14:33:18  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getUserDownloadRights [waitress-0]
2025-07-30 14:33:18  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getUserDownloadRights [waitress-0]
2025-07-30 14:33:18  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getOutline [waitress-2]
2025-07-30 14:33:18  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-30T14:33:15.118687', 'user_id': 12, 'username': 'test1002'} [waitress-0]
2025-07-30 14:33:18  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getOutline [waitress-2]
2025-07-30 14:33:18  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-30T14:33:15.118687', 'user_id': 12, 'username': 'test1002'} [waitress-0]
2025-07-30 14:33:18  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-30T14:33:15.118687', 'user_id': 12, 'username': 'test1002'} [waitress-2]
2025-07-30 14:33:18  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 12 [waitress-0]
2025-07-30 14:33:18  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-30T14:33:15.118687', 'user_id': 12, 'username': 'test1002'} [waitress-2]
2025-07-30 14:33:18  hook_register.py 131: INFO  🔍 提取的user_id: 12 [waitress-0]
2025-07-30 14:33:18  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 12 [waitress-2]
2025-07-30 14:33:18  hook_register.py 131: INFO  🔍 提取的user_id: 12 [waitress-2]
2025-07-30 14:33:18  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-30 14:33:18  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-30 14:33:18  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-30 14:33:18  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-30 14:33:18  EarlyBird.api.talk2ai.utils 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-2]
2025-07-30 14:33:18  utils.py 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-2]
2025-07-30 14:33:18  EarlyBird.api.talk2ai.utils 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-2]
2025-07-30 14:33:18  utils.py 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-2]
2025-07-30 14:33:18  EarlyBird.api.talk2ai.utils 161: INFO  提纲处理完成，根节点ID: 8PbNQczE9nc3q3SurUQ9iN [waitress-2]
2025-07-30 14:33:18  utils.py 161: INFO  提纲处理完成，根节点ID: 8PbNQczE9nc3q3SurUQ9iN [waitress-2]
2025-07-30 14:33:18  EarlyBird.api.thesis.service 1408: INFO  用户 12 VIP状态详细检查: [waitress-0]
2025-07-30 14:33:18  service.py 1408: INFO  用户 12 VIP状态详细检查: [waitress-0]
2025-07-30 14:33:18  EarlyBird.api.thesis.service 1409: INFO    - 用户对象存在: True [waitress-0]
2025-07-30 14:33:18  service.py 1409: INFO    - 用户对象存在: True [waitress-0]
2025-07-30 14:33:18  EarlyBird.api.thesis.service 1410: INFO    - 有isVip方法: True [waitress-0]
2025-07-30 14:33:18  service.py 1410: INFO    - 有isVip方法: True [waitress-0]
2025-07-30 14:33:18  EarlyBird.api.thesis.service 1411: INFO    - VIP开始时间: None [waitress-0]
2025-07-30 14:33:18  service.py 1411: INFO    - VIP开始时间: None [waitress-0]
2025-07-30 14:33:18  EarlyBird.api.thesis.service 1412: INFO    - VIP结束时间: None [waitress-0]
2025-07-30 14:33:18  service.py 1412: INFO    - VIP结束时间: None [waitress-0]
2025-07-30 14:33:18  EarlyBird.api.thesis.service 1413: INFO    - VIP等级: 1 [waitress-0]
2025-07-30 14:33:18  service.py 1413: INFO    - VIP等级: 1 [waitress-0]
2025-07-30 14:33:18  EarlyBird.api.thesis.service 1414: INFO    - 最终VIP状态: False [waitress-0]
2025-07-30 14:33:18  service.py 1414: INFO    - 最终VIP状态: False [waitress-0]
2025-07-30 14:33:18  EarlyBird.api.thesis.service 1452: INFO  🔍 用户 12 下载计数（统一逻辑）: [waitress-0]
2025-07-30 14:33:18  service.py 1452: INFO  🔍 用户 12 下载计数（统一逻辑）: [waitress-0]
2025-07-30 14:33:18  EarlyBird.api.thesis.service 1453: INFO    所有记录数: 9 [waitress-0]
2025-07-30 14:33:18  service.py 1453: INFO    所有记录数: 9 [waitress-0]
2025-07-30 14:33:18  EarlyBird.api.thesis.service 1454: INFO    今天的记录数: 0 [waitress-0]
2025-07-30 14:33:18  service.py 1454: INFO    今天的记录数: 0 [waitress-0]
2025-07-30 14:33:18  EarlyBird.api.thesis.service 1455: INFO    有效记录数（排除今天测试）: 9 [waitress-0]
2025-07-30 14:33:18  service.py 1455: INFO    有效记录数（排除今天测试）: 9 [waitress-0]
2025-07-30 14:33:18  EarlyBird.api.thesis.service 1462: INFO      记录1: 论文19, 今天=False, 测试记录=False, 有效=True [waitress-0]
2025-07-30 14:33:18  service.py 1462: INFO      记录1: 论文19, 今天=False, 测试记录=False, 有效=True [waitress-0]
2025-07-30 14:33:18  EarlyBird.api.thesis.service 1462: INFO      记录2: 论文21, 今天=False, 测试记录=True, 有效=True [waitress-0]
2025-07-30 14:33:18  service.py 1462: INFO      记录2: 论文21, 今天=False, 测试记录=True, 有效=True [waitress-0]
2025-07-30 14:33:18  EarlyBird.api.thesis.service 1462: INFO      记录3: 论文22, 今天=False, 测试记录=True, 有效=True [waitress-0]
2025-07-30 14:33:18  service.py 1462: INFO      记录3: 论文22, 今天=False, 测试记录=True, 有效=True [waitress-0]
2025-07-30 14:33:18  EarlyBird.api.thesis.service 1462: INFO      记录4: 论文23, 今天=False, 测试记录=True, 有效=True [waitress-0]
2025-07-30 14:33:18  service.py 1462: INFO      记录4: 论文23, 今天=False, 测试记录=True, 有效=True [waitress-0]
2025-07-30 14:33:18  EarlyBird.api.thesis.service 1462: INFO      记录5: 论文24, 今天=False, 测试记录=True, 有效=True [waitress-0]
2025-07-30 14:33:18  service.py 1462: INFO      记录5: 论文24, 今天=False, 测试记录=True, 有效=True [waitress-0]
2025-07-30 14:33:18  EarlyBird.api.thesis.service 1462: INFO      记录6: 论文25, 今天=False, 测试记录=False, 有效=True [waitress-0]
2025-07-30 14:33:18  service.py 1462: INFO      记录6: 论文25, 今天=False, 测试记录=False, 有效=True [waitress-0]
2025-07-30 14:33:18  EarlyBird.api.thesis.service 1462: INFO      记录7: 论文26, 今天=False, 测试记录=False, 有效=True [waitress-0]
2025-07-30 14:33:18  service.py 1462: INFO      记录7: 论文26, 今天=False, 测试记录=False, 有效=True [waitress-0]
2025-07-30 14:33:18  EarlyBird.api.thesis.service 1462: INFO      记录8: 论文27, 今天=False, 测试记录=False, 有效=True [waitress-0]
2025-07-30 14:33:18  service.py 1462: INFO      记录8: 论文27, 今天=False, 测试记录=False, 有效=True [waitress-0]
2025-07-30 14:33:18  EarlyBird.api.thesis.service 1462: INFO      记录9: 论文28, 今天=False, 测试记录=True, 有效=True [waitress-0]
2025-07-30 14:33:18  service.py 1462: INFO      记录9: 论文28, 今天=False, 测试记录=True, 有效=True [waitress-0]
2025-07-30 14:33:27  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/talk/getOutlineHistory [waitress-1]
2025-07-30 14:33:27  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/talk/getOutlineHistory [waitress-1]
2025-07-30 14:33:27  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-30T14:33:15.118687', 'user_id': 12, 'username': 'test1002'} [waitress-1]
2025-07-30 14:33:27  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-30T14:33:15.118687', 'user_id': 12, 'username': 'test1002'} [waitress-1]
2025-07-30 14:33:27  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 12 [waitress-1]
2025-07-30 14:33:27  hook_register.py 131: INFO  🔍 提取的user_id: 12 [waitress-1]
2025-07-30 14:33:27  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-30 14:33:27  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-30 14:33:27  EarlyBird.api.talk2ai.service 953: INFO  🔍 查询用户 12 的提纲历史记录 [waitress-1]
2025-07-30 14:33:27  service.py 953: INFO  🔍 查询用户 12 的提纲历史记录 [waitress-1]
2025-07-30 14:33:27  EarlyBird.api.talk2ai.service 975: INFO  📝 用户 12 的提纲历史记录：共 5 条，当前页 5 条 [waitress-1]
2025-07-30 14:33:27  service.py 975: INFO  📝 用户 12 的提纲历史记录：共 5 条，当前页 5 条 [waitress-1]
2025-07-30 14:33:28  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/talk/getTitleHistory [waitress-3]
2025-07-30 14:33:28  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/talk/getTitleHistory [waitress-3]
2025-07-30 14:33:28  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-30T14:33:15.118687', 'user_id': 12, 'username': 'test1002'} [waitress-3]
2025-07-30 14:33:28  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-30T14:33:15.118687', 'user_id': 12, 'username': 'test1002'} [waitress-3]
2025-07-30 14:33:28  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 12 [waitress-3]
2025-07-30 14:33:28  hook_register.py 131: INFO  🔍 提取的user_id: 12 [waitress-3]
2025-07-30 14:33:28  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-30 14:33:28  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-30 14:33:28  EarlyBird.api.talk2ai.service 899: INFO  🔍 查询用户 12 的选题历史记录 [waitress-3]
2025-07-30 14:33:28  service.py 899: INFO  🔍 查询用户 12 的选题历史记录 [waitress-3]
2025-07-30 14:33:28  EarlyBird.api.talk2ai.service 921: INFO  📋 用户 12 的选题历史记录：共 5 条，当前页 5 条 [waitress-3]
2025-07-30 14:33:28  service.py 921: INFO  📋 用户 12 的选题历史记录：共 5 条，当前页 5 条 [waitress-3]
2025-07-30 15:57:40  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getList [waitress-2]
2025-07-30 15:57:40  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getList [waitress-2]
2025-07-30 15:57:40  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-30T14:33:15.118687', 'user_id': 12, 'username': 'test1002'} [waitress-2]
2025-07-30 15:57:40  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-30T14:33:15.118687', 'user_id': 12, 'username': 'test1002'} [waitress-2]
2025-07-30 15:57:40  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 12 [waitress-2]
2025-07-30 15:57:40  hook_register.py 131: INFO  🔍 提取的user_id: 12 [waitress-2]
2025-07-30 15:57:40  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-30 15:57:40  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-30 15:57:40  EarlyBird.api.thesis.apis 92: INFO  用户 12 请求获取论文列表 [waitress-2]
2025-07-30 15:57:40  apis.py 92: INFO  用户 12 请求获取论文列表 [waitress-2]
2025-07-30 15:57:40  EarlyBird.api.thesis.service 867: INFO  查询论文列表，用户ID: 12 [waitress-2]
2025-07-30 15:57:40  service.py 867: INFO  查询论文列表，用户ID: 12 [waitress-2]
2025-07-30 15:57:40  EarlyBird.api.thesis.service 873: INFO  查询到 1 篇论文 [waitress-2]
2025-07-30 15:57:40  service.py 873: INFO  查询到 1 篇论文 [waitress-2]
2025-07-30 15:57:40  EarlyBird.api.thesis.service 883: INFO  论文ID: 28, 标题: 无损检测技术在地质工程中的应用与优化, 用户ID: 12 [waitress-2]
2025-07-30 15:57:40  service.py 883: INFO  论文ID: 28, 标题: 无损检测技术在地质工程中的应用与优化, 用户ID: 12 [waitress-2]
2025-07-30 15:57:40  EarlyBird.api.thesis.service 885: INFO  返回论文列表，共 1 篇 [waitress-2]
2025-07-30 15:57:40  service.py 885: INFO  返回论文列表，共 1 篇 [waitress-2]
2025-07-30 15:57:40  EarlyBird.api.thesis.apis 95: INFO  用户 12 获取论文列表成功，共 1 篇 [waitress-2]
2025-07-30 15:57:40  apis.py 95: INFO  用户 12 获取论文列表成功，共 1 篇 [waitress-2]
2025-07-30 15:57:40  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getDetail [waitress-0]
2025-07-30 15:57:40  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getDetail [waitress-0]
2025-07-30 15:57:40  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-30T14:33:15.118687', 'user_id': 12, 'username': 'test1002'} [waitress-0]
2025-07-30 15:57:40  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-30T14:33:15.118687', 'user_id': 12, 'username': 'test1002'} [waitress-0]
2025-07-30 15:57:40  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 12 [waitress-0]
2025-07-30 15:57:40  hook_register.py 131: INFO  🔍 提取的user_id: 12 [waitress-0]
2025-07-30 15:57:40  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-30 15:57:40  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-30 15:57:40  EarlyBird.api.talk2ai.utils 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-0]
2025-07-30 15:57:40  utils.py 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-0]
2025-07-30 15:57:40  EarlyBird.api.talk2ai.utils 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-0]
2025-07-30 15:57:40  utils.py 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-0]
2025-07-30 15:57:40  EarlyBird.api.talk2ai.utils 161: INFO  提纲处理完成，根节点ID: 8PbNQczE9nc3q3SurUQ9iN [waitress-0]
2025-07-30 15:57:40  utils.py 161: INFO  提纲处理完成，根节点ID: 8PbNQczE9nc3q3SurUQ9iN [waitress-0]
2025-07-30 15:57:40  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getUserDownloadRights [waitress-1]
2025-07-30 15:57:40  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getUserDownloadRights [waitress-1]
2025-07-30 15:57:40  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getOutline [waitress-3]
2025-07-30 15:57:40  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-30T14:33:15.118687', 'user_id': 12, 'username': 'test1002'} [waitress-1]
2025-07-30 15:57:40  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getOutline [waitress-3]
2025-07-30 15:57:40  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-30T14:33:15.118687', 'user_id': 12, 'username': 'test1002'} [waitress-3]
2025-07-30 15:57:40  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-30T14:33:15.118687', 'user_id': 12, 'username': 'test1002'} [waitress-1]
2025-07-30 15:57:40  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-30T14:33:15.118687', 'user_id': 12, 'username': 'test1002'} [waitress-3]
2025-07-30 15:57:40  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 12 [waitress-1]
2025-07-30 15:57:40  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 12 [waitress-3]
2025-07-30 15:57:40  hook_register.py 131: INFO  🔍 提取的user_id: 12 [waitress-1]
2025-07-30 15:57:40  hook_register.py 131: INFO  🔍 提取的user_id: 12 [waitress-3]
2025-07-30 15:57:40  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-30 15:57:40  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
ID: N/A [waitress-3]
2025-07-30 15:57:40  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-30 15:57:40  EarlyBird.api.talk2ai.utils 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-3]
2025-07-30 15:57:40  utils.py 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-3]
2025-07-30 15:57:40  EarlyBird.api.thesis.service 1408: INFO  用户 12 VIP状态详细检查: [waitress-1]
2025-07-30 15:57:40  EarlyBird.api.talk2ai.utils 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-3]
2025-07-30 15:57:40  service.py 1408: INFO  用户 12 VIP状态详细检查: [waitress-1]
2025-07-30 15:57:40  EarlyBird.api.thesis.service 1409: INFO    - 用户对象存在: True [waitress-1]
2025-07-30 15:57:40  utils.py 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-3]
2025-07-30 15:57:40  service.py 1409: INFO    - 用户对象存在: True [waitress-1]
2025-07-30 15:57:40  EarlyBird.api.talk2ai.utils 161: INFO  提纲处理完成，根节点ID: 8PbNQczE9nc3q3SurUQ9iN [waitress-3]
2025-07-30 15:57:40  EarlyBird.api.thesis.service 1410: INFO    - 有isVip方法: True [waitress-1]
2025-07-30 15:57:40  utils.py 161: INFO  提纲处理完成，根节点ID: 8PbNQczE9nc3q3SurUQ9iN [waitress-3]
2025-07-30 15:57:40  service.py 1410: INFO    - 有isVip方法: True [waitress-1]
2025-07-30 15:57:40  EarlyBird.api.thesis.service 1411: INFO    - VIP开始时间: None [waitress-1]
2025-07-30 15:57:40  service.py 1411: INFO    - VIP开始时间: None [waitress-1]
2025-07-30 15:57:40  EarlyBird.api.thesis.service 1412: INFO    - VIP结束时间: None [waitress-1]
2025-07-30 15:57:40  service.py 1412: INFO    - VIP结束时间: None [waitress-1]
2025-07-30 15:57:40  EarlyBird.api.thesis.service 1413: INFO    - VIP等级: 1 [waitress-1]
2025-07-30 15:57:40  service.py 1413: INFO    - VIP等级: 1 [waitress-1]
2025-07-30 15:57:40  EarlyBird.api.thesis.service 1414: INFO    - 最终VIP状态: False [waitress-1]
2025-07-30 15:57:40  service.py 1414: INFO    - 最终VIP状态: False [waitress-1]
2025-07-30 15:57:40  EarlyBird.api.thesis.service 1452: INFO  🔍 用户 12 下载计数（统一逻辑）: [waitress-1]
2025-07-30 15:57:40  service.py 1452: INFO  🔍 用户 12 下载计数（统一逻辑）: [waitress-1]
2025-07-30 15:57:40  EarlyBird.api.thesis.service 1453: INFO    所有记录数: 9 [waitress-1]
2025-07-30 15:57:40  service.py 1453: INFO    所有记录数: 9 [waitress-1]
2025-07-30 15:57:40  EarlyBird.api.thesis.service 1454: INFO    今天的记录数: 0 [waitress-1]
2025-07-30 15:57:40  service.py 1454: INFO    今天的记录数: 0 [waitress-1]
2025-07-30 15:57:40  EarlyBird.api.thesis.service 1455: INFO    有效记录数（排除今天测试）: 9 [waitress-1]
2025-07-30 15:57:40  service.py 1455: INFO    有效记录数（排除今天测试）: 9 [waitress-1]
2025-07-30 15:57:40  EarlyBird.api.thesis.service 1462: INFO      记录1: 论文19, 今天=False, 测试记录=False, 有效=True [waitress-1]
2025-07-30 15:57:40  service.py 1462: INFO      记录1: 论文19, 今天=False, 测试记录=False, 有效=True [waitress-1]
2025-07-30 15:57:40  EarlyBird.api.thesis.service 1462: INFO      记录2: 论文21, 今天=False, 测试记录=True, 有效=True [waitress-1]
2025-07-30 15:57:40  service.py 1462: INFO      记录2: 论文21, 今天=False, 测试记录=True, 有效=True [waitress-1]
2025-07-30 15:57:40  EarlyBird.api.thesis.service 1462: INFO      记录3: 论文22, 今天=False, 测试记录=True, 有效=True [waitress-1]
2025-07-30 15:57:40  service.py 1462: INFO      记录3: 论文22, 今天=False, 测试记录=True, 有效=True [waitress-1]
2025-07-30 15:57:40  EarlyBird.api.thesis.service 1462: INFO      记录4: 论文23, 今天=False, 测试记录=True, 有效=True [waitress-1]
2025-07-30 15:57:40  service.py 1462: INFO      记录4: 论文23, 今天=False, 测试记录=True, 有效=True [waitress-1]
2025-07-30 15:57:40  EarlyBird.api.thesis.service 1462: INFO      记录5: 论文24, 今天=False, 测试记录=True, 有效=True [waitress-1]
2025-07-30 15:57:40  service.py 1462: INFO      记录5: 论文24, 今天=False, 测试记录=True, 有效=True [waitress-1]
2025-07-30 15:57:40  EarlyBird.api.thesis.service 1462: INFO      记录6: 论文25, 今天=False, 测试记录=False, 有效=True [waitress-1]
2025-07-30 15:57:40  service.py 1462: INFO      记录6: 论文25, 今天=False, 测试记录=False, 有效=True [waitress-1]
2025-07-30 15:57:40  EarlyBird.api.thesis.service 1462: INFO      记录7: 论文26, 今天=False, 测试记录=False, 有效=True [waitress-1]
2025-07-30 15:57:40  service.py 1462: INFO      记录7: 论文26, 今天=False, 测试记录=False, 有效=True [waitress-1]
2025-07-30 15:57:40  EarlyBird.api.thesis.service 1462: INFO      记录8: 论文27, 今天=False, 测试记录=False, 有效=True [waitress-1]
2025-07-30 15:57:40  service.py 1462: INFO      记录8: 论文27, 今天=False, 测试记录=False, 有效=True [waitress-1]
2025-07-30 15:57:40  EarlyBird.api.thesis.service 1462: INFO      记录9: 论文28, 今天=False, 测试记录=True, 有效=True [waitress-1]
2025-07-30 15:57:40  service.py 1462: INFO      记录9: 论文28, 今天=False, 测试记录=True, 有效=True [waitress-1]
2025-07-30 15:57:42  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-2]
2025-07-30 15:57:42  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-2]
2025-07-30 15:57:42  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-30T14:33:15.118687', 'user_id': 12, 'username': 'test1002'} [waitress-2]
2025-07-30 15:57:42  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-30T14:33:15.118687', 'user_id': 12, 'username': 'test1002'} [waitress-2]
2025-07-30 15:57:42  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 12 [waitress-2]
2025-07-30 15:57:42  hook_register.py 131: INFO  🔍 提取的user_id: 12 [waitress-2]
2025-07-30 15:57:42  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-30 15:57:42  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.apis 145: INFO  用户 12 请求下载论文 28 [waitress-2]
2025-07-30 15:57:42  apis.py 145: INFO  用户 12 请求下载论文 28 [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 381: INFO  用户 12 请求导出论文 28 [waitress-2]
2025-07-30 15:57:42  service.py 381: INFO  用户 12 请求导出论文 28 [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 391: INFO  用户 12 VIP状态: False [waitress-2]
2025-07-30 15:57:42  service.py 391: INFO  用户 12 VIP状态: False [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 402: INFO  论文下载收费功能是否启用: True [waitress-2]
2025-07-30 15:57:42  service.py 402: INFO  论文下载收费功能是否启用: True [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 424: INFO  VIP用户是否免费下载: True [waitress-2]
2025-07-30 15:57:42  service.py 424: INFO  VIP用户是否免费下载: True [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 442: INFO  🔍 首次下载免费配置查询结果: {"id": 9, "name": "\u9996\u6b21\u4e0b\u8f7d\u662f\u5426\u514d\u8d39", "config_key": "thesis.download.first_free", "config_value": "true", "description": "\u7528\u6237\u9996\u6b21\u4e0b\u8f7d\u8bba\u6587\u662f\u5426\u514d\u8d39", "create_time": "2025-06-28 15:32:25"} [waitress-2]
2025-07-30 15:57:42  service.py 442: INFO  🔍 首次下载免费配置查询结果: {"id": 9, "name": "\u9996\u6b21\u4e0b\u8f7d\u662f\u5426\u514d\u8d39", "config_key": "thesis.download.first_free", "config_value": "true", "description": "\u7528\u6237\u9996\u6b21\u4e0b\u8f7d\u8bba\u6587\u662f\u5426\u514d\u8d39", "create_time": "2025-06-28 15:32:25"} [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 444: INFO    配置值: 'true' (类型: <class 'str'>) [waitress-2]
2025-07-30 15:57:42  service.py 444: INFO    配置值: 'true' (类型: <class 'str'>) [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 445: INFO    is_deleted: 0 [waitress-2]
2025-07-30 15:57:42  service.py 445: INFO    is_deleted: 0 [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 446: INFO    config_key: thesis.download.first_free [waitress-2]
2025-07-30 15:57:42  service.py 446: INFO    config_key: thesis.download.first_free [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 455: INFO    配置值处理: 'true' -> 'true' -> True [waitress-2]
2025-07-30 15:57:42  service.py 455: INFO    配置值处理: 'true' -> 'true' -> True [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 459: INFO  🎯 首次下载是否免费: True [waitress-2]
2025-07-30 15:57:42  service.py 459: INFO  🎯 首次下载是否免费: True [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 464: INFO  🔍 用户 12 下载记录检查（统一逻辑）: [waitress-2]
2025-07-30 15:57:42  service.py 464: INFO  🔍 用户 12 下载记录检查（统一逻辑）: [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 465: INFO    总下载记录数: 9 [waitress-2]
2025-07-30 15:57:42  service.py 465: INFO    总下载记录数: 9 [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 469: INFO    记录 1: 论文ID=19, 已支付=True, 支付方式=direct_download, 价格=0.0, 创建时间=2025-06-28 15:22:44 [waitress-2]
2025-07-30 15:57:42  service.py 469: INFO    记录 1: 论文ID=19, 已支付=True, 支付方式=direct_download, 价格=0.0, 创建时间=2025-06-28 15:22:44 [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 469: INFO    记录 2: 论文ID=21, 已支付=True, 支付方式=free_first_time, 价格=0.0, 创建时间=2025-07-03 15:11:42 [waitress-2]
2025-07-30 15:57:42  service.py 469: INFO    记录 2: 论文ID=21, 已支付=True, 支付方式=free_first_time, 价格=0.0, 创建时间=2025-07-03 15:11:42 [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 469: INFO    记录 3: 论文ID=22, 已支付=True, 支付方式=free_first_time, 价格=0.0, 创建时间=2025-07-03 15:12:41 [waitress-2]
2025-07-30 15:57:42  service.py 469: INFO    记录 3: 论文ID=22, 已支付=True, 支付方式=free_first_time, 价格=0.0, 创建时间=2025-07-03 15:12:41 [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 469: INFO    记录 4: 论文ID=23, 已支付=True, 支付方式=free_first_time, 价格=0.0, 创建时间=2025-07-03 15:12:54 [waitress-2]
2025-07-30 15:57:42  service.py 469: INFO    记录 4: 论文ID=23, 已支付=True, 支付方式=free_first_time, 价格=0.0, 创建时间=2025-07-03 15:12:54 [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 469: INFO    记录 5: 论文ID=24, 已支付=True, 支付方式=free_first_time, 价格=0.0, 创建时间=2025-07-03 15:16:47 [waitress-2]
2025-07-30 15:57:42  service.py 469: INFO    记录 5: 论文ID=24, 已支付=True, 支付方式=free_first_time, 价格=0.0, 创建时间=2025-07-03 15:16:47 [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 469: INFO    记录 6: 论文ID=25, 已支付=True, 支付方式=wechat, 价格=10.0, 创建时间=2025-07-14 18:37:40 [waitress-2]
2025-07-30 15:57:42  service.py 469: INFO    记录 6: 论文ID=25, 已支付=True, 支付方式=wechat, 价格=10.0, 创建时间=2025-07-14 18:37:40 [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 469: INFO    记录 7: 论文ID=26, 已支付=True, 支付方式=wechat, 价格=10.0, 创建时间=2025-07-14 21:09:17 [waitress-2]
2025-07-30 15:57:42  service.py 469: INFO    记录 7: 论文ID=26, 已支付=True, 支付方式=wechat, 价格=10.0, 创建时间=2025-07-14 21:09:17 [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 469: INFO    记录 8: 论文ID=27, 已支付=True, 支付方式=wechat, 价格=10.0, 创建时间=2025-07-14 21:58:48 [waitress-2]
2025-07-30 15:57:42  service.py 469: INFO    记录 8: 论文ID=27, 已支付=True, 支付方式=wechat, 价格=10.0, 创建时间=2025-07-14 21:58:48 [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 469: INFO    记录 9: 论文ID=28, 已支付=True, 支付方式=free_disabled, 价格=0.0, 创建时间=2025-07-14 21:59:18 [waitress-2]
2025-07-30 15:57:42  service.py 469: INFO    记录 9: 论文ID=28, 已支付=True, 支付方式=free_disabled, 价格=0.0, 创建时间=2025-07-14 21:59:18 [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 483: INFO    今天的记录数: 0 [waitress-2]
2025-07-30 15:57:42  service.py 483: INFO    今天的记录数: 0 [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 484: INFO    有效记录数（排除今天测试）: 9 [waitress-2]
2025-07-30 15:57:42  service.py 484: INFO    有效记录数（排除今天测试）: 9 [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 491: INFO      记录1: 论文19, 今天=False, 测试记录=False, 有效=True [waitress-2]
2025-07-30 15:57:42  service.py 491: INFO      记录1: 论文19, 今天=False, 测试记录=False, 有效=True [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 491: INFO      记录2: 论文21, 今天=False, 测试记录=True, 有效=True [waitress-2]
2025-07-30 15:57:42  service.py 491: INFO      记录2: 论文21, 今天=False, 测试记录=True, 有效=True [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 491: INFO      记录3: 论文22, 今天=False, 测试记录=True, 有效=True [waitress-2]
2025-07-30 15:57:42  service.py 491: INFO      记录3: 论文22, 今天=False, 测试记录=True, 有效=True [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 491: INFO      记录4: 论文23, 今天=False, 测试记录=True, 有效=True [waitress-2]
2025-07-30 15:57:42  service.py 491: INFO      记录4: 论文23, 今天=False, 测试记录=True, 有效=True [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 491: INFO      记录5: 论文24, 今天=False, 测试记录=True, 有效=True [waitress-2]
2025-07-30 15:57:42  service.py 491: INFO      记录5: 论文24, 今天=False, 测试记录=True, 有效=True [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 491: INFO      记录6: 论文25, 今天=False, 测试记录=False, 有效=True [waitress-2]
2025-07-30 15:57:42  service.py 491: INFO      记录6: 论文25, 今天=False, 测试记录=False, 有效=True [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 491: INFO      记录7: 论文26, 今天=False, 测试记录=False, 有效=True [waitress-2]
2025-07-30 15:57:42  service.py 491: INFO      记录7: 论文26, 今天=False, 测试记录=False, 有效=True [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 491: INFO      记录8: 论文27, 今天=False, 测试记录=False, 有效=True [waitress-2]
2025-07-30 15:57:42  service.py 491: INFO      记录8: 论文27, 今天=False, 测试记录=False, 有效=True [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 491: INFO      记录9: 论文28, 今天=False, 测试记录=True, 有效=True [waitress-2]
2025-07-30 15:57:42  service.py 491: INFO      记录9: 论文28, 今天=False, 测试记录=True, 有效=True [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 497: INFO  🎯 首次免费判断（统一逻辑）: [waitress-2]
2025-07-30 15:57:42  service.py 497: INFO  🎯 首次免费判断（统一逻辑）: [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 498: INFO    first_free配置: True [waitress-2]
2025-07-30 15:57:42  service.py 498: INFO    first_free配置: True [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 499: INFO    has_valid_downloads: True [waitress-2]
2025-07-30 15:57:42  service.py 499: INFO    has_valid_downloads: True [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 500: INFO    判断条件: first_free=True AND not has_valid_downloads=False [waitress-2]
2025-07-30 15:57:42  service.py 500: INFO    判断条件: first_free=True AND not has_valid_downloads=False [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 501: INFO    最终判断: False [waitress-2]
2025-07-30 15:57:42  service.py 501: INFO    最终判断: False [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 516: INFO  ❌ 用户 12 不符合首次免费条件，需要检查支付状态 [waitress-2]
2025-07-30 15:57:42  service.py 516: INFO  ❌ 用户 12 不符合首次免费条件，需要检查支付状态 [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 547: INFO  用户 12 有免费下载记录，可直接下载论文 28 [waitress-2]
2025-07-30 15:57:42  service.py 547: INFO  用户 12 有免费下载记录，可直接下载论文 28 [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 1331: INFO  add Para:无损检测技术在地质工程中的应用与优化 [waitress-2]
2025-07-30 15:57:42  service.py 1331: INFO  add Para:无损检测技术在地质工程中的应用与优化 [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 1331: INFO  add Para:绪论 [waitress-2]
2025-07-30 15:57:42  service.py 1331: INFO  add Para:绪论 [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 1331: INFO  add Para:研究背景与数据现状 [waitress-2]
2025-07-30 15:57:42  service.py 1331: INFO  add Para:研究背景与数据现状 [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 1331: INFO  add Para:无损检测技术在地质工程中的应用现状分析 [waitress-2]
2025-07-30 15:57:42  service.py 1331: INFO  add Para:无损检测技术在地质工程中的应用现状分析 [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 1331: INFO  add Para:本研究的意义及目标设定 [waitress-2]
2025-07-30 15:57:42  service.py 1331: INFO  add Para:本研究的意义及目标设定 [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 1331: INFO  add Para:理论基础与数据模型构建 [waitress-2]
2025-07-30 15:57:42  service.py 1331: INFO  add Para:理论基础与数据模型构建 [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 1331: INFO  add Para:无损检测技术基本原理及其在地质学中的适用性探讨 [waitress-2]
2025-07-30 15:57:42  service.py 1331: INFO  add Para:无损检测技术基本原理及其在地质学中的适用性探讨 [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 1331: INFO  add Para:基于大数据的地质信息处理模型设计 [waitress-2]
2025-07-30 15:57:42  service.py 1331: INFO  add Para:基于大数据的地质信息处理模型设计 [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 1331: INFO  add Para:用于地质结构分析的数据挖掘算法选择与优化 [waitress-2]
2025-07-30 15:57:42  service.py 1331: INFO  add Para:用于地质结构分析的数据挖掘算法选择与优化 [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 1331: INFO  add Para:实证研究：数据收集与统计分析 [waitress-2]
2025-07-30 15:57:42  service.py 1331: INFO  add Para:实证研究：数据收集与统计分析 [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 1331: INFO  add Para:不同地质环境下无损检测数据采集方法比较 [waitress-2]
2025-07-30 15:57:42  service.py 1331: INFO  add Para:不同地质环境下无损检测数据采集方法比较 [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 1331: INFO  add Para:运用统计软件进行数据清洗与预处理 [waitress-2]
2025-07-30 15:57:42  service.py 1331: INFO  add Para:运用统计软件进行数据清洗与预处理 [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 1331: INFO  add Para:关键指标选取与多维度数据分析结果展示 [waitress-2]
2025-07-30 15:57:42  service.py 1331: INFO  add Para:关键指标选取与多维度数据分析结果展示 [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 1331: INFO  add Para:案例分析：具体应用实例与效果评估 [waitress-2]
2025-07-30 15:57:42  service.py 1331: INFO  add Para:案例分析：具体应用实例与效果评估 [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 1331: INFO  add Para:某典型地区无损检测技术应用案例介绍 [waitress-2]
2025-07-30 15:57:42  service.py 1331: INFO  add Para:某典型地区无损检测技术应用案例介绍 [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 1331: INFO  add Para:案例中采用的不同技术路线对比分析 [waitress-2]
2025-07-30 15:57:42  service.py 1331: INFO  add Para:案例中采用的不同技术路线对比分析 [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 1331: INFO  add Para:通过定量与定性相结合的方法评价技术实施效果 [waitress-2]
2025-07-30 15:57:42  service.py 1331: INFO  add Para:通过定量与定性相结合的方法评价技术实施效果 [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 1331: INFO  add Para:创新点与挑战：新技术探索及问题解决策略 [waitress-2]
2025-07-30 15:57:42  service.py 1331: INFO  add Para:创新点与挑战：新技术探索及问题解决策略 [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 1331: INFO  add Para:结合最新科研成果提出的新一代无损检测方案 [waitress-2]
2025-07-30 15:57:42  service.py 1331: INFO  add Para:结合最新科研成果提出的新一代无损检测方案 [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 1331: INFO  add Para:针对现有技术局限性的改进措施建议 [waitress-2]
2025-07-30 15:57:42  service.py 1331: INFO  add Para:针对现有技术局限性的改进措施建议 [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 1331: INFO  add Para:未来发展方向预测与潜在障碍识别 [waitress-2]
2025-07-30 15:57:42  service.py 1331: INFO  add Para:未来发展方向预测与潜在障碍识别 [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 1331: INFO  add Para:结论：研究成果总结与政策建议 [waitress-2]
2025-07-30 15:57:42  service.py 1331: INFO  add Para:结论：研究成果总结与政策建议 [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 1331: INFO  add Para:主要发现概述与数据支持下的结论提炼 [waitress-2]
2025-07-30 15:57:42  service.py 1331: INFO  add Para:主要发现概述与数据支持下的结论提炼 [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 1331: INFO  add Para:对行业标准制定者和政策制定者的建议 [waitress-2]
2025-07-30 15:57:42  service.py 1331: INFO  add Para:对行业标准制定者和政策制定者的建议 [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 1331: INFO  add Para:进一步研究方向展望 [waitress-2]
2025-07-30 15:57:42  service.py 1331: INFO  add Para:进一步研究方向展望 [waitress-2]
2025-07-30 15:57:42  EarlyBird.common.docx 500: INFO  d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\download\[早鸟论文]_无损检测技术在地质工程中的应用与优化_V20250730_155742.docx [waitress-2]
2025-07-30 15:57:42  docx.py 500: INFO  d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\download\[早鸟论文]_无损检测技术在地质工程中的应用与优化_V20250730_155742.docx [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.service 1350: INFO  论文导出成功，下载URL: /resources/download/[早鸟论文]_无损检测技术在地质工程中的应用与优化_V20250730_155742.docx [waitress-2]
2025-07-30 15:57:42  service.py 1350: INFO  论文导出成功，下载URL: /resources/download/[早鸟论文]_无损检测技术在地质工程中的应用与优化_V20250730_155742.docx [waitress-2]
2025-07-30 15:57:42  EarlyBird.api.thesis.apis 160: INFO  用户 12 下载论文 28 成功 [waitress-2]
2025-07-30 15:57:42  apis.py 160: INFO  用户 12 下载论文 28 成功 [waitress-2]
2025-07-30 15:57:42  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/download [waitress-0]
2025-07-30 15:57:42  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/download [waitress-0]
2025-07-30 15:57:42  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-30T14:33:15.118687', 'user_id': 12, 'username': 'test1002'} [waitress-0]
2025-07-30 15:57:42  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-30T14:33:15.118687', 'user_id': 12, 'username': 'test1002'} [waitress-0]
2025-07-30 15:57:42  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 12 [waitress-0]
2025-07-30 15:57:42  hook_register.py 131: INFO  🔍 提取的user_id: 12 [waitress-0]
2025-07-30 15:57:42  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-30 15:57:42  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-30 15:57:42  EarlyBird.api.thesis.apis 250: INFO  下载请求 - 原始fileName: /resources/download/[早鸟论文]_无损检测技术在地质工程中的应用与优化_V20250730_155742.docx [waitress-0]
2025-07-30 15:57:42  apis.py 250: INFO  下载请求 - 原始fileName: /resources/download/[早鸟论文]_无损检测技术在地质工程中的应用与优化_V20250730_155742.docx [waitress-0]
2025-07-30 15:57:42  EarlyBird.api.thesis.apis 260: INFO  下载请求 - 处理后fileName: [早鸟论文]_无损检测技术在地质工程中的应用与优化_V20250730_155742.docx [waitress-0]
2025-07-30 15:57:42  apis.py 260: INFO  下载请求 - 处理后fileName: [早鸟论文]_无损检测技术在地质工程中的应用与优化_V20250730_155742.docx [waitress-0]
2025-07-30 15:57:42  EarlyBird.api.thesis.apis 267: INFO  下载请求 - 完整文件路径: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\download\[早鸟论文]_无损检测技术在地质工程中的应用与优化_V20250730_155742.docx [waitress-0]
2025-07-30 15:57:42  apis.py 267: INFO  下载请求 - 完整文件路径: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\download\[早鸟论文]_无损检测技术在地质工程中的应用与优化_V20250730_155742.docx [waitress-0]
2025-07-30 15:57:42  EarlyBird.api.thesis.apis 268: INFO  下载请求 - 文件是否存在: True [waitress-0]
2025-07-30 15:57:42  apis.py 268: INFO  下载请求 - 文件是否存在: True [waitress-0]
2025-07-30 15:57:42  EarlyBird.api.thesis.apis 274: INFO  下载成功 - 文件: [早鸟论文]_无损检测技术在地质工程中的应用与优化_V20250730_155742.docx [waitress-0]
2025-07-30 15:57:42  apis.py 274: INFO  下载成功 - 文件: [早鸟论文]_无损检测技术在地质工程中的应用与优化_V20250730_155742.docx [waitress-0]
2025-07-30 15:57:51  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/deleteThesis [waitress-3]
2025-07-30 15:57:51  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/deleteThesis [waitress-3]
2025-07-30 15:57:51  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-30T14:33:15.118687', 'user_id': 12, 'username': 'test1002'} [waitress-3]
2025-07-30 15:57:51  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-30T14:33:15.118687', 'user_id': 12, 'username': 'test1002'} [waitress-3]
2025-07-30 15:57:51  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 12 [waitress-3]
2025-07-30 15:57:51  hook_register.py 131: INFO  🔍 提取的user_id: 12 [waitress-3]
2025-07-30 15:57:51  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-30 15:57:51  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-30 15:57:53  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/talk/getOutlineHistory [waitress-1]
2025-07-30 15:57:53  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/talk/getOutlineHistory [waitress-1]
2025-07-30 15:57:53  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-30T14:33:15.118687', 'user_id': 12, 'username': 'test1002'} [waitress-1]
2025-07-30 15:57:53  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-30T14:33:15.118687', 'user_id': 12, 'username': 'test1002'} [waitress-1]
2025-07-30 15:57:53  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 12 [waitress-1]
2025-07-30 15:57:53  hook_register.py 131: INFO  🔍 提取的user_id: 12 [waitress-1]
2025-07-30 15:57:53  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-30 15:57:53  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-30 15:57:53  EarlyBird.api.talk2ai.service 953: INFO  🔍 查询用户 12 的提纲历史记录 [waitress-1]
2025-07-30 15:57:53  service.py 953: INFO  🔍 查询用户 12 的提纲历史记录 [waitress-1]
2025-07-30 15:57:53  EarlyBird.api.talk2ai.service 975: INFO  📝 用户 12 的提纲历史记录：共 5 条，当前页 5 条 [waitress-1]
2025-07-30 15:57:53  service.py 975: INFO  📝 用户 12 的提纲历史记录：共 5 条，当前页 5 条 [waitress-1]
2025-07-30 15:57:59  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/talk/getOutline [waitress-2]
2025-07-30 15:57:59  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/talk/getOutline [waitress-2]
2025-07-30 15:57:59  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-30T14:33:15.118687', 'user_id': 12, 'username': 'test1002'} [waitress-2]
2025-07-30 15:57:59  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-30T14:33:15.118687', 'user_id': 12, 'username': 'test1002'} [waitress-2]
2025-07-30 15:57:59  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 12 [waitress-2]
2025-07-30 15:57:59  hook_register.py 131: INFO  🔍 提取的user_id: 12 [waitress-2]
2025-07-30 15:57:59  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-30 15:57:59  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-30 15:57:59  EarlyBird.api.talk2ai.service 772: INFO  request $Gemini10 param: isVipUser=False userId=12 title='测试' length=4000 level='硕士' lang='中文' paragraphCount=6 secondParagraphCount=3 author='' style='' model='Gemini10' [waitress-2]
2025-07-30 15:57:59  service.py 772: INFO  request $Gemini10 param: isVipUser=False userId=12 title='测试' length=4000 level='硕士' lang='中文' paragraphCount=6 secondParagraphCount=3 author='' style='' model='Gemini10' [waitress-2]
2025-07-30 15:57:59  EarlyBird.api.talk2ai.model.model_proxy 28: INFO  ModelProxy 实例化.模型:Gemini10 [waitress-2]
2025-07-30 15:57:59  model_proxy.py 28: INFO  ModelProxy 实例化.模型:Gemini10 [waitress-2]
2025-07-30 15:57:59  EarlyBird.api.talk2ai.model.model_proxy 34: INFO  Using model: qianwen [waitress-2]
2025-07-30 15:57:59  model_proxy.py 34: INFO  Using model: qianwen [waitress-2]
2025-07-30 15:57:59  EarlyBird.common.ai 60: INFO  使用模型: qianwen [waitress-2]
2025-07-30 15:57:59  __init__.py 60: INFO  使用模型: qianwen [waitress-2]
2025-07-30 15:57:59  EarlyBird.common.ai.adapter.qianwen 33: INFO  使用千问 API Key: sk-81a15... [waitress-2]
2025-07-30 15:57:59  qianwen.py 33: INFO  使用千问 API Key: sk-81a15... [waitress-2]
2025-07-30 15:57:59  EarlyBird.common.ai.adapter.qianwen 35: INFO  角色:你是一位硕士学生.任务:要为一篇题目为'测试'的论文拟定提纲.语言:使用写论文的正式语言,加入更多的对该论题的深度理解和前沿内容.约束:写作语言是中文,论文包含6段落,每段包含3子段落,首先是'绪论',不要'参考文献'和'致谢'.

【数据丰富性要求】
1. 提纲结构要体现数据支撑：
   - 每个段落标题要体现具体的数据分析方向
   - 子段落要包含数据收集、数据分析、数据对比等环节
   - 确保有专门的段落用于数据展示和分析
2. 段落内容要求：
   - 绪论：包含研究背景、数据现状、研究意义
   - 理论分析：包含理论基础、数据模型、分析方法
   - 实证研究：包含数据收集、统计分析、结果展示
   - 案例分析：包含具体案例、数据对比、效果评估
   - 结论：包含数据总结、研究发现、政策建议
3. 子段落要求：
   - 每个子段落都要有明确的数据分析目标
   - 包含数据来源、分析方法、结果展示等要素
   - 确保逻辑清晰，数据支撑充分
格式:返回json格式,json数据中，只有一个'subtitle'字段,这个字段的值是数组。数组中每个元素，代表论文的一个段落。每段是一个json字典,其中包含一个'title'字段,表示段落标题。每段包含1个'subtitle'数组,表示该段的子段落,是一个数组,其中每个元素是一个字典,键是'title',值是标题。不要加入'content'字段,不要省略段落 [waitress-2]
2025-07-30 15:57:59  qianwen.py 35: INFO  角色:你是一位硕士学生.任务:要为一篇题目为'测试'的论文拟定提纲.语言:使用写论文的正式语言,加入更多的对该论题的深度理解和前沿内容.约束:写作语言是中文,论文包含6段落,每段包含3子段落,首先是'绪论',不要'参考文献'和'致谢'.

【数据丰富性要求】
1. 提纲结构要体现数据支撑：
   - 每个段落标题要体现具体的数据分析方向
   - 子段落要包含数据收集、数据分析、数据对比等环节
   - 确保有专门的段落用于数据展示和分析
2. 段落内容要求：
   - 绪论：包含研究背景、数据现状、研究意义
   - 理论分析：包含理论基础、数据模型、分析方法
   - 实证研究：包含数据收集、统计分析、结果展示
   - 案例分析：包含具体案例、数据对比、效果评估
   - 结论：包含数据总结、研究发现、政策建议
3. 子段落要求：
   - 每个子段落都要有明确的数据分析目标
   - 包含数据来源、分析方法、结果展示等要素
   - 确保逻辑清晰，数据支撑充分
格式:返回json格式,json数据中，只有一个'subtitle'字段,这个字段的值是数组。数组中每个元素，代表论文的一个段落。每段是一个json字典,其中包含一个'title'字段,表示段落标题。每段包含1个'subtitle'数组,表示该段的子段落,是一个数组,其中每个元素是一个字典,键是'title',值是标题。不要加入'content'字段,不要省略段落 [waitress-2]
2025-07-30 15:58:30  EarlyBird.common.ai.adapter.qianwen 45: INFO  {"status_code": 200, "request_id": "19b9b2a5-1114-997a-9b35-f0172439c024", "code": "", "message": "", "output": {"text": null, "finish_reason": null, "choices": [{"finish_reason": "stop", "message": {"role": "assistant", "content": "```json\n{\n  \"subtitle\": [\n    {\n      \"title\": \"绪论\",\n      \"subtitle\": [\n        {\n          \"title\": \"研究背景与问题提出\"\n        },\n        {\n          \"title\": \"数据现状及其局限性分析\"\n        },\n        {\n          \"title\": \"本研究的意义及目标设定\"\n        }\n      ]\n    },\n    {\n      \"title\": \"理论分析框架构建\",\n      \"subtitle\": [\n        {\n          \"title\": \"基于现有文献的理论基础梳理\"\n        },\n        {\n          \"title\": \"适用于本研究的数据模型选择\"\n        },\n        {\n          \"title\": \"定量与定性相结合的综合分析方法介绍\"\n        }\n      ]\n    },\n    {\n      \"title\": \"实证研究设计与实施\",\n      \"subtitle\": [\n        {\n          \"title\": \"数据收集渠道与样本描述\"\n        },\n        {\n          \"title\": \"统计分析方法的应用与调整\"\n        },\n        {\n          \"title\": \"初步数据分析结果展示\"\n        }\n      ]\n    },\n    {\n      \"title\": \"案例深度剖析\",\n      \"subtitle\": [\n        {\n          \"title\": \"精选案例的选择依据与背景介绍\"\n        },\n        {\n          \"title\": \"不同案例间的数据对比分析\"\n        },\n        {\n          \"title\": \"案例对研究假设的支持程度评估\"\n        }\n      ]\n    },\n    {\n      \"title\": \"研究发现与讨论\",\n      \"subtitle\": [\n        {\n          \"title\": \"关键数据总结与趋势识别\"\n        },\n        {\n          \"title\": \"主要研究发现及其解释\"\n        },\n        {\n          \"title\": \"对未来研究方向的展望\"\n        }\n      ]\n    },\n    {\n      \"title\": \"政策建议与实践应用\",\n      \"subtitle\": [\n        {\n          \"title\": \"根据研究结果提出的政策建议\"\n        },\n        {\n          \"title\": \"政策执行中可能遇到的问题与解决方案\"\n        },\n        {\n          \"title\": \"研究成果在实际场景中的应用前景\"\n        }\n      ]\n    }\n  ]\n}\n```"}}]}, "usage": {"input_tokens": 404, "output_tokens": 432, "total_tokens": 836, "prompt_tokens_details": {"cached_tokens": 0}}} [waitress-2]
2025-07-30 15:58:30  qianwen.py 45: INFO  {"status_code": 200, "request_id": "19b9b2a5-1114-997a-9b35-f0172439c024", "code": "", "message": "", "output": {"text": null, "finish_reason": null, "choices": [{"finish_reason": "stop", "message": {"role": "assistant", "content": "```json\n{\n  \"subtitle\": [\n    {\n      \"title\": \"绪论\",\n      \"subtitle\": [\n        {\n          \"title\": \"研究背景与问题提出\"\n        },\n        {\n          \"title\": \"数据现状及其局限性分析\"\n        },\n        {\n          \"title\": \"本研究的意义及目标设定\"\n        }\n      ]\n    },\n    {\n      \"title\": \"理论分析框架构建\",\n      \"subtitle\": [\n        {\n          \"title\": \"基于现有文献的理论基础梳理\"\n        },\n        {\n          \"title\": \"适用于本研究的数据模型选择\"\n        },\n        {\n          \"title\": \"定量与定性相结合的综合分析方法介绍\"\n        }\n      ]\n    },\n    {\n      \"title\": \"实证研究设计与实施\",\n      \"subtitle\": [\n        {\n          \"title\": \"数据收集渠道与样本描述\"\n        },\n        {\n          \"title\": \"统计分析方法的应用与调整\"\n        },\n        {\n          \"title\": \"初步数据分析结果展示\"\n        }\n      ]\n    },\n    {\n      \"title\": \"案例深度剖析\",\n      \"subtitle\": [\n        {\n          \"title\": \"精选案例的选择依据与背景介绍\"\n        },\n        {\n          \"title\": \"不同案例间的数据对比分析\"\n        },\n        {\n          \"title\": \"案例对研究假设的支持程度评估\"\n        }\n      ]\n    },\n    {\n      \"title\": \"研究发现与讨论\",\n      \"subtitle\": [\n        {\n          \"title\": \"关键数据总结与趋势识别\"\n        },\n        {\n          \"title\": \"主要研究发现及其解释\"\n        },\n        {\n          \"title\": \"对未来研究方向的展望\"\n        }\n      ]\n    },\n    {\n      \"title\": \"政策建议与实践应用\",\n      \"subtitle\": [\n        {\n          \"title\": \"根据研究结果提出的政策建议\"\n        },\n        {\n          \"title\": \"政策执行中可能遇到的问题与解决方案\"\n        },\n        {\n          \"title\": \"研究成果在实际场景中的应用前景\"\n        }\n      ]\n    }\n  ]\n}\n```"}}]}, "usage": {"input_tokens": 404, "output_tokens": 432, "total_tokens": 836, "prompt_tokens_details": {"cached_tokens": 0}}} [waitress-2]
2025-07-30 15:58:30  EarlyBird.api.talk2ai.model.model_proxy 101: WARNING  提纲缺少title字段，使用论文标题 [waitress-2]
2025-07-30 15:58:30  model_proxy.py 101: WARNING  提纲缺少title字段，使用论文标题 [waitress-2]
2025-07-30 15:58:30  EarlyBird.api.talk2ai.model.model_proxy 188: INFO  成功生成并修复 行业专家 风格的提纲 [waitress-2]
2025-07-30 15:58:30  model_proxy.py 188: INFO  成功生成并修复 行业专家 风格的提纲 [waitress-2]
2025-07-30 15:58:30  EarlyBird.api.talk2ai.model.model_proxy 34: INFO  Using model: qianwen [waitress-2]
2025-07-30 15:58:30  model_proxy.py 34: INFO  Using model: qianwen [waitress-2]
2025-07-30 15:58:30  EarlyBird.common.ai 60: INFO  使用模型: qianwen [waitress-2]
2025-07-30 15:58:30  __init__.py 60: INFO  使用模型: qianwen [waitress-2]
2025-07-30 15:58:30  EarlyBird.common.ai.adapter.qianwen 33: INFO  使用千问 API Key: sk-81a15... [waitress-2]
2025-07-30 15:58:30  qianwen.py 33: INFO  使用千问 API Key: sk-81a15... [waitress-2]
2025-07-30 15:58:30  EarlyBird.common.ai.adapter.qianwen 35: INFO  角色:你是一位硕士学生.任务:要为一篇题目为'测试'的论文拟定提纲.语言:使用写论文的正式语言,从基础理论入手，多分析现有数据，展示自己的学习成果.约束:写作语言是中文,论文包含6段落,每段包含3子段落,首先是'绪论',不要'参考文献'和'致谢'.

【数据丰富性要求】
1. 提纲结构要体现数据支撑：
   - 每个段落标题要体现具体的数据分析方向
   - 子段落要包含数据收集、数据分析、数据对比等环节
   - 确保有专门的段落用于数据展示和分析
2. 段落内容要求：
   - 绪论：包含研究背景、数据现状、研究意义
   - 理论分析：包含理论基础、数据模型、分析方法
   - 实证研究：包含数据收集、统计分析、结果展示
   - 案例分析：包含具体案例、数据对比、效果评估
   - 结论：包含数据总结、研究发现、政策建议
3. 子段落要求：
   - 每个子段落都要有明确的数据分析目标
   - 包含数据来源、分析方法、结果展示等要素
   - 确保逻辑清晰，数据支撑充分
格式:返回json格式,json数据中，只有一个'subtitle'字段,这个字段的值是数组。数组中每个元素，代表论文的一个段落。每段是一个json字典,其中包含一个'title'字段,表示段落标题。每段包含1个'subtitle'数组,表示该段的子段落,是一个数组,其中每个元素是一个字典,键是'title',值是标题。不要加入'content'字段,不要省略段落 [waitress-2]
2025-07-30 15:58:30  qianwen.py 35: INFO  角色:你是一位硕士学生.任务:要为一篇题目为'测试'的论文拟定提纲.语言:使用写论文的正式语言,从基础理论入手，多分析现有数据，展示自己的学习成果.约束:写作语言是中文,论文包含6段落,每段包含3子段落,首先是'绪论',不要'参考文献'和'致谢'.

【数据丰富性要求】
1. 提纲结构要体现数据支撑：
   - 每个段落标题要体现具体的数据分析方向
   - 子段落要包含数据收集、数据分析、数据对比等环节
   - 确保有专门的段落用于数据展示和分析
2. 段落内容要求：
   - 绪论：包含研究背景、数据现状、研究意义
   - 理论分析：包含理论基础、数据模型、分析方法
   - 实证研究：包含数据收集、统计分析、结果展示
   - 案例分析：包含具体案例、数据对比、效果评估
   - 结论：包含数据总结、研究发现、政策建议
3. 子段落要求：
   - 每个子段落都要有明确的数据分析目标
   - 包含数据来源、分析方法、结果展示等要素
   - 确保逻辑清晰，数据支撑充分
格式:返回json格式,json数据中，只有一个'subtitle'字段,这个字段的值是数组。数组中每个元素，代表论文的一个段落。每段是一个json字典,其中包含一个'title'字段,表示段落标题。每段包含1个'subtitle'数组,表示该段的子段落,是一个数组,其中每个元素是一个字典,键是'title',值是标题。不要加入'content'字段,不要省略段落 [waitress-2]
2025-07-30 15:58:53  EarlyBird.common.ai.adapter.qianwen 45: INFO  {"status_code": 200, "request_id": "786d0d6d-debf-9a77-8b33-424034ef08df", "code": "", "message": "", "output": {"text": null, "finish_reason": null, "choices": [{"finish_reason": "stop", "message": {"role": "assistant", "content": "```json\n{\n  \"subtitle\": [\n    {\n      \"title\": \"绪论\",\n      \"subtitle\": [\n        {\"title\": \"研究背景：基于大数据的测试领域现状\"},\n        {\"title\": \"数据现状：当前可获取的数据集及其特征分析\"},\n        {\"title\": \"研究意义：通过数据分析提升测试效率与准确性\"}\n      ]\n    },\n    {\n      \"title\": \"理论分析\",\n      \"subtitle\": [\n        {\"title\": \"理论基础：测试理论框架下的关键概念与原理\"},\n        {\"title\": \"数据模型：适用于测试数据处理的数学模型构建\"},\n        {\"title\": \"分析方法：采用统计学与机器学习技术进行数据分析\"}\n      ]\n    },\n    {\n      \"title\": \"实证研究\",\n      \"subtitle\": [\n        {\"title\": \"数据收集：从公开数据库及实验中获取样本数据\"},\n        {\"title\": \"统计分析：运用描述性统计和推断性统计方法处理数据\"},\n        {\"title\": \"结果展示：通过图表形式直观显示分析成果\"}\n      ]\n    },\n    {\n      \"title\": \"案例分析\",\n      \"subtitle\": [\n        {\"title\": \"具体案例：选取典型应用场景作为研究对象\"},\n        {\"title\": \"数据对比：将不同条件下的测试结果进行比较\"},\n        {\"title\": \"效果评估：评价所提方法在实际应用中的表现\"}\n      ]\n    },\n    {\n      \"title\": \"结论\",\n      \"subtitle\": [\n        {\"title\": \"数据总结：回顾研究过程中使用的主要数据集特点\"},\n        {\"title\": \"研究发现：提炼出本研究对于测试领域的贡献\"},\n        {\"title\": \"政策建议：提出基于研究成果对未来工作的指导性意见\"}\n      ]\n    }\n  ]\n}\n```"}}]}, "usage": {"input_tokens": 408, "output_tokens": 364, "total_tokens": 772, "prompt_tokens_details": {"cached_tokens": 0}}} [waitress-2]
2025-07-30 15:58:53  qianwen.py 45: INFO  {"status_code": 200, "request_id": "786d0d6d-debf-9a77-8b33-424034ef08df", "code": "", "message": "", "output": {"text": null, "finish_reason": null, "choices": [{"finish_reason": "stop", "message": {"role": "assistant", "content": "```json\n{\n  \"subtitle\": [\n    {\n      \"title\": \"绪论\",\n      \"subtitle\": [\n        {\"title\": \"研究背景：基于大数据的测试领域现状\"},\n        {\"title\": \"数据现状：当前可获取的数据集及其特征分析\"},\n        {\"title\": \"研究意义：通过数据分析提升测试效率与准确性\"}\n      ]\n    },\n    {\n      \"title\": \"理论分析\",\n      \"subtitle\": [\n        {\"title\": \"理论基础：测试理论框架下的关键概念与原理\"},\n        {\"title\": \"数据模型：适用于测试数据处理的数学模型构建\"},\n        {\"title\": \"分析方法：采用统计学与机器学习技术进行数据分析\"}\n      ]\n    },\n    {\n      \"title\": \"实证研究\",\n      \"subtitle\": [\n        {\"title\": \"数据收集：从公开数据库及实验中获取样本数据\"},\n        {\"title\": \"统计分析：运用描述性统计和推断性统计方法处理数据\"},\n        {\"title\": \"结果展示：通过图表形式直观显示分析成果\"}\n      ]\n    },\n    {\n      \"title\": \"案例分析\",\n      \"subtitle\": [\n        {\"title\": \"具体案例：选取典型应用场景作为研究对象\"},\n        {\"title\": \"数据对比：将不同条件下的测试结果进行比较\"},\n        {\"title\": \"效果评估：评价所提方法在实际应用中的表现\"}\n      ]\n    },\n    {\n      \"title\": \"结论\",\n      \"subtitle\": [\n        {\"title\": \"数据总结：回顾研究过程中使用的主要数据集特点\"},\n        {\"title\": \"研究发现：提炼出本研究对于测试领域的贡献\"},\n        {\"title\": \"政策建议：提出基于研究成果对未来工作的指导性意见\"}\n      ]\n    }\n  ]\n}\n```"}}]}, "usage": {"input_tokens": 408, "output_tokens": 364, "total_tokens": 772, "prompt_tokens_details": {"cached_tokens": 0}}} [waitress-2]
2025-07-30 15:58:53  EarlyBird.api.talk2ai.model.model_proxy 101: WARNING  提纲缺少title字段，使用论文标题 [waitress-2]
2025-07-30 15:58:53  model_proxy.py 101: WARNING  提纲缺少title字段，使用论文标题 [waitress-2]
2025-07-30 15:58:53  EarlyBird.api.talk2ai.model.model_proxy 188: INFO  成功生成并修复 大学学生 风格的提纲 [waitress-2]
2025-07-30 15:58:53  model_proxy.py 188: INFO  成功生成并修复 大学学生 风格的提纲 [waitress-2]
2025-07-30 15:58:53  EarlyBird.api.talk2ai.model.model_proxy 34: INFO  Using model: qianwen [waitress-2]
2025-07-30 15:58:53  model_proxy.py 34: INFO  Using model: qianwen [waitress-2]
2025-07-30 15:58:53  EarlyBird.common.ai 60: INFO  使用模型: qianwen [waitress-2]
2025-07-30 15:58:53  __init__.py 60: INFO  使用模型: qianwen [waitress-2]
2025-07-30 15:58:53  EarlyBird.common.ai.adapter.qianwen 33: INFO  使用千问 API Key: sk-81a15... [waitress-2]
2025-07-30 15:58:53  qianwen.py 33: INFO  使用千问 API Key: sk-81a15... [waitress-2]
2025-07-30 15:58:53  EarlyBird.common.ai.adapter.qianwen 35: INFO  角色:你是一位硕士学生.任务:要为一篇题目为'测试'的论文拟定提纲.语言:使用写论文的正式语言,注重理论框架的严谨性，强调方法论和创新点.约束:写作语言是中文,论文包含6段落,每段包含3子段落,首先是'绪论',不要'参考文献'和'致谢'.

【数据丰富性要求】
1. 提纲结构要体现数据支撑：
   - 每个段落标题要体现具体的数据分析方向
   - 子段落要包含数据收集、数据分析、数据对比等环节
   - 确保有专门的段落用于数据展示和分析
2. 段落内容要求：
   - 绪论：包含研究背景、数据现状、研究意义
   - 理论分析：包含理论基础、数据模型、分析方法
   - 实证研究：包含数据收集、统计分析、结果展示
   - 案例分析：包含具体案例、数据对比、效果评估
   - 结论：包含数据总结、研究发现、政策建议
3. 子段落要求：
   - 每个子段落都要有明确的数据分析目标
   - 包含数据来源、分析方法、结果展示等要素
   - 确保逻辑清晰，数据支撑充分
格式:返回json格式,json数据中，只有一个'subtitle'字段,这个字段的值是数组。数组中每个元素，代表论文的一个段落。每段是一个json字典,其中包含一个'title'字段,表示段落标题。每段包含1个'subtitle'数组,表示该段的子段落,是一个数组,其中每个元素是一个字典,键是'title',值是标题。不要加入'content'字段,不要省略段落 [waitress-2]
2025-07-30 15:58:53  qianwen.py 35: INFO  角色:你是一位硕士学生.任务:要为一篇题目为'测试'的论文拟定提纲.语言:使用写论文的正式语言,注重理论框架的严谨性，强调方法论和创新点.约束:写作语言是中文,论文包含6段落,每段包含3子段落,首先是'绪论',不要'参考文献'和'致谢'.

【数据丰富性要求】
1. 提纲结构要体现数据支撑：
   - 每个段落标题要体现具体的数据分析方向
   - 子段落要包含数据收集、数据分析、数据对比等环节
   - 确保有专门的段落用于数据展示和分析
2. 段落内容要求：
   - 绪论：包含研究背景、数据现状、研究意义
   - 理论分析：包含理论基础、数据模型、分析方法
   - 实证研究：包含数据收集、统计分析、结果展示
   - 案例分析：包含具体案例、数据对比、效果评估
   - 结论：包含数据总结、研究发现、政策建议
3. 子段落要求：
   - 每个子段落都要有明确的数据分析目标
   - 包含数据来源、分析方法、结果展示等要素
   - 确保逻辑清晰，数据支撑充分
格式:返回json格式,json数据中，只有一个'subtitle'字段,这个字段的值是数组。数组中每个元素，代表论文的一个段落。每段是一个json字典,其中包含一个'title'字段,表示段落标题。每段包含1个'subtitle'数组,表示该段的子段落,是一个数组,其中每个元素是一个字典,键是'title',值是标题。不要加入'content'字段,不要省略段落 [waitress-2]
2025-07-30 15:59:18  EarlyBird.common.ai.adapter.qianwen 45: INFO  {"status_code": 200, "request_id": "d78bd95f-548f-9c88-a912-f20134b86cd6", "code": "", "message": "", "output": {"text": null, "finish_reason": null, "choices": [{"finish_reason": "stop", "message": {"role": "assistant", "content": "```json\n{\n  \"subtitle\": [\n    {\n      \"title\": \"绪论\",\n      \"subtitle\": [\n        {\n          \"title\": \"研究背景与问题提出\"\n        },\n        {\n          \"title\": \"当前数据现状分析\"\n        },\n        {\n          \"title\": \"研究意义及预期目标\"\n        }\n      ]\n    },\n    {\n      \"title\": \"理论基础与数据模型构建\",\n      \"subtitle\": [\n        {\n          \"title\": \"相关理论综述及其对本研究的支持作用\"\n        },\n        {\n          \"title\": \"基于已有数据建立的预测或解释模型\"\n        },\n        {\n          \"title\": \"选择特定分析方法的理由及其实现路径\"\n        }\n      ]\n    },\n    {\n      \"title\": \"实证研究设计与实施\",\n      \"subtitle\": [\n        {\n          \"title\": \"数据收集策略与样本描述\"\n        },\n        {\n          \"title\": \"采用的主要统计技术介绍\"\n        },\n        {\n          \"title\": \"初步数据分析结果展示\"\n        }\n      ]\n    },\n    {\n      \"title\": \"案例深度剖析\",\n      \"subtitle\": [\n        {\n          \"title\": \"选取典型案例的标准与理由\"\n        },\n        {\n          \"title\": \"案例间关键指标的数据对比\"\n        },\n        {\n          \"title\": \"基于数据的案例效果综合评价\"\n        }\n      ]\n    },\n    {\n      \"title\": \"研究成果可视化与讨论\",\n      \"subtitle\": [\n        {\n          \"title\": \"重要发现的数据支持证据\"\n        },\n        {\n          \"title\": \"研究结果与其他文献成果的比较\"\n        },\n        {\n          \"title\": \"针对不同利益相关者的政策建议\"\n        }\n      ]\n    },\n    {\n      \"title\": \"结论\",\n      \"subtitle\": [\n        {\n          \"title\": \"主要研究发现总结\"\n        },\n        {\n          \"title\": \"对未来研究方向的展望\"\n        },\n        {\n          \"title\": \"研究局限性分析\"\n        }\n      ]\n    }\n  ]\n}\n```"}}]}, "usage": {"input_tokens": 407, "output_tokens": 424, "total_tokens": 831, "prompt_tokens_details": {"cached_tokens": 0}}} [waitress-2]
2025-07-30 15:59:18  qianwen.py 45: INFO  {"status_code": 200, "request_id": "d78bd95f-548f-9c88-a912-f20134b86cd6", "code": "", "message": "", "output": {"text": null, "finish_reason": null, "choices": [{"finish_reason": "stop", "message": {"role": "assistant", "content": "```json\n{\n  \"subtitle\": [\n    {\n      \"title\": \"绪论\",\n      \"subtitle\": [\n        {\n          \"title\": \"研究背景与问题提出\"\n        },\n        {\n          \"title\": \"当前数据现状分析\"\n        },\n        {\n          \"title\": \"研究意义及预期目标\"\n        }\n      ]\n    },\n    {\n      \"title\": \"理论基础与数据模型构建\",\n      \"subtitle\": [\n        {\n          \"title\": \"相关理论综述及其对本研究的支持作用\"\n        },\n        {\n          \"title\": \"基于已有数据建立的预测或解释模型\"\n        },\n        {\n          \"title\": \"选择特定分析方法的理由及其实现路径\"\n        }\n      ]\n    },\n    {\n      \"title\": \"实证研究设计与实施\",\n      \"subtitle\": [\n        {\n          \"title\": \"数据收集策略与样本描述\"\n        },\n        {\n          \"title\": \"采用的主要统计技术介绍\"\n        },\n        {\n          \"title\": \"初步数据分析结果展示\"\n        }\n      ]\n    },\n    {\n      \"title\": \"案例深度剖析\",\n      \"subtitle\": [\n        {\n          \"title\": \"选取典型案例的标准与理由\"\n        },\n        {\n          \"title\": \"案例间关键指标的数据对比\"\n        },\n        {\n          \"title\": \"基于数据的案例效果综合评价\"\n        }\n      ]\n    },\n    {\n      \"title\": \"研究成果可视化与讨论\",\n      \"subtitle\": [\n        {\n          \"title\": \"重要发现的数据支持证据\"\n        },\n        {\n          \"title\": \"研究结果与其他文献成果的比较\"\n        },\n        {\n          \"title\": \"针对不同利益相关者的政策建议\"\n        }\n      ]\n    },\n    {\n      \"title\": \"结论\",\n      \"subtitle\": [\n        {\n          \"title\": \"主要研究发现总结\"\n        },\n        {\n          \"title\": \"对未来研究方向的展望\"\n        },\n        {\n          \"title\": \"研究局限性分析\"\n        }\n      ]\n    }\n  ]\n}\n```"}}]}, "usage": {"input_tokens": 407, "output_tokens": 424, "total_tokens": 831, "prompt_tokens_details": {"cached_tokens": 0}}} [waitress-2]
2025-07-30 15:59:18  EarlyBird.api.talk2ai.model.model_proxy 101: WARNING  提纲缺少title字段，使用论文标题 [waitress-2]
2025-07-30 15:59:18  model_proxy.py 101: WARNING  提纲缺少title字段，使用论文标题 [waitress-2]
2025-07-30 15:59:18  EarlyBird.api.talk2ai.model.model_proxy 188: INFO  成功生成并修复 研究学者 风格的提纲 [waitress-2]
2025-07-30 15:59:18  model_proxy.py 188: INFO  成功生成并修复 研究学者 风格的提纲 [waitress-2]
2025-07-30 15:59:18  EarlyBird.api.talk2ai.service 811: INFO  为用户 12 保存提纲历史记录，ID: 20 [waitress-2]
2025-07-30 15:59:18  service.py 811: INFO  为用户 12 保存提纲历史记录，ID: 20 [waitress-2]
2025-07-30 15:59:21  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/talk/checkExistingThesis [waitress-0]
2025-07-30 15:59:21  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/talk/checkExistingThesis [waitress-0]
2025-07-30 15:59:21  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-30T14:33:15.118687', 'user_id': 12, 'username': 'test1002'} [waitress-0]
2025-07-30 15:59:21  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-30T14:33:15.118687', 'user_id': 12, 'username': 'test1002'} [waitress-0]
2025-07-30 15:59:21  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 12 [waitress-0]
2025-07-30 15:59:21  hook_register.py 131: INFO  🔍 提取的user_id: 12 [waitress-0]
2025-07-30 15:59:21  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-30 15:59:21  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-30 15:59:21  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/talk/select4Content [waitress-3]
2025-07-30 15:59:21  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/talk/select4Content [waitress-3]
2025-07-30 15:59:21  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-30T14:33:15.118687', 'user_id': 12, 'username': 'test1002'} [waitress-3]
2025-07-30 15:59:21  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-30T14:33:15.118687', 'user_id': 12, 'username': 'test1002'} [waitress-3]
2025-07-30 15:59:21  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 12 [waitress-3]
2025-07-30 15:59:21  hook_register.py 131: INFO  🔍 提取的user_id: 12 [waitress-3]
2025-07-30 15:59:21  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-30 15:59:21  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-30 15:59:21  EarlyBird.api.talk2ai.apis 128: INFO  Received select4Content request with body type: <class 'EarlyBird.api.talk2ai.dantic.ParamSelect4Content'> [waitress-3]
2025-07-30 15:59:21  apis.py 128: INFO  Received select4Content request with body type: <class 'EarlyBird.api.talk2ai.dantic.ParamSelect4Content'> [waitress-3]
2025-07-30 15:59:21  EarlyBird.api.talk2ai.apis 132: INFO  Request title: 测试, level: 硕士, lang: 中文 [waitress-3]
2025-07-30 15:59:21  apis.py 132: INFO  Request title: 测试, level: 硕士, lang: 中文 [waitress-3]
2025-07-30 15:59:21  EarlyBird.api.talk2ai.apis 133: INFO  Outline type: <class 'dict'> [waitress-3]
2025-07-30 15:59:21  apis.py 133: INFO  Outline type: <class 'dict'> [waitress-3]
2025-07-30 15:59:21  EarlyBird.api.talk2ai.apis 154: INFO  Outline keys: dict_keys(['subtitle', 'title']) [waitress-3]
2025-07-30 15:59:21  apis.py 154: INFO  Outline keys: dict_keys(['subtitle', 'title']) [waitress-3]
2025-07-30 15:59:21  EarlyBird.api.talk2ai.apis 170: INFO  VIP状态来源 - g.isVip: False, session有激活码: False [waitress-3]
2025-07-30 15:59:21  apis.py 170: INFO  VIP状态来源 - g.isVip: False, session有激活码: False [waitress-3]
2025-07-30 15:59:21  EarlyBird.api.talk2ai.apis 184: INFO  用户对象VIP状态检查: False [waitress-3]
2025-07-30 15:59:21  apis.py 184: INFO  用户对象VIP状态检查: False [waitress-3]
2025-07-30 15:59:21  EarlyBird.api.talk2ai.apis 188: INFO  最终设置的用户状态 - User ID: 12, VIP: False [waitress-3]
2025-07-30 15:59:21  apis.py 188: INFO  最终设置的用户状态 - User ID: 12, VIP: False [waitress-3]
2025-07-30 15:59:21  EarlyBird.api.talk2ai.apis 195: INFO  Processing select4Content for user 12, title: 测试 [waitress-3]
2025-07-30 15:59:21  apis.py 195: INFO  Processing select4Content for user 12, title: 测试 [waitress-3]
2025-07-30 15:59:21  EarlyBird.api.talk2ai.service 523: INFO  用户 12 已有 0 篇论文 [waitress-3]
2025-07-30 15:59:21  service.py 523: INFO  用户 12 已有 0 篇论文 [waitress-3]
2025-07-30 15:59:21  EarlyBird.api.talk2ai.service 524: INFO  用户 12 的VIP状态: False [waitress-3]
2025-07-30 15:59:21  service.py 524: INFO  用户 12 的VIP状态: False [waitress-3]
2025-07-30 15:59:21  EarlyBird.api.talk2ai.service 529: INFO  用户 12 在g对象中的VIP状态: False [waitress-3]
2025-07-30 15:59:21  service.py 529: INFO  用户 12 在g对象中的VIP状态: False [waitress-3]
2025-07-30 15:59:21  EarlyBird.api.talk2ai.service 546: INFO  用户对象 12 的VIP状态: False [waitress-3]
2025-07-30 15:59:21  service.py 546: INFO  用户对象 12 的VIP状态: False [waitress-3]
2025-07-30 15:59:21  EarlyBird.api.talk2ai.service 552: INFO  最终判断用户 12 的VIP状态: False [waitress-3]
2025-07-30 15:59:21  service.py 552: INFO  最终判断用户 12 的VIP状态: False [waitress-3]
2025-07-30 15:59:21  EarlyBird.api.talk2ai.service 578: INFO  接收到的提纲数据类型: <class 'dict'> [waitress-3]
2025-07-30 15:59:21  service.py 578: INFO  接收到的提纲数据类型: <class 'dict'> [waitress-3]
2025-07-30 15:59:21  EarlyBird.api.talk2ai.service 580: INFO  提纲数据键: dict_keys(['subtitle', 'title']) [waitress-3]
2025-07-30 15:59:21  service.py 580: INFO  提纲数据键: dict_keys(['subtitle', 'title']) [waitress-3]
2025-07-30 15:59:21  EarlyBird.api.talk2ai.service 581: INFO  提纲数据内容: {"subtitle": [{"subtitle": [{"subtitle": [], "title": "研究背景与问题提出", "id": "9dvupwp5bl83953517gefu"}, {"subtitle": [], "title": "数据现状及其局限性分析", "id": "303e1z77krxorakroh72br"}, {"subtitle": [], "title": ... [waitress-3]
2025-07-30 15:59:21  service.py 581: INFO  提纲数据内容: {"subtitle": [{"subtitle": [{"subtitle": [], "title": "研究背景与问题提出", "id": "9dvupwp5bl83953517gefu"}, {"subtitle": [], "title": "数据现状及其局限性分析", "id": "303e1z77krxorakroh72br"}, {"subtitle": [], "title": ... [waitress-3]
2025-07-30 15:59:21  EarlyBird.api.talk2ai.utils 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-3]
2025-07-30 15:59:21  utils.py 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-3]
2025-07-30 15:59:21  EarlyBird.api.talk2ai.utils 55: INFO  提纲数据键: dict_keys(['subtitle', 'title']) [waitress-3]
2025-07-30 15:59:21  utils.py 55: INFO  提纲数据键: dict_keys(['subtitle', 'title']) [waitress-3]
2025-07-30 15:59:21  EarlyBird.api.talk2ai.utils 161: INFO  提纲处理完成，根节点ID: Jx6tBAisoozv33XXEutgSS [waitress-3]
2025-07-30 15:59:21  utils.py 161: INFO  提纲处理完成，根节点ID: Jx6tBAisoozv33XXEutgSS [waitress-3]
2025-07-30 15:59:21  EarlyBird.api.talk2ai.service 677: INFO  提纲处理完成，ID: Jx6tBAisoozv33XXEutgSS [waitress-3]
2025-07-30 15:59:21  service.py 677: INFO  提纲处理完成，ID: Jx6tBAisoozv33XXEutgSS [waitress-3]
2025-07-30 15:59:21  EarlyBird.api.talk2ai.service 687: INFO  成功解析提纲，找到 25 个段落标题 [waitress-3]
2025-07-30 15:59:21  service.py 687: INFO  成功解析提纲，找到 25 个段落标题 [waitress-3]
2025-07-30 15:59:21  EarlyBird.api.talk2ai.service 703: INFO  已创建论文记录，ID: 42 [waitress-3]
2025-07-30 15:59:21  service.py 703: INFO  已创建论文记录，ID: 42 [waitress-3]
2025-07-30 15:59:21  EarlyBird.api.talk2ai.service 717: INFO  已保存 25 个段落记录 [waitress-3]
2025-07-30 15:59:21  service.py 717: INFO  已保存 25 个段落记录 [waitress-3]
2025-07-30 15:59:21  EarlyBird.api.talk2ai.apis 201: INFO  select4Content success for user 12, thesis ID: 42 [waitress-3]
2025-07-30 15:59:21  apis.py 201: INFO  select4Content success for user 12, thesis ID: 42 [waitress-3]
2025-07-30 15:59:21  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getList [waitress-1]
2025-07-30 15:59:21  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getList [waitress-1]
2025-07-30 15:59:21  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-30T14:33:15.118687', 'user_id': 12, 'username': 'test1002'} [waitress-1]
2025-07-30 15:59:21  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-30T14:33:15.118687', 'user_id': 12, 'username': 'test1002'} [waitress-1]
2025-07-30 15:59:21  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 12 [waitress-1]
2025-07-30 15:59:21  hook_register.py 131: INFO  🔍 提取的user_id: 12 [waitress-1]
2025-07-30 15:59:21  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-30 15:59:21  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-30 15:59:21  EarlyBird.api.thesis.apis 92: INFO  用户 12 请求获取论文列表 [waitress-1]
2025-07-30 15:59:21  apis.py 92: INFO  用户 12 请求获取论文列表 [waitress-1]
2025-07-30 15:59:21  EarlyBird.api.thesis.service 867: INFO  查询论文列表，用户ID: 12 [waitress-1]
2025-07-30 15:59:21  service.py 867: INFO  查询论文列表，用户ID: 12 [waitress-1]
2025-07-30 15:59:21  EarlyBird.api.thesis.service 873: INFO  查询到 1 篇论文 [waitress-1]
2025-07-30 15:59:21  service.py 873: INFO  查询到 1 篇论文 [waitress-1]
2025-07-30 15:59:21  EarlyBird.api.thesis.service 883: INFO  论文ID: 42, 标题: 测试, 用户ID: 12 [waitress-1]
2025-07-30 15:59:21  service.py 883: INFO  论文ID: 42, 标题: 测试, 用户ID: 12 [waitress-1]
2025-07-30 15:59:21  EarlyBird.api.thesis.service 885: INFO  返回论文列表，共 1 篇 [waitress-1]
2025-07-30 15:59:21  service.py 885: INFO  返回论文列表，共 1 篇 [waitress-1]
2025-07-30 15:59:21  EarlyBird.api.thesis.apis 95: INFO  用户 12 获取论文列表成功，共 1 篇 [waitress-1]
2025-07-30 15:59:21  apis.py 95: INFO  用户 12 获取论文列表成功，共 1 篇 [waitress-1]
2025-07-30 15:59:21  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getDetail [waitress-2]
2025-07-30 15:59:21  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getDetail [waitress-2]
2025-07-30 15:59:21  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-30T14:33:15.118687', 'user_id': 12, 'username': 'test1002'} [waitress-2]
2025-07-30 15:59:21  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-30T14:33:15.118687', 'user_id': 12, 'username': 'test1002'} [waitress-2]
2025-07-30 15:59:21  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 12 [waitress-2]
2025-07-30 15:59:21  hook_register.py 131: INFO  🔍 提取的user_id: 12 [waitress-2]
2025-07-30 15:59:21  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-30 15:59:21  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-30 15:59:21  EarlyBird.api.talk2ai.utils 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-2]
2025-07-30 15:59:21  utils.py 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-2]
2025-07-30 15:59:21  EarlyBird.api.talk2ai.utils 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-2]
2025-07-30 15:59:21  utils.py 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-2]
2025-07-30 15:59:21  EarlyBird.api.talk2ai.utils 161: INFO  提纲处理完成，根节点ID: Jx6tBAisoozv33XXEutgSS [waitress-2]
2025-07-30 15:59:21  utils.py 161: INFO  提纲处理完成，根节点ID: Jx6tBAisoozv33XXEutgSS [waitress-2]
2025-07-30 15:59:22  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getUserDownloadRights [waitress-0]
2025-07-30 15:59:22  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getOutline [waitress-3]
2025-07-30 15:59:22  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getUserDownloadRights [waitress-0]
2025-07-30 15:59:22  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getOutline [waitress-3]
2025-07-30 15:59:22  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-30T14:33:15.118687', 'user_id': 12, 'username': 'test1002'} [waitress-0]
2025-07-30 15:59:22  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-30T14:33:15.118687', 'user_id': 12, 'username': 'test1002'} [waitress-0]
2025-07-30 15:59:22  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-30T14:33:15.118687', 'user_id': 12, 'username': 'test1002'} [waitress-3]
2025-07-30 15:59:22  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 12 [waitress-0]
2025-07-30 15:59:22  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-30T14:33:15.118687', 'user_id': 12, 'username': 'test1002'} [waitress-3]
2025-07-30 15:59:22  hook_register.py 131: INFO  🔍 提取的user_id: 12 [waitress-0]
2025-07-30 15:59:22  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 12 [waitress-3]
2025-07-30 15:59:22  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-30 15:59:22  hook_register.py 131: INFO  🔍 提取的user_id: 12 [waitress-3]
2025-07-30 15:59:22  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-30 15:59:22  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-30 15:59:22  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-30 15:59:22  EarlyBird.api.thesis.service 1408: INFO  用户 12 VIP状态详细检查: [waitress-0]
2025-07-30 15:59:22  EarlyBird.api.talk2ai.utils 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-3]
2025-07-30 15:59:22  service.py 1408: INFO  用户 12 VIP状态详细检查: [waitress-0]
2025-07-30 15:59:22  utils.py 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-3]
2025-07-30 15:59:22  EarlyBird.api.thesis.service 1409: INFO    - 用户对象存在: True [waitress-0]
2025-07-30 15:59:22  EarlyBird.api.talk2ai.utils 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-3]
2025-07-30 15:59:22  service.py 1409: INFO    - 用户对象存在: True [waitress-0]
2025-07-30 15:59:22  utils.py 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-3]
2025-07-30 15:59:22  EarlyBird.api.thesis.service 1410: INFO    - 有isVip方法: True [waitress-0]
2025-07-30 15:59:22  EarlyBird.api.talk2ai.utils 161: INFO  提纲处理完成，根节点ID: Jx6tBAisoozv33XXEutgSS [waitress-3]
2025-07-30 15:59:22  service.py 1410: INFO    - 有isVip方法: True [waitress-0]
2025-07-30 15:59:22  utils.py 161: INFO  提纲处理完成，根节点ID: Jx6tBAisoozv33XXEutgSS [waitress-3]
2025-07-30 15:59:22  EarlyBird.api.thesis.service 1411: INFO    - VIP开始时间: None [waitress-0]
2025-07-30 15:59:22  service.py 1411: INFO    - VIP开始时间: None [waitress-0]
2025-07-30 15:59:22  EarlyBird.api.thesis.service 1412: INFO    - VIP结束时间: None [waitress-0]
2025-07-30 15:59:22  service.py 1412: INFO    - VIP结束时间: None [waitress-0]
2025-07-30 15:59:22  EarlyBird.api.thesis.service 1413: INFO    - VIP等级: 1 [waitress-0]
2025-07-30 15:59:22  service.py 1413: INFO    - VIP等级: 1 [waitress-0]
2025-07-30 15:59:22  EarlyBird.api.thesis.service 1414: INFO    - 最终VIP状态: False [waitress-0]
2025-07-30 15:59:22  service.py 1414: INFO    - 最终VIP状态: False [waitress-0]
2025-07-30 15:59:22  EarlyBird.api.thesis.service 1452: INFO  🔍 用户 12 下载计数（统一逻辑）: [waitress-0]
2025-07-30 15:59:22  service.py 1452: INFO  🔍 用户 12 下载计数（统一逻辑）: [waitress-0]
2025-07-30 15:59:22  EarlyBird.api.thesis.service 1453: INFO    所有记录数: 9 [waitress-0]
2025-07-30 15:59:22  service.py 1453: INFO    所有记录数: 9 [waitress-0]
2025-07-30 15:59:22  EarlyBird.api.thesis.service 1454: INFO    今天的记录数: 0 [waitress-0]
2025-07-30 15:59:22  service.py 1454: INFO    今天的记录数: 0 [waitress-0]
2025-07-30 15:59:22  EarlyBird.api.thesis.service 1455: INFO    有效记录数（排除今天测试）: 9 [waitress-0]
2025-07-30 15:59:22  service.py 1455: INFO    有效记录数（排除今天测试）: 9 [waitress-0]
2025-07-30 15:59:22  EarlyBird.api.thesis.service 1462: INFO      记录1: 论文19, 今天=False, 测试记录=False, 有效=True [waitress-0]
2025-07-30 15:59:22  service.py 1462: INFO      记录1: 论文19, 今天=False, 测试记录=False, 有效=True [waitress-0]
2025-07-30 15:59:22  EarlyBird.api.thesis.service 1462: INFO      记录2: 论文21, 今天=False, 测试记录=True, 有效=True [waitress-0]
2025-07-30 15:59:22  service.py 1462: INFO      记录2: 论文21, 今天=False, 测试记录=True, 有效=True [waitress-0]
2025-07-30 15:59:22  EarlyBird.api.thesis.service 1462: INFO      记录3: 论文22, 今天=False, 测试记录=True, 有效=True [waitress-0]
2025-07-30 15:59:22  service.py 1462: INFO      记录3: 论文22, 今天=False, 测试记录=True, 有效=True [waitress-0]
2025-07-30 15:59:22  EarlyBird.api.thesis.service 1462: INFO      记录4: 论文23, 今天=False, 测试记录=True, 有效=True [waitress-0]
2025-07-30 15:59:22  service.py 1462: INFO      记录4: 论文23, 今天=False, 测试记录=True, 有效=True [waitress-0]
2025-07-30 15:59:22  EarlyBird.api.thesis.service 1462: INFO      记录5: 论文24, 今天=False, 测试记录=True, 有效=True [waitress-0]
2025-07-30 15:59:22  service.py 1462: INFO      记录5: 论文24, 今天=False, 测试记录=True, 有效=True [waitress-0]
2025-07-30 15:59:22  EarlyBird.api.thesis.service 1462: INFO      记录6: 论文25, 今天=False, 测试记录=False, 有效=True [waitress-0]
2025-07-30 15:59:22  service.py 1462: INFO      记录6: 论文25, 今天=False, 测试记录=False, 有效=True [waitress-0]
2025-07-30 15:59:22  EarlyBird.api.thesis.service 1462: INFO      记录7: 论文26, 今天=False, 测试记录=False, 有效=True [waitress-0]
2025-07-30 15:59:22  service.py 1462: INFO      记录7: 论文26, 今天=False, 测试记录=False, 有效=True [waitress-0]
2025-07-30 15:59:22  EarlyBird.api.thesis.service 1462: INFO      记录8: 论文27, 今天=False, 测试记录=False, 有效=True [waitress-0]
2025-07-30 15:59:22  service.py 1462: INFO      记录8: 论文27, 今天=False, 测试记录=False, 有效=True [waitress-0]
2025-07-30 15:59:22  EarlyBird.api.thesis.service 1462: INFO      记录9: 论文28, 今天=False, 测试记录=True, 有效=True [waitress-0]
2025-07-30 15:59:22  service.py 1462: INFO      记录9: 论文28, 今天=False, 测试记录=True, 有效=True [waitress-0]
2025-07-30 15:59:25  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-1]
2025-07-30 15:59:25  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-1]
2025-07-30 15:59:25  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-30T14:33:15.118687', 'user_id': 12, 'username': 'test1002'} [waitress-1]
2025-07-30 15:59:25  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-30T14:33:15.118687', 'user_id': 12, 'username': 'test1002'} [waitress-1]
2025-07-30 15:59:25  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 12 [waitress-1]
2025-07-30 15:59:25  hook_register.py 131: INFO  🔍 提取的user_id: 12 [waitress-1]
2025-07-30 15:59:25  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-30 15:59:25  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-30 15:59:25  EarlyBird.api.thesis.apis 145: INFO  用户 12 请求下载论文 42 [waitress-1]
2025-07-30 15:59:25  apis.py 145: INFO  用户 12 请求下载论文 42 [waitress-1]
2025-07-30 15:59:25  EarlyBird.api.thesis.service 381: INFO  用户 12 请求导出论文 42 [waitress-1]
2025-07-30 15:59:25  service.py 381: INFO  用户 12 请求导出论文 42 [waitress-1]
2025-07-30 15:59:25  EarlyBird.api.thesis.service 391: INFO  用户 12 VIP状态: False [waitress-1]
2025-07-30 15:59:25  service.py 391: INFO  用户 12 VIP状态: False [waitress-1]
2025-07-30 15:59:25  EarlyBird.api.thesis.service 402: INFO  论文下载收费功能是否启用: True [waitress-1]
2025-07-30 15:59:25  service.py 402: INFO  论文下载收费功能是否启用: True [waitress-1]
2025-07-30 15:59:25  EarlyBird.api.thesis.service 424: INFO  VIP用户是否免费下载: True [waitress-1]
2025-07-30 15:59:25  service.py 424: INFO  VIP用户是否免费下载: True [waitress-1]
2025-07-30 15:59:25  EarlyBird.api.thesis.service 442: INFO  🔍 首次下载免费配置查询结果: {"id": 9, "name": "\u9996\u6b21\u4e0b\u8f7d\u662f\u5426\u514d\u8d39", "config_key": "thesis.download.first_free", "config_value": "true", "description": "\u7528\u6237\u9996\u6b21\u4e0b\u8f7d\u8bba\u6587\u662f\u5426\u514d\u8d39", "create_time": "2025-06-28 15:32:25"} [waitress-1]
2025-07-30 15:59:25  service.py 442: INFO  🔍 首次下载免费配置查询结果: {"id": 9, "name": "\u9996\u6b21\u4e0b\u8f7d\u662f\u5426\u514d\u8d39", "config_key": "thesis.download.first_free", "config_value": "true", "description": "\u7528\u6237\u9996\u6b21\u4e0b\u8f7d\u8bba\u6587\u662f\u5426\u514d\u8d39", "create_time": "2025-06-28 15:32:25"} [waitress-1]
2025-07-30 15:59:25  EarlyBird.api.thesis.service 444: INFO    配置值: 'true' (类型: <class 'str'>) [waitress-1]
2025-07-30 15:59:25  service.py 444: INFO    配置值: 'true' (类型: <class 'str'>) [waitress-1]
2025-07-30 15:59:25  EarlyBird.api.thesis.service 445: INFO    is_deleted: 0 [waitress-1]
2025-07-30 15:59:25  service.py 445: INFO    is_deleted: 0 [waitress-1]
2025-07-30 15:59:25  EarlyBird.api.thesis.service 446: INFO    config_key: thesis.download.first_free [waitress-1]
2025-07-30 15:59:25  service.py 446: INFO    config_key: thesis.download.first_free [waitress-1]
2025-07-30 15:59:25  EarlyBird.api.thesis.service 455: INFO    配置值处理: 'true' -> 'true' -> True [waitress-1]
2025-07-30 15:59:25  service.py 455: INFO    配置值处理: 'true' -> 'true' -> True [waitress-1]
2025-07-30 15:59:25  EarlyBird.api.thesis.service 459: INFO  🎯 首次下载是否免费: True [waitress-1]
2025-07-30 15:59:25  service.py 459: INFO  🎯 首次下载是否免费: True [waitress-1]
2025-07-30 15:59:25  EarlyBird.api.thesis.service 464: INFO  🔍 用户 12 下载记录检查（统一逻辑）: [waitress-1]
2025-07-30 15:59:25  service.py 464: INFO  🔍 用户 12 下载记录检查（统一逻辑）: [waitress-1]
2025-07-30 15:59:25  EarlyBird.api.thesis.service 465: INFO    总下载记录数: 9 [waitress-1]
2025-07-30 15:59:25  service.py 465: INFO    总下载记录数: 9 [waitress-1]
2025-07-30 15:59:25  EarlyBird.api.thesis.service 469: INFO    记录 1: 论文ID=19, 已支付=True, 支付方式=direct_download, 价格=0.0, 创建时间=2025-06-28 15:22:44 [waitress-1]
2025-07-30 15:59:25  service.py 469: INFO    记录 1: 论文ID=19, 已支付=True, 支付方式=direct_download, 价格=0.0, 创建时间=2025-06-28 15:22:44 [waitress-1]
2025-07-30 15:59:25  EarlyBird.api.thesis.service 469: INFO    记录 2: 论文ID=21, 已支付=True, 支付方式=free_first_time, 价格=0.0, 创建时间=2025-07-03 15:11:42 [waitress-1]
2025-07-30 15:59:25  service.py 469: INFO    记录 2: 论文ID=21, 已支付=True, 支付方式=free_first_time, 价格=0.0, 创建时间=2025-07-03 15:11:42 [waitress-1]
2025-07-30 15:59:25  EarlyBird.api.thesis.service 469: INFO    记录 3: 论文ID=22, 已支付=True, 支付方式=free_first_time, 价格=0.0, 创建时间=2025-07-03 15:12:41 [waitress-1]
2025-07-30 15:59:25  service.py 469: INFO    记录 3: 论文ID=22, 已支付=True, 支付方式=free_first_time, 价格=0.0, 创建时间=2025-07-03 15:12:41 [waitress-1]
2025-07-30 15:59:25  EarlyBird.api.thesis.service 469: INFO    记录 4: 论文ID=23, 已支付=True, 支付方式=free_first_time, 价格=0.0, 创建时间=2025-07-03 15:12:54 [waitress-1]
2025-07-30 15:59:25  service.py 469: INFO    记录 4: 论文ID=23, 已支付=True, 支付方式=free_first_time, 价格=0.0, 创建时间=2025-07-03 15:12:54 [waitress-1]
2025-07-30 15:59:25  EarlyBird.api.thesis.service 469: INFO    记录 5: 论文ID=24, 已支付=True, 支付方式=free_first_time, 价格=0.0, 创建时间=2025-07-03 15:16:47 [waitress-1]
2025-07-30 15:59:25  service.py 469: INFO    记录 5: 论文ID=24, 已支付=True, 支付方式=free_first_time, 价格=0.0, 创建时间=2025-07-03 15:16:47 [waitress-1]
2025-07-30 15:59:25  EarlyBird.api.thesis.service 469: INFO    记录 6: 论文ID=25, 已支付=True, 支付方式=wechat, 价格=10.0, 创建时间=2025-07-14 18:37:40 [waitress-1]
2025-07-30 15:59:25  service.py 469: INFO    记录 6: 论文ID=25, 已支付=True, 支付方式=wechat, 价格=10.0, 创建时间=2025-07-14 18:37:40 [waitress-1]
2025-07-30 15:59:25  EarlyBird.api.thesis.service 469: INFO    记录 7: 论文ID=26, 已支付=True, 支付方式=wechat, 价格=10.0, 创建时间=2025-07-14 21:09:17 [waitress-1]
2025-07-30 15:59:25  service.py 469: INFO    记录 7: 论文ID=26, 已支付=True, 支付方式=wechat, 价格=10.0, 创建时间=2025-07-14 21:09:17 [waitress-1]
2025-07-30 15:59:25  EarlyBird.api.thesis.service 469: INFO    记录 8: 论文ID=27, 已支付=True, 支付方式=wechat, 价格=10.0, 创建时间=2025-07-14 21:58:48 [waitress-1]
2025-07-30 15:59:25  service.py 469: INFO    记录 8: 论文ID=27, 已支付=True, 支付方式=wechat, 价格=10.0, 创建时间=2025-07-14 21:58:48 [waitress-1]
2025-07-30 15:59:25  EarlyBird.api.thesis.service 469: INFO    记录 9: 论文ID=28, 已支付=True, 支付方式=free_disabled, 价格=0.0, 创建时间=2025-07-14 21:59:18 [waitress-1]
2025-07-30 15:59:25  service.py 469: INFO    记录 9: 论文ID=28, 已支付=True, 支付方式=free_disabled, 价格=0.0, 创建时间=2025-07-14 21:59:18 [waitress-1]
2025-07-30 15:59:25  EarlyBird.api.thesis.service 483: INFO    今天的记录数: 0 [waitress-1]
2025-07-30 15:59:25  service.py 483: INFO    今天的记录数: 0 [waitress-1]
2025-07-30 15:59:25  EarlyBird.api.thesis.service 484: INFO    有效记录数（排除今天测试）: 9 [waitress-1]
2025-07-30 15:59:25  service.py 484: INFO    有效记录数（排除今天测试）: 9 [waitress-1]
2025-07-30 15:59:25  EarlyBird.api.thesis.service 491: INFO      记录1: 论文19, 今天=False, 测试记录=False, 有效=True [waitress-1]
2025-07-30 15:59:25  service.py 491: INFO      记录1: 论文19, 今天=False, 测试记录=False, 有效=True [waitress-1]
2025-07-30 15:59:25  EarlyBird.api.thesis.service 491: INFO      记录2: 论文21, 今天=False, 测试记录=True, 有效=True [waitress-1]
2025-07-30 15:59:25  service.py 491: INFO      记录2: 论文21, 今天=False, 测试记录=True, 有效=True [waitress-1]
2025-07-30 15:59:25  EarlyBird.api.thesis.service 491: INFO      记录3: 论文22, 今天=False, 测试记录=True, 有效=True [waitress-1]
2025-07-30 15:59:25  service.py 491: INFO      记录3: 论文22, 今天=False, 测试记录=True, 有效=True [waitress-1]
2025-07-30 15:59:25  EarlyBird.api.thesis.service 491: INFO      记录4: 论文23, 今天=False, 测试记录=True, 有效=True [waitress-1]
2025-07-30 15:59:25  service.py 491: INFO      记录4: 论文23, 今天=False, 测试记录=True, 有效=True [waitress-1]
2025-07-30 15:59:25  EarlyBird.api.thesis.service 491: INFO      记录5: 论文24, 今天=False, 测试记录=True, 有效=True [waitress-1]
2025-07-30 15:59:25  service.py 491: INFO      记录5: 论文24, 今天=False, 测试记录=True, 有效=True [waitress-1]
2025-07-30 15:59:25  EarlyBird.api.thesis.service 491: INFO      记录6: 论文25, 今天=False, 测试记录=False, 有效=True [waitress-1]
2025-07-30 15:59:25  service.py 491: INFO      记录6: 论文25, 今天=False, 测试记录=False, 有效=True [waitress-1]
2025-07-30 15:59:25  EarlyBird.api.thesis.service 491: INFO      记录7: 论文26, 今天=False, 测试记录=False, 有效=True [waitress-1]
2025-07-30 15:59:25  service.py 491: INFO      记录7: 论文26, 今天=False, 测试记录=False, 有效=True [waitress-1]
2025-07-30 15:59:25  EarlyBird.api.thesis.service 491: INFO      记录8: 论文27, 今天=False, 测试记录=False, 有效=True [waitress-1]
2025-07-30 15:59:25  service.py 491: INFO      记录8: 论文27, 今天=False, 测试记录=False, 有效=True [waitress-1]
2025-07-30 15:59:25  EarlyBird.api.thesis.service 491: INFO      记录9: 论文28, 今天=False, 测试记录=True, 有效=True [waitress-1]
2025-07-30 15:59:25  service.py 491: INFO      记录9: 论文28, 今天=False, 测试记录=True, 有效=True [waitress-1]
2025-07-30 15:59:25  EarlyBird.api.thesis.service 497: INFO  🎯 首次免费判断（统一逻辑）: [waitress-1]
2025-07-30 15:59:25  service.py 497: INFO  🎯 首次免费判断（统一逻辑）: [waitress-1]
2025-07-30 15:59:25  EarlyBird.api.thesis.service 498: INFO    first_free配置: True [waitress-1]
2025-07-30 15:59:25  service.py 498: INFO    first_free配置: True [waitress-1]
2025-07-30 15:59:25  EarlyBird.api.thesis.service 499: INFO    has_valid_downloads: True [waitress-1]
2025-07-30 15:59:25  service.py 499: INFO    has_valid_downloads: True [waitress-1]
2025-07-30 15:59:25  EarlyBird.api.thesis.service 500: INFO    判断条件: first_free=True AND not has_valid_downloads=False [waitress-1]
2025-07-30 15:59:25  service.py 500: INFO    判断条件: first_free=True AND not has_valid_downloads=False [waitress-1]
2025-07-30 15:59:25  EarlyBird.api.thesis.service 501: INFO    最终判断: False [waitress-1]
2025-07-30 15:59:25  service.py 501: INFO    最终判断: False [waitress-1]
2025-07-30 15:59:25  EarlyBird.api.thesis.service 516: INFO  ❌ 用户 12 不符合首次免费条件，需要检查支付状态 [waitress-1]
2025-07-30 15:59:25  service.py 516: INFO  ❌ 用户 12 不符合首次免费条件，需要检查支付状态 [waitress-1]
2025-07-30 15:59:25  EarlyBird.api.thesis.service 674: INFO  从配置中读取到论文下载价格: 13.88 [waitress-1]
2025-07-30 15:59:25  service.py 674: INFO  从配置中读取到论文下载价格: 13.88 [waitress-1]
2025-07-30 15:59:25  EarlyBird.api.thesis.service 682: INFO  用户 12 需要支付才能下载论文 42，价格: 13.88 [waitress-1]
2025-07-30 15:59:25  service.py 682: INFO  用户 12 需要支付才能下载论文 42，价格: 13.88 [waitress-1]
2025-07-30 15:59:25  EarlyBird.api.thesis.apis 151: INFO  用户 12 需要支付才能下载论文 42，价格: 13.88 [waitress-1]
2025-07-30 15:59:25  apis.py 151: INFO  用户 12 需要支付才能下载论文 42，价格: 13.88 [waitress-1]
2025-07-30 15:59:25  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/payDownload [waitress-2]
2025-07-30 15:59:25  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/payDownload [waitress-2]
2025-07-30 15:59:25  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-30T14:33:15.118687', 'user_id': 12, 'username': 'test1002'} [waitress-2]
2025-07-30 15:59:25  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-30T14:33:15.118687', 'user_id': 12, 'username': 'test1002'} [waitress-2]
2025-07-30 15:59:25  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 12 [waitress-2]
2025-07-30 15:59:25  hook_register.py 131: INFO  🔍 提取的user_id: 12 [waitress-2]
2025-07-30 15:59:25  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-30 15:59:25  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-30 15:59:25  EarlyBird.api.thesis.apis 199: INFO  开始处理支付请求 - 用户: 12, 论文: 42 [waitress-2]
2025-07-30 15:59:25  apis.py 199: INFO  开始处理支付请求 - 用户: 12, 论文: 42 [waitress-2]
2025-07-30 15:59:25  EarlyBird.api.thesis.service 990: INFO  处理支付请求 - 用户: 12, 论文: 42, 时间: 1753862365.7218711 [waitress-2]
2025-07-30 15:59:25  service.py 990: INFO  处理支付请求 - 用户: 12, 论文: 42, 时间: 1753862365.7218711 [waitress-2]
2025-07-30 15:59:25  EarlyBird.api.thesis.service 992: INFO  开始处理支付请求 - 用户: 12, 论文: 42, 支付方式: wechat [waitress-2]
2025-07-30 15:59:25  service.py 992: INFO  开始处理支付请求 - 用户: 12, 论文: 42, 支付方式: wechat [waitress-2]
2025-07-30 15:59:25  EarlyBird.api.thesis.service 1006: INFO  🔍 价格配置查询: [waitress-2]
2025-07-30 15:59:25  service.py 1006: INFO  🔍 价格配置查询: [waitress-2]
2025-07-30 15:59:25  EarlyBird.api.thesis.service 1007: INFO    配置记录: {"id": 10, "name": "\u8bba\u6587\u4e0b\u8f7d\u6536\u8d39\u91d1\u989d", "config_key": "thesis.download.price", "config_value": "13.88", "description": "\u8bba\u6587\u4e0b\u8f7d\u7684\u6536\u8d39\u91d1\u989d(\u5143)", "create_time": "2025-06-28 15:32:25"} [waitress-2]
2025-07-30 15:59:25  service.py 1007: INFO    配置记录: {"id": 10, "name": "\u8bba\u6587\u4e0b\u8f7d\u6536\u8d39\u91d1\u989d", "config_key": "thesis.download.price", "config_value": "13.88", "description": "\u8bba\u6587\u4e0b\u8f7d\u7684\u6536\u8d39\u91d1\u989d(\u5143)", "create_time": "2025-06-28 15:32:25"} [waitress-2]
2025-07-30 15:59:25  EarlyBird.api.thesis.service 1009: INFO    配置值: '13.88' (类型: <class 'str'>) [waitress-2]
2025-07-30 15:59:25  service.py 1009: INFO    配置值: '13.88' (类型: <class 'str'>) [waitress-2]
2025-07-30 15:59:25  EarlyBird.api.thesis.service 1010: INFO    is_deleted: 0 [waitress-2]
2025-07-30 15:59:25  service.py 1010: INFO    is_deleted: 0 [waitress-2]
2025-07-30 15:59:25  EarlyBird.api.thesis.service 1015: INFO  🎯 最终价格: 13.88 [waitress-2]
2025-07-30 15:59:25  service.py 1015: INFO  🎯 最终价格: 13.88 [waitress-2]
2025-07-30 15:59:25  EarlyBird.api.thesis.service 1061: INFO  创建支付记录成功 - 订单: thesis_download_7597b6cff81b, 金额: 13.88 [waitress-2]
2025-07-30 15:59:25  service.py 1061: INFO  创建支付记录成功 - 订单: thesis_download_7597b6cff81b, 金额: 13.88 [waitress-2]
2025-07-30 15:59:25  EarlyBird.config.wechat_pay_config 46: INFO  使用数据库中的微信支付配置 [waitress-2]
2025-07-30 15:59:25  wechat_pay_config.py 46: INFO  使用数据库中的微信支付配置 [waitress-2]
2025-07-30 15:59:25  EarlyBird.common.wechat_pay_v3 54: INFO  尝试加载私钥文件: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird/certs/privateKey.txt [waitress-2]
2025-07-30 15:59:25  wechat_pay_v3.py 54: INFO  尝试加载私钥文件: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird/certs/privateKey.txt [waitress-2]
2025-07-30 15:59:25  EarlyBird.common.wechat_pay_v3 62: INFO  商户私钥加载成功 [waitress-2]
2025-07-30 15:59:25  wechat_pay_v3.py 62: INFO  商户私钥加载成功 [waitress-2]
2025-07-30 15:59:26  EarlyBird.api.thesis.service 111: INFO  微信支付二维码生成成功 - 订单: thesis_download_7597b6cff81b [waitress-2]
2025-07-30 15:59:26  service.py 111: INFO  微信支付二维码生成成功 - 订单: thesis_download_7597b6cff81b [waitress-2]
2025-07-30 15:59:28  EarlyBird.api.thesis.apis 211: INFO  清除支付请求锁 - 用户: 12, 论文: 42 [Thread-4 (clear_lock)]
2025-07-30 15:59:28  apis.py 211: INFO  清除支付请求锁 - 用户: 12, 论文: 42 [Thread-4 (clear_lock)]
2025-07-30 15:59:29  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-30 15:59:29  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-3]
2025-07-30 15:59:29  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-30T14:33:15.118687', 'user_id': 12, 'username': 'test1002'} [waitress-3]
2025-07-30 15:59:29  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-30T14:33:15.118687', 'user_id': 12, 'username': 'test1002'} [waitress-3]
2025-07-30 15:59:29  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 12 [waitress-3]
2025-07-30 15:59:29  hook_register.py 131: INFO  🔍 提取的user_id: 12 [waitress-3]
2025-07-30 15:59:29  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-30 15:59:29  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-30 15:59:29  EarlyBird.api.thesis.service 1086: INFO  查询支付状态 - 用户: 12, 论文: 42, 订单: thesis_download_7597b6cff81b [waitress-3]
2025-07-30 15:59:29  service.py 1086: INFO  查询支付状态 - 用户: 12, 论文: 42, 订单: thesis_download_7597b6cff81b [waitress-3]
2025-07-30 15:59:29  EarlyBird.api.thesis.service 1130: INFO  找到支付记录 - 订单: thesis_download_7597b6cff81b, 状态: 0 [waitress-3]
2025-07-30 15:59:29  service.py 1130: INFO  找到支付记录 - 订单: thesis_download_7597b6cff81b, 状态: 0 [waitress-3]
2025-07-30 15:59:29  EarlyBird.api.thesis.service 1134: INFO  本地支付状态为未支付，查询微信支付平台状态 - 订单: thesis_download_7597b6cff81b [waitress-3]
2025-07-30 15:59:29  service.py 1134: INFO  本地支付状态为未支付，查询微信支付平台状态 - 订单: thesis_download_7597b6cff81b [waitress-3]
2025-07-30 15:59:29  EarlyBird.config.wechat_pay_config 46: INFO  使用数据库中的微信支付配置 [waitress-3]
2025-07-30 15:59:29  wechat_pay_config.py 46: INFO  使用数据库中的微信支付配置 [waitress-3]
2025-07-30 15:59:29  EarlyBird.common.wechat_pay_v3 54: INFO  尝试加载私钥文件: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird/certs/privateKey.txt [waitress-3]
2025-07-30 15:59:29  wechat_pay_v3.py 54: INFO  尝试加载私钥文件: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird/certs/privateKey.txt [waitress-3]
2025-07-30 15:59:29  EarlyBird.common.wechat_pay_v3 62: INFO  商户私钥加载成功 [waitress-3]
2025-07-30 15:59:29  wechat_pay_v3.py 62: INFO  商户私钥加载成功 [waitress-3]
2025-07-30 15:59:30  EarlyBird.api.thesis.service 1149: INFO  微信支付平台查询结果: {'success': True, 'trade_state': 'NOTPAY', 'trade_state_desc': '订单未支付', 'amount': 13.88, 'success_time': None, 'transaction_id': None} [waitress-3]
2025-07-30 15:59:30  service.py 1149: INFO  微信支付平台查询结果: {'success': True, 'trade_state': 'NOTPAY', 'trade_state_desc': '订单未支付', 'amount': 13.88, 'success_time': None, 'transaction_id': None} [waitress-3]
2025-07-30 15:59:32  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-30 15:59:32  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-0]
2025-07-30 15:59:32  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-30T14:33:15.118687', 'user_id': 12, 'username': 'test1002'} [waitress-0]
2025-07-30 15:59:32  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-30T14:33:15.118687', 'user_id': 12, 'username': 'test1002'} [waitress-0]
2025-07-30 15:59:32  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 12 [waitress-0]
2025-07-30 15:59:32  hook_register.py 131: INFO  🔍 提取的user_id: 12 [waitress-0]
2025-07-30 15:59:32  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-30 15:59:32  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-30 15:59:32  EarlyBird.api.thesis.service 1086: INFO  查询支付状态 - 用户: 12, 论文: 42, 订单: thesis_download_7597b6cff81b [waitress-0]
2025-07-30 15:59:32  service.py 1086: INFO  查询支付状态 - 用户: 12, 论文: 42, 订单: thesis_download_7597b6cff81b [waitress-0]
2025-07-30 15:59:32  EarlyBird.api.thesis.service 1130: INFO  找到支付记录 - 订单: thesis_download_7597b6cff81b, 状态: 0 [waitress-0]
2025-07-30 15:59:32  service.py 1130: INFO  找到支付记录 - 订单: thesis_download_7597b6cff81b, 状态: 0 [waitress-0]
2025-07-30 15:59:32  EarlyBird.api.thesis.service 1134: INFO  本地支付状态为未支付，查询微信支付平台状态 - 订单: thesis_download_7597b6cff81b [waitress-0]
2025-07-30 15:59:32  service.py 1134: INFO  本地支付状态为未支付，查询微信支付平台状态 - 订单: thesis_download_7597b6cff81b [waitress-0]
2025-07-30 15:59:32  EarlyBird.config.wechat_pay_config 46: INFO  使用数据库中的微信支付配置 [waitress-0]
2025-07-30 15:59:32  wechat_pay_config.py 46: INFO  使用数据库中的微信支付配置 [waitress-0]
2025-07-30 15:59:32  EarlyBird.common.wechat_pay_v3 54: INFO  尝试加载私钥文件: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird/certs/privateKey.txt [waitress-0]
2025-07-30 15:59:32  wechat_pay_v3.py 54: INFO  尝试加载私钥文件: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird/certs/privateKey.txt [waitress-0]
2025-07-30 15:59:32  EarlyBird.common.wechat_pay_v3 62: INFO  商户私钥加载成功 [waitress-0]
2025-07-30 15:59:32  wechat_pay_v3.py 62: INFO  商户私钥加载成功 [waitress-0]
2025-07-30 15:59:33  EarlyBird.api.thesis.service 1149: INFO  微信支付平台查询结果: {'success': True, 'trade_state': 'NOTPAY', 'trade_state_desc': '订单未支付', 'amount': 13.88, 'success_time': None, 'transaction_id': None} [waitress-0]
2025-07-30 15:59:33  service.py 1149: INFO  微信支付平台查询结果: {'success': True, 'trade_state': 'NOTPAY', 'trade_state_desc': '订单未支付', 'amount': 13.88, 'success_time': None, 'transaction_id': None} [waitress-0]
2025-07-30 15:59:35  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-30 15:59:35  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-30 15:59:35  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-30T14:33:15.118687', 'user_id': 12, 'username': 'test1002'} [waitress-1]
2025-07-30 15:59:35  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-30T14:33:15.118687', 'user_id': 12, 'username': 'test1002'} [waitress-1]
2025-07-30 15:59:35  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 12 [waitress-1]
2025-07-30 15:59:35  hook_register.py 131: INFO  🔍 提取的user_id: 12 [waitress-1]
2025-07-30 15:59:35  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-30 15:59:35  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-30 15:59:35  EarlyBird.api.thesis.service 1086: INFO  查询支付状态 - 用户: 12, 论文: 42, 订单: thesis_download_7597b6cff81b [waitress-1]
2025-07-30 15:59:35  service.py 1086: INFO  查询支付状态 - 用户: 12, 论文: 42, 订单: thesis_download_7597b6cff81b [waitress-1]
2025-07-30 15:59:35  EarlyBird.api.thesis.service 1130: INFO  找到支付记录 - 订单: thesis_download_7597b6cff81b, 状态: 0 [waitress-1]
2025-07-30 15:59:35  service.py 1130: INFO  找到支付记录 - 订单: thesis_download_7597b6cff81b, 状态: 0 [waitress-1]
2025-07-30 15:59:35  EarlyBird.api.thesis.service 1134: INFO  本地支付状态为未支付，查询微信支付平台状态 - 订单: thesis_download_7597b6cff81b [waitress-1]
2025-07-30 15:59:35  service.py 1134: INFO  本地支付状态为未支付，查询微信支付平台状态 - 订单: thesis_download_7597b6cff81b [waitress-1]
2025-07-30 15:59:35  EarlyBird.config.wechat_pay_config 46: INFO  使用数据库中的微信支付配置 [waitress-1]
2025-07-30 15:59:35  wechat_pay_config.py 46: INFO  使用数据库中的微信支付配置 [waitress-1]
2025-07-30 15:59:35  EarlyBird.common.wechat_pay_v3 54: INFO  尝试加载私钥文件: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird/certs/privateKey.txt [waitress-1]
2025-07-30 15:59:35  wechat_pay_v3.py 54: INFO  尝试加载私钥文件: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird/certs/privateKey.txt [waitress-1]
2025-07-30 15:59:35  EarlyBird.common.wechat_pay_v3 62: INFO  商户私钥加载成功 [waitress-1]
2025-07-30 15:59:35  wechat_pay_v3.py 62: INFO  商户私钥加载成功 [waitress-1]
2025-07-30 15:59:36  EarlyBird.api.thesis.service 1149: INFO  微信支付平台查询结果: {'success': True, 'trade_state': 'NOTPAY', 'trade_state_desc': '订单未支付', 'amount': 13.88, 'success_time': None, 'transaction_id': None} [waitress-1]
2025-07-30 15:59:36  service.py 1149: INFO  微信支付平台查询结果: {'success': True, 'trade_state': 'NOTPAY', 'trade_state_desc': '订单未支付', 'amount': 13.88, 'success_time': None, 'transaction_id': None} [waitress-1]
2025-07-30 15:59:38  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-30 15:59:38  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-2]
2025-07-30 15:59:38  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-30T14:33:15.118687', 'user_id': 12, 'username': 'test1002'} [waitress-2]
2025-07-30 15:59:38  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-30T14:33:15.118687', 'user_id': 12, 'username': 'test1002'} [waitress-2]
2025-07-30 15:59:38  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 12 [waitress-2]
2025-07-30 15:59:38  hook_register.py 131: INFO  🔍 提取的user_id: 12 [waitress-2]
2025-07-30 15:59:38  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-30 15:59:38  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-30 15:59:38  EarlyBird.api.thesis.service 1086: INFO  查询支付状态 - 用户: 12, 论文: 42, 订单: thesis_download_7597b6cff81b [waitress-2]
2025-07-30 15:59:38  service.py 1086: INFO  查询支付状态 - 用户: 12, 论文: 42, 订单: thesis_download_7597b6cff81b [waitress-2]
2025-07-30 15:59:38  EarlyBird.api.thesis.service 1130: INFO  找到支付记录 - 订单: thesis_download_7597b6cff81b, 状态: 0 [waitress-2]
2025-07-30 15:59:38  service.py 1130: INFO  找到支付记录 - 订单: thesis_download_7597b6cff81b, 状态: 0 [waitress-2]
2025-07-30 15:59:38  EarlyBird.api.thesis.service 1134: INFO  本地支付状态为未支付，查询微信支付平台状态 - 订单: thesis_download_7597b6cff81b [waitress-2]
2025-07-30 15:59:38  service.py 1134: INFO  本地支付状态为未支付，查询微信支付平台状态 - 订单: thesis_download_7597b6cff81b [waitress-2]
2025-07-30 15:59:38  EarlyBird.config.wechat_pay_config 46: INFO  使用数据库中的微信支付配置 [waitress-2]
2025-07-30 15:59:38  wechat_pay_config.py 46: INFO  使用数据库中的微信支付配置 [waitress-2]
2025-07-30 15:59:38  EarlyBird.common.wechat_pay_v3 54: INFO  尝试加载私钥文件: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird/certs/privateKey.txt [waitress-2]
2025-07-30 15:59:38  wechat_pay_v3.py 54: INFO  尝试加载私钥文件: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird/certs/privateKey.txt [waitress-2]
2025-07-30 15:59:38  EarlyBird.common.wechat_pay_v3 62: INFO  商户私钥加载成功 [waitress-2]
2025-07-30 15:59:38  wechat_pay_v3.py 62: INFO  商户私钥加载成功 [waitress-2]
2025-07-30 15:59:38  EarlyBird.api.thesis.service 1149: INFO  微信支付平台查询结果: {'success': True, 'trade_state': 'NOTPAY', 'trade_state_desc': '订单未支付', 'amount': 13.88, 'success_time': None, 'transaction_id': None} [waitress-2]
2025-07-30 15:59:38  service.py 1149: INFO  微信支付平台查询结果: {'success': True, 'trade_state': 'NOTPAY', 'trade_state_desc': '订单未支付', 'amount': 13.88, 'success_time': None, 'transaction_id': None} [waitress-2]
