/**
 * 作者名：EarlyBird
 * 作者邮箱：<EMAIL>
 * 官方网址：https://blog.zaoniao.vip
 * 
 * 支付相关API接口，包括微信支付配置管理
 */

import request from '@/utils/request'

/**
 * 获取支付商品列表
 * @returns {Promise} 支付商品列表
 */
export function getPaymentProducts() {
  return request({
    url: '/api/pay/products',
    method: 'get'
  })
}

/**
 * 创建支付订单
 * @param {Object} data 订单数据
 * @param {number} data.user_id 用户ID
 * @param {string} data.product_id 商品ID
 * @param {string} data.pay_type 支付方式，仅支持：wxpay
 * @returns {Promise} 支付订单信息
 */
export function createPayment(data) {
  return request({
    url: '/api/pay/create',
    method: 'post',
    data
  })
}

/**
 * 查询支付订单状态
 * @param {Object} data 查询参数
 * @param {string} data.out_trade_no 商户订单号
 * @returns {Promise} 订单状态信息
 */
export function queryPayment(data) {
  return request({
    url: '/api/pay/query',
    method: 'post',
    data
  })
}

/**
 * 获取微信支付配置
 * @returns {Promise} 微信支付配置信息
 */
export function getPaymentConfig() {
  return request({
    url: '/api/admin/wechat_pay_config/current',
    method: 'get'
  })
}

/**
 * 获取微信支付配置（新方法名）
 * @returns {Promise} 微信支付配置信息
 */
export function getWeChatPayConfig() {
  return request({
    url: '/api/admin/wechat_pay_config/current',
    method: 'get',
    retry: 0, // 禁用重试，避免请求冲突
    timeout: 10000, // 增加超时时间
    cancelToken: undefined // 禁用请求取消
  }).catch(error => {
    console.error('获取微信支付配置失败:', error)
    // 更详细的错误处理
    if (error.response) {
      // 服务器返回了错误状态码
      const data = error.response.data
      if (typeof data === 'string' && data.includes('<!doctype html>')) {
        return {
          success: false,
          code: 404,
          message: 'API端点未找到，可能是路由配置问题',
          data: null
        }
      }
    }
    return {
      success: false,
      code: error.response ? error.response.status : 500,
      message: error.message || '获取微信支付配置失败，请稍后重试',
      data: null
    }
  })
}

/**
 * 保存微信支付配置（新方法名）
 * @param {Object} data 微信支付配置数据
 * @returns {Promise} 保存结果
 */
export function saveWeChatPayConfig(data) {
  return request({
    url: '/api/admin/wechat_pay_config/current',
    method: 'post',
    data,
    timeout: 10000,
    retry: 0 // 禁用重试避免冲突
  }).catch(error => {
    console.error('保存微信支付配置失败:', error)
    return {
      success: false,
      code: error.response ? error.response.status : 500,
      message: error.message || '保存微信支付配置失败，请稍后重试',
      data: null
    }
  })
}

/**
 * 更新微信支付配置
 * @param {Object} data 微信支付配置数据
 * @returns {Promise} 保存结果
 */
export function updatePaymentConfig(data) {
  return request({
    url: '/api/admin/wechat_pay_config/update',
    method: 'post',
    data
  })
}

/**
 * 保存微信支付配置（新建或更新，自动判断）
 * @param {Object} data 微信支付配置数据
 * @returns {Promise} 保存结果
 */
export function savePaymentConfig(data) {
  if (data.id) {
    // 更新配置
    return request({
      url: `/api/admin/wechat_pay_config/update/${data.id}`,
      method: 'put',
      data,
      retry: 1,
      retryDelay: 1000
    }).catch(error => {
      return {
        success: false,
        code: error.response ? error.response.status : 500,
        message: error.message || '更新微信支付配置失败，请稍后重试',
        data: null
      }
    })
  } else {
    // 创建新配置
    return request({
      url: '/api/admin/wechat_pay_config/create',
      method: 'post',
      data,
      retry: 1,
      retryDelay: 1000
    }).catch(error => {
      return {
        success: false,
        code: error.response ? error.response.status : 500,
        message: error.message || '创建微信支付配置失败，请稍后重试',
        data: null
      }
    })
  }
}

/**
 * 获取支付商品列表（管理员）
 * @param {Object} params 查询参数
 * @param {number} params.page 页码
 * @param {number} params.limit 每页条数
 * @returns {Promise} 支付商品列表
 */
export function getAdminPaymentProducts(params) {
  return request({
    url: '/api/admin/wechat_pay_config/products', // 路径已修正
    method: 'get',
    params,
    headers: {
      'Cache-Control': 'no-cache',
      'Pragma': 'no-cache'
    },
    retry: 1,
    retryDelay: 1000
  }).catch(error => {
    return {
      success: false,
      code: error.response ? error.response.status : 500,
      message: error.message || '获取支付商品列表失败，请稍后重试',
      data: { list: [], total: 0 }
    }
  })
}

/**
 * 创建支付商品
 * @param {Object} data 商品数据
 * @returns {Promise} 创建结果
 */
export function createPaymentProduct(data) {
  return request({
    url: '/api/admin/wechat_pay_config/products', // 路径已修正
    method: 'post',
    data,
    retry: 1,
    retryDelay: 1000
  }).catch(error => {
    return {
      success: false,
      code: error.response ? error.response.status : 500,
      message: error.message || '创建支付商品失败，请稍后重试',
      data: null
    }
  })
}

/**
 * 更新支付商品
 * @param {number} productId 商品ID
 * @param {Object} data 商品数据
 * @returns {Promise} 更新结果
 */
export function updatePaymentProduct(productId, data) {
  return request({
    url: `/api/admin/wechat_pay_config/products/${productId}`, // 路径已修正
    method: 'put',
    data,
    retry: 1,
    retryDelay: 1000
  }).catch(error => {
    return {
      success: false,
      code: error.response ? error.response.status : 500,
      message: error.message || '更新支付商品失败，请稍后重试',
      data: null
    }
  })
}

/**
 * 删除支付商品
 * @param {number} productId 商品ID
 * @returns {Promise} 删除结果
 */
export function deletePaymentProduct(productId) {
  return request({
    url: `/api/admin/wechat_pay_config/products/${productId}`, // 路径已修正
    method: 'delete',
    retry: 1,
    retryDelay: 1000
  }).catch(error => {
    return {
      success: false,
      code: error.response ? error.response.status : 500,
      message: error.message || '删除支付商品失败，请稍后重试',
      data: null
    }
  })
}

/**
 * 测试支付接口连接
 * @returns {Promise} 测试结果
 */
export function testPaymentConnection() {
  return request({
    url: '/api/admin/wechat_pay_config/test', // 路径已修正
    method: 'post',
    retry: 1,
    retryDelay: 1000
  }).catch(error => {
    return {
      success: false,
      code: error.response ? error.response.status : 500,
      message: error.message || '测试支付接口连接失败，请稍后重试',
      data: null
    }
  })
}

/**
 * 测试微信支付接口连接
 * @param {Object} data 支付配置数据
 * @returns {Promise} 测试结果
 */
export function testWeChatPayConnection(data) {
  return request({
    url: '/api/pay/test',
    method: 'post',
    data,
    retry: 1,
    retryDelay: 1000,
    timeout: 30000 // 增加超时时间到30秒
  }).catch(error => {
    return {
      success: false,
      code: error.response ? error.response.status : 500,
      message: error.message || '测试微信支付接口连接失败，请稍后重试',
      data: null
    }
  })
}

// 默认导出 API 对象，方便组件中使用
const paymentApi = {
  getWeChatPayConfig,
  saveWeChatPayConfig,
  getPaymentConfig,
  updatePaymentConfig,
  savePaymentConfig,
  getPaymentProducts,
  createPayment,
  queryPayment,
  getAdminPaymentProducts,
  createPaymentProduct,
  updatePaymentProduct,
  deletePaymentProduct,
  testPaymentConnection,
  testWeChatPayConnection
}

export default paymentApi 