import json
import logging
from EarlyBird.common import ai

import shortuuid


from .model.model_proxy import ModelProxy
from .model.base import BaseAiModel


LOGGER = logging.getLogger(__name__)


def _noop(id: int = 0, title: str = ""):
    pass

def getModelByName(modelName) -> BaseAiModel:

    return ModelProxy(modelName)


def travelOutlineDict(d: dict, cb: callable = _noop) -> dict:
    """
    遍历提纲字典并处理每个节点
    
    Args:
        d: 提纲字典
        cb: 回调函数，处理每个节点
        
    Returns:
        处理后的提纲字典
    """
    LOGGER.info(f"开始处理提纲数据，类型: {type(d)}")
    
    # 检查输入是否为字典
    if not isinstance(d, dict):
        LOGGER.error(f"提纲数据不是字典类型: {type(d)}")
        # 尝试转换为字典
        try:
            if isinstance(d, str):
                import json
                d = json.loads(d)
                LOGGER.info(f"成功将字符串转换为字典")
            else:
                LOGGER.error(f"无法处理的提纲数据类型: {type(d)}")
                # 创建一个基本结构，避免后续处理错误
                d = {"title": "无标题", "subtitle": []}
        except Exception as e:
            LOGGER.exception(f"转换提纲数据失败: {e}")
            # 创建一个基本结构，避免后续处理错误
            d = {"title": "无标题", "subtitle": []}
    
    # 打印输入数据的键，帮助调试
    LOGGER.info(f"提纲数据键: {d.keys() if isinstance(d, dict) else '非字典类型'}")
    
    # 检查并修复提纲结构
    if isinstance(d, dict) and "subtitle" not in d and "title" in d:
        LOGGER.warning(f"提纲数据缺少subtitle字段，尝试修复结构")
        # 尝试从其他字段找到可能的subtitle内容
        for key in d.keys():
            if isinstance(d[key], list) and len(d[key]) > 0:
                LOGGER.info(f"找到可能的subtitle内容在字段 '{key}'")
                d["subtitle"] = d[key]
                break
        
        # 如果仍然没有找到，创建一个空的subtitle字段
        if "subtitle" not in d:
            LOGGER.warning(f"无法找到subtitle内容，创建空subtitle字段")
            d["subtitle"] = []

    def _travel(inputNode):
        # 检查输入节点是否为字典
        if not isinstance(inputNode, dict):
            LOGGER.error(f"提纲节点不是字典类型: {type(inputNode)}")
            # 创建一个基本节点结构
            return {
                "id": shortuuid.uuid(),
                "title": "格式错误的节点",
                "text": "",
                "subtitle": []
            }

        try:
            # 打印节点的键，帮助调试
            LOGGER.debug(f"处理节点，键: {inputNode.keys()}")
            
            idCurrentNode = shortuuid.uuid()
            if "id" in inputNode:
                idCurrentNode = inputNode["id"]
            
            # 处理title字段缺失的情况
            if "title" not in inputNode:
                LOGGER.warning(f"节点缺少title字段，尝试从其他字段获取")
                # 尝试从name、heading或text字段获取标题
                for field in ["name", "heading", "text"]:
                    if field in inputNode and inputNode[field]:
                        LOGGER.info(f"使用'{field}'字段作为标题")
                        inputNode["title"] = inputNode[field]
                        break
                
                # 如果仍然没有找到标题，使用默认值
                if "title" not in inputNode:
                    LOGGER.warning(f"无法找到标题，使用默认值")
                    inputNode["title"] = "无标题章节"
                
            # 构建基本节点结构
            result = {
                "id": idCurrentNode,
                "title": inputNode.get("title", ""),
                "text": inputNode.get("text", ""),
            }
            
            # 处理子标题
            result["subtitle"] = []
            
            # 检查subtitle字段
            if "subtitle" in inputNode:
                if isinstance(inputNode["subtitle"], list):
                    LOGGER.debug(f"处理subtitle列表，长度: {len(inputNode['subtitle'])}")
                    for subDict in inputNode["subtitle"]:
                        result["subtitle"].append(_travel(subDict))
                else:
                    LOGGER.warning(f"节点的subtitle不是列表类型: {type(inputNode['subtitle'])}")
                    # 尝试转换非列表类型的subtitle
                    if isinstance(inputNode["subtitle"], dict):
                        LOGGER.info(f"将subtitle字典转换为列表")
                        result["subtitle"].append(_travel(inputNode["subtitle"]))
                    elif inputNode["subtitle"] is None:
                        LOGGER.info(f"subtitle为None，使用空列表")
                    else:
                        LOGGER.warning(f"无法处理的subtitle类型: {type(inputNode['subtitle'])}")
            else:
                # 尝试从其他字段找到可能的子标题
                for key in ["children", "subheadings", "sections"]:
                    if key in inputNode and isinstance(inputNode[key], list) and len(inputNode[key]) > 0:
                        LOGGER.info(f"使用'{key}'字段作为subtitle")
                        for subDict in inputNode[key]:
                            result["subtitle"].append(_travel(subDict))
                        break
            
            # 调用回调函数处理节点
            try:
                cb(result)
            except Exception as e:
                LOGGER.exception(f"处理节点时回调函数出错: {e}")
                
            return result
        except Exception as e:
            LOGGER.exception(f"处理提纲节点时发生错误: {e}")
            # 返回一个基本节点，避免整个处理失败
            return {
                "id": shortuuid.uuid(),
                "title": "处理错误的节点",
                "text": "",
                "subtitle": []
            }

    try:
        result = _travel(d)
        LOGGER.info(f"提纲处理完成，根节点ID: {result.get('id')}")
        return result
    except Exception as e:
        LOGGER.exception(f"处理提纲时发生未捕获的错误: {e}")
        # 返回原始数据，避免完全失败
        return d



