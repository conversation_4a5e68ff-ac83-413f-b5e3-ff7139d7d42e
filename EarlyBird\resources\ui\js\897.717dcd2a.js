"use strict";(self["webpackChunkopen_essay_gui"]=self["webpackChunkopen_essay_gui"]||[]).push([[897],{897:(e,t,a)=>{a.r(t),a.d(t,{default:()=>h});var s=function(){var e=this,t=e._self._c;return t("div",{staticClass:"admin-thesis"},[t("el-card",[t("div",{attrs:{slot:"header"},slot:"header"},[t("span",[e._v("论文管理")]),t("el-button",{staticStyle:{float:"right",padding:"3px 0"},attrs:{type:"text"},on:{click:e.refreshData}},[e._v(" 刷新 ")])],1),t("div",{staticClass:"sub-menu"},[t("el-menu",{attrs:{mode:"horizontal","default-active":e.activeMenu},on:{select:e.handleMenuSelect}},[t("el-menu-item",{attrs:{index:"thesis-list"}},[e._v("论文列表")]),t("el-menu-item",{attrs:{index:"payment-orders"}},[e._v("支付订单日志")])],1)],1),"thesis-list"===e.showComponent?t("div",[t("div",{staticClass:"search-bar"},[t("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:e.searchForm}},[t("el-form-item",{attrs:{label:"关键词"}},[t("el-input",{attrs:{placeholder:"搜索标题或作者",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleSearch.apply(null,arguments)}},model:{value:e.searchForm.keyword,callback:function(t){e.$set(e.searchForm,"keyword",t)},expression:"searchForm.keyword"}})],1),t("el-form-item",{attrs:{label:"语言"}},[t("el-select",{attrs:{placeholder:"选择语言",clearable:""},model:{value:e.searchForm.lang,callback:function(t){e.$set(e.searchForm,"lang",t)},expression:"searchForm.lang"}},[t("el-option",{attrs:{label:"中文",value:"zh"}}),t("el-option",{attrs:{label:"英文",value:"en"}})],1)],1),t("el-form-item",{attrs:{label:"级别"}},[t("el-select",{attrs:{placeholder:"选择级别",clearable:""},model:{value:e.searchForm.level,callback:function(t){e.$set(e.searchForm,"level",t)},expression:"searchForm.level"}},[t("el-option",{attrs:{label:"本科",value:"本科"}}),t("el-option",{attrs:{label:"硕士",value:"硕士"}}),t("el-option",{attrs:{label:"博士",value:"博士"}})],1)],1),t("el-form-item",[t("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("搜索")]),t("el-button",{on:{click:e.handleReset}},[e._v("重置")])],1)],1)],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:e.thesisList},on:{"selection-change":e.handleSelectionChange}},[t("el-table-column",{attrs:{type:"selection",width:"55"}}),t("el-table-column",{attrs:{prop:"id",label:"ID",width:"80"}}),t("el-table-column",{attrs:{prop:"title",label:"标题","min-width":"200"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-link",{attrs:{type:"primary"},on:{click:function(t){return e.viewDetail(a.row)}}},[e._v(e._s(a.row.title))])]}}],null,!1,230412999)}),t("el-table-column",{attrs:{prop:"author",label:"作者",width:"120"}}),t("el-table-column",{attrs:{prop:"lang",label:"语言",width:"80"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-tag",{attrs:{type:"zh"===a.row.lang?"success":"warning"}},[e._v(" "+e._s("zh"===a.row.lang?"中文":"英文")+" ")])]}}],null,!1,3523744413)}),t("el-table-column",{attrs:{prop:"level",label:"级别",width:"80"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-tag",{attrs:{type:e.getLevelTagType(a.row.level)}},[e._v(e._s(a.row.level))])]}}],null,!1,3230517379)}),t("el-table-column",{attrs:{prop:"length",label:"字数",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.length||0)+" ")]}}],null,!1,1105256081)}),t("el-table-column",{attrs:{prop:"user.username",label:"用户",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.user?t.row.user.username:"N/A")+" ")]}}],null,!1,521605376)}),t("el-table-column",{attrs:{prop:"create_time",label:"创建时间",width:"160"}}),t("el-table-column",{attrs:{label:"操作",width:"150",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(t){return e.viewDetail(a.row)}}},[e._v("查看")]),t("el-button",{attrs:{size:"mini",type:"danger"},on:{click:function(t){return e.handleDelete(a.row)}}},[e._v("删除")])]}}],null,!1,3404043142)})],1),t("div",{staticClass:"pagination-container"},[t("el-pagination",{attrs:{"current-page":e.pagination.page,"page-sizes":[10,20,50,100],"page-size":e.pagination.size,layout:"total, sizes, prev, pager, next, jumper",total:e.pagination.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1):e._e(),"payment-orders"===e.showComponent?t("div",[t("payment-orders")],1):e._e()]),t("el-dialog",{attrs:{title:"论文详情",visible:e.detailDialogVisible,width:"60%","before-close":e.handleCloseDetail},on:{"update:visible":function(t){e.detailDialogVisible=t}}},[e.currentThesis?t("div",{staticClass:"thesis-detail"},[t("el-descriptions",{attrs:{column:2,border:""}},[t("el-descriptions-item",{attrs:{label:"论文ID"}},[e._v(e._s(e.currentThesis.id))]),t("el-descriptions-item",{attrs:{label:"用户"}},[e._v(e._s(e.currentThesis.user?e.currentThesis.user.username:"N/A"))]),t("el-descriptions-item",{attrs:{label:"标题"}},[e._v(e._s(e.currentThesis.title))]),t("el-descriptions-item",{attrs:{label:"作者"}},[e._v(e._s(e.currentThesis.author))]),t("el-descriptions-item",{attrs:{label:"语言"}},[e._v(e._s("zh"===e.currentThesis.lang?"中文":"英文"))]),t("el-descriptions-item",{attrs:{label:"级别"}},[e._v(e._s(e.currentThesis.level))]),t("el-descriptions-item",{attrs:{label:"字数"}},[e._v(e._s(e.currentThesis.length||0))]),t("el-descriptions-item",{attrs:{label:"创建时间"}},[e._v(e._s(e.currentThesis.create_time))])],1),t("el-divider",{attrs:{"content-position":"left"}},[e._v("摘要")]),t("div",{staticClass:"thesis-content"},[t("p",[e._v(e._s(e.currentThesis.digest||"暂无摘要"))])]),t("el-divider",{attrs:{"content-position":"left"}},[e._v("关键词")]),t("div",{staticClass:"thesis-content"},[t("p",[e._v(e._s(e.currentThesis.keywords||"暂无关键词"))])])],1):e._e()])],1)},l=[],i=a(9192),r=a(5178);const n={name:"AdminThesis",components:{PaymentOrders:r["default"]},data(){return{loading:!1,thesisList:[],searchForm:{keyword:"",lang:"",level:""},pagination:{page:1,size:20,total:0},selectedThesis:[],detailDialogVisible:!1,currentThesis:null,activeMenu:"thesis-list",showComponent:"thesis-list"}},mounted(){this.loadData()},methods:{handleMenuSelect(e){this.activeMenu=e,this.showComponent=e,"thesis-list"===e&&this.loadData()},async loadData(){this.loading=!0;try{const e={page:this.pagination.page,size:this.pagination.size,keyword:this.searchForm.keyword,lang:this.searchForm.lang,level:this.searchForm.level};console.log("请求参数:",e);const t=await i.cF.getList(e);console.log("API响应结果:",t),t.success?(this.thesisList=t.data.list||[],this.pagination.total=t.data.total||0,this.pagination.page=t.data.page||1,this.pagination.size=t.data.size||20):this.$message.error(t.message||"获取论文列表失败")}catch(e){console.error("获取论文列表失败:",e),this.$message.error("获取论文列表失败")}finally{this.loading=!1}},handleSearch(){this.pagination.page=1,this.loadData()},handleReset(){this.searchForm={keyword:"",lang:"",level:""},this.pagination.page=1,this.loadData()},refreshData(){"thesis-list"===this.showComponent&&this.loadData()},handleSizeChange(e){this.pagination.size=e,this.pagination.page=1,this.loadData()},handleCurrentChange(e){this.pagination.page=e,this.loadData()},handleSelectionChange(e){this.selectedThesis=e},async viewDetail(e){try{const t=await i.cF.getDetail(e.id);t.success?(this.currentThesis=t.data,this.detailDialogVisible=!0):this.$message.error(t.message||"获取论文详情失败")}catch(t){console.error("获取论文详情失败:",t),this.$message.error("获取论文详情失败")}},handleDelete(e){this.$confirm(`确定要删除论文"${e.title}"吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{try{const t=await i.cF.delete(e.id);t.success?(this.$message.success("删除成功"),this.loadData()):this.$message.error(t.message||"删除失败")}catch(t){console.error("删除论文失败:",t),this.$message.error("删除论文失败")}})).catch((()=>{}))},getLevelTagType(e){const t={本科:"info",硕士:"success",博士:"warning"};return t[e]||"info"},handleCloseDetail(){this.detailDialogVisible=!1,this.currentThesis=null}}},o=n;var c=a(1656),d=(0,c.A)(o,s,l,!1,null,"37168364",null);const h=d.exports},5178:(e,t,a)=>{a.r(t),a.d(t,{default:()=>d});var s=function(){var e=this,t=e._self._c;return t("div",{staticClass:"payment-orders"},[t("div",{staticClass:"page-header"},[t("h2",[e._v("订单管理")]),t("div",{staticClass:"header-actions"},[t("el-button",{attrs:{type:"primary",size:"small",icon:"el-icon-refresh"},on:{click:e.loadOrders}},[e._v("刷新")])],1)]),t("el-card",{staticClass:"filter-container"},[t("el-form",{attrs:{inline:!0,model:e.queryParams,size:"small"}},[t("el-form-item",{attrs:{label:"订单状态"}},[t("el-select",{attrs:{placeholder:"全部状态",clearable:""},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},[t("el-option",{attrs:{label:"未支付",value:0}}),t("el-option",{attrs:{label:"已支付",value:1}}),t("el-option",{attrs:{label:"已退款",value:2}})],1)],1),t("el-form-item",{attrs:{label:"用户ID"}},[t("el-input",{attrs:{placeholder:"用户ID",clearable:""},model:{value:e.queryParams.user_id,callback:function(t){e.$set(e.queryParams,"user_id",t)},expression:"queryParams.user_id"}})],1),t("el-form-item",{attrs:{label:"论文ID"}},[t("el-input",{attrs:{placeholder:"论文ID",clearable:""},model:{value:e.queryParams.product_id,callback:function(t){e.$set(e.queryParams,"product_id",t)},expression:"queryParams.product_id"}})],1),t("el-form-item",{attrs:{label:"时间范围"}},[t("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"yyyy-MM-dd",align:"right"},model:{value:e.dateRange,callback:function(t){e.dateRange=t},expression:"dateRange"}})],1),t("el-form-item",[t("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.handleSearch}},[e._v("搜索")]),t("el-button",{attrs:{icon:"el-icon-refresh-left"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1)],1),t("el-card",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"table-container"},[t("el-table",{staticStyle:{width:"100%"},attrs:{data:e.orderList,border:""}},[t("el-table-column",{attrs:{prop:"id",label:"ID",width:"80",align:"center"}}),t("el-table-column",{attrs:{prop:"user_id",label:"用户ID",width:"100",align:"center"}}),t("el-table-column",{attrs:{label:"论文信息","min-width":"180"},scopedSlots:e._u([{key:"default",fn:function(a){return[a.row.thesisInfo?t("div",[t("div",{staticClass:"thesis-info"},[t("span",{staticClass:"thesis-id"},[e._v("ID: "+e._s(a.row.thesisInfo.id))]),t("el-link",{staticClass:"thesis-title",attrs:{type:"primary"},on:{click:function(t){return e.viewThesisDetail(a.row.thesisInfo.id)}}},[e._v(" "+e._s(a.row.thesisInfo.title||"未知论文")+" ")])],1)]):a.row.product_id&&a.row.product_id.includes("thesis_download")?t("div",[t("div",{staticClass:"thesis-info pending"},[t("span",{staticClass:"thesis-id"},[e._v("ID: "+e._s(e.extractThesisId(a.row.product_id)))]),t("el-tooltip",{attrs:{content:"论文可能已被删除或无法访问",placement:"top"}},[t("span",{staticClass:"thesis-title-pending"},[e._v("论文信息未找到")])])],1)]):t("div",[t("span",{staticClass:"non-thesis-product"},[e._v(e._s(a.row.product_id||"未知商品"))])])]}}])}),t("el-table-column",{attrs:{prop:"amount",label:"金额",width:"100",align:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.amount.toFixed(2))+" 元 ")]}}])}),t("el-table-column",{attrs:{prop:"out_trade_no",label:"订单号","min-width":"180"}}),t("el-table-column",{attrs:{prop:"status",label:"状态",width:"100",align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-tag",{attrs:{type:e.getStatusType(a.row.status)}},[e._v(" "+e._s(e.getStatusText(a.row.status))+" ")])]}}])}),t("el-table-column",{attrs:{prop:"pay_type",label:"支付方式",width:"100",align:"center"}}),t("el-table-column",{attrs:{prop:"create_time",label:"创建时间",width:"160",align:"center"}}),t("el-table-column",{attrs:{prop:"pay_time",label:"支付时间",width:"160",align:"center"}}),t("el-table-column",{attrs:{label:"操作",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-button",{attrs:{type:"text",size:"small",icon:"el-icon-view"},on:{click:function(t){return e.viewOrderDetail(a.row)}}},[e._v(" 详情 ")])]}}])})],1),t("div",{staticClass:"pagination-container"},[t("el-pagination",{attrs:{"current-page":e.queryParams.page,"page-sizes":[10,20,50,100],"page-size":e.queryParams.size,layout:"total, sizes, prev, pager, next, jumper",total:e.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1)],1)},l=[],i=a(9192);const r={name:"PaymentOrders",data(){return{queryParams:{page:1,size:20,status:"",user_id:"",product_id:"",start_date:"",end_date:""},dateRange:[],orderList:[],thesisCache:{},total:0,loading:!1}},created(){this.loadOrders()},methods:{async loadOrders(){try{this.loading=!0,this.dateRange&&2===this.dateRange.length&&(this.queryParams.start_date=this.dateRange[0],this.queryParams.end_date=this.dateRange[1]);const e=await i.iX.getOrders(this.queryParams);e.success&&e.data?(this.orderList=e.data.list||[],this.total=e.data.total||0,this.loadThesisInfo()):this.$message.error(e.message||"加载订单列表失败")}catch(e){console.error("加载订单列表失败:",e),this.$message.error("加载订单列表失败: "+(e.message||"未知错误"))}finally{this.loading=!1}},async loadThesisInfo(){try{console.log("开始加载论文信息...");const t=this.orderList.filter((e=>e.product_id&&e.product_id.includes("thesis_download"))).map((e=>{const t=e.product_id.match(/thesis_download_?(\d+)?/),a=t&&t[1]||null;return console.log(`从订单 ${e.id} 的商品ID ${e.product_id} 中提取论文ID: ${a}`),a})).filter((e=>null!==e)),a=[...new Set(t)];console.log(`找到 ${a.length} 个唯一论文ID需要加载: ${a.join(", ")}`);for(const s of a)if(this.thesisCache[s])console.log(`使用缓存中的论文ID ${s} 信息: ${this.thesisCache[s].title}`);else try{console.log(`获取论文ID ${s} 的标题信息...`);const e=await this.$axios.get(`/api/admin/thesis/title/${s}`);e.data&&e.data.success&&e.data.data?(this.thesisCache[s]=e.data.data,console.log(`成功加载论文ID ${s} 的标题: ${e.data.data.title}`)):(console.warn(`获取论文ID ${s} 标题失败: ${e.data?e.data.message:"未知错误"}`),this.thesisCache[s]={id:s,title:`论文${s}`},console.log(`使用默认标题: 论文${s}`))}catch(e){console.error(`获取论文ID ${s} 标题失败:`,e),this.thesisCache[s]={id:s,title:`论文${s}`},console.log(`请求失败，使用默认标题: 论文${s}`)}this.orderList=this.orderList.map((e=>{if(e.product_id&&e.product_id.includes("thesis_download")){const t=e.product_id.match(/thesis_download_?(\d+)?/),a=t&&t[1]||null;if(a&&this.thesisCache[a])return console.log(`关联论文ID ${a} 到订单 ${e.id}`),{...e,thesisInfo:this.thesisCache[a]};console.log(`订单 ${e.id} 的论文ID ${a} 未找到对应信息`)}return e}))}catch(e){console.error("加载论文信息失败:",e)}},viewThesisDetail(e){e&&this.$router.push({name:"AdminThesisList",query:{id:e}})},extractThesisId(e){const t=e.match(/thesis_download_?(\d+)?/);return t&&t[1]||"未知"},getStatusType(e){switch(e){case 0:return"warning";case 1:return"success";case 2:return"info";default:return"info"}},getStatusText(e){switch(e){case 0:return"未支付";case 1:return"已支付";case 2:return"已退款";default:return"未知"}},viewOrderDetail(e){this.$router.push({name:"AdminPaymentOrderDetail",params:{id:e.id}})},handleSearch(){this.queryParams.page=1,this.loadOrders()},resetQuery(){this.queryParams={page:1,size:20,status:"",user_id:"",product_id:"",start_date:"",end_date:""},this.dateRange=[],this.loadOrders()},handleSizeChange(e){this.queryParams.size=e,this.loadOrders()},handleCurrentChange(e){this.queryParams.page=e,this.loadOrders()},goBack(){this.$router.push({name:"AdminThesis"})},goToStats(){this.$message.info("统计分析功能即将上线")}}},n=r;var o=a(1656),c=(0,o.A)(n,s,l,!1,null,"6f310e36",null);const d=c.exports}}]);