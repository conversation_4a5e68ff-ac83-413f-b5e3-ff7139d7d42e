<template>
  <div class="admin-thesis">
    <el-card>
      <div slot="header">
        <span>论文管理</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="refreshData">
          刷新
        </el-button>
      </div>
      
      <!-- 子菜单 -->
      <div class="sub-menu">
        <el-menu mode="horizontal" :default-active="activeMenu" @select="handleMenuSelect">
          <el-menu-item index="thesis-list">论文列表</el-menu-item>
          <el-menu-item index="payment-orders">支付订单日志</el-menu-item>
        </el-menu>
      </div>
      
      <!-- 论文列表组件 -->
      <div v-if="showComponent === 'thesis-list'">
        <!-- 搜索栏 -->
        <div class="search-bar">
          <el-form :inline="true" :model="searchForm" class="demo-form-inline">
            <el-form-item label="关键词">
              <el-input
                v-model="searchForm.keyword"
                placeholder="搜索标题或作者"
                clearable
                @keyup.enter.native="handleSearch"
              ></el-input>
            </el-form-item>
            <el-form-item label="语言">
              <el-select v-model="searchForm.lang" placeholder="选择语言" clearable>
                <el-option label="中文" value="zh"></el-option>
                <el-option label="英文" value="en"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="级别">
              <el-select v-model="searchForm.level" placeholder="选择级别" clearable>
                <el-option label="本科" value="本科"></el-option>
                <el-option label="硕士" value="硕士"></el-option>
                <el-option label="博士" value="博士"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">搜索</el-button>
              <el-button @click="handleReset">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        
        <!-- 论文列表 -->
        <el-table
          v-loading="loading"
          :data="thesisList"
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column prop="id" label="ID" width="80"></el-table-column>
          <el-table-column prop="title" label="标题" min-width="200">
            <template slot-scope="scope">
              <el-link type="primary" @click="viewDetail(scope.row)">{{ scope.row.title }}</el-link>
            </template>
          </el-table-column>
          <el-table-column prop="author" label="作者" width="120"></el-table-column>
          <el-table-column prop="lang" label="语言" width="80">
            <template slot-scope="scope">
              <el-tag :type="scope.row.lang === 'zh' ? 'success' : 'warning'">
                {{ scope.row.lang === 'zh' ? '中文' : '英文' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="level" label="级别" width="80">
            <template slot-scope="scope">
              <el-tag :type="getLevelTagType(scope.row.level)">{{ scope.row.level }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="length" label="字数" width="100">
            <template slot-scope="scope">
              {{ scope.row.length || 0 }}
            </template>
          </el-table-column>
          <el-table-column prop="user.username" label="用户" width="120">
            <template slot-scope="scope">
              {{ scope.row.user ? scope.row.user.username : 'N/A' }}
            </template>
          </el-table-column>
          <el-table-column prop="create_time" label="创建时间" width="160"></el-table-column>
          <el-table-column label="操作" width="150" fixed="right">
            <template slot-scope="scope">
              <el-button size="mini" type="primary" @click="viewDetail(scope.row)">查看</el-button>
              <el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pagination.page"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pagination.size"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total"
          >
          </el-pagination>
        </div>
      </div>
      
      <!-- 支付订单日志组件 -->
      <div v-if="showComponent === 'payment-orders'">
        <payment-orders></payment-orders>
      </div>
    </el-card>
    
    <!-- 论文详情对话框 -->
    <el-dialog
      title="论文详情"
      :visible.sync="detailDialogVisible"
      width="60%"
      :before-close="handleCloseDetail"
    >
      <div v-if="currentThesis" class="thesis-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="论文ID">{{ currentThesis.id }}</el-descriptions-item>
          <el-descriptions-item label="用户">{{ currentThesis.user ? currentThesis.user.username : 'N/A' }}</el-descriptions-item>
          <el-descriptions-item label="标题">{{ currentThesis.title }}</el-descriptions-item>
          <el-descriptions-item label="作者">{{ currentThesis.author }}</el-descriptions-item>
          <el-descriptions-item label="语言">{{ currentThesis.lang === 'zh' ? '中文' : '英文' }}</el-descriptions-item>
          <el-descriptions-item label="级别">{{ currentThesis.level }}</el-descriptions-item>
          <el-descriptions-item label="字数">{{ currentThesis.length || 0 }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ currentThesis.create_time }}</el-descriptions-item>
        </el-descriptions>
        
        <el-divider content-position="left">摘要</el-divider>
        <div class="thesis-content">
          <p>{{ currentThesis.digest || '暂无摘要' }}</p>
        </div>
        
        <el-divider content-position="left">关键词</el-divider>
        <div class="thesis-content">
          <p>{{ currentThesis.keywords || '暂无关键词' }}</p>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { adminThesis } from '@/api/admin'
import PaymentOrders from '@/views/admin/settings/PaymentOrders.vue'

export default {
  name: 'AdminThesis',
  components: {
    PaymentOrders
  },
  data() {
    return {
      loading: false,
      thesisList: [],
      searchForm: {
        keyword: '',
        lang: '',
        level: ''
      },
      pagination: {
        page: 1,
        size: 20,
        total: 0
      },
      selectedThesis: [],
      detailDialogVisible: false,
      currentThesis: null,
      activeMenu: 'thesis-list',
      showComponent: 'thesis-list'
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    // 处理菜单选择
    handleMenuSelect(key) {
      this.activeMenu = key
      this.showComponent = key
      
      if (key === 'thesis-list') {
        this.loadData()
      }
    },
    // 加载数据
    async loadData() {
      this.loading = true
      try {
        const params = {
          page: this.pagination.page,
          size: this.pagination.size,
          keyword: this.searchForm.keyword,
          lang: this.searchForm.lang,
          level: this.searchForm.level
        }
        
        console.log('请求参数:', params)
        const response = await adminThesis.getList(params)
        console.log('API响应结果:', response)
        
        if (response.success) {
          this.thesisList = response.data.list || []
          this.pagination.total = response.data.total || 0
          this.pagination.page = response.data.page || 1
          this.pagination.size = response.data.size || 20
        } else {
          this.$message.error(response.message || '获取论文列表失败')
        }
      } catch (error) {
        console.error('获取论文列表失败:', error)
        this.$message.error('获取论文列表失败')
      } finally {
        this.loading = false
      }
    },
    
    // 搜索
    handleSearch() {
      this.pagination.page = 1
      this.loadData()
    },
    
    // 重置搜索
    handleReset() {
      this.searchForm = {
        keyword: '',
        lang: '',
        level: ''
      }
      this.pagination.page = 1
      this.loadData()
    },
    
    // 刷新数据
    refreshData() {
      if (this.showComponent === 'thesis-list') {
        this.loadData()
      }
    },
    
    // 分页大小改变
    handleSizeChange(size) {
      this.pagination.size = size
      this.pagination.page = 1
      this.loadData()
    },
    
    // 当前页改变
    handleCurrentChange(page) {
      this.pagination.page = page
      this.loadData()
    },
    
    // 选择改变
    handleSelectionChange(selection) {
      this.selectedThesis = selection
    },
    
    // 查看详情
    async viewDetail(thesis) {
      try {
        const response = await adminThesis.getDetail(thesis.id)
        if (response.success) {
          this.currentThesis = response.data
          this.detailDialogVisible = true
        } else {
          this.$message.error(response.message || '获取论文详情失败')
        }
      } catch (error) {
        console.error('获取论文详情失败:', error)
        this.$message.error('获取论文详情失败')
      }
    },
    
    // 删除论文
    handleDelete(thesis) {
      this.$confirm(`确定要删除论文"${thesis.title}"吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const response = await adminThesis.delete(thesis.id)
          if (response.success) {
            this.$message.success('删除成功')
            this.loadData()
          } else {
            this.$message.error(response.message || '删除失败')
          }
        } catch (error) {
          console.error('删除论文失败:', error)
          this.$message.error('删除论文失败')
        }
      }).catch(() => {
        // 取消删除
      })
    },
    
    // 获取级别标签类型
    getLevelTagType(level) {
      const typeMap = {
        '本科': 'info',
        '硕士': 'success',
        '博士': 'warning'
      }
      return typeMap[level] || 'info'
    },
    
    // 关闭详情对话框
    handleCloseDetail() {
      this.detailDialogVisible = false
      this.currentThesis = null
    }
  }
}
</script>

<style lang="scss" scoped>
.admin-thesis {
  .search-bar {
    margin-bottom: 20px;
    padding: 20px;
    background-color: #f5f7fa;
    border-radius: 4px;
  }
  
  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }

  .sub-menu {
    margin-bottom: 20px;
    padding: 10px 20px;
    background-color: #f5f7fa;
    border-radius: 4px;
  }
  
  .thesis-detail {
    .thesis-content {
      padding: 10px;
      background-color: #f5f7fa;
      border-radius: 4px;
      margin: 10px 0;
      
      p {
        margin: 0;
        line-height: 1.6;
        white-space: pre-wrap;
      }
    }
  }
}
</style> 