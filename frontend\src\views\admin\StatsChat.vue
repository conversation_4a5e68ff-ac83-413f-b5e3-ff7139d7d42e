<template>
  <div class="admin-stats-chat">
    <el-card>
      <div slot="header" class="clearfix">
        <span>聊天统计</span>
        <div class="header-right">
          <el-radio-group v-model="period" size="small" @change="fetchChatStats">
            <el-radio-button label="7d">最近7天</el-radio-button>
            <el-radio-button label="30d">最近30天</el-radio-button>
            <el-radio-button label="90d">最近90天</el-radio-button>
          </el-radio-group>
          <el-button style="margin-left: 10px;" size="small" type="primary" icon="el-icon-refresh" @click="fetchChatStats">
            刷新
          </el-button>
        </div>
      </div>
      
      <div v-loading="loading">
        <!-- 数据卡片 -->
        <div class="stat-cards">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-card shadow="hover" class="stat-card">
                <div class="stat-card-title">总聊天记录数</div>
                <div class="stat-card-value">{{ statsData.total_chat_logs || 0 }}</div>
              </el-card>
            </el-col>
            <el-col :span="12">
              <el-card shadow="hover" class="stat-card">
                <div class="stat-card-title">新增聊天记录数</div>
                <div class="stat-card-value">{{ statsData.new_chat_logs || 0 }}</div>
              </el-card>
            </el-col>
          </el-row>
        </div>

        <!-- 图表区域 -->
        <div class="chart-container">
          <el-row :gutter="20">
            <!-- 每日聊天记录趋势图 -->
            <el-col :span="24">
              <el-card shadow="hover" class="chart-card">
                <div slot="header" class="clearfix">
                  <span>每日聊天记录趋势</span>
                </div>
                <div class="chart" id="daily-chat-chart" style="height: 300px;"></div>
              </el-card>
            </el-col>
          </el-row>

          <el-row :gutter="20" style="margin-top: 20px;">
            <!-- 模型使用分布 -->
            <el-col :span="12">
              <el-card shadow="hover" class="chart-card">
                <div slot="header" class="clearfix">
                  <span>模型使用分布</span>
                </div>
                <div class="chart" id="model-chart" style="height: 300px;"></div>
              </el-card>
            </el-col>
            
            <!-- 聊天类型分布 -->
            <el-col :span="12">
              <el-card shadow="hover" class="chart-card">
                <div slot="header" class="clearfix">
                  <span>聊天类型分布</span>
                </div>
                <div class="chart" id="type-chart" style="height: 300px;"></div>
              </el-card>
            </el-col>
          </el-row>
          
          <!-- 数据表格 -->
          <el-row :gutter="20" style="margin-top: 20px;">
            <el-col :span="24">
              <el-card shadow="hover">
                <div slot="header" class="clearfix">
                  <span>聊天数据表格</span>
                </div>
                <el-table :data="tableData" style="width: 100%">
                  <el-table-column prop="date" label="日期" width="180"></el-table-column>
                  <el-table-column prop="count" label="聊天记录数"></el-table-column>
                </el-table>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { adminStats } from '@/api/admin'
import * as echarts from 'echarts'

export default {
  name: 'AdminStatsChat',
  data() {
    return {
      loading: false,
      period: '7d',
      statsData: {},
      tableData: [],
      charts: {
        dailyChatChart: null,
        modelChart: null,
        typeChart: null
      }
    }
  },
  mounted() {
    this.fetchChatStats()
    // 窗口大小变化时重绘图表
    window.addEventListener('resize', this.resizeCharts)
  },
  beforeDestroy() {
    // 移除事件监听
    window.removeEventListener('resize', this.resizeCharts)
    // 销毁图表实例
    Object.keys(this.charts).forEach(key => {
      if (this.charts[key]) {
        this.charts[key].dispose()
      }
    })
  },
  methods: {
    async fetchChatStats() {
      this.loading = true
      try {
        const response = await adminStats.getChatStats({ period: this.period })
        if (response.success) {
          this.statsData = response.data
          this.tableData = this.statsData.daily_stats || []
          this.$nextTick(() => {
            this.initCharts()
          })
        } else {
          this.$message.error(response.message || '获取聊天统计数据失败')
        }
      } catch (error) {
        this.$message.error('获取聊天统计数据失败：' + error.message)
      } finally {
        this.loading = false
      }
    },
    initCharts() {
      this.initDailyChatChart()
      this.initModelChart()
      this.initTypeChart()
    },
    initDailyChatChart() {
      const chartDom = document.getElementById('daily-chat-chart')
      if (!chartDom) return
      
      if (this.charts.dailyChatChart) {
        this.charts.dailyChatChart.dispose()
      }
      this.charts.dailyChatChart = echarts.init(chartDom)
      
      const dailyStats = this.statsData.daily_stats || []
      const dates = dailyStats.map(item => item.date)
      const counts = dailyStats.map(item => item.count)
      
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: [{
          type: 'category',
          data: dates,
          axisLabel: {
            rotate: 45
          }
        }],
        yAxis: [{
          type: 'value'
        }],
        series: [{
          name: '聊天记录数',
          data: counts,
          type: 'line',
          smooth: true,
          symbolSize: 8,
          itemStyle: {
            color: '#F56C6C'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [{
                offset: 0, color: 'rgba(245, 108, 108, 0.5)'
              }, {
                offset: 1, color: 'rgba(245, 108, 108, 0.1)'
              }]
            }
          }
        }]
      }
      
      this.charts.dailyChatChart.setOption(option)
    },
    initModelChart() {
      const chartDom = document.getElementById('model-chart')
      if (!chartDom) return
      
      if (this.charts.modelChart) {
        this.charts.modelChart.dispose()
      }
      this.charts.modelChart = echarts.init(chartDom)
      
      const modelStats = this.statsData.model_stats || []
      const data = modelStats.map(item => {
        return {
          name: item.model || '未知',
          value: item.count
        }
      })
      
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          right: 10,
          top: 'center',
          data: data.map(item => item.name)
        },
        series: [
          {
            name: '模型使用',
            type: 'pie',
            radius: '55%',
            center: ['40%', '50%'],
            data: data,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }
      
      this.charts.modelChart.setOption(option)
    },
    initTypeChart() {
      const chartDom = document.getElementById('type-chart')
      if (!chartDom) return
      
      if (this.charts.typeChart) {
        this.charts.typeChart.dispose()
      }
      this.charts.typeChart = echarts.init(chartDom)
      
      const typeStats = this.statsData.type_stats || []
      const data = typeStats.map(item => {
        let name = item.type || '未知'
        if (name === 'chat') name = '普通聊天'
        if (name === 'title') name = '标题生成'
        if (name === 'outline') name = '提纲生成'
        if (name === 'content') name = '内容生成'
        return {
          name: name,
          value: item.count
        }
      })
      
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          right: 10,
          top: 'center',
          data: data.map(item => item.name)
        },
        series: [
          {
            name: '聊天类型',
            type: 'pie',
            radius: '55%',
            center: ['40%', '50%'],
            data: data,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }
      
      this.charts.typeChart.setOption(option)
    },
    resizeCharts() {
      Object.keys(this.charts).forEach(key => {
        if (this.charts[key]) {
          this.charts[key].resize()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.admin-stats-chat {
  .header-right {
    float: right;
  }
  
  .stat-cards {
    margin-bottom: 20px;
    
    .stat-card {
      text-align: center;
      
      .stat-card-title {
        font-size: 14px;
        color: #606266;
      }
      
      .stat-card-value {
        font-size: 24px;
        font-weight: bold;
        margin-top: 10px;
        color: #303133;
      }
    }
  }
  
  .chart-container {
    margin-top: 20px;
    
    .chart-card {
      margin-bottom: 20px;
    }
  }
}
</style> 