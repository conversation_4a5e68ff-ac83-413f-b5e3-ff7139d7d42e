from pydantic import BaseModel, ValidationError, constr, Field
from typing import Optional, Dict, List
from EarlyBird.common import ai

# BaseModel是flask pydantic包提供的一个基础类，ParamOutline是这个BaseModel的继承类
# 继承Pydantic的好处在于 Pydantic可以很容易的处理  数据验证，序列化，数据格式转换如dictionary-json等操作。

"""
google靠不住
qianwen太慢
gpt收费贵

需要找到一个收费不太贵，速度还可以的。
"""
_defaultModel = ai.MODEL_QIANWEN

_defaultModel = ai.MODEL_GPT35

# _defaultModel = common.MODEL_GEMINI10


class _BaseFields(BaseModel):
    isVipUser: Optional[bool] = True
    userId: Optional[int] = 9527


class ParamGetChatLog(_BaseFields):
    pageNo: Optional[int] = 1
    pageSize: Optional[int] = 20


class ParamDelChat(_BaseFields):
    chatId: Optional[int] = -1

class ParamChat(_BaseFields):
    userMessage: Optional[str] = ""
    modelName: Optional[str] = ""


# 生成标题
class ParamTitle(_BaseFields):
    domain: str
    level: Optional[str] = Field(default="本科")
    lang: Optional[str] = Field(default="中文")
    topic: Optional[str] = Field(default="")
    keyword: Optional[str] = Field(default="")
    keyword1: Optional[str] = Field(default="")
    size: Optional[int] = Field(default=4)
    model: Optional[str] = ai.MODEL_KIMI


# 生成提纲
class ParamOutline(_BaseFields):
    title: str
    # 这是一种来自Pydantic的定义属性attribute的方法
    length: Optional[int] = Field(default="800")
    level: Optional[str] = Field(default="本科")
    lang: Optional[str] = Field(default="中文")
    paragraphCount: Optional[int] = Field(default=6)
    secondParagraphCount: Optional[int] = Field(default=3)
    author: Optional[str] = Field(default="", description="作者身份说明")
    style: Optional[str] = Field(default="", description="成文风格")
    model: Optional[str] = ai.MODEL_GEMINI10


# 不连ai，选择一个提纲
class ParamSelect4Content(_BaseFields):
    title: str
    # 这是一种来自Pydantic的定义属性attribute的方法
    length: Optional[str] = Field(default="800")
    level: Optional[str] = Field(default="本科")
    lang: Optional[str] = Field(default="中文")
    outline: Optional[dict] = Field(default_factory=dict)  # 使用default_factory避免可变默认值问题
    model: Optional[str] = _defaultModel
    # 添加更多可选字段
    thesisId: Optional[int] = None
    
    class Config:
        # 允许额外的字段
        extra = "allow"
        # 允许从ORM模型创建
        orm_mode = True
        # 允许字段别名
        allow_population_by_field_name = True
        # 允许任意类型转换
        arbitrary_types_allowed = True
        
    # 添加验证方法
    def validate_outline(cls, v):
        """确保outline是字典类型"""
        if v is None:
            return {}
        if isinstance(v, dict):
            return v
        if isinstance(v, str):
            try:
                import json
                return json.loads(v)
            except:
                return {"title": cls.title, "subtitle": []}
        return {"title": cls.title, "subtitle": []}


# 全部生成
class ParamGetContentFromOutline(_BaseFields):
    title: str
    # 这是一种来自Pydantic的定义属性attribute的方法
    length: Optional[str] = Field(default="800")
    level: Optional[str] = Field(default="本科")
    lang: Optional[str] = Field(default="中文")
    outline: dict
    thesisId: Optional[int]
    model: Optional[str] = ai.MODEL_GPT35


class ParamThesisId(_BaseFields):
    thesisId: int
    model: Optional[str] = ai.MODEL_GPT35


# 生成单独一段
class ParamGenerateSingleParagraph(_BaseFields):
    id: int = -1
    thesisId: int
    paragraphId: str
    title: Optional[str] = Field(default="标题")
    lang: Optional[str] = Field(default="中文")
    paragraph: Optional[str] = Field(default="")
    paragraphTitle: Optional[str] = Field(default="")
    length: Optional[int] = Field(default=500)
    lengthMode: Optional[str] = Field(default="fixed", description="字数控制模式：fixed-固定字数, range-字数范围, auto-智能调整")
    minLength: Optional[int] = Field(default=100, description="最小字数（范围模式使用）")
    maxLength: Optional[int] = Field(default=3000, description="最大字数（范围模式使用）")
    contentStyle: Optional[str] = Field(default="academic", description="内容风格：academic-学术严谨, popular-通俗易懂, concise-简洁明了, detailed-详细深入, case_rich-案例丰富")
    instruction: Optional[str] = Field(default="")
    context: Optional[dict] = Field(default=None, description="论文上下文信息，包含已生成段落和整体结构")
    model: Optional[str] = ai.MODEL_GEMINI10


# 生成摘要
class ParamRegenDigest(_BaseFields):
    thesisId: int
    title: Optional[str]
    lang: Optional[str]
    outline: dict
    model: Optional[str] = ai.MODEL_GEMINI10


# 查询用户选题历史
class ParamGetTitleHistory(_BaseFields):
    pageNo: Optional[int] = Field(default=1)
    pageSize: Optional[int] = Field(default=10)

# 查询用户提纲历史
class ParamGetOutlineHistory(_BaseFields):
    pageNo: Optional[int] = Field(default=1)
    pageSize: Optional[int] = Field(default=10)

# 批量生成所有段落
class ParamGenerateAll(_BaseFields):
    thesisId: int
    globalSettings: Optional[dict] = Field(default=None, description="全局设置，包含字数控制等")
    model: Optional[str] = ai.MODEL_GEMINI10
