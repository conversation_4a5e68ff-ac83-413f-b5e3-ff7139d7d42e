#!/bin/bash

echo "================================================"
echo "🚀 早鸟论文系统 - CentOS部署脚本"
echo "================================================"
echo

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [ "$EUID" -ne 0 ]; then
        log_error "请使用root用户运行此脚本"
        exit 1
    fi
}

# 检查CentOS版本
check_centos() {
    if [ ! -f /etc/centos-release ]; then
        log_error "此脚本仅支持CentOS系统"
        exit 1
    fi
    
    CENTOS_VERSION=$(cat /etc/centos-release | grep -oE '[0-9]+' | head -1)
    log_info "检测到CentOS版本: $CENTOS_VERSION"
}

# 更新系统
update_system() {
    log_info "更新系统包..."
    yum update -y
    yum install -y epel-release
}

# 安装Python 3.8+
install_python() {
    log_info "检查Python版本..."
    
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
        log_info "当前Python版本: $PYTHON_VERSION"
        
        # 检查版本是否满足要求
        if python3 -c 'import sys; exit(0 if sys.version_info >= (3, 8) else 1)'; then
            log_info "Python版本满足要求"
            return 0
        fi
    fi
    
    log_info "安装Python 3.8..."
    yum install -y python38 python38-pip python38-devel
    
    # 创建软链接
    ln -sf /usr/bin/python3.8 /usr/bin/python3
    ln -sf /usr/bin/pip3.8 /usr/bin/pip3
}

# 安装系统依赖
install_system_deps() {
    log_info "安装系统依赖..."
    yum install -y \
        gcc \
        gcc-c++ \
        make \
        openssl-devel \
        libffi-devel \
        mysql-devel \
        git \
        wget \
        curl \
        supervisor
}

# 安装Python依赖
install_python_deps() {
    log_info "升级pip..."
    python3 -m pip install --upgrade pip
    
    log_info "安装Python依赖..."
    
    # 检查requirements.txt是否存在
    if [ ! -f "requirements.txt" ]; then
        log_error "未找到requirements.txt文件"
        exit 1
    fi
    
    # 使用清华镜像源安装
    python3 -m pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
    
    if [ $? -ne 0 ]; then
        log_warn "使用清华镜像源安装失败，尝试使用默认源..."
        python3 -m pip install -r requirements.txt
    fi
}

# 配置MySQL数据库
setup_mysql() {
    log_info "配置MySQL数据库..."
    
    # 检查MySQL是否已安装
    if ! command -v mysql &> /dev/null; then
        log_info "安装MySQL..."
        yum install -y mysql-server mysql
        systemctl start mysqld
        systemctl enable mysqld
        
        # 获取临时密码
        TEMP_PASSWORD=$(grep 'temporary password' /var/log/mysqld.log | awk '{print $NF}')
        log_info "MySQL临时密码: $TEMP_PASSWORD"
        log_warn "请手动运行 mysql_secure_installation 来设置MySQL"
    fi
}

# 配置防火墙
setup_firewall() {
    log_info "配置防火墙..."
    
    # 检查firewalld是否运行
    if systemctl is-active --quiet firewalld; then
        firewall-cmd --permanent --add-port=3301/tcp
        firewall-cmd --reload
        log_info "已开放3301端口"
    else
        log_warn "firewalld未运行，请手动配置防火墙"
    fi
}

# 创建systemd服务
create_service() {
    log_info "创建systemd服务..."
    
    PROJECT_DIR=$(pwd)
    
    cat > /etc/systemd/system/earlybird.service << EOF
[Unit]
Description=EarlyBird Paper System
After=network.target mysql.service

[Service]
Type=simple
User=root
WorkingDirectory=$PROJECT_DIR
Environment=FLASK_ENV=production
ExecStart=/usr/bin/python3 $PROJECT_DIR/EarlyBird/web_server.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

    systemctl daemon-reload
    systemctl enable earlybird
    log_info "已创建earlybird服务"
}

# 测试部署
test_deployment() {
    log_info "测试部署..."
    
    # 测试Python导入
    python3 -c "
import sys
sys.path.insert(0, '.')
try:
    from EarlyBird.common.ai import getAdapter
    print('✅ AI模块导入成功')
except Exception as e:
    print(f'⚠️  AI模块导入警告: {e}')

try:
    from EarlyBird.api.app import create_app
    print('✅ Flask应用模块导入成功')
except Exception as e:
    print(f'❌ Flask应用模块导入失败: {e}')
    sys.exit(1)
"
    
    if [ $? -eq 0 ]; then
        log_info "部署测试通过"
    else
        log_error "部署测试失败"
        exit 1
    fi
}

# 主函数
main() {
    log_info "开始CentOS部署..."
    
    check_root
    check_centos
    update_system
    install_python
    install_system_deps
    install_python_deps
    setup_mysql
    setup_firewall
    create_service
    test_deployment
    
    echo
    log_info "🎉 部署完成！"
    echo
    echo "下一步操作："
    echo "1. 配置数据库连接: 编辑 EarlyBird/config/production.ini"
    echo "2. 配置AI API密钥: 编辑 EarlyBird/config/api_keys.ini"
    echo "3. 启动服务: systemctl start earlybird"
    echo "4. 查看状态: systemctl status earlybird"
    echo "5. 查看日志: journalctl -u earlybird -f"
    echo
    echo "访问地址: http://your-server-ip:3301"
}

# 运行主函数
main "$@"
