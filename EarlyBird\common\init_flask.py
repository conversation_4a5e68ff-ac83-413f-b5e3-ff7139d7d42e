import os
from datetime import datetime, timedelta
import logging
import platform
import threading
import psutil
import math
from prettytable import PrettyTable

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s  %(filename)s %(lineno)s: %(levelname)s  %(message)s %(threadName)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)

LOGGER = logging.getLogger(__name__)

def printEnvInfo():
    flask_env = os.environ.get("FLASK_ENV")
    pyVersion = platform.python_version()
    table = PrettyTable(["PyVersion", pyVersion])
    table.align["PyVersion"] = "r"
    table.align[pyVersion] = "l"

    uname = platform.uname()
    libInfo = _try_get_info()
    LOGGER.info(libInfo)
    table.add_row(["DateTime", datetime.now()])
    table.add_row(["Os", uname.system + " " + uname.version + " " + uname.node])
    table.add_row(["CpuCount", libInfo["CpuCount"]])
    table.add_row(["Mem", libInfo["Mem"]])
    table.add_row(["FlaskEnv", flask_env])
    table.add_row(["os.CurPwd", os.getcwd()])
    table.add_row(["Pid", os.getpid()])
    table.add_row(["ThreadId", threading.get_ident()])
    print(table)

def _try_get_info():
    info = {"CpuCount": "-", "Mem": "-"}
    try:
        info["CpuCount"] = psutil.cpu_count(logical=False)
        info["Mem"] = f"{math.ceil(psutil.virtual_memory().total/1024/1024/1024)} GB"
    except Exception as e:
        LOGGER.exception(e)
    return info

def init_app(app, config):
    """初始化Flask应用"""
    # 设置会话cookie名称
    app.config['SESSION_COOKIE_NAME'] = 'earlybird_paper_session'
    
    # 设置会话过期时间为7天
    app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(days=7)
    
    # 设置会话安全
    app.config['SESSION_TYPE'] = 'filesystem'
    app.config['SESSION_USE_SIGNER'] = True
    app.config['SESSION_KEY_PREFIX'] = 'earlybird_paper:'
