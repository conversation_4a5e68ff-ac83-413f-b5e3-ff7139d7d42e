import os
import platform
import logging
import hashlib
import uuid

logger = logging.getLogger(__name__)

def is_windows():
    """检测当前系统是否为Windows"""
    return platform.system().lower() == 'windows'

def is_linux():
    """检测当前系统是否为Linux"""
    return platform.system().lower() == 'linux'

def get_machine_id():
    """
    获取机器唯一标识，兼容Windows和Linux
    Windows: 使用原RkSDK方法生成机器码
    Linux: 使用/etc/machine-id或硬件信息生成唯一ID
    """
    if is_windows():
        # 在Windows环境下，我们会使用原始RkSDK获取机器码
        try:
            # 注意：将导入RkSDK移到函数内部的try块中以避免循环导入
            from .RkSDK import RkSDK
            return RkSDK()._RkSDK__GetMacCode()
        except Exception as e:
            logger.error(f"无法使用RkSDK获取Windows机器码: {str(e)}")
            # 如果RkSDK方法失败，使用备用方法
            return _get_backup_machine_id()
    else:
        # Linux环境下，使用多种方法获取机器唯一标识
        return _get_linux_machine_id()

def _get_linux_machine_id():
    """Linux环境获取机器ID的实现"""
    machine_id = ""
    
    # 方法1: 尝试读取/etc/machine-id (大多数现代Linux发行版)
    try:
        if os.path.exists('/etc/machine-id'):
            with open('/etc/machine-id', 'r') as f:
                machine_id = f.read().strip()
                if machine_id:
                    logger.info("使用/etc/machine-id作为机器标识")
                    return machine_id
    except Exception as e:
        logger.warning(f"无法读取/etc/machine-id: {str(e)}")
    
    # 方法2: 尝试使用DMI系统信息
    try:
        if os.path.exists('/sys/class/dmi/id/product_uuid'):
            with open('/sys/class/dmi/id/product_uuid', 'r') as f:
                machine_id = f.read().strip()
                if machine_id:
                    logger.info("使用DMI product_uuid作为机器标识")
                    return machine_id
    except Exception as e:
        logger.warning(f"无法读取DMI产品UUID: {str(e)}")
    
    # 方法3: 使用主机名+CPU信息+MAC地址的组合
    try:
        hostname = platform.node()
        cpu_info = ""
        mac_address = ""
        
        # 获取CPU信息
        if os.path.exists('/proc/cpuinfo'):
            with open('/proc/cpuinfo', 'r') as f:
                for line in f:
                    if line.startswith('processor') or line.startswith('model name') or line.startswith('physical id'):
                        cpu_info += line.strip()
        
        # 获取MAC地址
        try:
            import netifaces
            for interface in netifaces.interfaces():
                try:
                    mac = netifaces.ifaddresses(interface)[netifaces.AF_LINK][0]['addr']
                    if mac and mac != '00:00:00:00:00:00':
                        mac_address = mac
                        break
                except:
                    pass
        except ImportError:
            logger.warning("netifaces库未安装，无法获取MAC地址")
            
        # 如果我们有足够的信息，创建一个唯一ID
        combined_info = f"{hostname}:{cpu_info}:{mac_address}"
        if combined_info.strip(":"):
            machine_id = hashlib.md5(combined_info.encode()).hexdigest()
            logger.info("使用系统信息组合作为机器标识")
            return machine_id
    except Exception as e:
        logger.warning(f"无法获取系统信息: {str(e)}")
    
    # 后备方案
    return _get_backup_machine_id()

def _get_backup_machine_id():
    """获取备用机器ID的方法（适用于所有无法获取主要标识的情况）"""
    logger.warning("无法获取可靠的机器ID，使用随机生成的ID")
    backup_id_file = os.path.join(os.path.expanduser("~"), ".EarlyBird_machine_id")
    try:
        if os.path.exists(backup_id_file):
            with open(backup_id_file, 'r') as f:
                machine_id = f.read().strip()
                if machine_id:
                    return machine_id
        
        # 如果文件不存在或为空，创建新ID
        machine_id = str(uuid.uuid4())
        with open(backup_id_file, 'w') as f:
            f.write(machine_id)
        return machine_id
    except Exception as e:
        logger.error(f"无法保存随机生成的ID: {str(e)}")
        # 最后的后备选项
        return str(uuid.uuid4()) 