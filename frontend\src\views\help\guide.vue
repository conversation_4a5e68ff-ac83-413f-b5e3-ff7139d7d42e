<!--  -->
<template>
  <div class="guide-container">
    <!-- 顶部警告提示 -->
    <div class="disclaimer-banner">
      <div class="disclaimer-content">
        <div class="disclaimer-header">
          <i class="el-icon-warning-outline"></i>
          <span>免责声明</span>
        </div>
        <div class="disclaimer-text">
          <p>早鸟论文是一款论文写作辅助工具，托管地址：</p>
          <a href="https://gitee.com/zaoniao/open-paper-gui" target="_blank" class="repo-link">
            <i class="el-icon-link"></i>
            https://papaer.zaoniao.vip/
          </a>
          <ul>
            <li>本软件仅作为论文写作的辅助工具，提供选题建议、提纲规划和内容创作等参考性功能。</li>
            <li>用户应当遵守学术规范，对使用本软件生成的内容进行二次加工和修改，确保符合学术要求。</li>
            <li>本软件不支持任何形式的学术不端行为，用户的具体使用行为及其产生的后果由用户自行承担。</li>
            <li>本软件不对生成内容的准确性、时效性和完整性做出保证，用户应当自行验证和完善。</li>
            <li>本软件中使用的AI接口需要用户自行申请和管理，相关费用和安全由用户自行负责。</li>
          </ul>
          <p class="highlight">使用本软件，即表示您已充分理解并同意以上声明。</p>
          <div class="contact-info">
            <i class="el-icon-chat-dot-round"></i>
            用户交流QQ群：544391109
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="guide-content">
      <div v-for="(section, index) in paras" :key="index" class="guide-section" :class="{ 'fade-in': true }" :style="{ animationDelay: index * 0.1 + 's' }">
        <div class="section-header">
          <div class="section-number">{{ index + 1 }}</div>
          <h2>{{ section.title }}</h2>
        </div>
        <div class="section-body">
          <p v-for="(text, textIndex) in section.text" 
             :key="textIndex" 
             class="section-text"
             v-html="text">
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Guide',
  data() {
    return {
      paras: [
        {
          title: "感谢安装和使用",
          text: [
            "感谢您选择使用早鸟论文！我们致力于为您提供高效、便捷的论文写作辅助工具。您的支持是我们不断进步的动力！"
          ]
        },
        {
          title: "运行环境要求",
          text: [
            "早鸟论文采用现代Web技术开发，为确保最佳使用体验，我们建议：",
            "<span class='success-text'>✓ 推荐使用 Chrome、Edge、Firefox 等主流浏览器的最新版本</span>",
            "<span class='success-text'>✓ 建议使用分辨率1920×1080及以上的显示器，以获得最佳显示效果</span>",
            "<span class='warning-text'>⚠ 如果使用Safari浏览器，请确保版本在15.0以上</span>",
            "<span class='error-text'>✗ 不建议使用IE浏览器，可能会出现显示异常</span>",
            "<span class='info-text'>ℹ 如遇到任何使用问题，欢迎加入QQ群咨询反馈</span>"
          ]
        },
        {
          title: "关于AI模型接口",
          text: [
            "早鸟论文软件本身是<span class='success-text'>完全免费</span>的，我们致力于为用户提供优质的论文写作辅助工具。",
            "由于AI模型调用需要支付服务商费用，为了维持软件的正常运营，我们采用了卡密激活的方式：",
            "<div class='api-notice'>⚡ 重要说明：</div>",
            "<ul class='feature-list'>",
            "  <li>软件本体永久免费，无需付费下载</li>",
            "  <li>卡密仅用于激活AI模型使用权限</li>",
            "  <li>激活后可以无限使用，直到卡密到期</li>",
            "  <li>支持多种主流AI模型，如千问、DeepSeek、文心一言等</li>",
            "  <li>我们会定期优化接口性能，确保稳定服务</li>",
            "</ul>",
            "<span class='info-text'>ℹ 首次使用时，请在\"系统设置\"中完成卡密激活，即可开始使用全部功能。</span>"
          ]
        }
      ]
    }
  }
}
</script>

<style lang="scss" scoped>
.guide-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
  color: #2c3e50;

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}

.disclaimer-banner {
  background: linear-gradient(135deg, #f5f7fa 0%, #e4e7eb 100%);
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 32px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  animation: fadeIn 0.6s ease-out;

  .disclaimer-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
    
    i {
      font-size: 24px;
      color: #409EFF;
    }
    
    span {
      font-size: 20px;
      font-weight: 600;
      color: #2c3e50;
    }
  }

  .disclaimer-text {
    color: #4a5568;
    font-size: 14px;
    line-height: 1.6;

    .repo-link {
      display: inline-flex;
      align-items: center;
      gap: 8px;
      color: #4a5568;
      text-decoration: none;
      background: rgba(255, 255, 255, 0.5);
      padding: 8px 16px;
      border-radius: 6px;
      margin: 12px 0;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.8);
        transform: translateX(5px);
        color: #409EFF;
      }
    }

    ul {
      list-style: none;
      padding: 0;
      margin: 16px 0;

      li {
        position: relative;
        padding-left: 24px;
        margin-bottom: 8px;

        &:before {
          content: "•";
          position: absolute;
          left: 8px;
          color: #409EFF;
        }
      }
    }

    .highlight {
      font-weight: 600;
      color: #409EFF;
      margin: 16px 0;
    }

    .contact-info {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-top: 16px;
      padding: 8px 16px;
      background: rgba(255, 255, 255, 0.5);
      border-radius: 6px;
      width: fit-content;

      i {
        color: #409EFF;
      }
    }
  }
}

.guide-content {
  .guide-section {
    background: white;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
    opacity: 0;
    animation: fadeIn 0.6s ease-out forwards;

    .section-header {
      display: flex;
      align-items: center;
      gap: 16px;
      margin-bottom: 20px;

      .section-number {
        width: 32px;
        height: 32px;
        background: #409EFF;
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 16px;
      }

      h2 {
        margin: 0;
        font-size: 18px;
        color: #2c3e50;
        font-weight: 600;
      }
    }

    .section-body {
      color: #34495e;
      font-size: 14px;
      line-height: 1.8;

      .section-text {
        margin-bottom: 16px;
        
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

// 文本样式
.success-text {
  color: #00b894;
  font-weight: 500;
}

.warning-text {
  color: #fdcb6e;
  font-weight: 500;
}

.error-text {
  color: #d63031;
  font-weight: 500;
}

.info-text {
  color: #0984e3;
  font-weight: 500;
}

.api-notice {
  background: #fff3cd;
  border-left: 4px solid #ffc107;
  padding: 12px 16px;
  margin: 16px 0;
  border-radius: 4px;
  color: #856404;
}

.feature-list {
  list-style: none;
  padding: 0;
  margin: 16px 0;

  li {
    position: relative;
    padding: 8px 0 8px 28px;
    color: #2c3e50;
    
    &:before {
      content: "✦";
      position: absolute;
      left: 8px;
      color: #409EFF;
      font-size: 14px;
    }

    &:hover {
      background: rgba(64, 158, 255, 0.05);
      border-radius: 6px;
      transform: translateX(5px);
      transition: all 0.3s ease;
    }
  }
}

.highlight-link {
  color: #409EFF;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;

  &:hover {
    color: #66b1ff;
    text-decoration: underline;
  }
}
</style>
