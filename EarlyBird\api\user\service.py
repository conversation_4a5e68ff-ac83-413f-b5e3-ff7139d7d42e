from flask import session, make_response, current_app
from EarlyBird.ExtendRegister.db_register import db
from EarlyBird.ExtendRegister.model_register import User, ApiLog
from EarlyBird.api.user.entity import VipLogListModel, ApiLogListModel
from EarlyBird.common import Result, PageNav
import logging
import math
import hashlib
from datetime import datetime, timedelta

LOGGER = logging.getLogger(__name__)


class UserService:

    def get_user_info(self, uid: int) -> Result:
        try:
            # 从数据库查询用户
            user = User.query.get(uid)
            
            # 如果找不到用户，返回错误
            if user is None:
                LOGGER.warning(f"用户ID {uid} 不存在")
                return Result().error("用户不存在")
            
            return Result().setData(user.get_info())
        except Exception as e:
            LOGGER.exception(e)
            return Result().error(str(e))
    
    def login(self, username: str, password: str) -> Result:
        """
        用户登录验证
        
        Args:
            username: 用户名
            password: 密码
            
        Returns:
            Result: 登录结果，成功时data为用户对象
        """
        try:
            # 参数验证
            if not username or not password:
                return Result().error("用户名和密码不能为空")
            
            # 查询用户
            user = User.query.filter_by(username=username).first()
            
            # 用户不存在
            if not user:
                LOGGER.warning(f"登录失败: 用户名 {username} 不存在")
                return Result().error("用户名或密码错误")
            
            # 账号被锁定
            if user.is_lock:
                LOGGER.warning(f"登录失败: 用户 {username} 已被锁定")
                return Result().error("账号已被锁定，请联系管理员")
            
            # 验证密码
            if not self._verify_password(user, password):
                LOGGER.warning(f"登录失败: 用户 {username} 密码错误")
                return Result().error("用户名或密码错误")
            
            # 更新登录信息
            user.last_login_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            # 获取客户端IP
            try:
                from flask import request
                user.last_login_ip = request.remote_addr
            except:
                user.last_login_ip = "unknown"
            
            # 保存到数据库
            try:
                db.session.commit()
            except Exception as e:
                LOGGER.error(f"更新用户登录信息失败: {str(e)}")
                db.session.rollback()
            
            LOGGER.info(f"用户 {username}(ID={user.id}) 登录成功")
            return Result().setData(user)
            
        except Exception as e:
            LOGGER.exception(f"登录过程发生异常: {str(e)}")
            return Result().error(f"登录失败: {str(e)}")
    
    def _verify_password(self, user: User, password: str) -> bool:
        """
        验证用户密码
        
        Args:
            user: 用户对象
            password: 待验证的密码
            
        Returns:
            bool: 密码是否正确
        """
        try:
            # 如果用户没有设置密码，使用默认验证逻辑
            if not user.password_hash:
                # 临时用户使用用户名作为密码
                if user.username == password:
                    LOGGER.info(f"临时用户 {user.username} 使用用户名登录成功")
                    return True
                    
                return False
            
            # 使用User模型的check_password方法进行密码验证
            # 这会使用werkzeug.security的check_password_hash
            return user.check_password(password)
            
        except Exception as e:
            LOGGER.exception(f"密码验证过程发生异常: {str(e)}")
            return False

    def register(self, username: str, password: str, nickname: str = None, email: str = None) -> Result:
        """
        用户注册
        
        Args:
            username: 用户名
            password: 密码
            nickname: 昵称（可选）
            email: 电子邮箱（可选）
            
        Returns:
            Result: 注册结果，成功时data为用户对象
        """
        try:
            # 参数验证
            if not username or not password:
                return Result().error("用户名和密码不能为空")
            
            # 检查用户名是否已存在
            existing_user = User.query.filter_by(username=username).first()
            if existing_user:
                LOGGER.warning(f"注册失败: 用户名 {username} 已存在")
                return Result().error("用户名已存在")
            
            # 创建新用户
            user = User()
            user.username = username
            
            # 设置密码（使用User模型的password setter，会自动加密）
            user.password = password
            
            # 设置昵称
            user.nickname = nickname if nickname else f"用户_{username}"
            
            # 设置电子邮箱
            if email:
                user.email = email
            
            # 设置注册时间
            user.create_time = datetime.now()
            
            # 保存到数据库
            try:
                db.session.add(user)
                db.session.commit()
                LOGGER.info(f"用户 {username}(ID={user.id}) 注册成功（普通用户）")
            except Exception as e:
                db.session.rollback()
                LOGGER.error(f"保存用户到数据库失败: {str(e)}")
                return Result().error(f"注册失败: {str(e)}")
            
            return Result().setData(user)
            
        except Exception as e:
            LOGGER.exception(f"注册过程发生异常: {str(e)}")
            return Result().error(f"注册失败: {str(e)}")

