<!--
作者名：EarlyBird
作者邮箱：<EMAIL>
官方网址：https://blog.zaoniao.vip

支付配置管理页面
-->
<template>
  <div class="payment-settings">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <i class="el-icon-wallet header-icon"></i>
          <h1 class="page-title">支付配置管理</h1>
        </div>
        <div class="header-right">
          <el-button 
            type="primary" 
            icon="el-icon-refresh" 
            @click="loadConfig"
            :loading="loading"
            size="small">
            刷新配置
          </el-button>
        </div>
      </div>
    </div>

    <!-- 配置表单 -->
    <div class="config-container">
      <el-card class="config-card" shadow="hover">
        <div slot="header" class="card-header">
          <div class="header-title">
            <i class="el-icon-key"></i>
            <span>微信支付配置</span>
          </div>
          <el-switch
            v-model="formData.is_enabled"
            active-text="启用"
            inactive-text="禁用"
            @change="handleEnableChange">
          </el-switch>
        </div>

        <el-form 
          ref="configForm" 
          :model="formData" 
          :rules="formRules" 
          label-width="140px"
          v-loading="loading">
          
          <!-- 基础配置 -->
          <div class="config-section">
            <h3 class="section-title">
              <i class="el-icon-setting"></i>
              微信支付基础配置
            </h3>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="配置名称" prop="name">
                  <el-input 
                    v-model="formData.name" 
                    placeholder="请输入配置名称，如：早鸟论文微信支付"
                    prefix-icon="el-icon-edit">
                  </el-input>
                  <div class="form-item-tip">用于标识这个支付配置的名称</div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="公众号APPID" prop="app_id">
                  <el-input 
                    v-model="formData.app_id" 
                    placeholder="例如：wxfb9c26d95fcd8b21"
                    prefix-icon="el-icon-mobile">
                  </el-input>
                  <div class="form-item-tip">微信公众号的应用ID</div>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="商户号" prop="mch_id">
                  <el-input 
                    v-model="formData.mch_id" 
                    placeholder="例如：**********"
                    prefix-icon="el-icon-bank-card">
                  </el-input>
                  <div class="form-item-tip">微信支付商户号，纯数字</div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="商户证书序列号" prop="serial_no">
                  <el-input 
                    v-model="formData.serial_no" 
                    placeholder="例如：56D0EF3D9FFBAF91F2E8C477C732449D503A2290"
                    prefix-icon="el-icon-document">
                  </el-input>
                  <div class="form-item-tip">商户API证书的序列号，大写字母+数字</div>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="用户私钥路径" prop="private_key_path">
                  <el-input 
                    v-model="formData.private_key_path" 
                    placeholder="例如：/static/cert/privateKey.txt"
                    prefix-icon="el-icon-folder">
                  </el-input>
                  <div class="form-item-tip">商户私钥文件的服务器路径</div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="API V3密钥" prop="api_v3_key">
                  <el-input 
                    v-model="formData.api_v3_key" 
                    type="password" 
                    placeholder="例如：yx159357QWERasdfZXCVtgbYHNujmIKO"
                    prefix-icon="el-icon-key"
                    show-password>
                  </el-input>
                  <div class="form-item-tip">微信支付API v3密钥，32位字符</div>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="异步通知URL" prop="notify_url">
                  <el-input 
                    v-model="formData.notify_url" 
                    placeholder="请输入支付结果通知地址，例如：https://blog.zaoniao.vip/api/pay/notify"
                    prefix-icon="el-icon-link">
                  </el-input>
                  <div class="form-item-tip">微信支付完成后的异步通知回调地址，必须是HTTPS</div>
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 模式配置 -->
          <div class="config-section">
            <h3 class="section-title">
              <i class="el-icon-cpu"></i>
              模式配置
            </h3>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="测试模式">
                  <el-switch
                    v-model="formData.is_test_mode"
                    active-text="启用"
                    inactive-text="禁用">
                  </el-switch>
                  <div class="form-item-tip">开启后将使用测试环境</div>
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="备注信息">
              <el-input 
                v-model="formData.remark" 
                type="textarea" 
                :rows="3"
                placeholder="请输入配置备注信息"
                maxlength="500"
                show-word-limit>
              </el-input>
            </el-form-item>
          </div>

          <!-- 操作按钮 -->
          <div class="form-actions">
            <el-button 
              type="primary" 
              @click="saveConfig" 
              :loading="saving"
              icon="el-icon-check">
              保存配置
            </el-button>
            <el-button 
              @click="resetForm"
              icon="el-icon-refresh-left">
              重置表单
            </el-button>
            <el-button 
              type="success" 
              @click="testConnection"
              :loading="testing"
              icon="el-icon-connection">
              测试连接
            </el-button>
          </div>
        </el-form>
      </el-card>
    </div>

    <!-- 状态提示 -->
    <div class="status-info">
      <el-alert
        v-if="lastSaveTime"
        :title="`最后保存时间：${lastSaveTime}`"
        type="info"
        :closable="false"
        show-icon>
      </el-alert>
    </div>
  </div>
</template>

<script>
import paymentApi from '@/api/payment'

export default {
  name: 'PaymentSettings',
  data() {
    return {
      // 表单数据 - 使用真实的微信支付配置示例
      formData: {
        name: '早鸟论文微信支付',
        app_id: 'wxfb9c26d95fcd8b21',
        mch_id: '**********',
        api_key: '', // 保持为空，需要用户输入
        api_v3_key: 'yx159357QWERasdfZXCVtgbYHNujmIKO',
        serial_no: '56D0EF3D9FFBAF91F2E8C477C732449D503A2290',
        private_key_path: '/static/cert/privateKey.txt',
        notify_url: 'https://blog.zaoniao.vip/api/pay/notify',
        is_enabled: true,
        is_test_mode: false,
        remark: '早鸟论文系统微信支付配置，支持论文生成服务付费'
      },
      
      // 表单验证规则 - 符合微信支付官方要求
      formRules: {
        name: [
          { required: true, message: '请输入配置名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        app_id: [
          { required: true, message: '请输入公众号APPID', trigger: 'blur' },
          { pattern: /^wx[a-zA-Z0-9]{16}$/, message: 'APPID格式不正确，应以wx开头共18位', trigger: 'blur' }
        ],
        mch_id: [
          { required: true, message: '请输入商户号', trigger: 'blur' },
          { pattern: /^\d{8,10}$/, message: '商户号应为8-10位数字', trigger: 'blur' }
        ],
        api_v3_key: [
          { required: true, message: '请输入API V3密钥', trigger: 'blur' },
          { min: 32, max: 32, message: 'API V3密钥长度必须为32位', trigger: 'blur' },
          { pattern: /^[a-zA-Z0-9]{32}$/, message: 'API V3密钥只能包含字母和数字', trigger: 'blur' }
        ],
        serial_no: [
          { required: true, message: '请输入商户证书序列号', trigger: 'blur' },
          { min: 40, max: 40, message: '证书序列号长度必须为40位', trigger: 'blur' },
          { pattern: /^[A-F0-9]{40}$/, message: '证书序列号格式不正确，应为40位大写字母和数字', trigger: 'blur' }
        ],
        private_key_path: [
          { required: true, message: '请输入用户私钥文件路径', trigger: 'blur' },
          { pattern: /^\/.*\.(txt|pem|key)$/, message: '私钥路径应以/开头，文件扩展名为.txt、.pem或.key', trigger: 'blur' }
        ],
        notify_url: [
          { required: true, message: '请输入异步通知URL', trigger: 'blur' },
          { type: 'url', message: '请输入正确的URL格式', trigger: 'blur' },
          { pattern: /^https:\/\//, message: '通知URL必须使用HTTPS协议', trigger: 'blur' }
        ]
      },
      
      // 状态变量
      loading: false,
      saving: false,
      testing: false,
      lastSaveTime: null
    }
  },
  
  created() {
    // 组件创建时加载配置
    this.loadConfig()
  },
  
  methods: {
    // 加载配置数据
    async loadConfig() {
      this.loading = true
      try {
        const response = await paymentApi.getWeChatPayConfig()
        if (response && response.success) {
          // 合并默认值和服务器返回的数据
          this.formData = { ...this.formData, ...response.data }
          this.$message.success('配置加载成功')
        } else {
          throw new Error(response?.message || '加载配置失败')
        }
      } catch (error) {
        console.error('加载配置失败:', error)
        this.$message.error(`加载配置失败: ${error.message}`)
      } finally {
        this.loading = false
      }
    },
    
    // 保存配置
    async saveConfig() {
      // 表单验证
      try {
        await this.$refs.configForm.validate()
      } catch (error) {
        this.$message.warning('请检查表单填写是否正确')
        return
      }
      
      this.saving = true
      try {
        const response = await paymentApi.saveWeChatPayConfig(this.formData)
        if (response && response.success) {
          this.$message.success('配置保存成功')
          this.lastSaveTime = new Date().toLocaleString()
          // 重新加载配置以确保数据同步
          await this.loadConfig()
        } else {
          throw new Error(response?.message || '保存配置失败')
        }
      } catch (error) {
        console.error('保存配置失败:', error)
        this.$message.error(`保存配置失败: ${error.message}`)
      } finally {
        this.saving = false
      }
    },
    
    // 重置表单
    resetForm() {
      this.$refs.configForm.resetFields()
      this.$message.info('表单已重置')
    },
    
    // 启用状态变化处理
    handleEnableChange(value) {
      if (value) {
        this.$message.success('支付配置已启用')
      } else {
        this.$message.warning('支付配置已禁用')
      }
    },
    
    // 测试连接
    async testConnection() {
      this.testing = true
      try {
        // 这里可以调用测试连接的API
        await new Promise(resolve => setTimeout(resolve, 2000)) // 模拟API调用
        this.$message.success('连接测试成功！支付配置有效')
      } catch (error) {
        console.error('连接测试失败:', error)
        this.$message.error('连接测试失败，请检查配置')
      } finally {
        this.testing = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.payment-settings {
  padding: 20px;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.page-header {
  margin-bottom: 20px;
  
  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    
    .header-left {
      display: flex;
      align-items: center;
      
      .header-icon {
        font-size: 28px;
        color: #409EFF;
        margin-right: 12px;
      }
      
      .page-title {
        margin: 0;
        font-size: 24px;
        font-weight: 600;
        color: #303133;
      }
    }
  }
}

.config-container {
  .config-card {
    border-radius: 8px;
    overflow: hidden;
    
    ::v-deep .el-card__header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border: none;
      
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .header-title {
          display: flex;
          align-items: center;
          font-size: 16px;
          font-weight: 600;
          
          i {
            margin-right: 8px;
            font-size: 18px;
          }
        }
      }
    }
    
    ::v-deep .el-card__body {
      padding: 30px;
    }
  }
}

.config-section {
  margin-bottom: 30px;
  
  .section-title {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #E4E7ED;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    
    i {
      margin-right: 8px;
      color: #409EFF;
    }
  }
}

.form-item-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
  line-height: 1.4;
  padding: 2px 0;
  
  // 为重要提示添加特殊样式
  &.important {
    color: #F56C6C;
    font-weight: 500;
  }
  
  &.success {
    color: #67C23A;
    font-weight: 500;
  }
}

.form-actions {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #E4E7ED;
  
  .el-button {
    margin: 0 10px;
    padding: 12px 24px;
    font-weight: 500;
  }
}

.status-info {
  margin-top: 20px;
}

// 表单样式优化
::v-deep .el-form-item__label {
  font-weight: 500;
  color: #606266;
}

::v-deep .el-input__inner {
  border-radius: 6px;
  transition: all 0.3s ease;
  
  &:focus {
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
  }
}

::v-deep .el-textarea__inner {
  border-radius: 6px;
  transition: all 0.3s ease;
  
  &:focus {
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
  }
}

::v-deep .el-switch {
  .el-switch__core {
    border-radius: 20px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .payment-settings {
    padding: 10px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 15px;
    
    .header-left .page-title {
      font-size: 20px;
    }
  }
  
  .config-container ::v-deep .el-card__body {
    padding: 20px;
  }
  
  .form-actions .el-button {
    margin: 5px;
  }
}
</style> 