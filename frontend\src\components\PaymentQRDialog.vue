<template>
  <el-dialog 
    title="论文下载支付" 
    :visible.sync="visible" 
    width="500px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <div class="payment-qr-dialog" v-loading="loading">
      <!-- 支付信息 -->
      <div class="payment-info">
        <div class="info-item">
          <span class="label">论文标题：</span>
          <span class="value">{{ paymentData.thesisTitle || '未知论文' }}</span>
        </div>
        <div class="info-item">
          <span class="label">支付金额：</span>
          <span class="value price">¥{{ paymentData.price || 0 }}</span>
        </div>
        <div class="info-item">
          <span class="label">订单编号：</span>
          <span class="value">{{ paymentData.orderId || '生成中...' }}</span>
        </div>
      </div>

      <!-- 支付方式选择 -->
      <div class="payment-methods" v-if="!qrCodeUrl">
        <h4>请选择支付方式：</h4>
        <div class="method-list">
          <div 
            v-for="method in availableMethods" 
            :key="method.key"
            class="method-item"
            :class="{ active: selectedMethod === method.key }"
            @click="selectMethod(method.key)"
          >
            <i :class="method.icon"></i>
            <span>{{ method.name }}</span>
          </div>
        </div>
        <div class="method-actions">
          <el-button type="primary" @click="createPayment" :loading="creating">
            确认支付
          </el-button>
          <el-button @click="handleClose">取消</el-button>
        </div>
      </div>

      <!-- 支付二维码 -->
      <div class="payment-qrcode" v-if="qrCodeUrl">
        <div class="qrcode-header">
          <h4>{{ getMethodName(selectedMethod) }}扫码支付</h4>
          <p>请使用{{ getMethodName(selectedMethod) }}扫描下方二维码完成支付</p>
        </div>
        
        <div class="qrcode-container">
          <img :src="qrCodeUrl" alt="支付二维码" class="qrcode-image">
        </div>
        
        <div class="qrcode-tips">
          <p><i class="el-icon-info"></i> 支付完成后，系统将自动跳转到下载页面</p>
          <p><i class="el-icon-time"></i> 二维码有效期为5分钟，请及时支付</p>
        </div>
        
        <div class="qrcode-actions">
          <el-button @click="checkPaymentStatus" :loading="checking">
            <i class="el-icon-refresh"></i>
            检查支付状态
          </el-button>
          <el-button @click="openPayUrl" v-if="payUrl">
            <i class="el-icon-link"></i>
            打开支付页面
          </el-button>
          <el-button @click="handleClose">
            取消支付
          </el-button>
        </div>
      </div>

      <!-- 支付状态 -->
      <div class="payment-status" v-if="paymentStatus">
        <div class="status-item" :class="paymentStatus">
          <i :class="getStatusIcon(paymentStatus)"></i>
          <span>{{ getStatusText(paymentStatus) }}</span>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { createPayment, queryPayment } from '@/api/payment'

export default {
  name: 'PaymentQRDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    paymentData: {
      type: Object,
      default: () => ({
        thesisId: null,
        thesisTitle: '',
        price: 0,
        userId: null
      })
    }
  },
  data() {
    return {
      loading: false,
      creating: false,
      checking: false,
      selectedMethod: 'wxpay',
      qrCodeUrl: '',
      payUrl: '',
      orderId: '',
      paymentStatus: '',
      checkTimer: null,
      availableMethods: [
        { key: 'wxpay', name: '微信支付', icon: 'el-icon-chat-dot-round' }
      ]
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.initPayment()
      } else {
        this.resetPayment()
      }
    }
  },
  methods: {
    // 初始化支付
    initPayment() {
      this.resetPayment()
      this.selectedMethod = 'wxpay' // 默认选择微信支付
    },

    // 重置支付状态
    resetPayment() {
      this.loading = false
      this.creating = false
      this.checking = false
      this.qrCodeUrl = ''
      this.payUrl = ''
      this.orderId = ''
      this.paymentStatus = ''
      
      // 清除定时器
      if (this.checkTimer) {
        clearInterval(this.checkTimer)
        this.checkTimer = null
      }
    },

    // 选择支付方式
    selectMethod(method) {
      this.selectedMethod = method
    },

    // 创建支付订单
    async createPayment() {
      try {
        this.loading = true
        
        const orderData = {
          user_id: this.paymentData.userId,
          product_id: `thesis_download_${this.paymentData.thesisId}`,
          pay_type: 'wxpay'  // 只支持微信支付
        }
        
        const result = await createPayment(orderData)
        
        if (result.success) {
          this.orderId = result.data.out_trade_no || result.data.order_id
          this.qrCodeUrl = result.data.qr_code_url || result.data.qr_url
          this.payUrl = result.data.pay_url || result.data.redirect_url
          
          this.$message.success('支付订单创建成功')
          
          // 开始定时检查支付状态
          this.startPaymentCheck()
        } else {
          this.$message.error(result.message || '创建支付订单失败')
        }
      } catch (error) {
        this.$message.error('创建支付订单失败: ' + (error.message || '未知错误'))
      } finally {
        this.loading = false
      }
    },

    // 开始定时检查支付状态
    startPaymentCheck() {
      // 每3秒检查一次支付状态
      this.checkTimer = setInterval(() => {
        this.checkPaymentStatus()
      }, 3000)
    },

    // 检查支付状态
    async checkPaymentStatus() {
      if (!this.orderId) return

      this.checking = true
      try {
        const result = await queryPayment({
          out_trade_no: this.orderId
        })

        if (result.code === 0 || result.success) {
          const orderInfo = result.data || result
          const status = orderInfo.status

          if (status === 1 || status === 'paid') {
            // 支付成功
            this.paymentStatus = 'success'
            this.$message.success('支付成功！正在跳转到下载页面...')
            
            // 清除定时器
            if (this.checkTimer) {
              clearInterval(this.checkTimer)
              this.checkTimer = null
            }
            
            // 延迟跳转，让用户看到成功提示
            setTimeout(() => {
              this.handlePaymentSuccess()
            }, 1500)
          } else if (status === 2 || status === 'failed') {
            // 支付失败
            this.paymentStatus = 'failed'
            this.$message.error('支付失败，请重试')
          } else {
            // 支付中
            this.paymentStatus = 'pending'
          }
        } else {
          console.error('查询支付状态失败:', result)
        }
      } catch (error) {
        console.error('检查支付状态失败:', error)
      } finally {
        this.checking = false
      }
    },

    // 打开支付页面
    openPayUrl() {
      if (this.payUrl) {
        window.open(this.payUrl, '_blank')
      }
    },

    // 支付成功处理
    handlePaymentSuccess() {
      this.$emit('payment-success', {
        orderId: this.orderId,
        thesisId: this.paymentData.thesisId,
        amount: this.paymentData.price
      })
      this.handleClose()
    },

    // 关闭弹框
    handleClose() {
      this.resetPayment()
      this.$emit('update:visible', false)
      this.$emit('close')
    },

    // 获取支付方式名称
    getMethodName(method) {
      const methodMap = {
        wxpay: '微信'
      }
      return methodMap[method] || method
    },

    // 获取状态图标
    getStatusIcon(status) {
      const iconMap = {
        success: 'el-icon-success',
        failed: 'el-icon-error',
        pending: 'el-icon-loading'
      }
      return iconMap[status] || 'el-icon-info'
    },

    // 获取状态文本
    getStatusText(status) {
      const textMap = {
        success: '支付成功',
        failed: '支付失败',
        pending: '支付中...'
      }
      return textMap[status] || '未知状态'
    }
  },
  
  beforeDestroy() {
    // 组件销毁时清除定时器
    if (this.checkTimer) {
      clearInterval(this.checkTimer)
    }
  }
}
</script>

<style lang="scss" scoped>
.payment-qr-dialog {
  .payment-info {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 20px;

    .info-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        font-weight: 500;
        color: #666;
      }

      .value {
        color: #333;
        
        &.price {
          color: #e6a23c;
          font-weight: bold;
          font-size: 16px;
        }
      }
    }
  }

  .payment-methods {
    h4 {
      margin-bottom: 15px;
      color: #333;
    }

    .method-list {
      display: flex;
      gap: 10px;
      margin-bottom: 20px;

      .method-item {
        flex: 1;
        padding: 15px;
        border: 2px solid #e4e7ed;
        border-radius: 6px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
          border-color: #409eff;
        }

        &.active {
          border-color: #409eff;
          background: #ecf5ff;
        }

        i {
          font-size: 24px;
          color: #409eff;
          margin-bottom: 8px;
          display: block;
        }

        span {
          font-size: 14px;
          color: #333;
        }
      }
    }

    .method-actions {
      text-align: center;
    }
  }

  .payment-qrcode {
    text-align: center;

    .qrcode-header {
      margin-bottom: 20px;

      h4 {
        color: #333;
        margin-bottom: 8px;
      }

      p {
        color: #666;
        font-size: 14px;
      }
    }

    .qrcode-container {
      margin: 20px 0;
      display: inline-block;
      padding: 20px;
      background: #fff;
      border: 1px solid #e4e7ed;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .qrcode-image {
        width: 200px;
        height: 200px;
        display: block;
      }
    }

    .qrcode-tips {
      margin: 20px 0;
      text-align: left;

      p {
        margin: 8px 0;
        color: #666;
        font-size: 13px;

        i {
          margin-right: 5px;
          color: #409eff;
        }
      }
    }

    .qrcode-actions {
      margin-top: 20px;

      .el-button {
        margin: 0 5px;
      }
    }
  }

  .payment-status {
    margin-top: 20px;
    text-align: center;

    .status-item {
      display: inline-flex;
      align-items: center;
      padding: 10px 20px;
      border-radius: 6px;
      font-weight: 500;

      &.success {
        background: #f0f9ff;
        color: #67c23a;
        border: 1px solid #c2e7b0;
      }

      &.failed {
        background: #fef0f0;
        color: #f56c6c;
        border: 1px solid #fbc4c4;
      }

      &.pending {
        background: #fdf6ec;
        color: #e6a23c;
        border: 1px solid #f5dab1;
      }

      i {
        margin-right: 8px;
        font-size: 16px;
      }
    }
  }
}
</style> 