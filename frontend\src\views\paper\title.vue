<template>
  <!-- 生成论文标题的VUE子模块，这个模块定义了调用服务器的方法和路由，具体在/api/generate中进行了定义 -->
  <div class="flex">
    <div class="home-main grid-content bg-purple-light">
      <!-- VIP用户提示信息 -->
      <div class="vip-notice" v-if="isLoggedIn">
        <div class="notice-content">
          <div class="notice-icon">
            <i :class="isVip ? 'el-icon-star-on' : 'el-icon-star-off'"></i>
          </div>
          <div class="notice-text">
            <div class="notice-title">
              {{ isVip ? 'VIP会员' : '免费用户' }}
            </div>
            <div class="notice-desc">
              {{ isVip ? '您当前是VIP会员，一次最多可生成5个论文标题' : '您当前是免费用户，一次最多可生成2个论文标题' }}
            </div>
            <div class="notice-usage" v-if="isVip">
              <span class="usage-text">本次可生成：5个标题</span>
              <el-progress 
                :percentage="100" 
                :stroke-width="6"
                :show-text="false"
                class="usage-progress"
              ></el-progress>
            </div>
            <div class="notice-usage" v-else>
              <span class="usage-text">本次可生成：2个标题</span>
              <el-progress 
                :percentage="100" 
                :stroke-width="6"
                :show-text="false"
                class="usage-progress"
              ></el-progress>
            </div>
          </div>
          <div class="notice-action" v-if="!isVip">
            <el-button type="warning" size="small" @click="showUpgradeInfo">
              升级VIP
            </el-button>
          </div>
        </div>
      </div>

      <el-form class="form-box" inline ref="form" :model="form" label-width="80px">
        <el-form-item :label="$t('titlePage.major')" required>
          <el-input
            v-model="form.domain"
            :placeholder="$t('titlePage.placeholderMajor')"
            clearable
            style="width: 400px"
          >
            <template slot="append">
              <el-button @click.prevent="openMajorSelector" style="color: #409eff">{{
                $t("titlePage.btnSelectMajor")
              }}</el-button>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item :label="$t('titlePage.topic')" required>
          <el-input
            v-model="form.topic"
            :placeholder="$t('titlePage.placeholderTopic')"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item :label="$t('titlePage.lang')" required>
          <el-radio-group v-model="form.lang" size="small">
            <el-radio-button
              v-for="lang in $t('languageList')"
              :key="lang"
              :label="lang"
              >{{ lang }}</el-radio-button
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t('titlePage.level')" required>
          <el-radio-group v-model="form.level" size="small">
            <el-radio-button :label="$t('education.中学')"></el-radio-button>
            <el-radio-button :label="$t('education.大专')"></el-radio-button>
            <el-radio-button :label="$t('education.本科')"></el-radio-button>
            <el-radio-button :label="$t('education.硕士')"></el-radio-button>
            <el-radio-button :label="$t('education.博士')"></el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t('titlePage.keyword')">
          <el-input
            v-model="form.keyword"
            width="200px"
            :placeholder="$t('titlePage.relativeKeyword')"
          ></el-input>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            class="xiaolong-btn"
            @click="handleSubmit"
            size="medium"
            >{{ $t("titlePage.btnSubmit") }}</el-button
          >
        </el-form-item>
      </el-form>

      <div class="result-title" v-if="titles && titles.length > 0">
        <span v-if="showHistory" class="history-notice">
          <i class="el-icon-time"></i>
          正在显示您的历史选题记录，您可以重新生成新的选题
        </span>
        <span v-else>
          {{ $t("titlePage.tipSelectThis") }}
        </span>
      </div>
      <div class="result-box" v-if="titles && titles.length > 0">
        <div class="title" v-for="(t, index) in titles" :key="index">
          <span
            >{{ $t("titlePage.title") }} {{ index + 1 }}&nbsp;:&nbsp;{{ t.kk }}
            <el-button type="primary" @click.prevent="selectTitle(t)" size="small">
              {{ $t("titlePage.btnSelectTitle") }}
            </el-button>
          </span>
          <span>{{ t.tt }}</span>
          <div class="kw">
            <el-tag type="info" v-for="kw in t.kws" :key="kw" size="medium">{{
              kw
            }}</el-tag>
          </div>
        </div>
      </div>

      <Placeholder v-if="!titles || titles.length == 0"></Placeholder>
    </div>
    <div class="home-sidebar grid-content bg-purple">
      <PageLeftBox />
    </div>
    <el-dialog
      :title="$t('titlePage.btnSelectMajor')"
      :visible.sync="majorSelectorVisible"
      width="70%"
      top="30px"
    >
      <div class="major-seletor">
        <div class="major" v-for="(k, v) in majorList" :key="k">
          <div class="major-title">{{ v }}</div>
          <div class="major-list">
            <span v-for="i in k" :key="i" @click="selectMajor(i)">{{ i }}</span>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="majorSelectorVisible = false">{{
          $t("titlePage.btnClose")
        }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { getTitle, getTitleHistory } from "@/api/generate";
import { Loading } from "element-ui";
import PageLeftBox from "../components/PageLeftBox";
import Placeholder from "../components/Placeholder";
import { getMajorLetter } from "@/utils/major";
import { mapMutations, mapGetters } from "vuex";
import { safeJsonParse } from "@/utils";

const KEY_LAST_FORM = "lastTitleForm";
const KEY_LAST_RESULT = "lastTitleResult";
const KEY_LAST_TITLE = "lastTitle";
const KEY_LAST_OUTLINE = "lastOutline";
export default {
  name: "GetOutline",
  components: {
    PageLeftBox,
    Placeholder,
  },
  computed: {
    ...mapGetters('user', ['userInfo', 'isLoggedIn', 'isVip']),
  },
  data() {
    return {
      titles: [],
      majorSelectorVisible: false,
      majorList: [],
      form: {
        domain: "",
        level: "本科",
        lang: "中文",
        topic: "",
        keyword: "",
      },
      historyTitles: [], // 用户历史选题
      showHistory: false, // 是否显示历史记录
    };
  },
  created() {
    // 优先加载用户历史数据
    this.loadUserHistory();
    
    // 作为备选，从localStorage恢复表单数据
    let lastTitleForm = localStorage.getItem(KEY_LAST_FORM);
    if (lastTitleForm) {
      const parsedForm = safeJsonParse(lastTitleForm);
      if (parsedForm) {
        this.form = parsedForm;
      } else {
        localStorage.removeItem(KEY_LAST_FORM);
      }
    }
    
    // 只有在没有服务器历史数据时才使用localStorage的结果
    let lastResult = localStorage.getItem(KEY_LAST_RESULT);
    if (lastResult && this.titles.length === 0) {
      const parsedResult = safeJsonParse(lastResult);
      if (parsedResult) {
        this.titles = parsedResult;
      } else {
        localStorage.removeItem(KEY_LAST_RESULT);
      }
    }
  },

  methods: {
    loadUserHistory() {
      // 从数据库加载用户历史标题
      getTitleHistory()
        .then(res => {
          if (res.is_success && res.data && res.data.list && res.data.list.length > 0) {
            // 后端返回的是分页格式 {list: [], total: 0}
            this.historyTitles = res.data.list;
            // 如果有历史记录，使用最新的一条
            const latestHistory = res.data.list[0];
            if (latestHistory.formData) {
              // 恢复表单数据
              this.form.domain = latestHistory.formData.domain || "";
              this.form.topic = latestHistory.formData.topic || "";
              this.form.level = latestHistory.formData.level || "本科";
              this.form.lang = latestHistory.formData.lang || "中文";
              this.form.keyword = latestHistory.formData.keyword || "";
            }
            // 恢复标题结果 - 处理嵌套的titles结构
            if (latestHistory.titles) {
              let titlesArray = latestHistory.titles;
              
              // 如果是字符串，先解析JSON
              if (typeof titlesArray === 'string') {
                try {
                  titlesArray = JSON.parse(titlesArray);
                } catch (e) {
                  console.log('⚠️ JSON解析失败:', e);
                }
              }
              
              // 如果titles是对象且包含titles字段，则提取titles字段
              if (
                typeof titlesArray === 'object' &&
                titlesArray !== null &&
                titlesArray.titles &&
                Array.isArray(titlesArray.titles)
              ) {
                titlesArray = titlesArray.titles;
              }
              
              // 确保titlesArray是数组
              if (Array.isArray(titlesArray)) {
                this.titles = titlesArray;
              } else if (typeof titlesArray === 'object' && titlesArray !== null) {
                // 兜底：取对象里第一个数组
                const arr = Object.values(titlesArray).find(v => Array.isArray(v));
                if (arr) {
                  this.titles = arr;
                }
              }
            }
          } else if (res.is_success && res.data && Array.isArray(res.data) && res.data.length > 0) {
            // 兼容旧格式（直接返回数组）
            this.historyTitles = res.data;
            // 如果有历史记录，使用最新的一条
            const latestHistory = res.data[0];
            if (latestHistory.form_data) {
              // 恢复表单数据
              this.form.domain = latestHistory.form_data.domain || "";
              this.form.topic = latestHistory.form_data.topic || "";
              this.form.level = latestHistory.form_data.level || "本科";
              this.form.lang = latestHistory.form_data.lang || "中文";
              this.form.keyword = latestHistory.form_data.keyword || "";
            }
            // 恢复标题结果 - 处理嵌套的titles结构
            if (latestHistory.titles) {
              let titlesArray = latestHistory.titles;
              
              // 如果是字符串，先解析JSON
              if (typeof titlesArray === 'string') {
                try {
                  titlesArray = JSON.parse(titlesArray);
                } catch (e) {
                  console.log('⚠️ JSON解析失败:', e);
                }
              }
              
              // 如果titles是对象且包含titles字段，则提取titles字段
              if (
                typeof titlesArray === 'object' &&
                titlesArray !== null &&
                titlesArray.titles &&
                Array.isArray(titlesArray.titles)
              ) {
                titlesArray = titlesArray.titles;
              }
              
              // 确保titlesArray是数组
              if (Array.isArray(titlesArray)) {
                this.titles = titlesArray;
              } else if (typeof titlesArray === 'object' && titlesArray !== null) {
                // 兜底：取对象里第一个数组
                const arr = Object.values(titlesArray).find(v => Array.isArray(v));
                if (arr) {
                  this.titles = arr;
                }
              }
            }
          }
        })
        .catch(error => {
          console.error('加载历史标题失败:', error);
        });
    },
    
    updateHistoryList() {
      // 只更新历史记录列表，不覆盖当前标题
      getTitleHistory()
        .then(res => {
          if (res.is_success && res.data && res.data.list) {
            this.historyTitles = res.data.list;
          } else if (res.is_success && res.data && Array.isArray(res.data)) {
            this.historyTitles = res.data;
          }
        })
        .catch(error => {
          console.error('更新历史标题列表失败:', error);
        });
    },

    selectHistoryTitle(history) {
      if (history.formData) {
        // 恢复表单数据（新格式）
        this.form.domain = history.formData.domain || "";
        this.form.topic = history.formData.topic || "";
        this.form.level = history.formData.level || "本科";
        this.form.lang = history.formData.lang || "中文";
        this.form.keyword = history.formData.keyword || "";
      } else if (history.form_data) {
        // 兼容旧格式
        this.form.domain = history.form_data.domain || "";
        this.form.topic = history.form_data.topic || "";
        this.form.level = history.form_data.level || "本科";
        this.form.lang = history.form_data.lang || "中文";
        this.form.keyword = history.form_data.keyword || "";
      }
      // 恢复标题结果 - 处理嵌套的titles结构
      if (history.titles) {
        let titlesArray = history.titles;
        
        // 如果是字符串，先解析JSON
        if (typeof titlesArray === 'string') {
          try {
            titlesArray = JSON.parse(titlesArray);
          } catch (e) {
            console.log('⚠️ JSON解析失败:', e);
          }
        }
        
        // 如果titles是对象且包含titles字段，则提取titles字段
        if (
          typeof titlesArray === 'object' &&
          titlesArray !== null &&
          Array.isArray(titlesArray.titles)
        ) {
          titlesArray = titlesArray.titles;
        }
        
        // 确保titlesArray是数组
        if (Array.isArray(titlesArray)) {
          this.titles = titlesArray;
        } else if (typeof titlesArray === 'object' && titlesArray !== null) {
          // 兜底：取对象里第一个数组
          const arr = Object.values(titlesArray).find(v => Array.isArray(v));
          if (arr) {
            this.titles = arr;
          }
        }
      }
      this.showHistory = false;
    },

    openLoginBox() {},

    handleGotoUserRank() {
      this.$router.push({
        path: "/my/userrank",
      });
    },
    selectTitle(t) {
      // 清除localStorage的提纲缓存，因为要重新生成
      localStorage.removeItem(KEY_LAST_OUTLINE);
      // 保存选中的标题到localStorage（兼容现有逻辑）
      localStorage.setItem(KEY_LAST_TITLE, JSON.stringify(t));

      this.$router.push({
        path: "/paper/outline",
      });
    },
    selectMajor(i) {
      this.form.domain = i;
      this.majorSelectorVisible = false;
    },
    openMajorSelector() {
      this.majorSelectorVisible = true;
      this.majorList = getMajorLetter();
    },
    errMsg(msg) {
      this.$notify.error({ title: msg, duration: 3000 });
    },
    handleSubmit() {
      if (this.form.domain == "") {
        this.$message({ message: "请选择研究学科", type: "error" });
        return false;
      }
      if (this.form.topic == "") {
        this.$message({ message: "请填写细分研究方向", type: "error" });
        return false;
      }

      let progress = 0;
      let loadingInstance = Loading.service({
        text: "正在分析研究方向，生成专业论文题目...",
        background: "rgba(255, 255, 255, 0.6)",
        customClass: "loading-with-percentage"
      });
      
      // 保存表单数据到localStorage（兼容性）
      localStorage.setItem(KEY_LAST_FORM, JSON.stringify(this.form));
      localStorage.removeItem(KEY_LAST_RESULT);
      
      // 创建进度更新函数
      const updateProgress = () => {
        if (progress < 90) {
          progress += Math.random() * 10;
          if (progress > 90) progress = 90;
          loadingInstance.setText(`正在分析研究方向，生成专业论文题目...<br><span class="loading-percentage">${Math.floor(progress)}%</span>`);
        }
      };
      
      // 启动进度更新
      const progressInterval = setInterval(updateProgress, 300);
      
      getTitle(this.form)
        .then((res) => {
          clearInterval(progressInterval);
          if (res.is_success) {
            progress = 100;
            loadingInstance.setText(`正在分析研究方向，生成专业论文题目...<br><span class="loading-percentage">${progress}%</span>`);
            setTimeout(() => {
              loadingInstance.close();
              
              // 后端返回的数据格式是 {titles: [...]}，需要正确处理
              let titlesArray = res.data;
              
              // 如果是字符串，先解析JSON
              if (typeof titlesArray === 'string') {
                try {
                  titlesArray = JSON.parse(titlesArray);
                } catch (e) {
                  console.log('⚠️ JSON解析失败:', e);
                }
              }
              
              // 如果res.data是对象且包含titles字段，则提取titles字段
              if (
                typeof titlesArray === 'object' &&
                titlesArray !== null &&
                titlesArray.titles &&
                Array.isArray(titlesArray.titles)
              ) {
                titlesArray = titlesArray.titles;
              }
              
              // 确保titlesArray是数组
              if (Array.isArray(titlesArray)) {
                this.titles = titlesArray;
                localStorage.setItem(KEY_LAST_RESULT, JSON.stringify(this.titles));
                this.showHistory = false;
                
                // 只更新历史记录列表，不覆盖当前标题
                this.updateHistoryList();
              } else if (typeof titlesArray === 'object' && titlesArray !== null) {
                // 兜底：取对象里第一个数组
                const arr = Object.values(titlesArray).find(v => Array.isArray(v));
                if (arr) {
                  this.titles = arr;
                  localStorage.setItem(KEY_LAST_RESULT, JSON.stringify(this.titles));
                  this.showHistory = false;
                  
                  // 只更新历史记录列表，不覆盖当前标题
                  this.updateHistoryList();
                } else {
                  this.$message({ type: "error", message: "标题数据格式错误" });
                }
              } else {
                this.$message({ type: "error", message: "标题数据格式错误" });
              }
            }, 200);
          } else {
            loadingInstance.close();
            this.$message({ type: "error", message: res.message });
          }
        })
        .catch((e) => {
          clearInterval(progressInterval);
          loadingInstance.close();
          console.error("请求失败:", e);
          this.$message({ type: "error", message: typeof e === 'string' ? e : '请求失败，请稍后重试' });
        });
    },
    showUpgradeInfo() {
      this.$alert(`
        <div style="text-align: left;">
          <h3 style="color: #E6A23C; margin-bottom: 15px;">🌟 升级VIP会员</h3>
          <div style="margin-bottom: 10px;">
            <strong>📝 论文标题：</strong>从2个提升至5个（每次生成）
          </div>
          <div style="margin-bottom: 10px;">
            <strong>🚀 生成速度：</strong>享受优先处理
          </div>
          <div style="margin-bottom: 10px;">
            <strong>💎 高级功能：</strong>解锁所有AI功能
          </div>
          <div style="margin-bottom: 10px;">
            <strong>🎯 专属服务：</strong>优先客服支持
          </div>
          <div style="color: #F56C6C; font-weight: bold;">
            立即升级，享受更多权益！
          </div>
        </div>
      `, '升级VIP', {
        dangerouslyUseHTMLString: true,
        confirmButtonText: '立即升级',
        cancelButtonText: '稍后再说',
        type: 'warning'
      }).then(() => {
        // TODO: 跳转到升级页面
        this.$message.info('升级功能开发中...')
      }).catch(() => {
        // 用户取消
      })
    },
  },
};
</script>

<style scoped lang="scss">
.flex {
  display: flex;
  flex-direction: row;
  height: 100%;
  background: #f8f9fa;
}

.form-box {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  margin-bottom: 24px;

  .el-form-item {
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .el-input {
    .el-input__inner {
      border-radius: 8px;
      border: 1px solid #e0e3e7;
      transition: all 0.3s ease;

      &:hover, &:focus {
        border-color: #409EFF;
        box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.1);
      }
    }

    .el-input-group__append {
      border-top-right-radius: 8px;
      border-bottom-right-radius: 8px;
      border-color: #e0e3e7;
      background: #f8f9fa;
      transition: all 0.3s ease;

      &:hover {
        background: #edf2f7;
      }

      button {
        border: none;
        background: none;
        padding: 0;
        margin: 0;
        font-weight: 500;
      }
    }
  }

  .el-radio-button__inner {
    border-radius: 6px;
    margin: 0 4px;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-1px);
    }
  }

  .xiaolong-btn {
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #409EFF 0%, #66b1ff 100%);
    border: none;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
    }
  }
}

.home-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 24px;
  background: #f8f9fa;
  min-width: 0;

  .result-title {
    color: #4a5568;
    font-size: 16px;
    padding: 16px 0;
    font-weight: 500;
  }

  .result-box {
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 0;
    flex: 1;
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 8px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 4px;
      
      &:hover {
        background: #a8a8a8;
      }
    }
  }
}

.title {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  }

  span {
    display: block;
    line-height: 1.6;
  }

  > span:first-child {
    font-size: 18px;
    color: #2d3748;
    font-weight: 500;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    button {
      padding: 8px 16px;
      border-radius: 6px;
      background: linear-gradient(135deg, #409EFF 0%, #66b1ff 100%);
      border: none;
      color: white;
      font-weight: 500;
      transition: all 0.3s ease;
      
      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
      }
    }
  }

  > span:nth-child(2) {
    font-size: 14px;
    color: #718096;
    margin-bottom: 16px;
  }
}

.kw {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 16px;

  .el-tag {
    border-radius: 6px;
    padding: 6px 12px;
    font-size: 13px;
    background: #f7fafc;
    border: 1px solid #e2e8f0;
    color: #4a5568;
    transition: all 0.3s ease;

    &:hover {
      background: #edf2f7;
      transform: translateY(-1px);
    }
  }
}

.major-seletor {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 24px;
  padding: 16px;

  .major {
    background: white;
    border-radius: 12px;
    padding: 20px;
    width: calc(33.33% - 16px);
    min-width: 280px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    gap: 16px;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);

      .major-title {
        color: #409EFF;
      }
    }
  }

  .major-title {
    font-size: 32px;
    font-weight: 600;
    color: #2d3748;
    transition: all 0.3s ease;
  }

  .major-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    span {
      padding: 6px 12px;
      border-radius: 6px;
      background: #f7fafc;
      color: #4a5568;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: #edf2f7;
        color: #409EFF;
        transform: translateY(-1px);
      }
    }
  }
}

.vip-notice {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.2);
  transition: all 0.3s ease;
  color: white;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(102, 126, 234, 0.3);
  }

  .notice-content {
    display: flex;
    align-items: center;
    gap: 24px;

    .notice-icon {
      i {
        font-size: 48px;
        color: #FFD700;
        display: block;
        text-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);
      }
    }

    .notice-text {
      flex: 1;

      .notice-title {
        font-size: 24px;
        font-weight: 700;
        color: white;
        margin-bottom: 8px;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .notice-desc {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 16px;
        line-height: 1.5;
      }

      .notice-usage {
        display: flex;
        align-items: center;
        gap: 12px;

        .usage-text {
          font-size: 13px;
          color: rgba(255, 255, 255, 0.8);
          font-weight: 500;
          min-width: 80px;
        }

        .usage-progress {
          flex: 1;
          
          .el-progress-bar__outer {
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
          }
          
          .el-progress-bar__inner {
            background: linear-gradient(90deg, #FFD700 0%, #FFA500 100%);
            border-radius: 10px;
          }
        }
      }
    }

    .notice-action {
      button {
        padding: 12px 24px;
        border-radius: 12px;
        background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
        border: none;
        color: #333;
        font-weight: 600;
        font-size: 14px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4);
          background: linear-gradient(135deg, #FFA500 0%, #FF8C00 100%);
        }
      }
    }
  }
}

.history-notice {
  color: #909399;
  font-size: 14px;
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  
  i {
    margin-right: 5px;
  }
}
</style>
