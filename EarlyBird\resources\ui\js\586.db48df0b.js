"use strict";(self["webpackChunkopen_essay_gui"]=self["webpackChunkopen_essay_gui"]||[]).push([[586],{7586:(s,e,t)=>{t.r(e),t.d(e,{default:()=>m});var o=function(){var s=this,e=s._self._c;return e("div",{staticClass:"base-config"},[e("h2",[s._v("系统基础信息设置")]),e("el-form",{attrs:{model:s.form,"label-width":"150px"}},[e("div",{staticClass:"card-container"},[e("el-card",{staticClass:"config-card"},[e("div",{attrs:{slot:"header"},slot:"header"},[e("span",[s._v("基础信息设置")])]),e("el-form-item",{attrs:{label:"系统名称"}},[e("el-input",{attrs:{placeholder:"请输入系统名称"},model:{value:s.form.systemName,callback:function(e){s.$set(s.form,"systemName",e)},expression:"form.systemName"}})],1),e("el-form-item",{attrs:{label:"系统Logo"}},[e("el-upload",{staticClass:"logo-uploader",attrs:{action:"","show-file-list":!1,"before-upload":s.beforeLogoUpload,"on-change":s.handleLogoChange}},[s.form.logoUrl?e("img",{staticClass:"logo-img",attrs:{src:s.form.logoUrl}}):e("i",{staticClass:"el-icon-plus logo-upload-icon"})])],1),e("el-form-item",{attrs:{label:"系统描述"}},[e("el-input",{attrs:{type:"textarea",rows:3,placeholder:"请输入系统描述"},model:{value:s.form.description,callback:function(e){s.$set(s.form,"description",e)},expression:"form.description"}})],1)],1),e("el-card",{staticClass:"config-card"},[e("div",{attrs:{slot:"header"},slot:"header"},[e("span",[s._v("论文下载价格设置")])]),e("el-form-item",{attrs:{label:"启用论文下载收费"}},[e("el-switch",{model:{value:s.form.thesisDownloadEnabled,callback:function(e){s.$set(s.form,"thesisDownloadEnabled",e)},expression:"form.thesisDownloadEnabled"}}),e("span",{staticClass:"form-tip"},[s._v("开启后，用户下载论文需要支付费用")])],1),e("el-form-item",{attrs:{label:"论文下载价格"}},[e("el-input-number",{attrs:{min:.01,max:999.99,step:.01,precision:2,disabled:!s.form.thesisDownloadEnabled},model:{value:s.form.thesisDownloadPrice,callback:function(e){s.$set(s.form,"thesisDownloadPrice",e)},expression:"form.thesisDownloadPrice"}}),e("span",{staticClass:"form-tip"},[s._v("单位：元")])],1),e("el-form-item",{attrs:{label:"VIP用户免费下载"}},[e("el-switch",{attrs:{disabled:!s.form.thesisDownloadEnabled},model:{value:s.form.thesisDownloadVipFree,callback:function(e){s.$set(s.form,"thesisDownloadVipFree",e)},expression:"form.thesisDownloadVipFree"}}),e("span",{staticClass:"form-tip"},[s._v("开启后，VIP用户可以免费下载论文")])],1),e("el-form-item",{attrs:{label:"首次下载免费"}},[e("el-switch",{attrs:{disabled:!s.form.thesisDownloadEnabled},model:{value:s.form.thesisDownloadFirstFree,callback:function(e){s.$set(s.form,"thesisDownloadFirstFree",e)},expression:"form.thesisDownloadFirstFree"}}),e("span",{staticClass:"form-tip"},[s._v("开启后，用户首次下载论文免费")])],1)],1)],1),e("el-form-item",[e("el-button",{attrs:{type:"primary"},on:{click:s.onSave}},[s._v("保存设置")]),e("el-button",{on:{click:s.resetForm}},[s._v("重置")])],1)],1)],1)},a=[],i=t(9192);const r={name:"BaseConfig",data(){return{form:{systemName:"",logoUrl:"",description:"",thesisDownloadEnabled:!1,thesisDownloadPrice:10,thesisDownloadVipFree:!0,thesisDownloadFirstFree:!0},loading:!1}},created(){this.loadConfig()},methods:{async loadConfig(){try{this.loading=!0;const e=localStorage.getItem("baseConfig");if(e){const s=JSON.parse(e);this.form={...this.form,...s}}try{const s=await i.XN.getSettings();s.success&&s.data&&(s.data.system&&(this.form.systemName=s.data.system.name||this.form.systemName,this.form.logoUrl=s.data.system.logo||this.form.logoUrl,this.form.description=s.data.system.description||this.form.description),s.data.thesis_download&&(this.form.thesisDownloadEnabled="true"===s.data.thesis_download.is_active,this.form.thesisDownloadPrice=parseFloat(s.data.thesis_download.price)||this.form.thesisDownloadPrice,this.form.thesisDownloadVipFree="true"===s.data.thesis_download.vip_free,this.form.thesisDownloadFirstFree="true"===s.data.thesis_download.first_free))}catch(s){console.warn("从后端加载配置失败，使用本地配置",s)}}finally{this.loading=!1}},async onSave(){try{this.loading=!0,localStorage.setItem("baseConfig",JSON.stringify(this.form));try{const s={system:{name:this.form.systemName,logo:this.form.logoUrl,description:this.form.description},thesis_download:{is_active:this.form.thesisDownloadEnabled?"true":"false",price:this.form.thesisDownloadPrice.toString(),vip_free:this.form.thesisDownloadVipFree?"true":"false",first_free:this.form.thesisDownloadFirstFree?"true":"false"}},e=await i.XN.saveSettings(s);e.success?this.$message.success("设置保存成功"):this.$message.warning("保存到后端失败: "+(e.message||"未知错误"))}catch(s){console.error("保存到后端失败",s),this.$message.warning("保存到后端失败，但已保存到本地")}}finally{this.loading=!1}},resetForm(){this.loadConfig()},beforeLogoUpload(s){const e=s.type.startsWith("image/");return e||this.$message.error("只能上传图片文件！"),e},handleLogoChange(s){const e=new FileReader;e.onload=s=>{this.form.logoUrl=s.target.result},e.readAsDataURL(s.raw)}}},l=r;var n=t(1656),d=(0,n.A)(l,o,a,!1,null,"e566386e",null);const m=d.exports}}]);