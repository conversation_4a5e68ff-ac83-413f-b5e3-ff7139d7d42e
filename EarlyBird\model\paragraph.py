from werkzeug.security import generate_password_hash, check_password_hash

from EarlyBird.common.libs.BaseModel import BaseModel
from sqlalchemy import (
    Integer,
    String,
    Boolean,
    Column,
    JSON,
    Enum,
    SmallInteger,
    DateTime,
    Table,
    BIGINT,
    ForeignKey,
    MetaData,
    TEXT,
)
from sqlalchemy.orm import relationship
import json
from . import TABLE_PREFIX


class Paragraph(BaseModel):

    __tablename__ = f"{TABLE_PREFIX}paragraph"
    __table_args__ = {"comment": "论文的中段落文字"}

    uid = Column(Integer, nullable=False, comment="用户id")
    thesisId = Column(Integer, nullable=False, comment="论文id")
    paraId = Column(String(50), nullable=False, comment="段落id")
    title = Column(String(600), default="", comment="标题")
    paragraph = Column(TEXT(65536), default="", comment="摘要")

    def __repr__(self):
        return json.dumps(
            {
                "uid": self.uid,
                "thesisId": self.thesisId,
                "paraId": self.paraId,
                "title": self.title,
                "paragraph": self.paragraph,
            }
        )
