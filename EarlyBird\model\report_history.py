from EarlyBird.common.libs.BaseModel import BaseModel
from sqlalchemy import (
    Integer,
    String,
    Column,
    JSON,
    TEXT,
    DateTime,
)
import json
from datetime import datetime
from . import TABLE_PREFIX


class ReportHistory(BaseModel):
    """报告历史记录模型"""
    
    __tablename__ = f"{TABLE_PREFIX}report_history"
    __table_args__ = {"comment": "用户报告历史记录"}
    
    uid = Column(Integer, nullable=False, comment="用户id")
    report_type = Column(String(50), comment="报告类型(daily/weekly/monthly/summary)")
    title = Column(String(500), comment="报告标题")
    content = Column(TEXT(65536), comment="报告内容")
    form_data = Column(JSON, comment="表单数据")
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "uid": self.uid,
            "report_type": self.report_type,
            "title": self.title,
            "content": self.content,
            "form_data": self.form_data,
            "create_time": self.create_time.strftime("%Y-%m-%d %H:%M:%S") if self.create_time else None,
            "update_time": self.update_time.strftime("%Y-%m-%d %H:%M:%S") if self.update_time else None,
        }
    
    def __repr__(self):
        return json.dumps(
            {
                "id": self.id,
                "uid": self.uid,
                "report_type": self.report_type,
                "title": self.title,
            },
            ensure_ascii=False
        ) 