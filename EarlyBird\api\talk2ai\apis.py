from EarlyBird.api.talk2ai import talk2ai_blueprint
from flask import jsonify, make_response, g
import sys

from EarlyBird.common import ApiResponse, Result
from flask_pydantic import validate
import logging
import json

# 添加数据库导入
from EarlyBird.ExtendRegister.db_register import db

from EarlyBird.api.talk2ai.service import Service
from EarlyBird.api.talk2ai.dantic import *

from EarlyBird.api.talk2ai.model.model_proxy import ModelProxy
from EarlyBird.api.talk2ai.model.base import InvokeResult
from EarlyBird.model.report_history import ReportHistory
from EarlyBird.ExtendRegister.model_register import Thesis

LOGGER = logging.getLogger(__name__)


@talk2ai_blueprint.route("/getChatLog", methods=["POST"])
@validate()
def getChatLog(body: ParamGetChatLog):
    try:
        body.userId = g.userid
    except Exception as e:
        LOGGER.error(e)
        return ApiResponse().needLogin().json()

    res: Result = Service().getChatLog(body)
    if res.isSucc():
        return ApiResponse().set_data(res.data).json()
    return ApiResponse().error(res.message).json()


@talk2ai_blueprint.route("/delChat", methods=["POST"])
@validate()
def delChat(body: ParamDelChat):
    try:
        body.userId = g.userid
    except Exception as e:
        LOGGER.error(e)
        return ApiResponse().needLogin().json()

    res: Result = Service().delChat(body)
    if res.isSucc():
        return ApiResponse(message=res.data).json()
    return ApiResponse().error(res.message).json()


@talk2ai_blueprint.route("/chat", methods=["POST"])
@validate()
def chat(body: ParamChat):
    try:
        body.userId = g.userid
    except Exception as e:
        LOGGER.error(e)
        return ApiResponse().needLogin().json()

    res: Result = Service().getChatReply(body)
    if res.isSucc():
        return (
            ApiResponse()
            .set_data(res.data)
            .json()
        )
    return ApiResponse().error(res.message).json()


@talk2ai_blueprint.route("/getTitle", methods=["POST"])
@validate()
def getTitle(body: ParamTitle):

    try:
        body.userId = g.userid
        body.isVipUser = g.isVip
    except Exception as e:
        LOGGER.error(e)
        return ApiResponse().needLogin().json()

    res: Result = Service().getTitle(body)
    if res.isSucc():
        return ApiResponse().set_data(res.data).json()
    return ApiResponse().error(res.message).json()


@talk2ai_blueprint.route("/getOutline", methods=["POST"])
@validate()
def getOutline(body: ParamOutline):
    try:

        body.userId = g.userid
        body.isVipUser = g.isVip
    except Exception as e:
        LOGGER.error(e)
        return ApiResponse().needLogin().json()

    res: Result = Service().getOutline(body)
    if res.isSucc():
        return ApiResponse().set_data(res.data).json()
    return ApiResponse().error(res.message).json()


@talk2ai_blueprint.route("/exportOutline", methods=["POST"])
@validate()
def exportOutline(body: ParamSelect4Content):

    try:
        body.userId = g.userid
        body.isVipUser = g.isVip

    except Exception as e:
        return ApiResponse().needLogin().json()

    res: Result = Service().exportOutline(body)
    if res.is_success():
        return ApiResponse().set_data(res.data).json()
    return ApiResponse().error(res.message).json()


@talk2ai_blueprint.route("/select4Content", methods=["POST"])
@validate()
def select4Content(body: ParamSelect4Content):
    try:
        LOGGER.info(f"Received select4Content request with body type: {type(body)}")
        
        # 记录请求体的关键字段
        try:
            LOGGER.info(f"Request title: {body.title}, level: {body.level}, lang: {body.lang}")
            LOGGER.info(f"Outline type: {type(body.outline)}")
            
            # 检查outline字段
            if not body.outline:
                LOGGER.warning("Outline is empty or None")
                return ApiResponse().error("提纲数据为空").json()
                
            # 检查outline是否有必要的字段
            if not isinstance(body.outline, dict):
                LOGGER.error(f"Outline is not a dict: {type(body.outline)}")
                return ApiResponse().error("提纲格式错误").json()
                
            # 检查outline是否有title和subtitle字段
            if "title" not in body.outline:
                LOGGER.warning("Outline has no title field, using request title")
                body.outline["title"] = body.title
                
            if "subtitle" not in body.outline:
                LOGGER.warning("Outline has no subtitle field, creating empty list")
                body.outline["subtitle"] = []
                
            LOGGER.info(f"Outline keys: {body.outline.keys()}")
        except Exception as e:
            LOGGER.exception(f"Error checking outline: {e}")
            
        # 设置用户ID和VIP状态
        try:
            from flask import session, g
            
            # 获取用户ID
            body.userId = g.userid
            
            # 检查多个VIP状态来源
            g_vip = getattr(g, 'isVip', False)
            session_has_activation = 'activation_key' in session and session['activation_key']
            
            # 记录各种VIP状态来源
            LOGGER.info(f"VIP状态来源 - g.isVip: {g_vip}, session有激活码: {session_has_activation}")
            
            # 如果g对象中有VIP状态或session中有激活码，则用户为VIP
            body.isVipUser = g_vip or session_has_activation
            
            # 如果用户有激活码，强制设置为VIP用户
            if session_has_activation:
                LOGGER.info(f"用户 {body.userId} 有激活码 {session.get('activation_key')}，强制设置为VIP用户")
                body.isVipUser = True
            
            # 获取用户对象，检查VIP状态
            user = getattr(g, 'user', None)
            if user:
                user_is_vip = getattr(user, 'isVip', lambda: False)()
                LOGGER.info(f"用户对象VIP状态检查: {user_is_vip}")
                if user_is_vip:
                    body.isVipUser = True
            
            LOGGER.info(f"最终设置的用户状态 - User ID: {body.userId}, VIP: {body.isVipUser}")
        except Exception as e:
            LOGGER.error(f"Authorization error in select4Content: {e}")
            return ApiResponse().needLogin().json()

        try:
            # 记录服务请求
            LOGGER.info(f"Processing select4Content for user {body.userId}, title: {body.title}")
            
            # 调用服务层处理请求
            res: Result = Service().select4Content(body)
            
            if res.is_success():
                LOGGER.info(f"select4Content success for user {body.userId}, thesis ID: {res.data.get('thesisId')}")
                return ApiResponse().set_data(res.data).json()
            
            LOGGER.error(f"select4Content failed for user {body.userId}: {res.message}")
            return ApiResponse().error(res.message).json()
        except Exception as e:
            LOGGER.exception(f"Unexpected error in select4Content: {e}")
            return ApiResponse().error(f"服务器内部错误: {str(e)}").json()
    except Exception as outer_e:
        LOGGER.exception(f"Critical error in select4Content API handler: {outer_e}")
        return ApiResponse().error(f"请求处理失败: {str(outer_e)}").json()


@talk2ai_blueprint.route("/stopGenerateAll", methods=["POST"])
@validate()
def stopGenerateAll(body: ParamThesisId):
    try:
        body.userId = g.userid
        body.isVipUser = g.isVip
    except Exception as e:
        return ApiResponse().needLogin().json()

    try:

        res: Result = Service().stopThesisGenerate(body)
        if res.isSucc():
            return ApiResponse().json()
        return ApiResponse().error(res.message).json()
    except Exception as e:
        LOGGER.exception(e)
        return ApiResponse().error(str(e)).json()


@talk2ai_blueprint.route("/generateAll", methods=["POST"])
@validate()
def pushGenerateTask(body: ParamGenerateAll):

    try:
        body.userId = g.userid
        body.isVipUser = g.isVip

        thesisId = body.thesisId
        thesis = Service().getThesisById(thesisId)
        if thesis is None:
            LOGGER.error(f"id {thesisId} 未查询到论文")
            return ApiResponse().error("").json()

        if type(thesis.outline) is not dict:
            LOGGER.error(f"id {thesisId} 论文存储数据格式错误")
            return ApiResponse().error("论文存储数据格式错误").json()

        if thesis.uid != g.userid:
            LOGGER.error(
                f"thesis.uid = {thesis.uid},g.userid = {g.userid} 有人要访问别人的论文"
            )
            return ApiResponse().error("未查询到此论文").json()

        # 获取全局设置
        globalSettings = body.globalSettings if hasattr(body, 'globalSettings') else None
        if globalSettings:
            LOGGER.info(f"使用全局字数控制设置: {globalSettings}")

        res: Result = Service().setThesisNeedGenerate(thesisId, globalSettings)
        if res.isSucc():
            return ApiResponse().json()
        return ApiResponse().error(res.message).json()
    except Exception as e:
        LOGGER.exception(e)
        return ApiResponse().error(str(e)).json()


@talk2ai_blueprint.route("/generateSingleParagraph", methods=["POST"])
@validate()
def generateSingleParagraph(body: ParamGenerateSingleParagraph):

    try:
        body.userId = g.userid
        body.isVipUser = g.isVip
    except Exception as e:
        return ApiResponse().needLogin().json()

    res: Result = Service().generateSingleParagraph(body)
    if res.is_success():
        return ApiResponse().json()
    return ApiResponse().error(res.message).json()


@talk2ai_blueprint.route("/generateDigest", methods=["POST"])
@validate()
def generateDigest(body: ParamThesisId):

    try:

        body.userId = g.userid
        body.isVipUser = g.isVip
    except Exception as e:
        return ApiResponse().needLogin().json()

    res: Result = Service().generateDigest(body)
    if not res.isSucc():
        return ApiResponse().error(res.message).json()
    res: Result = Service().generateDigestEnglish(body)
    if not res.isSucc():
        return ApiResponse().error(res.message).json()
    return ApiResponse().json()


@talk2ai_blueprint.route("/generateThanks", methods=["POST"])
@validate()
def generateThanks(body: ParamThesisId):

    try:

        body.userId = g.userid
        body.isVipUser = g.isVip
    except Exception as e:
        return ApiResponse().needLogin().json()

    res: Result = Service().generateThanks(body)
    if not res.isSucc():
        return ApiResponse().error(res.message).json()
    return ApiResponse().json()


@talk2ai_blueprint.route("/generateReference", methods=["POST"])
@validate()
def generateReference(body: ParamThesisId):

    try:

        body.userId = g.userid
        body.isVipUser = g.isVip
    except Exception as e:
        return ApiResponse().needLogin().json()

    res: Result = Service().generateReference(body)
    if not res.isSucc():
        return ApiResponse().error(res.message).json()
    return ApiResponse().json()


@talk2ai_blueprint.route("/getTitleHistory", methods=["POST"])
@validate()
def getTitleHistory(body: ParamGetTitleHistory):
    try:
        body.userId = g.userid
        body.isVipUser = g.isVip
    except Exception as e:
        LOGGER.error(e)
        return ApiResponse().needLogin().json()

    res: Result = Service().getTitleHistory(body)
    if res.isSucc():
        return ApiResponse().set_data(res.data).json()
    return ApiResponse().error(res.message).json()


@talk2ai_blueprint.route("/getOutlineHistory", methods=["POST"])
@validate()
def getOutlineHistory(body: ParamGetOutlineHistory):
    try:
        body.userId = g.userid
        body.isVipUser = g.isVip
    except Exception as e:
        LOGGER.error(e)
        return ApiResponse().needLogin().json()

    res: Result = Service().getOutlineHistory(body)
    if res.isSucc():
        return ApiResponse().set_data(res.data).json()
    return ApiResponse().error(res.message).json()


@talk2ai_blueprint.route("/generateReport", methods=["POST"])
@validate()
def generateReport(body: dict):
    try:
        # 检查用户登录状态
        try:
            userId = g.userid
            isVipUser = g.isVip
        except Exception as e:
            return ApiResponse().needLogin().json()
        
        # 获取请求参数
        report_type = body.get("reportType")
        report_data = body.get("reportData")
        
        if not report_type or not report_data:
            return ApiResponse().error("缺少必要参数").json()
        
        # 根据报告类型构建不同的提示词
        prompt = ""
        title = ""
        
        if report_type == "daily":
            title = report_data.get('title', '日常工作报告')
            prompt = f"请你作为一名专业的工作报告撰写专家，根据以下信息生成一份详细的日报。\n\n"
            prompt += f"报告主题：{title}\n"
            prompt += f"日期：{report_data.get('date', '')}\n"
            prompt += f"工作内容：{report_data.get('content', '')}\n"
            prompt += f"报告风格：{report_data.get('style', 'concise')}\n\n"
            prompt += "请生成一份专业、结构清晰的日报，包含以下部分：\n"
            prompt += "1. 今日工作内容（详细描述完成的任务）\n"
            prompt += "2. 工作成果（量化成果和影响）\n"
            prompt += "3. 遇到的问题及解决方案\n"
            prompt += "4. 明日工作计划\n"
            prompt += "请使用Markdown格式输出。"
            
        elif report_type == "weekly":
            title = report_data.get('title', '周工作总结')
            prompt = f"请你作为一名专业的工作报告撰写专家，根据以下信息生成一份详细的周报。\n\n"
            prompt += f"报告主题：{title}\n"
            prompt += f"周期：{report_data.get('dateRange', ['', ''])[0]} 至 {report_data.get('dateRange', ['', ''])[1]}\n"
            prompt += f"工作内容：{report_data.get('content', '')}\n"
            prompt += f"重点项目：{report_data.get('keyProject', '')}\n"
            prompt += f"报告风格：{report_data.get('style', 'concise')}\n\n"
            prompt += "请生成一份专业、结构清晰的周报，包含以下部分：\n"
            prompt += "1. 本周工作内容概述\n"
            prompt += "2. 重点项目进展\n"
            prompt += "3. 工作成果及数据\n"
            prompt += "4. 存在的问题及解决方案\n"
            prompt += "5. 下周工作计划\n"
            prompt += "请使用Markdown格式输出。"
            
        elif report_type == "monthly":
            title = report_data.get('title', '月度工作总结')
            prompt = f"请你作为一名专业的工作报告撰写专家，根据以下信息生成一份详细的月报。\n\n"
            prompt += f"报告主题：{title}\n"
            prompt += f"月份：{report_data.get('month', '')}\n"
            prompt += f"部门/团队：{report_data.get('department', '')}\n"
            prompt += f"工作内容：{report_data.get('content', '')}\n"
            prompt += f"KPI完成度：{report_data.get('kpiCompletion', 80)}%\n"
            prompt += f"报告风格：{report_data.get('style', 'concise')}\n\n"
            prompt += "请生成一份专业、结构清晰的月报，包含以下部分：\n"
            prompt += "1. 本月工作概述\n"
            prompt += "2. 主要工作内容及成果\n"
            prompt += "3. KPI完成情况分析\n"
            prompt += "4. 问题与挑战\n"
            prompt += "5. 经验与启示\n"
            prompt += "6. 下月工作计划\n"
            prompt += "请使用Markdown格式输出。"
            
        elif report_type == "summary":
            title = report_data.get('title', '工作总结')
            prompt = f"请你作为一名专业的工作报告撰写专家，根据以下信息生成一份详细的{report_data.get('summaryType', 'project')}总结。\n\n"
            prompt += f"总结主题：{title}\n"
            prompt += f"总结类型：{report_data.get('summaryType', 'project')}\n"
            prompt += f"工作内容：{report_data.get('content', '')}\n"
            prompt += f"成果亮点：{report_data.get('achievements', '')}\n"
            prompt += f"问题与改进：{report_data.get('improvements', '')}\n"
            prompt += f"报告风格：{report_data.get('style', 'concise')}\n\n"
            prompt += "请生成一份专业、结构清晰的总结报告，包含以下部分：\n"
            prompt += "1. 总体概述\n"
            prompt += "2. 主要工作内容\n"
            prompt += "3. 取得的成果\n"
            prompt += "4. 存在的问题\n"
            prompt += "5. 经验与教训\n"
            prompt += "6. 未来展望与计划\n"
            prompt += "请使用Markdown格式输出。"
        else:
            return ApiResponse().error("不支持的报告类型").json()
        
        # 调用AI模型生成报告
        mp = ModelProxy("qwen")  # 使用默认模型
        result: InvokeResult = mp._sendRequest(prompt)
        
        if not result.isSuccess:
            return ApiResponse().error(f"生成报告失败: {result.message}").json()
        
        # 保存报告到数据库
        report = ReportHistory(
            uid=userId,
            report_type=report_type,
            title=title,
            content=result.data,
            form_data=report_data
        )
        
        try:
            db.session.add(report)
            db.session.commit()
            LOGGER.info(f"为用户 {userId} 保存报告历史记录，ID: {report.id}")
        except Exception as e:
            LOGGER.error(f"保存报告失败: {str(e)}")
            db.session.rollback()
        
        # 返回生成的报告内容和ID
        return ApiResponse().set_data({
            "content": result.data,
            "reportId": report.id if report.id else None
        }).json()
    except Exception as e:
        LOGGER.exception(e)
        return ApiResponse().error(str(e)).json()


@talk2ai_blueprint.route("/getReportHistory", methods=["POST", "GET"])
@validate()
def getReportHistory():
    """获取用户的报告历史记录"""
    try:
        try:
            userId = g.userid
        except Exception as e:
            return ApiResponse().needLogin().json()
        
        # 查询用户的报告历史（只查询未删除的）
        reports = ReportHistory.query.filter(
            ReportHistory.uid == userId,
            ReportHistory.is_deleted == 0
        ).order_by(ReportHistory.id.desc()).limit(50).all()
        
        # 转换为字典列表
        report_list = [report.to_dict() for report in reports]
        
        # 返回分页格式的数据，与前端期望的格式一致
        return ApiResponse().set_data({
            "total": len(report_list),
            "list": report_list,
            "page": 1,
            "pageSize": 50
        }).json()
    except Exception as e:
        LOGGER.exception(e)
        return ApiResponse().error(str(e)).json()


@talk2ai_blueprint.route("/deleteReport", methods=["POST", "GET"])
@validate()
def deleteReport(body: dict):
    """删除报告"""
    try:
        try:
            userId = g.userid
        except Exception as e:
            return ApiResponse().needLogin().json()
        
        report_id = body.get("reportId")
        if not report_id:
            return ApiResponse().error("缺少报告ID").json()
        
        # 查询报告
        report = ReportHistory.query.filter_by(
            id=report_id,
            uid=userId
        ).first()
        
        if not report:
            return ApiResponse().error("报告不存在或无权限删除").json()
        
        # 删除报告
        db.session.delete(report)
        db.session.commit()
        
        return ApiResponse().json()
    except Exception as e:
        LOGGER.exception(e)
        return ApiResponse().error(str(e)).json()


@talk2ai_blueprint.route("/checkExistingThesis", methods=["POST", "GET"])
@validate()
def checkExistingThesis():
    """检查用户是否已有论文"""
    try:
        try:
            user_id = g.userid
            is_vip = g.isVip
        except Exception as e:
            LOGGER.error(f"Authorization error in checkExistingThesis: {e}")
            return ApiResponse().needLogin().json()
        
        # 查询用户的论文数量
        thesis_count = Thesis.query.filter(Thesis.uid == user_id).count()
        
        # 获取用户类型的论文上限
        max_thesis = 5 if is_vip else 1  # VIP用户最多5篇，免费用户最多1篇
        
        # 返回结果
        return ApiResponse().set_data({
            "has_thesis": thesis_count > 0,
            "thesis_count": thesis_count,
            "max_thesis": max_thesis,
            "can_create": thesis_count < max_thesis,
            "is_vip": is_vip
        }).json()
    except Exception as e:
        LOGGER.exception(f"Error in checkExistingThesis: {e}")
        return ApiResponse().error(str(e)).json()
