<template>
  <div class="thesis-download-config">
    <el-card>
      <div slot="header">
        <span>论文下载收费配置</span>
      </div>
      
      <div v-loading="loading" class="config-content">
        <el-form 
          ref="configForm" 
          :model="configForm" 
          label-width="150px"
          class="config-form"
        >
          <!-- 基础配置 -->
          <el-card class="config-section" shadow="never">
            <div slot="header">
              <i class="el-icon-setting"></i>
              <span>基础配置</span>
            </div>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="是否启用收费">
                  <el-switch
                    v-model="configForm.is_active"
                    active-text="启用"
                    inactive-text="禁用">
                  </el-switch>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="首次下载是否免费">
                  <el-switch
                    v-model="configForm.first_free"
                    active-text="免费"
                    inactive-text="收费">
                  </el-switch>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="下载收费金额(元)">
                  <el-input-number 
                    v-model="configForm.price" 
                    :min="0.01" 
                    :max="1000" 
                    :step="0.01"
                    :precision="2">
                  </el-input-number>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="VIP用户是否免费">
                  <el-switch
                    v-model="configForm.vip_free"
                    active-text="免费"
                    inactive-text="收费">
                  </el-switch>
                </el-form-item>
              </el-col>
            </el-row>
          </el-card>
          
          <!-- 说明信息 -->
          <el-card class="config-section" shadow="never">
            <div slot="header">
              <i class="el-icon-info"></i>
              <span>功能说明</span>
            </div>
            
            <div class="description-box">
              <p><i class="el-icon-check"></i> 启用收费功能后，用户下载论文时需要支付相应费用</p>
              <p><i class="el-icon-check"></i> 可以设置用户首次下载免费，后续下载收费</p>
              <p><i class="el-icon-check"></i> 可以设置VIP用户下载免费，普通用户下载收费</p>
              <p><i class="el-icon-check"></i> 收费金额可以根据实际需求进行调整</p>
              <p><i class="el-icon-warning"></i> 请确保已正确配置支付接口，否则收费功能可能无法正常使用</p>
            </div>
          </el-card>
          
          <!-- 操作按钮 -->
          <el-card class="config-section" shadow="never">
            <div class="action-buttons">
              <el-button type="primary" @click="saveConfig" :loading="saving">
                <i class="el-icon-check"></i>
                保存配置
              </el-button>
              <el-button @click="loadConfig">
                <i class="el-icon-refresh-right"></i>
                刷新数据
              </el-button>
            </div>
          </el-card>
        </el-form>
      </div>
    </el-card>
  </div>
</template>

<script>
import { getPaymentConfig, savePaymentConfig } from '@/api/payment'

export default {
  name: "ThesisDownloadConfig",
  data() {
    return {
      loading: false,
      saving: false,
      configForm: {
        is_active: false,
        first_free: true,
        price: 10,
        vip_free: true
      }
    }
  },
  created() {
    this.loadConfig()
  },
  methods: {
    // 加载配置
    async loadConfig() {
      try {
        this.loading = true
        const result = await getPaymentConfig()
        
        if (result.success) {
          const configData = result.data || {}
          
          // 确保configData存在
          if (!configData) {
            console.warn('配置数据为空');
            return;
          }
          
          const thesisConfig = configData.thesis_download || {}
          
          // 更新表单数据
          this.configForm = {
            is_active: thesisConfig.is_active?.value === 'true',
            first_free: thesisConfig.first_free?.value === 'true',
            vip_free: thesisConfig.vip_free?.value === 'true',
            price: thesisConfig.price?.value || 0
          }
          
          this.$message.success('配置加载成功')
        } else {
          this.$message.error(result.message || '加载配置失败')
        }
      } catch (error) {
        this.$message.error('加载配置失败: ' + (error.message || '未知错误'))
      } finally {
        this.loading = false
      }
    },
    
    // 保存配置
    async saveConfig() {
      try {
        this.$refs.configForm.validate(async (valid) => {
          if (!valid) {
            return
          }
          
          this.saving = true
          
          const saveData = {
            thesis_download: {
              is_active: this.configForm.is_active,
              first_free: this.configForm.first_free,
              vip_free: this.configForm.vip_free,
              price: this.configForm.price
            }
          }
          
          const result = await savePaymentConfig(saveData)
          
          if (result.success) {
            this.$message.success('配置保存成功')
            this.loadConfig()
          } else {
            this.$message.error(result.message || '保存配置失败')
          }
        })
      } catch (error) {
        this.$message.error('保存配置失败: ' + (error.message || '未知错误'))
      } finally {
        this.saving = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.thesis-download-config {
  .config-content {
    padding: 20px 0;
  }
  
  .config-form {
    .config-section {
      margin-bottom: 20px;
      
      .el-card__header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #e9ecef;
        
        i {
          margin-right: 8px;
          color: #409eff;
        }
        
        span {
          font-weight: 600;
          color: #303133;
        }
      }
      
      .el-card__body {
        padding: 20px;
      }
    }
    
    .action-buttons {
      text-align: center;
      
      .el-button {
        margin: 0 10px;
        
        i {
          margin-right: 5px;
        }
      }
    }
  }
  
  .description-box {
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 4px;
    
    p {
      margin: 8px 0;
      
      i {
        margin-right: 8px;
        color: #67c23a;
        
        &.el-icon-warning {
          color: #e6a23c;
        }
      }
    }
  }
}
</style> 