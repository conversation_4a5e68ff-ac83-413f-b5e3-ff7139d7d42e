"use strict";(self["webpackChunkopen_essay_gui"]=self["webpackChunkopen_essay_gui"]||[]).push([[503],{9503:(e,t,a)=>{a.r(t),a.d(t,{default:()=>c});var r=function(){var e=this,t=e._self._c;return t("div",{staticClass:"order-detail"},[t("div",{staticClass:"page-header"},[t("h2",[e._v("支付订单详情")]),t("div",{staticClass:"header-actions"},[t("el-button",{attrs:{type:"primary",icon:"el-icon-refresh"},on:{click:e.loadOrderDetail}},[e._v("刷新")]),t("el-button",{attrs:{icon:"el-icon-back"},on:{click:e.goBack}},[e._v("返回")])],1)]),t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}]},[t("el-card",{staticClass:"detail-card"},[t("div",{attrs:{slot:"header"},slot:"header"},[t("span",[e._v("基本信息")])]),t("el-descriptions",{attrs:{column:2,border:""}},[t("el-descriptions-item",{attrs:{label:"订单ID"}},[e._v(e._s(e.orderDetail.id||"-"))]),t("el-descriptions-item",{attrs:{label:"用户ID"}},[e._v(e._s(e.orderDetail.user_id||"-"))]),t("el-descriptions-item",{attrs:{label:"商品ID"}},[e._v(e._s(e.orderDetail.product_id||"-"))]),t("el-descriptions-item",{attrs:{label:"支付金额"}},[e._v(e._s(e.orderDetail.amount?`${e.orderDetail.amount.toFixed(2)} 元`:"-"))]),t("el-descriptions-item",{attrs:{label:"商户订单号"}},[e._v(e._s(e.orderDetail.out_trade_no||"-"))]),t("el-descriptions-item",{attrs:{label:"平台订单号"}},[e._v(e._s(e.orderDetail.trade_no||"-"))]),t("el-descriptions-item",{attrs:{label:"支付状态"}},[t("el-tag",{attrs:{type:e.getStatusType(e.orderDetail.status)}},[e._v(" "+e._s(e.getStatusText(e.orderDetail.status))+" ")])],1),t("el-descriptions-item",{attrs:{label:"支付方式"}},[e._v(e._s(e.orderDetail.pay_type||"-"))]),t("el-descriptions-item",{attrs:{label:"创建时间"}},[e._v(e._s(e.orderDetail.create_time||"-"))]),t("el-descriptions-item",{attrs:{label:"支付时间"}},[e._v(e._s(e.orderDetail.pay_time||"-"))])],1)],1),e.orderDetail.download_records&&e.orderDetail.download_records.length>0?t("el-card",{staticClass:"detail-card"},[t("div",{attrs:{slot:"header"},slot:"header"},[t("span",[e._v("关联下载记录")])]),t("el-table",{staticStyle:{width:"100%"},attrs:{data:e.orderDetail.download_records,border:""}},[t("el-table-column",{attrs:{prop:"id",label:"记录ID",width:"80",align:"center"}}),t("el-table-column",{attrs:{prop:"thesis_id",label:"论文ID",width:"100",align:"center"}}),t("el-table-column",{attrs:{prop:"is_paid",label:"支付状态",width:"100",align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-tag",{attrs:{type:a.row.is_paid?"success":"warning"}},[e._v(" "+e._s(a.row.is_paid?"已支付":"未支付")+" ")])]}}],null,!1,2467579289)}),t("el-table-column",{attrs:{prop:"payment_time",label:"支付时间",width:"180",align:"center"}}),t("el-table-column",{attrs:{prop:"create_time",label:"创建时间",width:"180",align:"center"}})],1)],1):e._e(),e.orderDetail.notify_data?t("el-card",{staticClass:"detail-card"},[t("div",{attrs:{slot:"header"},slot:"header"},[t("span",[e._v("回调通知数据")])]),t("div",{staticClass:"json-viewer"},[t("pre",[e._v(e._s(e.formatJSON(e.orderDetail.notify_data)))])])]):e._e(),e.orderDetail.notify_data_parsed?t("el-card",{staticClass:"detail-card"},[t("div",{attrs:{slot:"header"},slot:"header"},[t("span",[e._v("解析后的通知数据")])]),t("div",{staticClass:"json-viewer"},[t("pre",[e._v(e._s(e.formatJSON(e.orderDetail.notify_data_parsed)))])])]):e._e()],1)])},s=[],i=a(9192);const l={name:"PaymentOrderDetail",data(){return{orderId:null,orderDetail:{},loading:!1}},created(){this.orderId=this.$route.params.id,this.orderId?this.loadOrderDetail():(this.$message.error("订单ID不能为空"),this.goBack())},methods:{async loadOrderDetail(){if(this.orderId)try{this.loading=!0;const e=await i.iX.getOrderDetail(this.orderId);e.success&&e.data?this.orderDetail=e.data:this.$message.error(e.message||"加载订单详情失败")}catch(e){console.error("加载订单详情失败:",e),this.$message.error("加载订单详情失败: "+(e.message||"未知错误"))}finally{this.loading=!1}},getStatusType(e){switch(e){case 0:return"warning";case 1:return"success";case 2:return"info";default:return"info"}},getStatusText(e){switch(e){case 0:return"未支付";case 1:return"已支付";case 2:return"已退款";default:return"未知"}},formatJSON(e){if(!e)return"";try{return"string"===typeof e?JSON.stringify(JSON.parse(e),null,2):JSON.stringify(e,null,2)}catch(t){return e}},goBack(){this.$router.push({name:"AdminPaymentOrders"})}}},d=l;var o=a(1656),n=(0,o.A)(d,r,s,!1,null,"20b3eda8",null);const c=n.exports}}]);