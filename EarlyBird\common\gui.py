import webview
import logging

from config.config import AppConfig
from threading import Event
import os

logger = logging.getLogger(__name__)


global WINDOW


class JsApi:

    def closeWindow(self):
        if WINDOW is not None:
            WINDOW.destroy()
        else:
            logger.error("WINDOW is None")

    def minWindow(self):
        logger.info("Minimizing window")
        if WINDOW is not None:
            WINDOW.minimize()
        else:
            logger.error("WINDOW is None")

    def maxWindow(self):
        logger.info("Maximizing window")
        if WINDOW is not None:
            WINDOW.maximize()
        else:
            logger.error("WINDOW is None")

    def restoreWindow(self):
        logger.info("Restoring window")
        if WINDOW is not None:
            WINDOW.restore()
        else:
            logger.error("WINDOW is None")


def startFunc(window):
    pass


class Win:
    def __init__(self, stopEvent: Event) -> None:
        self.stopEvent = stopEvent

    def open(self):
        global WINDOW

        icon_path = os.path.join(
            os.path.dirname(os.getcwd()), 
            "frontend", 
            "public", 
            "logo.png"
        )
        
        if not os.path.exists(icon_path):
            logger.warning(f"Logo文件不存在: {icon_path}")
            current_dir_icon = os.path.join(os.getcwd(), "frontend", "public", "logo.png")
            if os.path.exists(current_dir_icon):
                logger.info(f"在当前目录找到Logo: {current_dir_icon}")
                icon_path = current_dir_icon

        webview.settings["ALLOW_DOWNLOADS"] = True
        WINDOW = webview.create_window(
            AppConfig.WIN_TITLE,
            AppConfig.WIN_HOME_PAGE,
            draggable=False,
            easy_drag=False,
            confirm_close=True,
            frameless=True,
            # fullscreen=True,
            maximized=True,
            js_api=JsApi(),
        )

        WINDOW.events.closed += self.onWinClosed
        WINDOW.events.closing += self.onWinClosing
        WINDOW.events.loaded += self.onWinLoaded

        webview.start(startFunc, WINDOW)

    def onWinClosed(self):
        logger.info("onWinClose")
        self.stopEvent.set()

    def onWinClosing(self):
        logger.info("onWinClosing")

    def onWinLoaded(self, window):
        logger.info("DOM is ready")

        window.events.loaded -= self.onWinLoaded
