from werkzeug.security import generate_password_hash, check_password_hash
from EarlyBird.common.libs.BaseModel import BaseModel
from sqlalchemy import (
    Integer,
    String,
    Boolean,
    Column,
    JSON,
    DateTime,
    Text,
)
import json
from datetime import datetime
from . import TABLE_PREFIX


def _format_date(d, f: str = "%Y-%m-%d"):
    if d is None or type(d) is not datetime:
        return ""
    return datetime.strftime(d, f)


class Admin(BaseModel):
    """管理员模型"""
    __tablename__ = f"{TABLE_PREFIX}admin"
    __table_args__ = {"comment": "管理员表"}

    username = Column(String(50), unique=True, nullable=False, comment="管理员账号")
    password_hash = Column(Text, nullable=False, comment="密码")
    realname = Column(String(100), comment="真实姓名")
    email = Column(String(100), comment="邮箱")
    phone = Column(String(20), comment="手机号")
    role = Column(String(20), default="admin", comment="角色")
    is_superadmin = Column(Boolean, default=False, comment="是否为超级管理员")
    permissions = Column(JSON, comment="权限列表")
    is_active = Column(Boolean, default=True, comment="是否激活")
    last_login_time = Column(DateTime, comment="最后登录时间")
    last_login_ip = Column(String(50), comment="最后登录IP")

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        if not self.permissions:
            self.permissions = self.get_default_permissions()

    def get_default_permissions(self):
        """获取默认权限"""
        # 超级管理员拥有所有权限
        if self.is_superadmin:
            return {
                'user': ['view', 'create', 'edit', 'delete', 'vip'],
                'thesis': ['view', 'delete', 'stats'],
                'system': ['config', 'logs', 'stats'],
                'admin': ['view', 'create', 'edit', 'delete']  # 管理员管理权限
            }
        # 普通管理员的默认权限
        return {
            'user': ['view', 'create', 'edit', 'delete', 'vip'],
            'thesis': ['view', 'delete', 'stats'],
            'system': ['config', 'logs', 'stats']
        }

    @property
    def password(self):
        raise AttributeError("password 字段不可读")

    @password.setter
    def password(self, raw_password):
        self.password_hash = generate_password_hash(raw_password)

    def check_password(self, raw_password):
        """验证密码"""
        return check_password_hash(self.password_hash, raw_password)

    def has_permission(self, resource, action):
        """检查是否有指定权限"""
        if not self.permissions:
            return False
        return action in self.permissions.get(resource, [])

    def get_info(self):
        """获取管理员信息"""
        return {
            "id": self.id,
            "username": self.username,
            "realname": self.realname,
            "email": self.email,
            "phone": self.phone,
            "role": self.role,
            "is_superadmin": self.is_superadmin,
            "permissions": self.permissions,
            "is_active": self.is_active,
            "last_login_time": _format_date(self.last_login_time, "%Y-%m-%d %H:%M:%S"),
            "last_login_ip": self.last_login_ip,
            "create_time": _format_date(self.create_time, "%Y-%m-%d %H:%M:%S"),
        }

    def update_login_info(self, ip_address):
        """更新登录信息"""
        self.last_login_time = datetime.now()
        self.last_login_ip = ip_address
        self.save()

    def __repr__(self):
        return json.dumps(self.get_info())


class AdminLog(BaseModel):
    """管理员操作日志模型"""
    __tablename__ = f"{TABLE_PREFIX}admin_log"
    __table_args__ = {"comment": "管理员操作日志表"}

    admin_id = Column(Integer, nullable=False, comment="管理员ID")
    admin_username = Column(String(50), comment="管理员账号")
    action = Column(String(100), comment="操作类型")
    resource = Column(String(100), comment="操作资源")
    resource_id = Column(Integer, comment="资源ID")
    description = Column(Text, comment="操作描述")
    ip_address = Column(String(50), comment="IP地址")
    user_agent = Column(String(500), comment="用户代理")
    request_data = Column(JSON, comment="请求数据")

    def get_info(self):
        """获取日志信息"""
        return {
            "id": self.id,
            "admin_id": self.admin_id,
            "admin_username": self.admin_username,
            "action": self.action,
            "resource": self.resource,
            "resource_id": self.resource_id,
            "description": self.description,
            "ip_address": self.ip_address,
            "user_agent": self.user_agent,
            "request_data": self.request_data,
            "create_time": _format_date(self.create_time, "%Y-%m-%d %H:%M:%S"),
        }

    @classmethod
    def create_log(cls, admin_id, admin_username, action, resource, resource_id=None, 
                   description="", ip_address="", user_agent="", request_data=None):
        """创建操作日志"""
        log = cls(
            admin_id=admin_id,
            admin_username=admin_username,
            action=action,
            resource=resource,
            resource_id=resource_id,
            description=description,
            ip_address=ip_address,
            user_agent=user_agent,
            request_data=request_data or {}
        )
        log.save()
        return log

    def __repr__(self):
        return json.dumps(self.get_info()) 