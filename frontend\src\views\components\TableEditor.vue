<template>
  <el-dialog
    title="智能表格"
    :visible.sync="visible"
    width="800px"
    append-to-body
    :before-close="handleClose"
  >
    <div class="table-editor">
      <!-- AI生成区域 -->
      <div class="ai-section">
        <h4>AI生成表格</h4>
        <el-form :model="aiConfig" label-width="80px" size="small">
          <el-form-item label="内容">
            <div v-if="context && context.content" class="auto-fill-tip">
              <el-alert
                title="✅ 已自动填充当前段落内容"
                type="success"
                :closable="false"
                show-icon
                size="small"
              >
                <template slot="default">
                  系统已自动读取当前段落内容，AI将根据内容自动判断合适的表格结构。您可以直接点击"生成表格"按钮，或修改内容后重新生成。
                </template>
              </el-alert>
            </div>
            <el-input
              v-model="aiConfig.content"
              type="textarea"
              :rows="4"
              placeholder="输入要生成表格的内容"
            ></el-input>
          </el-form-item>
          <el-form-item label="表格设置">
            <el-checkbox v-model="aiConfig.autoAdjust">自动调整行列数</el-checkbox>
            <div v-if="!aiConfig.autoAdjust" style="margin-top: 10px;">
              <el-row :gutter="10">
                <el-col :span="12">
                  <el-input-number
                    v-model="aiConfig.rows"
                    :min="1"
                    :max="10"
                    size="small"
                    placeholder="行数"
                  ></el-input-number>
                  <span style="margin-left: 5px;">行</span>
                </el-col>
                <el-col :span="12">
                  <el-input-number
                    v-model="aiConfig.columns"
                    :min="1"
                    :max="8"
                    size="small"
                    placeholder="列数"
                  ></el-input-number>
                  <span style="margin-left: 5px;">列</span>
                </el-col>
              </el-row>
            </div>
          </el-form-item>
          <el-form-item>
            <el-button 
              type="primary" 
              @click="generateTableContent" 
              :loading="generating"
            >
              生成表格
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 预览区域 -->
      <div v-if="aiPreviewHtml" class="preview-section">
        <h4>AI生成的表格预览</h4>
        <div v-html="aiPreviewHtml" class="preview-table"></div>
        <div class="preview-actions">
          <el-button type="success" @click="applyAiTable">直接插入</el-button>
          <el-button type="primary" @click="editAiTable">编辑表格</el-button>
          <el-button @click="regenerateTable">重新生成</el-button>
        </div>
      </div>

      <!-- 手动编辑区域 -->
      <div v-if="!aiPreviewHtml" class="manual-section">
        <h4>手动编辑表格</h4>
        <div class="table-actions">
          <el-button size="small" @click="addRow">添加行</el-button>
          <el-button size="small" @click="addColumn">添加列</el-button>
        </div>
        
        <div class="table-wrapper">
          <table class="editable-table">
            <thead>
              <tr>
                <th v-for="(col, colIndex) in tableData[0]" :key="colIndex">
                  <el-input
                    v-model="tableData[0][colIndex]"
                    size="small"
                    placeholder="表头"
                  ></el-input>
                  <el-button
                    size="mini"
                    type="danger"
                    @click="deleteColumn(colIndex)"
                  >删除</el-button>
                </th>
                <th>
                  <el-button size="small" @click="addColumn">+</el-button>
                </th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(row, rowIndex) in tableData.slice(1)" :key="rowIndex">
                <td v-for="(cell, colIndex) in row" :key="colIndex">
                  <el-input
                    v-model="tableData[rowIndex + 1][colIndex]"
                    size="small"
                    type="textarea"
                    :rows="2"
                    placeholder="内容"
                  ></el-input>
                </td>
                <td>
                  <el-button
                    size="mini"
                    type="danger"
                    @click="deleteRow(rowIndex + 1)"
                  >删除</el-button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
    
    <div slot="footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleConfirm">插入表格</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { generateTableContent } from '@/api/generate'

export default {
  name: 'TableEditor',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    context: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      visible: false,
      generating: false,
      tableConfig: {
        title: '',
        width: '100%'
      },
      aiConfig: {
        content: '',
        autoAdjust: true,  // 默认自动调整
        rows: 3,           // 默认行数
        columns: 3         // 默认列数
      },
      tableData: [
        ['列1', '列2', '列3'],
        ['内容1-1', '内容1-2', '内容1-3'],
        ['内容2-1', '内容2-2', '内容2-3']
      ],
      aiPreviewHtml: '',
      tableTitle: ''
    }
  },
  mounted() {
    // 如果有上下文内容，自动填充
    if (this.context && this.context.content) {
      this.aiConfig.content = this.context.content;
      this.$message.success('已自动填充段落内容，请点击"生成表格"按钮');
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.updatePreview();
        // 当对话框打开且有上下文内容时，自动填充
        if (this.context && this.context.content) {
          this.aiConfig.content = this.context.content;
          this.$message.success('已自动填充段落内容，请点击"生成表格"按钮');
        }
      }
    },
    context: {
      handler(newContext) {
        if (newContext && newContext.content && this.visible) {
          this.aiConfig.content = newContext.content;
          this.$message.success('已自动填充段落内容，请点击"生成表格"按钮');
        }
      },
      deep: true
    }
  },
  methods: {
    addRow() {
      const newRow = new Array(this.tableData[0].length).fill('')
      this.tableData.push(newRow)
    },
    
    addColumn() {
      this.tableData.forEach(row => row.push(''))
    },
    
    deleteRow(rowIndex) {
      this.tableData.splice(rowIndex, 1)
    },
    
    deleteColumn(colIndex) {
      for (let i = 0; i < this.tableData.length; i++) {
        this.tableData[i].splice(colIndex, 1)
      }
    },
    
    async generateTableContent() {
      if (!this.aiConfig.content.trim()) {
        this.$message.warning('请输入要生成表格的内容');
        return;
      }
      
      this.generating = true;
      
      try {
        const { generateTableContent } = await import('@/api/generate');
        const response = await generateTableContent({
          content: this.aiConfig.content,
          rows: this.aiConfig.autoAdjust ? 0 : this.aiConfig.rows,  // 自动调整时传递0
          columns: this.aiConfig.autoAdjust ? 0 : this.aiConfig.columns,  // 自动调整时传递0
          requirements: '请基于段落内容生成详细的数据表格，包含具体的数据和对比信息，表格要清晰易读。请根据内容自动判断合适的表格结构，不要限制行列数。'
        });
        
        if (response.data) {
          this.aiPreviewHtml = this.generateTableHtmlFromResponse(response.data);
          this.$message.success('AI表格生成完成！');
        } else {
          this.$message.error(response.message || '生成失败');
        }
      } catch (error) {
        console.error('生成表格失败:', error);
        this.$message.error('生成表格失败');
      } finally {
        this.generating = false;
      }
    },
    
    generateTableHtmlFromResponse(data) {
      let tableData = data.tableData || [];
      
      // 如果没有表格数据，尝试从其他字段获取
      if (!tableData || tableData.length === 0) {
        if (data.content) {
          // 尝试解析content字段
          tableData = this.parseContentToTableData(data.content);
        } else if (data.html) {
          // 直接返回HTML
          return data.html;
        }
      }
      
      // 生成表格HTML
      let html = '';
      
      // 检查是否有表格标题
      let title = '';
      
      // 优先使用AI返回的标题字段
      if (data.title) {
        title = data.title;
      } else if (this.tableTitle) {
        // 使用解析时保存的标题
        title = this.tableTitle;
      }
      
      // 添加表格标题
      if (title) {
        html += `<p style="text-align: center; font-weight: bold; margin-bottom: 10px; font-size: 16px;">${title}</p>`;
      }
      
      html += '<table style="width: 100%; border-collapse: collapse; margin: 10px 0;">';
      
      // 添加表头
      if (tableData[0] && tableData[0].length > 0) {
        html += '<thead><tr>';
        tableData[0].forEach(cell => {
          html += `<th style="padding: 8px; text-align: center; border: 1px solid #ddd; background-color: #f5f5f5;">${cell}</th>`;
        });
        html += '</tr></thead>';
      }
      
      // 添加表格内容
      html += '<tbody>';
      for (let i = 1; i < tableData.length; i++) {
        html += '<tr>';
        tableData[i].forEach(cell => {
          html += `<td style="padding: 8px; border: 1px solid #ddd;">${cell}</td>`;
        });
        html += '</tr>';
      }
      html += '</tbody></table>';
      
      return html;
    },
    
    // 解析内容为表格数据
    parseContentToTableData(content) {
      // 简单的解析逻辑，可以根据需要改进
      const lines = content.split('\n').filter(line => line.trim());
      const tableData = [];
      
      if (lines.length === 0) {
        return tableData;
      }
      
      // 检查第一行是否可能是标题（通常标题行比较短，或者包含特定关键词）
      const firstLine = lines[0];
      const firstLineCells = firstLine.split(/\t|,|;|\|/).map(cell => cell.trim());
      
      // 如果第一行看起来像标题（包含"研究"、"技术"、"方法"等关键词，或者比较短）
      const titleKeywords = ['研究', '技术', '方法', '分析', '测试', '检测', '改进', '措施', '成果'];
      const isLikelyTitle = titleKeywords.some(keyword => firstLine.includes(keyword)) || 
                           firstLine.length < 50 || 
                           firstLineCells.length <= 3;
      
      if (isLikelyTitle && lines.length > 1) {
        // 第一行是标题，从第二行开始是表格数据
        for (let i = 1; i < lines.length; i++) {
          const cells = lines[i].split(/\t|,|;|\|/).map(cell => cell.trim());
          if (cells.length > 0) {
            tableData.push(cells);
          }
        }
        
        // 将标题保存到data中
        this.tableTitle = firstLine;
      } else {
        // 没有标题，所有行都是表格数据
        lines.forEach(line => {
          const cells = line.split(/\t|,|;|\|/).map(cell => cell.trim());
          if (cells.length > 0) {
            tableData.push(cells);
          }
        });
      }
      
      return tableData;
    },
    
    applyAiTable() {
      if (this.aiPreviewHtml) {
        this.$emit('onConfirm', this.aiPreviewHtml);
        this.aiPreviewHtml = '';
        this.$message.success('表格已插入！如需删除，可在段落内容中手动编辑。');
      }
    },
    
    editAiTable() {
      // 将AI生成的HTML转换为表格数据
      this.convertHtmlToTableData(this.aiPreviewHtml);
      this.aiPreviewHtml = '';
      this.$message.success('已切换到编辑模式，您可以手动修改表格内容');
    },
    
    // 将HTML转换为表格数据
    convertHtmlToTableData(html) {
      // 创建临时DOM元素来解析HTML
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = html;
      
      const table = tempDiv.querySelector('table');
      if (table) {
        const rows = table.querySelectorAll('tr');
        const tableData = [];
        
        rows.forEach(row => {
          const cells = row.querySelectorAll('th, td');
          const rowData = [];
          cells.forEach(cell => {
            rowData.push(cell.textContent.trim());
          });
          tableData.push(rowData);
        });
        
        if (tableData.length > 0) {
          this.tableData = tableData;
          this.$message.success('AI生成的表格已加载到编辑器中');
        } else {
          this.$message.warning('无法解析表格数据，请重新生成');
        }
      } else {
        this.$message.error('未找到表格元素');
      }
    },
    
    regenerateTable() {
      this.aiPreviewHtml = '';
      this.generateTableContent();
    },
    
    generateTableHtml() {
      let html = '<table style="width: 100%; border-collapse: collapse; margin: 10px 0;">';
      
      // 添加表头
      if (this.tableData[0] && this.tableData[0].length > 0) {
        html += '<thead><tr>';
        this.tableData[0].forEach(cell => {
          html += `<th style="padding: 8px; text-align: center; border: 1px solid #ddd; background-color: #f5f5f5;">${cell}</th>`;
        });
        html += '</tr></thead>';
      }
      
      // 添加表格内容
      html += '<tbody>';
      for (let i = 1; i < this.tableData.length; i++) {
        html += '<tr>';
        this.tableData[i].forEach(cell => {
          html += `<td style="padding: 8px; border: 1px solid #ddd;">${cell}</td>`;
        });
        html += '</tr>';
      }
      html += '</tbody></table>';
      
      return html;
    },
    
    handleConfirm() {
      const tableHtml = this.aiPreviewHtml || this.generateTableHtml();
      this.$emit('onConfirm', tableHtml);
      this.$message.success('表格已插入！如需删除，可在段落内容中手动编辑。');
      this.handleClose();
    },
    
    handleClose() {
      this.visible = false;
      this.aiPreviewHtml = '';
      this.tableTitle = '';
      this.tableData = [
        ['列1', '列2', '列3'],
        ['内容1-1', '内容1-2', '内容1-3'],
        ['内容2-1', '内容2-2', '内容2-3']
      ];
      this.aiConfig.content = '';
    },
    
    updatePreview() {
      // 简单的预览更新
    }
  }
}
</script>

<style lang="scss" scoped>
.table-editor {
  .ai-section {
    margin-bottom: 20px;
    padding: 15px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    
    h4 {
      margin: 0 0 15px 0;
      color: #333;
    }
    
    .auto-fill-tip {
      margin-bottom: 10px;
    }
  }

  .preview-section {
    margin-bottom: 20px;
    padding: 15px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    
    h4 {
      margin: 0 0 15px 0;
      color: #333;
    }
    
    .preview-table {
      margin-bottom: 15px;
    }
    
    .preview-actions {
      display: flex;
      gap: 10px;
    }
  }

  .manual-section {
    padding: 15px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    
    h4 {
      margin: 0 0 15px 0;
      color: #333;
    }
    
    .table-actions {
      margin-bottom: 15px;
      display: flex;
      gap: 10px;
    }
  }

  .table-wrapper {
    overflow-x: auto;
    
    .editable-table {
      width: 100%;
      border-collapse: collapse;
      
      th, td {
        border: 1px solid #dcdfe6;
        padding: 8px;
        vertical-align: top;
      }
      
      th {
        background-color: #f5f7fa;
        font-weight: 500;
      }
    }
  }
}
</style> 