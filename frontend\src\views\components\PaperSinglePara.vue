<!--  -->
<template>
  <div class="page">
    <div class="page_title">{{ title }}</div>
    <div class="page_notice" v-if="content != '' && content != undefined">{{ notice }}(下载word文件中无此警告)</div>

    <div class="page_body" v-if="!isEditDialogOpen">{{ content }}</div>
    <div class="page_edit-form" v-if="isEditDialogOpen">
      <el-form>
        <el-form-item label="内容修改">
          <el-input type="textarea" v-model="editableContent"></el-input>
        </el-form-item>

        <el-form-item>
          <el-button @click="isEditDialogOpen = false">取消</el-button>

          <el-button type="primary" @click="save">保存</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="paragraph_edit_toos" v-if="!isEditDialogOpen">
      <el-tooltip class="item" effect="dark" content="AI生成" placement="top">
        <i class="el-icon-refresh-left" @click="reGenerate"></i>
      </el-tooltip>
      <el-tooltip class="item" effect="dark" content="手动编辑" placement="top">
        <i class="el-icon-edit" @click="isEditDialogOpen = true"></i>
      </el-tooltip>
    </div>
  </div>
</template>
<style scoped lang="scss">
.page {
  background-color: #f6f6f6;
  border-radius: 5px;
  margin-top: 20px;

  .page_title {
    padding: 20px;
    font-weight: bold;
    font-family: "黑体";
    text-align: center;
    font-size: 20px;
  }
  .page_notice {
    padding: 20px;
    margin: 20px;
    border: 5px solid red;
    background: red;
    color: #fff;
    font-size: 20px;
    font-weight: bold;
  }

  .page_body {
    line-height: 30px;
    color: #444;
    text-indent: 2em;
    margin: 0;
    padding: 20px 20px;
    white-space: pre-wrap;
  }

  .page_edit-form {
    padding: 20px 40px;
  }
}

.paragraph_edit_toos {
  color: #409eff;
  padding: 12px 20px;
  padding-top: 0;

  i {
    width: 40px;
    height: 40px;
    border-radius: 6px;
    text-align: center;
    line-height: 38px;
    font-size: 20px;
    font-weight: bold;
    background: #fff;
    cursor: pointer;
    margin-right: 20px;
  }

  i:hover {
    background-color: #fff;
  }
}

::v-deep .el-textarea__inner {
  min-height: 300px !important;
}
</style>
<script>
export default {
  props: {
    title: String,
    notice: String,
    content: {
      type: String,
      // default: '拉布拉多海，位于加拿大东海岸外，以其低温、高盐度的海水特征而闻名，与北大西洋暖流交汇，形成显著的密度梯度。这种独特的海洋环境促使海水下沉，进而驱动深层洋流的流动，进而影响AMOC的强度和稳定性。拉布拉多海的冰川融化、海洋生物活动，以及季节性和长期气候变化，都可能引发局部海洋环境的微小扰动，进而放大或削弱AMOC的效应。'
    },
  },
  data() {
    return {
      isEditDialogOpen: false,
      editableContent: this.content,
    };
  },
  methods: {
    save() {
      this.$emit("onSave", this.editableContent);
    },
    reGenerate() {
      this.$emit("onReGenerate");
    },
  },
  watch: {
    content(n, o) {
      this.editableContent = n;
      this.isEditDialogOpen = false;
    },
  },
  created() {},
};
</script>
