import json
import logging
import requests
from .. import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from EarlyBird.config.config import AppConfig
from EarlyBird.common import Result

# deepseek-chat 模型对应 DeepSeek-V3；deepseek-reasoner 模型对应 DeepSeek-R1。

LOGGER = logging.getLogger(__name__)

class DeepSeekR1(AiAdapter):

    def __init__(self) -> None:
        super().__init__()
        self.modelName = "DeepSeek-R1"

    def query(self, query: AiQuery) -> AiQueryResult:
        try:
            API_KEY = AppConfig.DEEPSEEK_API_KEY
            if not API_KEY or API_KEY == "your_deepseek_api_key_here":
                return AiQueryResult(isValid=False, errMessage="请在配置文件中设置 DeepSeek API Key")
            
            LOGGER.info(f"使用 DeepSeek API Key: {API_KEY[:8]}...")
            LOGGER.info(f"发出请求：{query.userMessage}")

            url = AppConfig.DEEPSEEK_API_URL + "/v1/chat/completions"  # 修正API路径
            headers = {
                "Authorization": f"Bearer {API_KEY}",
                "Content-Type": "application/json",
            }
            payload = {
                "model": "deepseek-chat",  # 使用正确的模型名称
                "messages": [{"role": "user", "content": query.userMessage}],
                "temperature": 0.7,  # 降低温度以获得更稳定的输出
                "max_tokens": 2000,  # 添加最大token限制
            }

            LOGGER.info(f"请求URL: {url}")
            LOGGER.info(f"请求头: {headers}")
            LOGGER.info(f"请求体: {payload}")

            requests.packages.urllib3.disable_warnings()
            r = requests.post(url=url, headers=headers, json=payload, verify=False)
            
            LOGGER.info(f"响应状态码: {r.status_code}")
            LOGGER.info(f"响应内容: {r.text}")

            if r.status_code != 200:
                error_msg = f"API请求失败: HTTP {r.status_code}"
                try:
                    error_json = r.json()
                    if "error" in error_json:
                        error_msg = f"{error_msg} - {error_json['error'].get('message', '')}"
                except:
                    error_msg = f"{error_msg} - {r.text}"
                return AiQueryResult(isValid=False, errMessage=error_msg)

            try:
                jsonResponse = r.json()  # 使用r.json()而不是json.loads(r.text)
                return AiQueryResult(
                    text=jsonResponse["choices"][0]["message"]["content"],
                    totalToken=jsonResponse["usage"]["total_tokens"],
                )
            except (KeyError, json.JSONDecodeError) as e:
                LOGGER.error(f"解析响应失败: {str(e)}")
                return AiQueryResult(isValid=False, errMessage=f"解析API响应失败: {str(e)}")
                
        except requests.exceptions.RequestException as e:
            LOGGER.error(f"网络请求错误: {str(e)}")
            return AiQueryResult(isValid=False, errMessage=f"网络请求失败: {str(e)}")
        except Exception as e:
            LOGGER.exception("DeepSeek请求异常")
            return AiQueryResult(isValid=False, errMessage=f"请求异常: {str(e)}")
