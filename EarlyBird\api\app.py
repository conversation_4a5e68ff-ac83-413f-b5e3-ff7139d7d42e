import os
import logging
from flask import Flask
from flask_cors import CORS
from flask_migrate import Migrate
import secrets
from datetime import timed<PERSON>ta

logger = logging.getLogger(__name__)

def register_bp(app):
    from EarlyBird.ExtendRegister.bp_register import register_bp as register_blueprints
    register_blueprints(app)

def create_app():
    try:
        # Static files are in EarlyBird/resources/ui
        current_dir = os.path.dirname(os.path.abspath(__file__))  # api目录
        EarlyBird_dir = os.path.dirname(current_dir)  # EarlyBird目录
        staticDir = os.path.join(EarlyBird_dir, "resources", "ui")
        
        logger.info(f"flask current_dir: {current_dir}")
        logger.info(f"flask EarlyBird_dir: {EarlyBird_dir}")
        logger.info(f"flask static_dir : {staticDir}")

        if not os.path.exists(staticDir):
            logger.error(f"Static directory does not exist: {staticDir}")
        else:
            logger.info(f"Static directory found: {staticDir}")
            # 列出静态目录中的文件
            try:
                files = os.listdir(staticDir)
                logger.info(f"Static directory contents: {files}")
            except Exception as e:
                logger.error(f"Error listing static directory: {e}")

        app = Flask(__name__, static_folder=staticDir, static_url_path='')
        app.config["SEND_FILE_MAX_AGE_DEFAULT"] = 1
        # 使用固定的SECRET_KEY，确保重启后session不失效
        app.config["SECRET_KEY"] = "KeAEVZPYBGCcs7aW-zaoniao-paper-2024"
        app.config["SESSION_COOKIE_HTTPONLY"] = True
        app.config["SESSION_COOKIE_SECURE"] = False  # 开发环境设为False，生产环境设为True
        app.config["PERMANENT_SESSION_LIFETIME"] = timedelta(days=7)  # session有效期7天
        
        CORS(app, supports_credentials=True)

        # 注册配置
        from EarlyBird.ExtendRegister.conf_register import register_config
        register_config(app)

        # 注册数据库
        from EarlyBird.ExtendRegister.db_register import register_db, db
        register_db(app)
        
        # 初始化Flask-Migrate
        migrate = Migrate(app, db)

        # 注册钩子函数
        from EarlyBird.ExtendRegister.hook_register import register_hook
        register_hook(app)

        # 注册异常处理
        from EarlyBird.ExtendRegister.excep_register import register_excep
        register_excep(app)

        # 注册路由（包含管理员API）
        register_bp(app)

        logger.info("init flaskApp done")
        return app
    except Exception as e:
        logger.exception(e)
        return None


if __name__ == "__main__":
    os.environ["FLASK_ENV"] = "development"
    logger.info(os.environ.get("FLASK_ENV"))

    app = create_app()
    if app:
        # 使用配置文件中的端口和主机
        from EarlyBird.config.config import AppConfig
        host = AppConfig.RUN_HOST
        port = AppConfig.RUN_PORT
        logger.info(f"启动服务: {host}:{port}")
        app.run(host, port, debug=True)
    else:
        logger.error("Flask应用创建失败")
