"use strict";(self["webpackChunkopen_essay_gui"]=self["webpackChunkopen_essay_gui"]||[]).push([[632],{6632:(e,t,s)=>{s.r(t),s.d(t,{default:()=>d});var a=function(){var e=this,t=e._self._c;return t("div",{staticClass:"admin-accounts-container"},[t("div",{staticClass:"page-header"},[t("div",{staticClass:"header-content"},[e._m(0),t("div",{staticClass:"header-actions"},[t("el-button",{attrs:{type:"primary",icon:"el-icon-plus",size:"medium"},on:{click:e.handleCreate}},[e._v(" 新增管理员 ")]),t("el-button",{attrs:{type:"success",icon:"el-icon-refresh",size:"medium"},on:{click:e.fetchData}},[e._v(" 刷新数据 ")])],1)])]),t("el-card",{staticClass:"search-card",attrs:{shadow:"hover"}},[t("div",{staticClass:"search-header"},[t("h3",{staticClass:"search-title"},[t("i",{staticClass:"el-icon-search"}),e._v(" 搜索筛选 ")])]),t("el-form",{staticClass:"search-form",attrs:{inline:!0,model:e.listQuery}},[t("el-form-item",{attrs:{label:"关键词"}},[t("el-input",{staticClass:"search-input",attrs:{placeholder:"用户名/姓名/邮箱",clearable:"","prefix-icon":"el-icon-search"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleFilter.apply(null,arguments)}},model:{value:e.listQuery.keyword,callback:function(t){e.$set(e.listQuery,"keyword",t)},expression:"listQuery.keyword"}})],1),t("el-form-item",{staticClass:"search-buttons"},[t("el-button",{staticClass:"search-btn",attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.handleFilter}},[e._v(" 搜索 ")]),t("el-button",{staticClass:"reset-btn",attrs:{icon:"el-icon-refresh"},on:{click:e.resetSearch}},[e._v(" 重置 ")])],1)],1)],1),t("el-card",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"table-card",attrs:{shadow:"hover"}},[t("div",{staticClass:"table-header"},[t("h3",{staticClass:"table-title"},[t("i",{staticClass:"el-icon-document"}),e._v(" 管理员列表 ")]),t("div",{staticClass:"table-actions"},[t("el-button",{attrs:{size:"small",icon:"el-icon-download"},on:{click:e.exportData}},[e._v(" 导出数据 ")])],1)]),e.error?t("el-alert",{staticClass:"error-alert",attrs:{title:"数据加载失败",type:"error",description:e.error,"show-icon":"",closable:!1}},[t("el-button",{staticClass:"retry-btn",attrs:{size:"small",type:"primary"},on:{click:e.fetchData}},[e._v("重试")])],1):e._e(),e.error?e._e():t("el-table",{staticStyle:{width:"100%"},attrs:{data:e.list,border:"",stripe:"","header-cell-style":{background:"#f5f7fa",color:"#606266"}}},[t("el-table-column",{attrs:{prop:"id",label:"ID",width:"80",align:"center"}}),t("el-table-column",{attrs:{prop:"username",label:"用户名","min-width":"120"}}),t("el-table-column",{attrs:{prop:"realname",label:"姓名","min-width":"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.realname||"-")+" ")]}}],null,!1,1471145037)}),t("el-table-column",{attrs:{prop:"email",label:"邮箱","min-width":"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.email||"-")+" ")]}}],null,!1,2719091484)}),t("el-table-column",{attrs:{label:"角色",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(s){return[s.row.is_superadmin?t("el-tag",{attrs:{type:"success",effect:"dark"}},[e._v(" 超级管理员 ")]):t("el-tag",{attrs:{type:"info",effect:"plain"}},[e._v(" 管理员 ")])]}}],null,!1,3487911326)}),t("el-table-column",{attrs:{label:"状态",width:"100",align:"center"},scopedSlots:e._u([{key:"default",fn:function(s){return[s.row.is_active?t("el-tag",{attrs:{type:"success"}},[e._v("正常")]):t("el-tag",{attrs:{type:"danger"}},[e._v("禁用")])]}}],null,!1,1047381183)}),t("el-table-column",{attrs:{prop:"created_at",label:"创建时间",width:"160",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.created_at||"-")+" ")]}}],null,!1,1473701626)}),t("el-table-column",{attrs:{label:"操作",width:"280",fixed:"right",align:"center"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-button",{attrs:{size:"mini",type:"primary",icon:"el-icon-edit"},on:{click:function(t){return e.handleUpdate(s.row)}}},[e._v(" 编辑 ")]),t("el-button",{attrs:{size:"mini",type:"warning",icon:"el-icon-key"},on:{click:function(t){return e.handleResetPassword(s.row)}}},[e._v(" 重置密码 ")]),t("el-button",{attrs:{size:"mini",type:"danger",icon:"el-icon-delete",disabled:s.row.is_superadmin},on:{click:function(t){return e.handleDelete(s.row)}}},[e._v(" 删除 ")])]}}],null,!1,2548645454)}),t("template",{slot:"empty"},[t("div",{staticClass:"empty-data"},[t("i",{staticClass:"el-icon-document"}),t("p",[e._v("暂无数据")])])])],2),t("div",{staticClass:"pagination-container"},[t("el-pagination",{attrs:{background:"",layout:"total, sizes, prev, pager, next, jumper","current-page":e.listQuery.page,"page-sizes":[10,20,50,100],"page-size":e.listQuery.size,total:e.total},on:{"update:currentPage":function(t){return e.$set(e.listQuery,"page",t)},"update:current-page":function(t){return e.$set(e.listQuery,"page",t)},"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1),t("el-dialog",{attrs:{title:"重置密码",visible:e.passwordDialogVisible,width:"400px","close-on-click-modal":!1},on:{"update:visible":function(t){e.passwordDialogVisible=t}}},[t("el-form",{ref:"passwordForm",attrs:{model:e.passwordForm,rules:e.passwordRules,"label-width":"100px"}},[t("el-form-item",{attrs:{label:"新密码",prop:"new_password"}},[t("el-input",{attrs:{type:"password","show-password":"",placeholder:"请输入新密码"},model:{value:e.passwordForm.new_password,callback:function(t){e.$set(e.passwordForm,"new_password",t)},expression:"passwordForm.new_password"}})],1),t("el-form-item",{attrs:{label:"确认密码",prop:"confirm_password"}},[t("el-input",{attrs:{type:"password","show-password":"",placeholder:"请再次输入密码"},model:{value:e.passwordForm.confirm_password,callback:function(t){e.$set(e.passwordForm,"confirm_password",t)},expression:"passwordForm.confirm_password"}})],1)],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.passwordDialogVisible=!1}}},[e._v("取 消")]),t("el-button",{attrs:{type:"primary",loading:e.passwordLoading},on:{click:e.confirmResetPassword}},[e._v(" 确 定 ")])],1)],1),t("el-dialog",{attrs:{title:"删除确认",visible:e.deleteDialogVisible,width:"400px","close-on-click-modal":!1},on:{"update:visible":function(t){e.deleteDialogVisible=t}}},[t("div",{staticClass:"delete-confirm-content"},[t("i",{staticClass:"el-icon-warning warning-icon"}),t("div",{staticClass:"confirm-text"},[t("p",[e._v('确定要删除管理员 "'+e._s(e.currentAdmin?e.currentAdmin.username:"")+'" 吗？')]),t("p",{staticClass:"warning-text"},[e._v("此操作不可逆，请谨慎操作！")])])]),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.deleteDialogVisible=!1}}},[e._v("取 消")]),t("el-button",{attrs:{type:"danger",loading:e.deleteLoading},on:{click:e.confirmDelete}},[e._v(" 确认删除 ")])],1)])],1)},i=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"header-left"},[t("div",{staticClass:"title-section"},[t("h1",{staticClass:"page-title"},[t("i",{staticClass:"el-icon-user"}),e._v(" 管理员账户 ")]),t("p",{staticClass:"page-subtitle"},[e._v("管理系统中的管理员账户和权限")])])])}],r=s(9192);const l={name:"AdminAccountsList",data(){const e=(e,t,s)=>{""===t?s(new Error("请输入密码")):t.length<6?s(new Error("密码长度不能少于6个字符")):(""!==this.passwordForm.confirm_password&&this.$refs.passwordForm.validateField("confirm_password"),s())},t=(e,t,s)=>{""===t?s(new Error("请再次输入密码")):t!==this.passwordForm.new_password?s(new Error("两次输入密码不一致")):s()};return{loading:!1,error:null,listQuery:{page:1,size:10,keyword:""},list:[],total:0,passwordDialogVisible:!1,passwordLoading:!1,passwordForm:{new_password:"",confirm_password:""},passwordRules:{new_password:[{validator:e,trigger:"blur"}],confirm_password:[{validator:t,trigger:"blur"}]},currentAdmin:null,deleteDialogVisible:!1,deleteLoading:!1}},computed:{totalPages(){return Math.ceil(this.total/this.listQuery.size)||1}},created(){this.fetchData()},methods:{fetchData(){this.loading=!0,this.error=null,console.log("开始获取管理员列表数据..."),r.jC.getList(this.listQuery).then((e=>{this.loading=!1,console.log("管理员列表API响应:",e),e.success?(this.list=e.data.list||[],this.total=e.data.total||0,console.log(`管理员列表数据加载成功，共${this.total}条记录，当前显示${this.list.length}条`)):(this.error=e.message||"获取管理员列表失败",console.error("管理员列表API业务错误:",this.error),e.data&&e.data.list&&(this.list=e.data.list,this.total=e.data.total||0,this.error=null))})).catch((e=>{this.loading=!1,this.error="网络错误，请稍后重试",console.error("获取管理员列表失败:",e),this.useMockData()}))},useMockData(){console.log("使用模拟数据");const e=[{id:1,username:"admin",realname:"系统管理员",email:"<EMAIL>",phone:"13800138000",is_superadmin:!0,is_active:!0,created_at:"2025-06-21 08:59:17",last_login_at:"2025-06-25 15:30:00"},{id:2,username:"ceshiadmin",realname:"测试管理员",email:"<EMAIL>",phone:"***********",is_superadmin:!1,is_active:!0,created_at:"2025-06-25 11:26:30",last_login_at:null}];this.list=e,this.total=e.length,this.error=null},handleFilter(){this.listQuery.page=1,this.fetchData()},resetSearch(){this.listQuery.keyword="",this.handleFilter()},handleSizeChange(e){this.listQuery.size=e,this.fetchData()},handleCurrentChange(e){this.listQuery.page=e,this.fetchData()},handleCreate(){this.$router.push({name:"AdminAccountsCreate"})},handleUpdate(e){this.$router.push({name:"AdminAccountsEdit",params:{id:e.id}})},handleResetPassword(e){this.currentAdmin=e,this.passwordForm.new_password="",this.passwordForm.confirm_password="",this.passwordDialogVisible=!0,this.$refs.passwordForm&&this.$refs.passwordForm.resetFields()},confirmResetPassword(){this.$refs.passwordForm.validate((e=>{e&&(this.passwordLoading=!0,r.jC.resetPassword(this.currentAdmin.id,{new_password:this.passwordForm.new_password}).then((e=>{this.passwordLoading=!1,e.success?(this.passwordDialogVisible=!1,this.$message({type:"success",message:"密码重置成功"})):this.$message.error(e.message||"密码重置失败")})).catch((e=>{this.passwordLoading=!1,this.$message.error("网络错误，请稍后重试"),console.error("重置密码失败:",e)})))}))},handleDelete(e){e.is_superadmin?this.$message.warning("不能删除超级管理员"):(this.currentAdmin=e,this.deleteDialogVisible=!0)},confirmDelete(){this.deleteLoading=!0,r.jC.delete(this.currentAdmin.id).then((e=>{this.deleteLoading=!1,e.success?(this.deleteDialogVisible=!1,this.$message({type:"success",message:"删除成功"}),this.fetchData()):this.$message.error(e.message||"删除失败")})).catch((e=>{this.deleteLoading=!1,this.$message.error("网络错误，请稍后重试"),console.error("删除管理员失败:",e)}))},exportData(){this.$message({type:"info",message:"导出功能开发中"})}}},o=l;var n=s(1656),c=(0,n.A)(o,a,i,!1,null,"df1a2c30",null);const d=c.exports}}]);