#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
作者名：EarlyBird
作者邮箱：<EMAIL>
官方网址：https://blog.zaoniao.vip

后台管理微信支付配置API接口
"""

from flask import Blueprint, request, jsonify
from EarlyBird.common.libs.api_result import ApiResult
from EarlyBird.model.wechat_pay_config import WeChatPayConfig
from EarlyBird.common.database import db
from .auth import admin_auth_required
import logging
import traceback
import json

bp = Blueprint('admin_wechat_pay_config', __name__, url_prefix='/wechat_pay_config')
logger = logging.getLogger(__name__)

@bp.route('/current', methods=['GET'])
@admin_auth_required
def get_current_config():
    """获取当前启用的微信支付配置"""
    try:
        admin = request.admin
        logger.info(f"管理员 {admin.username}(ID:{admin.id}) 正在获取微信支付配置 - 请求路径: {request.path}")
        
        # 检查权限
        if not admin.has_permission('system', 'config'):
            logger.warning(f"管理员 {admin.username}(ID:{admin.id}) 没有查看微信支付配置的权限")
            return ApiResult.error("没有查看微信支付配置的权限")
        
        # 查询当前启用的配置
        config = WeChatPayConfig.query.filter_by(is_enabled=True).first()
        
        if not config:
            logger.warning("未找到启用的微信支付配置，返回默认配置模板")
            # 返回符合前端期望的默认配置模板
            default_config = {
                'id': None,
                'name': '早鸟论文微信支付',
                'app_id': 'wxfb9c26d95fcd8b21',
                'mch_id': '1636691881',
                'api_v3_key': 'yx159357QWERasdfZXCVtgbYHNujmIKO',
                'serial_no': '56D0EF3D9FFBAF91F2E8C477C732449D503A2290',
                'private_key_path': '/static/cert/privateKey.txt',
                'notify_url': 'https://blog.zaoniao.vip/api/pay/notify',
                'is_enabled': True,
                'is_test_mode': False,
                'remark': '早鸟论文系统微信支付配置，支持论文生成服务付费'
            }
            logger.info(f"返回默认配置: {json.dumps(default_config, ensure_ascii=False)}")
            return ApiResult.success(default_config, "未找到现有配置，返回默认模板")
        
        config_dict = config.to_dict()
        logger.info(f"获取当前启用的微信支付配置成功 - 配置ID: {config.id}")
        logger.info(f"返回数据: {json.dumps(config_dict, ensure_ascii=False)}")
        
        return ApiResult.success(config_dict, "获取当前配置成功")
        
    except Exception as e:
        logger.exception(f"获取当前启用的微信支付配置失败: {str(e)}")
        tb = traceback.format_exc()
        logger.error(f"详细错误信息: {tb}")
        return ApiResult.error(f"获取当前配置失败: {str(e)}")

@bp.route('/current', methods=['POST'])
@admin_auth_required
def save_current_config():
    """保存微信支付配置"""
    try:
        admin = request.admin
        data = request.get_json()
        logger.info(f"管理员 {admin.username}(ID:{admin.id}) 正在保存微信支付配置: {data}")
        
        # 检查权限
        if not admin.has_permission('system', 'config'):
            logger.warning(f"管理员 {admin.username}(ID:{admin.id}) 没有修改微信支付配置的权限")
            return ApiResult.error("没有修改微信支付配置的权限")
        
        if not data:
            return ApiResult.error("请求数据为空")
        
        # 验证必填字段，支持两种字段名格式
        required_fields = {
            'app_id': ['app_id', 'appid'],
            'mch_id': ['mch_id', 'mchid'], 
            'api_v3_key': ['api_v3_key'],
            'serial_no': ['serial_no'],
            'private_key_path': ['private_key_path'],
            'notify_url': ['notify_url']
        }
        
        # 标准化字段名，优先使用新格式
        normalized_data = {}
        for std_field, possible_names in required_fields.items():
            found_value = None
            for name in possible_names:
                if data.get(name):
                    found_value = data[name]
                    break
            
            if not found_value:
                logger.warning(f"缺少必填字段: {std_field} (也可以是 {possible_names})")
                return ApiResult.error(f"缺少必填字段: {std_field}")
            
            normalized_data[std_field] = found_value
        
        # 查找或创建配置
        config = WeChatPayConfig.query.filter_by(is_enabled=True).first()
        
        if config:
            # 更新现有配置，使用模型的实际字段名
            config.name = data.get('name', config.name)
            config.appid = normalized_data['app_id']  # 模型字段是appid
            config.mchid = normalized_data['mch_id']  # 模型字段是mchid
            config.api_v3_key = normalized_data['api_v3_key']
            config.serial_no = normalized_data['serial_no']
            config.private_key_path = normalized_data['private_key_path']
            config.notify_url = normalized_data['notify_url']
            config.is_enabled = data.get('is_enabled', True)
            config.is_test_mode = data.get('is_test_mode', False)
            config.remark = data.get('remark', config.remark)
            
            logger.info(f"更新微信支付配置 - 配置ID: {config.id}")
        else:
            # 创建新配置，使用模型的实际字段名
            config = WeChatPayConfig(
                name=data.get('name', '早鸟论文微信支付'),
                appid=normalized_data['app_id'],    # 模型字段是appid
                mchid=normalized_data['mch_id'],    # 模型字段是mchid
                api_v3_key=normalized_data['api_v3_key'],
                serial_no=normalized_data['serial_no'],
                private_key_path=normalized_data['private_key_path'],
                notify_url=normalized_data['notify_url'],
                is_enabled=data.get('is_enabled', True),
                is_test_mode=data.get('is_test_mode', False),
                remark=data.get('remark', '早鸟论文系统微信支付配置')
            )
            db.session.add(config)
            logger.info(f"创建新的微信支付配置")
        
        db.session.commit()
        
        logger.info(f"保存微信支付配置成功 - 配置ID: {config.id}")
        return ApiResult.success(config.to_dict(), "保存配置成功")
        
    except Exception as e:
        db.session.rollback()
        logger.exception(f"保存微信支付配置失败: {str(e)}")
        return ApiResult.error(f"保存配置失败: {str(e)}")

@bp.route('/list', methods=['GET'])
@admin_auth_required
def get_config_list():
    """获取微信支付配置列表"""
    try:
        logger.info(f"获取微信支付配置列表 - 请求参数: {request.args}")
        page = request.args.get('page', 1, type=int)
        size = request.args.get('size', 10, type=int)
        
        # 分页查询
        pagination = WeChatPayConfig.query.paginate(
            page=page, 
            per_page=size, 
            error_out=False
        )
        
        configs = [config.to_dict() for config in pagination.items]
        
        result = ApiResult.success({
            'list': configs,
            'total': pagination.total,
            'page': page,
            'size': size,
            'pages': pagination.pages
        }, "获取配置列表成功")
        
        logger.info(f"获取微信支付配置列表成功 - 返回: {len(configs)}条记录")
        return result
        
    except Exception as e:
        logger.exception(f"获取微信支付配置列表失败: {str(e)}")
        return ApiResult.error(f"获取配置列表失败: {str(e)}")

@bp.route('/detail/<int:config_id>', methods=['GET'])
def get_config_detail(config_id):
    """获取微信支付配置详情"""
    try:
        logger.info(f"获取微信支付配置详情 - 配置ID: {config_id}")
        config = WeChatPayConfig.query.get(config_id)
        if not config:
            logger.warning(f"配置不存在 - 配置ID: {config_id}")
            return ApiResult.error("配置不存在")
        
        result = ApiResult.success(config.to_dict(), "获取配置详情成功")
        logger.info(f"获取微信支付配置详情成功 - 配置ID: {config_id}")
        return result
        
    except Exception as e:
        logger.exception(f"获取微信支付配置详情失败: {str(e)}")
        return ApiResult.error(f"获取配置详情失败: {str(e)}")

@bp.route('/create', methods=['POST'])
def create_config():
    """创建微信支付配置"""
    try:
        data = request.get_json()
        logger.info(f"创建微信支付配置 - 请求数据: {json.dumps(data, ensure_ascii=False)}")
        
        if not data:
            logger.warning("请求数据为空")
            return ApiResult.error("请求数据为空")
        
        # 验证必填字段
        required_fields = ['appid', 'mchid', 'api_v3_key', 'serial_no', 'private_key_path', 'notify_url']
        for field in required_fields:
            if not data.get(field):
                logger.warning(f"缺少必填字段: {field}")
                return ApiResult.error(f"缺少必填字段: {field}")
        
        # 检查是否已存在配置
        existing_config = WeChatPayConfig.query.filter_by(is_enabled=True).first()
        if existing_config:
            logger.warning(f"已存在启用的配置 - 配置ID: {existing_config.id}")
            return ApiResult.error("已存在启用的配置，请先禁用现有配置")
        
        # 创建新配置
        config = WeChatPayConfig(
            appid=data['appid'],
            mchid=data['mchid'],
            api_v3_key=data['api_v3_key'],
            serial_no=data['serial_no'],
            private_key_path=data['private_key_path'],
            platform_cert_path=data.get('platform_cert_path'),
            notify_url=data['notify_url'],
            is_sandbox=data.get('is_sandbox', False),
            sandbox_appid=data.get('sandbox_appid'),
            sandbox_mchid=data.get('sandbox_mchid'),
            sandbox_api_v3_key=data.get('sandbox_api_v3_key'),
            sandbox_serial_no=data.get('sandbox_serial_no'),
            sandbox_private_key_path=data.get('sandbox_private_key_path'),
            is_enabled=data.get('is_enabled', True),
            is_test_mode=data.get('is_test_mode', False),
            remark=data.get('remark')
        )
        
        db.session.add(config)
        db.session.commit()
        
        logger.info(f"创建微信支付配置成功: {config.id}")
        return ApiResult.success(config.to_dict(), "创建配置成功")
        
    except Exception as e:
        db.session.rollback()
        logger.exception(f"创建微信支付配置失败: {str(e)}")
        return ApiResult.error(f"创建配置失败: {str(e)}")

@bp.route('/update', methods=['POST'])
@admin_auth_required
def update_config_general():
    """通用更新配置接口（无需ID，更新当前启用的配置或创建新配置）"""
    try:
        admin = request.admin
        logger.info(f"管理员 {admin.username}(ID:{admin.id}) 正在更新微信支付配置 - 请求路径: {request.path}")

        # 检查权限
        if not admin.has_permission('system', 'config'):
            logger.warning(f"管理员 {admin.username}(ID:{admin.id}) 没有编辑微信支付配置的权限")
            return ApiResult.error("没有编辑微信支付配置的权限")

        data = request.get_json()
        if not data:
            return ApiResult.error("请求数据不能为空")
        
        logger.info(f"接收到的数据: {json.dumps(data, ensure_ascii=False)}")
        
        # 查找当前启用的配置
        config = WeChatPayConfig.query.filter_by(is_enabled=True).first()
        
        if config:
            # 更新现有配置
            logger.info(f"更新现有配置，ID: {config.id}")
            config.name = data.get('name', config.name)
            config.app_id = data.get('app_id', config.app_id)
            config.mch_id = data.get('mch_id', config.mch_id)
            config.api_key = data.get('api_key', config.api_key)
            config.is_enabled = data.get('is_enabled', config.is_enabled)
        else:
            # 创建新配置
            logger.info("创建新配置")
            config = WeChatPayConfig(
                name=data.get('name', '微信支付配置'),
                app_id=data.get('app_id', ''),
                mch_id=data.get('mch_id', ''),
                api_key=data.get('api_key', ''),
                is_enabled=data.get('is_enabled', True)
            )
            db.session.add(config)
        
        db.session.commit()
        
        config_dict = config.to_dict()
        logger.info(f"微信支付配置保存成功 - 配置ID: {config.id}")
        
        return ApiResult.success(config_dict, "配置保存成功")
        
    except Exception as e:
        db.session.rollback()
        logger.exception(f"保存微信支付配置失败: {str(e)}")
        tb = traceback.format_exc()
        logger.error(f"详细错误信息: {tb}")
        return ApiResult.error(f"保存配置失败: {str(e)}")

@bp.route('/update/<int:config_id>', methods=['PUT'])
def update_config(config_id):
    """更新微信支付配置"""
    try:
        config = WeChatPayConfig.query.get(config_id)
        if not config:
            logger.warning(f"配置不存在 - 配置ID: {config_id}")
            return ApiResult.error("配置不存在")
        
        data = request.get_json()
        logger.info(f"更新微信支付配置 - 配置ID: {config_id}, 请求数据: {json.dumps(data, ensure_ascii=False)}")
        
        if not data:
            logger.warning("请求数据为空")
            return ApiResult.error("请求数据为空")
        
        # 更新字段
        if 'appid' in data:
            config.appid = data['appid']
        if 'mchid' in data:
            config.mchid = data['mchid']
        if 'api_v3_key' in data:
            config.api_v3_key = data['api_v3_key']
        if 'serial_no' in data:
            config.serial_no = data['serial_no']
        if 'private_key_path' in data:
            config.private_key_path = data['private_key_path']
        if 'platform_cert_path' in data:
            config.platform_cert_path = data['platform_cert_path']
        if 'notify_url' in data:
            config.notify_url = data['notify_url']
        if 'is_sandbox' in data:
            config.is_sandbox = data['is_sandbox']
        if 'sandbox_appid' in data:
            config.sandbox_appid = data['sandbox_appid']
        if 'sandbox_mchid' in data:
            config.sandbox_mchid = data['sandbox_mchid']
        if 'sandbox_api_v3_key' in data:
            config.sandbox_api_v3_key = data['sandbox_api_v3_key']
        if 'sandbox_serial_no' in data:
            config.sandbox_serial_no = data['sandbox_serial_no']
        if 'sandbox_private_key_path' in data:
            config.sandbox_private_key_path = data['sandbox_private_key_path']
        if 'is_enabled' in data:
            config.is_enabled = data['is_enabled']
        if 'is_test_mode' in data:
            config.is_test_mode = data['is_test_mode']
        if 'remark' in data:
            config.remark = data['remark']
        
        db.session.commit()
        
        logger.info(f"更新微信支付配置成功 - 配置ID: {config_id}")
        return ApiResult.success(config.to_dict(), "更新配置成功")
        
    except Exception as e:
        db.session.rollback()
        logger.exception(f"更新微信支付配置失败: {str(e)}")
        return ApiResult.error(f"更新配置失败: {str(e)}")

@bp.route('/delete/<int:config_id>', methods=['DELETE'])
def delete_config(config_id):
    """删除微信支付配置"""
    try:
        config = WeChatPayConfig.query.get(config_id)
        if not config:
            logger.warning(f"配置不存在 - 配置ID: {config_id}")
            return ApiResult.error("配置不存在")
        
        # 检查是否为当前启用的配置
        if config.is_enabled:
            logger.warning(f"无法删除已启用的配置 - 配置ID: {config_id}")
            return ApiResult.error("无法删除已启用的配置，请先禁用")
        
        db.session.delete(config)
        db.session.commit()
        
        logger.info(f"删除微信支付配置成功 - 配置ID: {config_id}")
        return ApiResult.success(None, "删除配置成功")
        
    except Exception as e:
        db.session.rollback()
        logger.exception(f"删除微信支付配置失败: {str(e)}")
        return ApiResult.error(f"删除配置失败: {str(e)}")

@bp.route('/enable/<int:config_id>', methods=['POST'])
def enable_config(config_id):
    """启用微信支付配置"""
    try:
        config = WeChatPayConfig.query.get(config_id)
        if not config:
            logger.warning(f"配置不存在 - 配置ID: {config_id}")
            return ApiResult.error("配置不存在")
        
        # 禁用其他配置
        WeChatPayConfig.query.update({'is_enabled': False})
        
        # 启用当前配置
        config.is_enabled = True
        db.session.commit()
        
        logger.info(f"启用微信支付配置成功 - 配置ID: {config_id}")
        return ApiResult.success(config.to_dict(), "启用配置成功")
        
    except Exception as e:
        db.session.rollback()
        logger.exception(f"启用微信支付配置失败: {str(e)}")
        return ApiResult.error(f"启用配置失败: {str(e)}")

@bp.route('/disable/<int:config_id>', methods=['POST'])
def disable_config(config_id):
    """禁用微信支付配置"""
    try:
        config = WeChatPayConfig.query.get(config_id)
        if not config:
            logger.warning(f"配置不存在 - 配置ID: {config_id}")
            return ApiResult.error("配置不存在")
        
        config.is_enabled = False
        db.session.commit()
        
        logger.info(f"禁用微信支付配置成功 - 配置ID: {config_id}")
        return ApiResult.success(config.to_dict(), "禁用配置成功")
        
    except Exception as e:
        db.session.rollback()
        logger.exception(f"禁用微信支付配置失败: {str(e)}")
        return ApiResult.error(f"禁用配置失败: {str(e)}")

@bp.route('/test', methods=['POST'])
def test_config():
    """测试微信支付配置"""
    try:
        data = request.get_json()
        logger.info(f"测试微信支付配置 - 请求数据: {json.dumps(data, ensure_ascii=False) if data else 'None'}")
        
        if not data:
            logger.warning("请求数据为空")
            return ApiResult.error("请求数据为空")
        
        # 构建测试配置
        test_config = {
            'appid': data.get('appid'),
            'mchid': data.get('mchid'),
            'api_v3_key': data.get('api_v3_key'),
            'serial_no': data.get('serial_no'),
            'private_key_path': data.get('private_key_path'),
            'notify_url': data.get('notify_url'),
            'sandbox': data.get('is_sandbox', False)
        }
        
        # 验证配置
        from EarlyBird.common.wechat_pay_v3 import WeChatPayV3
        from EarlyBird.config.wechat_pay_config import WeChatPayConfig
        
        # 检查必填字段
        required_fields = ['appid', 'mchid', 'api_v3_key', 'serial_no', 'private_key_path', 'notify_url']
        for field in required_fields:
            if not test_config.get(field):
                logger.warning(f"缺少必填字段: {field}")
                return ApiResult.error(f"缺少必填字段: {field}")
        
        # 检查私钥文件
        import os
        if not os.path.exists(test_config['private_key_path']):
            logger.warning(f"私钥文件不存在: {test_config['private_key_path']}")
            return ApiResult.error(f"私钥文件不存在: {test_config['private_key_path']}")
        
        # 尝试初始化微信支付
        wechat_pay = WeChatPayV3(
            appid=test_config['appid'],
            mchid=test_config['mchid'],
            private_key_path=test_config['private_key_path'],
            serial_no=test_config['serial_no'],
            api_v3_key=test_config['api_v3_key']
        )
        
        # 测试下单接口（使用最小金额）
        import time
        result = wechat_pay.create_native_order(
            out_trade_no=f"TEST_{int(time.time())}",
            description="配置测试",
            amount=0.01,
            notify_url=test_config['notify_url']
        )
        
        logger.info(f"测试微信支付配置成功 - 结果: {json.dumps(result, ensure_ascii=False)}")
        return ApiResult.success(result, "测试成功")
        
    except Exception as e:
        logger.exception(f"测试微信支付配置失败: {str(e)}")
        return ApiResult.error(f"测试失败: {str(e)}") 