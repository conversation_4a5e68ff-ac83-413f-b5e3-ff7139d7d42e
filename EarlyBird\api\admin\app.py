from flask import Blueprint
import logging

logger = logging.getLogger(__name__)

# 创建后台API主蓝图
admin_app_bp = Blueprint('admin', __name__, url_prefix='/api/admin')

def init_admin_api(app):
    """初始化后台API"""
    try:
        # 导入子蓝图
        from .auth import admin_auth_bp
        from .user import admin_user_bp
        from .thesis import admin_thesis_bp
        from .stats import admin_stats_bp
        from .settings import admin_settings_bp
        from .account import admin_account_bp
        
        # 注册子蓝图
        admin_app_bp.register_blueprint(admin_auth_bp, url_prefix='/auth')
        admin_app_bp.register_blueprint(admin_user_bp, url_prefix='/users')
        admin_app_bp.register_blueprint(admin_thesis_bp, url_prefix='/thesis')
        admin_app_bp.register_blueprint(admin_stats_bp, url_prefix='/stats')
        admin_app_bp.register_blueprint(admin_settings_bp, url_prefix='/settings')
        admin_app_bp.register_blueprint(admin_account_bp, url_prefix='/accounts')
        
        # 注册到主应用
        app.register_blueprint(admin_app_bp)
        
        # 为确保API路由正确，也直接注册每个蓝图
        app.register_blueprint(admin_auth_bp, url_prefix='/api/admin/auth')
        app.register_blueprint(admin_user_bp, url_prefix='/api/admin/users')
        app.register_blueprint(admin_thesis_bp, url_prefix='/api/admin/thesis')
        app.register_blueprint(admin_stats_bp, url_prefix='/api/admin/stats')
        app.register_blueprint(admin_settings_bp, url_prefix='/api/admin/settings')
        app.register_blueprint(admin_account_bp, url_prefix='/api/admin/accounts')
        
        logger.info("✅ 后台管理系统API已注册")
        print("✅ 后台管理系统API已注册")
        
    except Exception as e:
        logger.error(f"❌ 后台管理系统API注册失败: {str(e)}")
        print(f"❌ 后台管理系统API注册失败: {str(e)}")
        import traceback
        traceback.print_exc()
        raise 