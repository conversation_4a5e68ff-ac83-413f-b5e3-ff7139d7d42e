from sqlalchemy import Enum
from pydantic import BaseModel, ValidationError, constr, Field
import json
import logging


from queue import Queue


TASK_QUEUE = Queue(20)

LOGGER = logging.getLogger(__name__)


# 走redis 发布的消息有两种，1生成，2退出
class TaskType:
    GEN_THESIS: int = 1
    STOP_LISTEN: int = 11


class TaskModel(BaseModel):
    type: int = TaskType.GEN_THESIS
    thesisId: int = -1
    globalSettings: dict = None  # 添加全局设置字段


def popTaskBlocking():
    try:
        return TASK_QUEUE.get(timeout=5)
    except Exception as e:
        # 删除频繁的"task queue empty"日志，队列为空是正常状态
        return None


def pubTask(task: TaskModel):
    TASK_QUEUE.put(json.dumps(task.dict()))
    LOGGER.info(f"pub essay generate task {task}")


def pubGenerateThesisTask(thesisId: int):
    task = TaskModel(type=TaskType.GEN_THESIS, thesisId=thesisId)
    pubTask(task)
