<template>
  <div class="report-container">
    <div class="report-header">
      <h2>报告生成助手</h2>
      <p class="subtitle">快速生成各类工作报告，提高工作效率</p>
    </div>
    
    <div class="report-content">
      <!-- 左侧历史报告列表 -->
      <div class="report-history-sidebar">
        <ReportHistoryList 
          :selectedReportId="selectedHistoryId" 
          :reportType="getReportTypeKey(selectedType)"
          @select="selectHistoryReport"
          ref="historyList"
        />
      </div>
      
      <!-- 中间表单和结果区域 -->
      <div class="report-main">
        <!-- 表单区域 -->
        <div class="form-container" v-if="!reportResult || isEditing">
          <div class="form-header">
            <h3>{{ reportTypes[selectedType].name }}</h3>
            <p class="description">{{ reportTypes[selectedType].description }}</p>
          </div>
          
          <el-form :model="formData" label-width="100px" class="report-form">
            <!-- 通用字段 -->
            <el-form-item label="报告主题">
              <el-input v-model="formData.title" placeholder="请输入报告主题，如项目名称、工作内容等"></el-input>
            </el-form-item>
            
            <el-form-item label="工作内容">
              <el-input 
                type="textarea" 
                v-model="formData.content" 
                :rows="4"
                placeholder="请简要描述您的工作内容，关键词或要点即可，AI将自动扩展"
              ></el-input>
            </el-form-item>
            
            <el-form-item label="报告风格">
              <el-select v-model="formData.style" placeholder="请选择报告风格">
                <el-option label="简洁明了" value="concise"></el-option>
                <el-option label="详细专业" value="detailed"></el-option>
                <el-option label="成果导向" value="achievement"></el-option>
                <el-option label="问题分析" value="problem"></el-option>
              </el-select>
            </el-form-item>
            
            <!-- 日报特有字段 -->
            <template v-if="selectedType === 0">
              <el-form-item label="日期">
                <el-date-picker
                  v-model="formData.date"
                  type="date"
                  placeholder="选择日期"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd">
                </el-date-picker>
              </el-form-item>
            </template>
            
            <!-- 周报特有字段 -->
            <template v-if="selectedType === 1">
              <el-form-item label="周期">
                <el-date-picker
                  v-model="formData.dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd">
                </el-date-picker>
              </el-form-item>
              
              <el-form-item label="重点项目">
                <el-input v-model="formData.keyProject" placeholder="请输入本周重点项目"></el-input>
              </el-form-item>
            </template>
            
            <!-- 月报特有字段 -->
            <template v-if="selectedType === 2">
              <el-form-item label="月份">
                <el-date-picker
                  v-model="formData.month"
                  type="month"
                  placeholder="选择月份"
                  format="yyyy-MM"
                  value-format="yyyy-MM">
                </el-date-picker>
              </el-form-item>
              
              <el-form-item label="部门/团队">
                <el-input v-model="formData.department" placeholder="请输入您的部门或团队名称"></el-input>
              </el-form-item>
              
              <el-form-item label="KPI完成度">
                <el-slider v-model="formData.kpiCompletion" :step="5" show-stops></el-slider>
              </el-form-item>
            </template>
            
            <!-- 总结特有字段 -->
            <template v-if="selectedType === 3">
              <el-form-item label="总结类型">
                <el-select v-model="formData.summaryType" placeholder="请选择总结类型">
                  <el-option label="项目总结" value="project"></el-option>
                  <el-option label="季度总结" value="quarter"></el-option>
                  <el-option label="半年总结" value="halfyear"></el-option>
                  <el-option label="年度总结" value="annual"></el-option>
                </el-select>
              </el-form-item>
              
              <el-form-item label="成果亮点">
                <el-input 
                  type="textarea" 
                  v-model="formData.achievements" 
                  :rows="3"
                  placeholder="请列出主要成果和亮点"
                ></el-input>
              </el-form-item>
              
              <el-form-item label="问题与改进">
                <el-input 
                  type="textarea" 
                  v-model="formData.improvements" 
                  :rows="3"
                  placeholder="请列出存在的问题和改进方向"
                ></el-input>
              </el-form-item>
            </template>
            
            <el-form-item>
              <el-button type="primary" @click="generateReport" :loading="isGenerating">生成报告</el-button>
              <el-button @click="resetForm">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        
        <!-- 结果区域 -->
        <div class="result-container" v-if="reportResult">
          <div class="result-header">
            <h3>{{ currentReportTitle }}</h3>
            <div class="result-actions">
              <el-button size="small" type="primary" @click="toggleEditMode">
                {{ isEditing ? '预览报告' : '编辑报告' }}
              </el-button>
              <el-button 
                size="small" 
                type="success" 
                @click="saveReportManually"
              >
                保存报告
              </el-button>
              <el-button size="small" type="primary" icon="el-icon-document-copy" @click="copyReport">复制</el-button>
              <el-dropdown trigger="click" @command="handleDownload">
                <el-button size="small" type="success" icon="el-icon-download">
                  下载 <i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="markdown">Markdown格式</el-dropdown-item>
                  <el-dropdown-item command="html">HTML格式</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </div>
          
          <!-- 调试信息 -->
          <div class="debug-info" v-if="false">
            <pre>{{ reportResult }}</pre>
          </div>
          
          <!-- Markdown渲染区域 -->
          <div class="markdown-view-container" v-if="!isEditing">
            <MarkdownRenderer :content="reportResult" class="markdown-view" />
          </div>
          
          <!-- 源码编辑区域 -->
          <div class="source-edit" v-else>
            <el-input
              type="textarea"
              v-model="reportResult"
              :rows="20"
              resize="none"
            ></el-input>
          </div>
        </div>
      </div>
      
      <!-- 右侧报告类型选择 -->
      <div class="report-sidebar">
        <div class="report-types">
          <div 
            v-for="(type, index) in reportTypes" 
            :key="index"
            :class="['report-type-item', selectedType === index ? 'active' : '']"
            @click="selectReportType(index)"
          >
            <i :class="type.icon"></i>
            <span>{{ type.name }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { generateReport } from '@/api/generate'
import { exportReportDocx } from '@/api/generate'
import { exportReportHtml } from '@/api/generate'
import { saveReportHistory } from '@/api/generate'
import MarkdownRenderer from '@/views/components/MarkdownRenderer.vue'
import ReportHistoryList from '@/views/components/ReportHistoryList.vue'
import { marked } from 'marked'
import { getReportHistory } from '@/api/generate'
import { deleteReport } from '@/api/generate'

export default {
  name: "ReportGenerator",
  components: {
    MarkdownRenderer,
    ReportHistoryList
  },
  data() {
    return {
      selectedType: 0,
      isEditing: false,
      selectedHistoryId: -1,
      historyReports: [],
      reportTypes: [
        {
          name: "日报生成",
          icon: "el-icon-date",
          description: "快速生成每日工作报告，记录日常工作内容和成果"
        },
        {
          name: "周报生成",
          icon: "el-icon-document",
          description: "汇总一周工作内容，突出重点项目进展和成果"
        },
        {
          name: "月报生成",
          icon: "el-icon-collection",
          description: "全面总结月度工作，分析成果与不足，提出下月计划"
        },
        {
          name: "总结生成",
          icon: "el-icon-data-analysis",
          description: "项目、季度、年度等各类总结报告，全面分析成果与经验"
        }
      ],
      formData: {
        title: "",
        content: "",
        style: "concise",
        date: new Date().toISOString().split('T')[0],
        dateRange: [
          new Date(new Date().setDate(new Date().getDate() - 7)).toISOString().split('T')[0],
          new Date().toISOString().split('T')[0]
        ],
        keyProject: "",
        month: new Date().toISOString().slice(0, 7),
        department: "",
        kpiCompletion: 80,
        summaryType: "project",
        achievements: "",
        improvements: ""
      },
      isGenerating: false,
      reportResult: ""
    };
  },
  computed: {
    currentReportTitle() {
      if (this.reportResult) {
        return this.formData.title || '生成结果';
      }
      return this.formData.title || '生成结果';
    }
  },
  mounted() {
    // 处理URL参数，设置默认选中的报告类型
    const typeParam = this.$route.query.type;
    if (typeParam) {
      switch (typeParam) {
        case 'daily':
          this.selectedType = 0;
          break;
        case 'weekly':
          this.selectedType = 1;
          break;
        case 'monthly':
          this.selectedType = 2;
          break;
        case 'summary':
          this.selectedType = 3;
          break;
      }
    }
    
    // 加载历史报告
    this.loadHistoryReports();
  },
  watch: {
    // 监听路由变化，更新选中的报告类型
    '$route.query.type'(newType) {
      if (newType) {
        switch (newType) {
          case 'daily':
            this.selectedType = 0;
            break;
          case 'weekly':
            this.selectedType = 1;
            break;
          case 'monthly':
            this.selectedType = 2;
            break;
          case 'summary':
            this.selectedType = 3;
            break;
        }
      }
    }
  },
  methods: {
    loadHistoryReports() {
      // 从数据库加载报告历史
      getReportHistory()
        .then(res => {
          if (res.is_success) {
            this.historyReports = res.data || [];
          } else {
            this.$message.error('加载报告历史失败');
          }
        })
        .catch(error => {
          console.error('加载历史报告失败:', error);
          this.$message.error('加载报告历史失败');
        });
    },
    
    selectHistoryReport(report, index) {
      if (!report) {
        // 清除选中状态
        this.selectedHistoryId = -1;
        this.reportResult = '';
        return;
      }
      
      this.selectedHistoryId = report.id;
      
      // 更新表单数据
      try {
        const formData = report.form_data || {};
        this.formData = { ...formData };
      } catch (error) {
        console.error('解析表单数据失败:', error);
        this.formData = {};
      }
      
      // 更新报告内容
      this.reportResult = report.content || '';
      
      // 更新报告类型
      const reportTypes = ['daily', 'weekly', 'monthly', 'summary'];
      const typeIndex = reportTypes.indexOf(report.report_type);
      if (typeIndex >= 0) {
        this.selectedType = typeIndex;
      }
      
      // 退出编辑模式
      this.isEditing = false;
    },
    
    selectReportType(index) {
      this.selectedType = index;
      this.selectedHistoryId = -1;
      this.reportResult = '';
      this.isEditing = false;
    },
    
    toggleEditMode() {
      this.isEditing = !this.isEditing;
    },
    
    generateReport() {
      if (!this.validateForm()) {
        return;
      }
      
      const loading = this.$loading({
        lock: true,
        text: '正在生成报告...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      
      const reportData = this.getReportData();
      
      generateReport({
        reportType: this.getReportTypeString(),
        reportData: reportData
      }).then(response => {
        loading.close();
        if (response.is_success) {
          this.reportResult = response.data.content || response.data;
          this.isEditing = false;
          
          // 保存成功后重新加载历史记录
          this.loadHistoryReports();
          
          // 如果返回了reportId，选中该报告
          if (response.data.reportId) {
            this.selectedHistoryId = response.data.reportId;
          }
          
          this.$message.success('报告生成成功！');
        } else {
          this.$message.error(response.message || '生成报告失败');
        }
      }).catch(error => {
        loading.close();
        console.error('生成报告失败:', error);
        this.$message.error('生成报告失败，请重试');
      });
    },
    
    // 新方法：保存报告到历史记录
    saveReportToHistory(reportType, content) {
      // 检查内容是否为空
      if (!content || content.trim() === '') {
        console.error('报告内容为空，无法保存');
        this.$message.error('报告内容为空，无法保存');
        return;
      }
      
      // 准备请求参数
      const params = {
        reportType: reportType,
        reportData: this.formData,
        reportContent: content
      };
      
      console.log('保存历史记录，参数:', params);
      console.log('报告内容长度:', content ? content.length : 0);
      
      // 显示加载提示
      const loading = this.$loading({
        lock: true,
        text: '正在保存报告...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      
      // 调用API保存历史记录
      saveReportHistory(params)
        .then(res => {
          loading.close();
          if (res.is_success) {
            console.log('保存历史记录成功:', res.data);
            // 更新选中的历史记录
            this.selectedHistoryId = res.data.id;
            // 刷新历史记录列表
            this.$refs.historyList && this.$refs.historyList.refreshList();
            // 显示成功提示
            this.$message.success('报告已保存到历史记录');
          } else {
            console.error('保存历史记录失败:', res.message);
            this.$message.error(res.message || '保存历史记录失败');
          }
        })
        .catch(error => {
          loading.close();
          console.error('保存历史记录出错:', error);
          this.$message.error('保存历史记录失败，请稍后重试');
        });
    },
    
    resetForm() {
      this.formData = {
        title: "",
        content: "",
        style: "concise",
        date: new Date().toISOString().split('T')[0],
        dateRange: [
          new Date(new Date().setDate(new Date().getDate() - 7)).toISOString().split('T')[0],
          new Date().toISOString().split('T')[0]
        ],
        keyProject: "",
        month: new Date().toISOString().slice(0, 7),
        department: "",
        kpiCompletion: 80,
        summaryType: "project",
        achievements: "",
        improvements: ""
      };
      this.reportResult = "";
      this.selectedHistoryId = -1;
      this.isEditing = false;
    },
    
    copyReport() {
      if (!this.reportResult) return;
      
      // 创建临时textarea元素
      const textarea = document.createElement('textarea');
      textarea.value = this.reportResult;
      document.body.appendChild(textarea);
      textarea.select();
      
      try {
        document.execCommand('copy');
        this.$message.success('报告已复制到剪贴板');
      } catch (err) {
        this.$message.error('复制失败，请手动复制');
      }
      
      document.body.removeChild(textarea);
    },
    
    handleDownload(type) {
      if (type === 'markdown') {
        this.downloadMarkdown();
      } else if (type === 'html') {
        this.downloadHtml();
      }
    },
    
    downloadMarkdown() {
      if (!this.reportResult) return;
      
      const type = this.reportTypes[this.selectedType].name;
      const fileName = `${type.replace(/生成$/, '')}_${new Date().toISOString().slice(0, 10)}.md`;
      
      const blob = new Blob([this.reportResult], { type: 'text/markdown' });
      const url = URL.createObjectURL(blob);
      
      const a = document.createElement('a');
      a.href = url;
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      
      setTimeout(() => {
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }, 0);
    },
    
    downloadHtml() {
      if (!this.reportResult) return;
      
      try {
        // 显示加载提示
        const loading = this.$loading({
          lock: true,
          text: '正在生成HTML文档...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        
        // 准备请求参数
        const params = {
          reportType: ['daily', 'weekly', 'monthly', 'summary'][this.selectedType],
          reportData: { ...this.formData },
          reportContent: this.reportResult
        };
        
        console.log('导出HTML文档，参数:', params);
        
        // 调用API导出HTML文档
        exportReportHtml(params)
          .then(res => {
            console.log('导出HTML结果:', res);
            if (res.is_success) {
              // 创建下载链接
              let link = document.createElement('a');
              link.style.display = 'none';
              link.href = '/api/thesis/download?fileName=' + res.data.file;
              document.body.appendChild(link);
              link.click();
              
              setTimeout(() => {
                document.body.removeChild(link);
                loading.close();
                
                // 显示成功提示
                this.$notify({
                  title: '导出成功',
                  message: '报告已成功导出为HTML格式',
                  type: 'success',
                  duration: 3000
                });
              }, 100);
            } else {
              loading.close();
              this.$message.error(res.message || '导出HTML文档失败');
            }
          })
          .catch(error => {
            console.error('导出HTML文档失败:', error);
            loading.close();
            this.$message.error('导出HTML文档失败，请稍后重试');
          });
      } catch (error) {
        console.error('生成HTML文档失败:', error);
        this.$message.error('生成HTML文档失败，请稍后重试');
      }
    },
    
    downloadReport() {
      this.downloadMarkdown();
    },
    
    // 更新已有报告
    updateReport() {
      if (!this.reportResult || this.selectedHistoryId <= 0) {
        this.$message.warning('没有可更新的报告');
        return;
      }
      
      // 获取报告类型
      const reportTypes = ['daily', 'weekly', 'monthly', 'summary'];
      const reportType = reportTypes[this.selectedType];
      
      // 准备请求参数
      const params = {
        id: this.selectedHistoryId,
        reportType: reportType,
        reportData: this.formData,
        reportContent: this.reportResult
      };
      
      console.log('更新报告，参数:', params);
      
      // 调用API保存历史记录
      saveReportHistory(params)
        .then(res => {
          if (res.is_success) {
            console.log('更新报告成功:', res.data);
            // 刷新历史记录列表
            this.$refs.historyList && this.$refs.historyList.refreshList();
            // 显示成功提示
            this.$message.success('报告已更新');
            // 退出编辑模式
            this.isEditing = false;
          } else {
            console.error('更新报告失败:', res.message);
            this.$message.error(res.message || '更新报告失败');
          }
        })
        .catch(error => {
          console.error('更新报告出错:', error);
          this.$message.error('更新报告失败，请稍后重试');
        });
    },
    
    saveReportManually() {
      if (!this.reportResult) {
        this.$message.warning('没有可保存的报告内容');
        return;
      }
      
      // 获取报告类型
      const reportTypes = ['daily', 'weekly', 'monthly', 'summary'];
      const reportType = reportTypes[this.selectedType];
      
      // 准备请求参数
      const params = {
        reportType: reportType,
        reportData: this.formData,
        reportContent: this.reportResult
      };
      
      // 如果是更新已有报告，添加ID
      if (this.selectedHistoryId > 0) {
        params.id = this.selectedHistoryId;
      }
      
      console.log('保存报告，参数:', params);
      
      // 调用API保存报告
      saveReportHistory(params)
        .then(res => {
          if (res.is_success) {
            console.log('保存报告成功:', res.data);
            
            // 更新选中的报告ID
            this.selectedHistoryId = res.data.id;
            
            // 刷新历史记录列表
            this.$refs.historyList && this.$refs.historyList.refreshList();
            
            // 显示成功提示
            this.$message.success('报告已保存到历史记录');
            
            // 退出编辑模式
            this.isEditing = false;
          } else {
            console.error('保存报告失败:', res.message);
            this.$message.error(res.message || '保存报告失败');
          }
        })
        .catch(error => {
          console.error('保存报告出错:', error);
          this.$message.error('保存报告失败，请稍后重试');
        });
    },
    
    getReportTypeKey(typeIndex) {
      const reportTypes = ['daily', 'weekly', 'monthly', 'summary'];
      return reportTypes[typeIndex] || 'daily';
    },
    
    deleteHistoryReport(report) {
      this.$confirm('确定要删除这份报告吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteReport({ reportId: report.id })
          .then(res => {
            if (res.is_success) {
              this.$message.success('删除成功');
              this.loadHistoryReports();
              // 如果删除的是当前选中的报告，清空内容
              if (this.selectedHistoryId === report.id) {
                this.selectedHistoryId = -1;
                this.reportResult = '';
              }
            } else {
              this.$message.error(res.message || '删除失败');
            }
          })
          .catch(error => {
            this.$message.error('删除失败');
          });
      }).catch(() => {
        // 用户取消删除
      });
    },
    
    validateForm() {
      // 表单验证逻辑
      if (!this.formData.title || !this.formData.content) {
        this.$message.warning('请填写报告主题和工作内容');
        return false;
      }
      return true;
    },
    
    getReportData() {
      // 获取表单数据
      return { ...this.formData };
    },
    
    getReportTypeString() {
      // 获取报告类型字符串
      const reportTypes = ['daily', 'weekly', 'monthly', 'summary'];
      return reportTypes[this.selectedType] || 'daily';
    }
  }
};
</script>

<style lang="scss" scoped>
.report-container {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.report-header {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
  
  h2 {
    margin: 0;
    font-size: 24px;
    color: #303133;
  }
  
  .subtitle {
    margin-top: 8px;
    color: #909399;
    font-size: 14px;
  }
}

.report-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.report-history-sidebar {
  width: 220px;
  border-right: 1px solid #ebeef5;
  overflow: hidden;
}

.report-main {
  flex: 1;
  padding: 0 20px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.report-sidebar {
  width: 180px;
  padding-left: 20px;
  border-left: 1px solid #ebeef5;
}

.report-types {
  .report-type-item {
    padding: 12px 15px;
    margin-bottom: 8px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    
    i {
      margin-right: 8px;
      font-size: 18px;
    }
    
    &:hover {
      background-color: #f5f7fa;
    }
    
    &.active {
      background-color: #ecf5ff;
      color: #409EFF;
    }
  }
}

.form-container {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  
  .form-header {
    margin-bottom: 20px;
    
    h3 {
      margin-top: 0;
      margin-bottom: 10px;
      font-size: 18px;
      color: #303133;
    }
    
    .description {
      color: #909399;
      font-size: 14px;
      margin: 0;
    }
  }
}

.report-form {
  max-width: 800px;
}

.result-container {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  flex: 1;
  display: flex;
  flex-direction: column;
  
  .result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    
    h3 {
      margin: 0;
      font-size: 18px;
      color: #303133;
    }
    
    .result-actions {
      display: flex;
      gap: 10px;
    }
  }
  
  .debug-info {
    flex: 1;
    overflow: auto;
  }
  
  .markdown-view-container {
    flex: 1;
    overflow: auto;
    background-color: #fff;
    border-radius: 4px;
    padding: 0;
  }
  
  .markdown-view {
    flex: 1;
    overflow: auto;
  }
  
  .source-edit {
    flex: 1;
    
    :deep(.el-textarea) {
      height: 100%;
      
      .el-textarea__inner {
        height: 100%;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'source-code-pro', monospace;
      }
    }
  }
}
</style> 