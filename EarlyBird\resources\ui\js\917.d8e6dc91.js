"use strict";(self["webpackChunkopen_essay_gui"]=self["webpackChunkopen_essay_gui"]||[]).push([[917],{3917:(t,a,s)=>{s.r(a),s.d(a,{default:()=>n});var e=function(){var t=this,a=t._self._c;return a("div",{staticClass:"admin-stats-chat"},[a("el-card",[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("聊天统计")]),a("div",{staticClass:"header-right"},[a("el-radio-group",{attrs:{size:"small"},on:{change:t.fetchChatStats},model:{value:t.period,callback:function(a){t.period=a},expression:"period"}},[a("el-radio-button",{attrs:{label:"7d"}},[t._v("最近7天")]),a("el-radio-button",{attrs:{label:"30d"}},[t._v("最近30天")]),a("el-radio-button",{attrs:{label:"90d"}},[t._v("最近90天")])],1),a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{size:"small",type:"primary",icon:"el-icon-refresh"},on:{click:t.fetchChatStats}},[t._v(" 刷新 ")])],1)]),a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}]},[a("div",{staticClass:"stat-cards"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-card",{staticClass:"stat-card",attrs:{shadow:"hover"}},[a("div",{staticClass:"stat-card-title"},[t._v("总聊天记录数")]),a("div",{staticClass:"stat-card-value"},[t._v(t._s(t.statsData.total_chat_logs||0))])])],1),a("el-col",{attrs:{span:12}},[a("el-card",{staticClass:"stat-card",attrs:{shadow:"hover"}},[a("div",{staticClass:"stat-card-title"},[t._v("新增聊天记录数")]),a("div",{staticClass:"stat-card-value"},[t._v(t._s(t.statsData.new_chat_logs||0))])])],1)],1)],1),a("div",{staticClass:"chart-container"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:24}},[a("el-card",{staticClass:"chart-card",attrs:{shadow:"hover"}},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("每日聊天记录趋势")])]),a("div",{staticClass:"chart",staticStyle:{height:"300px"},attrs:{id:"daily-chat-chart"}})])],1)],1),a("el-row",{staticStyle:{"margin-top":"20px"},attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-card",{staticClass:"chart-card",attrs:{shadow:"hover"}},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("模型使用分布")])]),a("div",{staticClass:"chart",staticStyle:{height:"300px"},attrs:{id:"model-chart"}})])],1),a("el-col",{attrs:{span:12}},[a("el-card",{staticClass:"chart-card",attrs:{shadow:"hover"}},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("聊天类型分布")])]),a("div",{staticClass:"chart",staticStyle:{height:"300px"},attrs:{id:"type-chart"}})])],1)],1),a("el-row",{staticStyle:{"margin-top":"20px"},attrs:{gutter:20}},[a("el-col",{attrs:{span:24}},[a("el-card",{attrs:{shadow:"hover"}},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("聊天数据表格")])]),a("el-table",{staticStyle:{width:"100%"},attrs:{data:t.tableData}},[a("el-table-column",{attrs:{prop:"date",label:"日期",width:"180"}}),a("el-table-column",{attrs:{prop:"count",label:"聊天记录数"}})],1)],1)],1)],1)],1)])])],1)},r=[],i=s(9192),l=s(9393);const h={name:"AdminStatsChat",data(){return{loading:!1,period:"7d",statsData:{},tableData:[],charts:{dailyChatChart:null,modelChart:null,typeChart:null}}},mounted(){this.fetchChatStats(),window.addEventListener("resize",this.resizeCharts)},beforeDestroy(){window.removeEventListener("resize",this.resizeCharts),Object.keys(this.charts).forEach((t=>{this.charts[t]&&this.charts[t].dispose()}))},methods:{async fetchChatStats(){this.loading=!0;try{const t=await i.bk.getChatStats({period:this.period});t.success?(this.statsData=t.data,this.tableData=this.statsData.daily_stats||[],this.$nextTick((()=>{this.initCharts()}))):this.$message.error(t.message||"获取聊天统计数据失败")}catch(t){this.$message.error("获取聊天统计数据失败："+t.message)}finally{this.loading=!1}},initCharts(){this.initDailyChatChart(),this.initModelChart(),this.initTypeChart()},initDailyChatChart(){const t=document.getElementById("daily-chat-chart");if(!t)return;this.charts.dailyChatChart&&this.charts.dailyChatChart.dispose(),this.charts.dailyChatChart=l.init(t);const a=this.statsData.daily_stats||[],s=a.map((t=>t.date)),e=a.map((t=>t.count)),r={tooltip:{trigger:"axis",axisPointer:{type:"cross"}},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:[{type:"category",data:s,axisLabel:{rotate:45}}],yAxis:[{type:"value"}],series:[{name:"聊天记录数",data:e,type:"line",smooth:!0,symbolSize:8,itemStyle:{color:"#F56C6C"},areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"rgba(245, 108, 108, 0.5)"},{offset:1,color:"rgba(245, 108, 108, 0.1)"}]}}}]};this.charts.dailyChatChart.setOption(r)},initModelChart(){const t=document.getElementById("model-chart");if(!t)return;this.charts.modelChart&&this.charts.modelChart.dispose(),this.charts.modelChart=l.init(t);const a=this.statsData.model_stats||[],s=a.map((t=>({name:t.model||"未知",value:t.count}))),e={tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",right:10,top:"center",data:s.map((t=>t.name))},series:[{name:"模型使用",type:"pie",radius:"55%",center:["40%","50%"],data:s,emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]};this.charts.modelChart.setOption(e)},initTypeChart(){const t=document.getElementById("type-chart");if(!t)return;this.charts.typeChart&&this.charts.typeChart.dispose(),this.charts.typeChart=l.init(t);const a=this.statsData.type_stats||[],s=a.map((t=>{let a=t.type||"未知";return"chat"===a&&(a="普通聊天"),"title"===a&&(a="标题生成"),"outline"===a&&(a="提纲生成"),"content"===a&&(a="内容生成"),{name:a,value:t.count}})),e={tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",right:10,top:"center",data:s.map((t=>t.name))},series:[{name:"聊天类型",type:"pie",radius:"55%",center:["40%","50%"],data:s,emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]};this.charts.typeChart.setOption(e)},resizeCharts(){Object.keys(this.charts).forEach((t=>{this.charts[t]&&this.charts[t].resize()}))}}},o=h;var c=s(1656),d=(0,c.A)(o,e,r,!1,null,"d56bd5f0",null);const n=d.exports}}]);