<template>
  <div class="admin-stats-thesis">
    <el-card>
      <div slot="header" class="clearfix">
        <span>论文统计</span>
        <div class="header-right">
          <el-radio-group v-model="period" size="small" @change="fetchThesisStats">
            <el-radio-button label="7d">最近7天</el-radio-button>
            <el-radio-button label="30d">最近30天</el-radio-button>
            <el-radio-button label="90d">最近90天</el-radio-button>
          </el-radio-group>
          <el-button style="margin-left: 10px;" size="small" type="primary" icon="el-icon-refresh" @click="fetchThesisStats">
            刷新
          </el-button>
        </div>
      </div>
      
      <div v-loading="loading">
        <!-- 数据卡片 -->
        <div class="stat-cards">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-card shadow="hover" class="stat-card">
                <div class="stat-card-title">总论文数</div>
                <div class="stat-card-value">{{ statsData.total_thesis || 0 }}</div>
              </el-card>
            </el-col>
            <el-col :span="12">
              <el-card shadow="hover" class="stat-card">
                <div class="stat-card-title">新增论文数</div>
                <div class="stat-card-value">{{ statsData.new_thesis || 0 }}</div>
              </el-card>
            </el-col>
          </el-row>
        </div>

        <!-- 图表区域 -->
        <div class="chart-container">
          <el-row :gutter="20">
            <!-- 每日新增论文趋势图 -->
            <el-col :span="24">
              <el-card shadow="hover" class="chart-card">
                <div slot="header" class="clearfix">
                  <span>每日新增论文趋势</span>
                </div>
                <div class="chart" id="daily-thesis-chart" style="height: 300px;"></div>
              </el-card>
            </el-col>
          </el-row>

          <el-row :gutter="20" style="margin-top: 20px;">
            <!-- 论文语言分布 -->
            <el-col :span="8">
              <el-card shadow="hover" class="chart-card">
                <div slot="header" class="clearfix">
                  <span>论文语言分布</span>
                </div>
                <div class="chart" id="lang-chart" style="height: 300px;"></div>
              </el-card>
            </el-col>
            
            <!-- 论文级别分布 -->
            <el-col :span="8">
              <el-card shadow="hover" class="chart-card">
                <div slot="header" class="clearfix">
                  <span>论文级别分布</span>
                </div>
                <div class="chart" id="level-chart" style="height: 300px;"></div>
              </el-card>
            </el-col>
            
            <!-- 论文长度分布 -->
            <el-col :span="8">
              <el-card shadow="hover" class="chart-card">
                <div slot="header" class="clearfix">
                  <span>论文长度分布</span>
                </div>
                <div class="chart" id="length-chart" style="height: 300px;"></div>
              </el-card>
            </el-col>
          </el-row>
          
          <!-- 数据表格 -->
          <el-row :gutter="20" style="margin-top: 20px;">
            <el-col :span="24">
              <el-card shadow="hover">
                <div slot="header" class="clearfix">
                  <span>论文数据表格</span>
                </div>
                <el-table :data="tableData" style="width: 100%">
                  <el-table-column prop="date" label="日期" width="180"></el-table-column>
                  <el-table-column prop="count" label="新增论文数"></el-table-column>
                </el-table>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { adminStats } from '@/api/admin'
import * as echarts from 'echarts'

export default {
  name: 'AdminStatsThesis',
  data() {
    return {
      loading: false,
      period: '7d',
      statsData: {},
      tableData: [],
      charts: {
        dailyThesisChart: null,
        langChart: null,
        levelChart: null,
        lengthChart: null
      }
    }
  },
  mounted() {
    this.fetchThesisStats()
    // 窗口大小变化时重绘图表
    window.addEventListener('resize', this.resizeCharts)
  },
  beforeDestroy() {
    // 移除事件监听
    window.removeEventListener('resize', this.resizeCharts)
    // 销毁图表实例
    Object.keys(this.charts).forEach(key => {
      if (this.charts[key]) {
        this.charts[key].dispose()
      }
    })
  },
  methods: {
    async fetchThesisStats() {
      this.loading = true
      try {
        const response = await adminStats.getThesisStats({ period: this.period })
        if (response.success) {
          this.statsData = response.data
          this.tableData = this.statsData.daily_stats || []
          this.$nextTick(() => {
            this.initCharts()
          })
        } else {
          this.$message.error(response.message || '获取论文统计数据失败')
        }
      } catch (error) {
        this.$message.error('获取论文统计数据失败：' + error.message)
      } finally {
        this.loading = false
      }
    },
    initCharts() {
      this.initDailyThesisChart()
      this.initLangChart()
      this.initLevelChart()
      this.initLengthChart()
    },
    initDailyThesisChart() {
      const chartDom = document.getElementById('daily-thesis-chart')
      if (!chartDom) return
      
      if (this.charts.dailyThesisChart) {
        this.charts.dailyThesisChart.dispose()
      }
      this.charts.dailyThesisChart = echarts.init(chartDom)
      
      const dailyStats = this.statsData.daily_stats || []
      const dates = dailyStats.map(item => item.date)
      const counts = dailyStats.map(item => item.count)
      
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: [{
          type: 'category',
          data: dates,
          axisLabel: {
            rotate: 45
          }
        }],
        yAxis: [{
          type: 'value'
        }],
        series: [{
          name: '新增论文',
          data: counts,
          type: 'bar',
          itemStyle: {
            color: '#67C23A'
          }
        }]
      }
      
      this.charts.dailyThesisChart.setOption(option)
    },
    initLangChart() {
      const chartDom = document.getElementById('lang-chart')
      if (!chartDom) return
      
      if (this.charts.langChart) {
        this.charts.langChart.dispose()
      }
      this.charts.langChart = echarts.init(chartDom)
      
      const langStats = this.statsData.lang_stats || []
      const data = langStats.map(item => {
        return {
          name: item.lang || '未知',
          value: item.count
        }
      })
      
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          right: 10,
          top: 'center',
          data: data.map(item => item.name)
        },
        series: [
          {
            name: '语言分布',
            type: 'pie',
            radius: ['50%', '70%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '16',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: data
          }
        ]
      }
      
      this.charts.langChart.setOption(option)
    },
    initLevelChart() {
      const chartDom = document.getElementById('level-chart')
      if (!chartDom) return
      
      if (this.charts.levelChart) {
        this.charts.levelChart.dispose()
      }
      this.charts.levelChart = echarts.init(chartDom)
      
      const levelStats = this.statsData.level_stats || []
      const data = levelStats.map(item => {
        return {
          name: item.level || '未知',
          value: item.count
        }
      })
      
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          right: 10,
          top: 'center',
          data: data.map(item => item.name)
        },
        series: [
          {
            name: '级别分布',
            type: 'pie',
            radius: ['50%', '70%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '16',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: data
          }
        ]
      }
      
      this.charts.levelChart.setOption(option)
    },
    initLengthChart() {
      const chartDom = document.getElementById('length-chart')
      if (!chartDom) return
      
      if (this.charts.lengthChart) {
        this.charts.lengthChart.dispose()
      }
      this.charts.lengthChart = echarts.init(chartDom)
      
      const lengthStats = this.statsData.length_stats || []
      const data = lengthStats.map(item => {
        return {
          name: item.length ? `${item.length}字` : '未知',
          value: item.count
        }
      })
      
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          right: 10,
          top: 'center',
          data: data.map(item => item.name)
        },
        series: [
          {
            name: '长度分布',
            type: 'pie',
            radius: ['50%', '70%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '16',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: data
          }
        ]
      }
      
      this.charts.lengthChart.setOption(option)
    },
    resizeCharts() {
      Object.keys(this.charts).forEach(key => {
        if (this.charts[key]) {
          this.charts[key].resize()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.admin-stats-thesis {
  .header-right {
    float: right;
  }
  
  .stat-cards {
    margin-bottom: 20px;
    
    .stat-card {
      text-align: center;
      
      .stat-card-title {
        font-size: 14px;
        color: #606266;
      }
      
      .stat-card-value {
        font-size: 24px;
        font-weight: bold;
        margin-top: 10px;
        color: #303133;
      }
    }
  }
  
  .chart-container {
    margin-top: 20px;
    
    .chart-card {
      margin-bottom: 20px;
    }
  }
}
</style> 