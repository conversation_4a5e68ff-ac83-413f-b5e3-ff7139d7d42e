import { login, register, logout, getUserInfo } from '@/api/user'

// 安全地解析JSON字符串
function safeJsonParse(jsonString, defaultValue = null) {
  if (!jsonString || jsonString === 'undefined' || jsonString === 'null') {
    return defaultValue;
  }
  
  try {
    return JSON.parse(jsonString);
  } catch (error) {
    console.warn('JSON解析失败:', error, '原始值:', jsonString);
    return defaultValue;
  }
}

const state = {
  userInfo: null,
  token: null,
  isLoggedIn: false
}

const mutations = {
  SET_USER_INFO: (state, userInfo) => {
    state.userInfo = userInfo
    state.isLoggedIn = !!userInfo
  },
  SET_TOKEN: (state, token) => {
    state.token = token
  },
  CLEAR_USER: (state) => {
    state.userInfo = null
    state.token = null
    state.isLoggedIn = false
  }
}

const actions = {
  // 用户登录
  async login({ commit }, loginData) {
    try {
      const response = await login(loginData)
      if (response.is_success) {
        // 后端返回的数据结构：{ userId, username, nickname, isVip, headimg, email }
        commit('SET_USER_INFO', response.data)
        // 存储到本地存储
        localStorage.setItem('userInfo', JSON.stringify(response.data))
        localStorage.setItem('loginTime', new Date().toISOString())
        return response
      } else {
        throw new Error(response.message || '登录失败')
      }
    } catch (error) {
      throw error
    }
  },

  // 用户注册
  async register({ commit }, registerData) {
    try {
      const response = await register(registerData)
      if (response.is_success) {
        // 后端返回的数据结构：{ userId, username, nickname, isVip, headimg, email }
        commit('SET_USER_INFO', response.data)
        // 存储到本地存储
        localStorage.setItem('userInfo', JSON.stringify(response.data))
        localStorage.setItem('loginTime', new Date().toISOString())
        return response
      } else {
        throw new Error(response.message || '注册失败')
      }
    } catch (error) {
      throw error
    }
  },

  // 用户退出登录
  async logout({ commit }) {
    try {
      await logout()
      commit('CLEAR_USER')
      // 清除本地存储
      localStorage.removeItem('userInfo')
      localStorage.removeItem('loginTime')
      // 清除论文相关缓存
      localStorage.removeItem('lastTitleForm')
      localStorage.removeItem('lastTitleResult')
      localStorage.removeItem('lastTitle')
      localStorage.removeItem('lastOutline')
      localStorage.removeItem('report_history')
      return { success: true }
    } catch (error) {
      // 即使退出失败，也清除本地状态
      commit('CLEAR_USER')
      localStorage.removeItem('userInfo')
      localStorage.removeItem('loginTime')
      localStorage.removeItem('lastTitleForm')
      localStorage.removeItem('lastTitleResult')
      localStorage.removeItem('lastTitle')
      localStorage.removeItem('lastOutline')
      localStorage.removeItem('report_history')
      throw error
    }
  },

  // 获取用户信息
  async getUserInfo({ commit }) {
    try {
      const response = await getUserInfo()
      if (response.is_success) {
        // 后端返回的数据结构：{ roles: [], user: {...} }
        // 注意：getUserInfo接口返回的user对象字段名是id而不是userId
        const userData = response.data.user
        // 转换为前端期望的格式
        const normalizedUserData = {
          userId: userData.id,
          username: userData.username,
          nickname: userData.nickname,
          isVip: userData.isVip,
          headimg: userData.headimg,
          email: userData.email,
          create_time: userData.create_time,
          last_login_time: userData.last_login_time,
          // 添加VIP详细信息
          vip_level: userData.vip_level,
          vip_balance_days: userData.vip_balance_days,
          vip_expire_at: userData.vip_expire_at,
          vip_start_at: userData.vip_start_at
        }
        commit('SET_USER_INFO', normalizedUserData)
        // 更新本地存储
        localStorage.setItem('userInfo', JSON.stringify(normalizedUserData))
        return response.data
      } else {
        throw new Error(response.message || '获取用户信息失败')
      }
    } catch (error) {
      throw error
    }
  },

  // 从本地存储恢复用户状态
  async restoreUserState({ commit }) {
    try {
      const userInfo = localStorage.getItem('userInfo')
      const loginTime = localStorage.getItem('loginTime')
      
      if (userInfo && loginTime) {
        const parsedUserInfo = safeJsonParse(userInfo)
        if (!parsedUserInfo) {
          console.warn('用户信息解析失败，清除本地状态')
          localStorage.removeItem('userInfo')
          localStorage.removeItem('loginTime')
          return false
        }
        
        const loginDate = new Date(loginTime)
        const now = new Date()
        
        // 检查登录时间是否在7天内（与后端session有效期一致）
        const daysDiff = (now - loginDate) / (1000 * 60 * 60 * 24)
        
        if (daysDiff <= 7) {
          // 登录时间在有效期内，先尝试从后端验证session是否有效
          try {
            const response = await this.dispatch('user/getUserInfo')
            console.log('✅ 用户状态恢复成功，后端session有效')
            return true
          } catch (error) {
            console.warn('⚠️ 后端session可能已过期，清除本地状态')
            // 后端session无效，清除本地状态
            commit('CLEAR_USER')
            localStorage.removeItem('userInfo')
            localStorage.removeItem('loginTime')
            // 清除所有可能的缓存数据
            this.dispatch('user/clearAllCache')
            return false
          }
        } else {
          console.warn('⚠️ 本地登录状态已过期，清除本地状态')
          // 本地状态已过期，清除
          commit('CLEAR_USER')
          localStorage.removeItem('userInfo')
          localStorage.removeItem('loginTime')
          // 清除所有可能的缓存数据
          this.dispatch('user/clearAllCache')
          return false
        }
      }
      return false
    } catch (error) {
      console.error('恢复用户状态失败:', error)
      localStorage.removeItem('userInfo')
      localStorage.removeItem('loginTime')
      // 清除所有可能的缓存数据
      this.dispatch('user/clearAllCache')
      return false
    }
  },

  // 清除用户状态
  clearUserState({ commit }) {
    commit('CLEAR_USER')
    localStorage.removeItem('userInfo')
    localStorage.removeItem('loginTime')
  },

  // 清除所有缓存数据
  clearAllCache({ commit }) {
    // 清除用户状态
    commit('CLEAR_USER')
    
    // 清除所有本地存储
    localStorage.removeItem('userInfo')
    localStorage.removeItem('loginTime')
    localStorage.removeItem('lastTitleForm')
    localStorage.removeItem('lastTitleResult')
    localStorage.removeItem('lastTitle')
    localStorage.removeItem('lastOutline')
    localStorage.removeItem('report_history')
    
    // 清除其他可能的缓存
    localStorage.removeItem('thesisData')
    localStorage.removeItem('outlineData')
    localStorage.removeItem('contentData')
    localStorage.removeItem('chatHistory')
    localStorage.removeItem('userSettings')
    
    // 清除sessionStorage
    sessionStorage.clear()
    
    console.log('🧹 已清除所有缓存数据')
  }
}

const getters = {
  userInfo: state => state.userInfo,
  isLoggedIn: state => state.isLoggedIn,
  isVip: state => state.userInfo && state.userInfo.isVip,
  username: state => state.userInfo ? state.userInfo.username : '',
  nickname: state => state.userInfo ? state.userInfo.nickname : '',
  userId: state => state.userInfo ? state.userInfo.userId : null
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
} 