from EarlyBird.api.home import home_bp
from flask import jsonify, Blueprint, make_response, g
from EarlyBird.common.libs.api_result import ApiResponse, Result
from flask_pydantic import validate
import logging
import json
from EarlyBird.api.home.dantic import ParamSetting
from EarlyBird.api.home.service import HomeService


LOGGER = logging.getLogger(__name__)


@home_bp.route("/getSetting", methods=["POST", "GET"])
@validate()
def getSetting():

    res: Result = HomeService().getSetting()
    if res.isSucc():
        return ApiResponse().set_data(res.data).json()
    return ApiResponse().error(res.message).json()

@home_bp.route("/saveSetting", methods=["POST", "GET"])
@validate()
def saveSetting(body: ParamSetting):

    res: Result = HomeService().saveSetting(body)
    if res.isSucc():
        return ApiResponse().set_data(res.data).json()
    return ApiResponse().error(res.message).json()
