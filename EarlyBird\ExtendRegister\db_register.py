# 导入所有模型
from EarlyBird.model.user import User
from EarlyBird.model.thesis import Thesis
from EarlyBird.model.paragraph import Paragraph
from EarlyBird.model.apilog import ApiLog
from EarlyBird.model.generate_task import GenerateTask
from EarlyBird.model.setting import Setting
from EarlyBird.model.chat_log import ChatLog
from EarlyBird.common.database import db

def register_db(app):
    """db注册"""
    db.init_app(app)

    with app.app_context():
        # 确保所有模型都被导入
        models = [User, Thesis, Paragraph, ApiLog, GenerateTask, Setting, ChatLog]
        # 只创建不存在的表，不删除现有数据
        db.create_all()
