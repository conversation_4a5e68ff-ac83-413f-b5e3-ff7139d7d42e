<!-- 
作者名：EarlyBird
作者邮箱：<EMAIL>
官方网址：https://blog.zaoniao.vip
-->
<template>
  <div class="base-config">
    <h2>系统基础信息设置</h2>
    <el-form :model="form" label-width="150px">
      <!-- 卡片容器 - 使用flex布局 -->
      <div class="card-container">
        <!-- 基础信息设置 -->
        <el-card class="config-card">
          <div slot="header">
            <span>基础信息设置</span>
          </div>
          <el-form-item label="系统名称">
            <el-input v-model="form.systemName" placeholder="请输入系统名称"></el-input>
          </el-form-item>
          <el-form-item label="系统Logo">
            <el-upload
              class="logo-uploader"
              action=""
              :show-file-list="false"
              :before-upload="beforeLogoUpload"
              :on-change="handleLogoChange"
            >
              <img v-if="form.logoUrl" :src="form.logoUrl" class="logo-img" />
              <i v-else class="el-icon-plus logo-upload-icon"></i>
            </el-upload>
          </el-form-item>
          <el-form-item label="系统描述">
            <el-input type="textarea" v-model="form.description" :rows="3" placeholder="请输入系统描述"></el-input>
          </el-form-item>
        </el-card>
        
        <!-- 论文下载价格设置 -->
        <el-card class="config-card">
          <div slot="header">
            <span>论文下载价格设置</span>
          </div>
          <el-form-item label="启用论文下载收费">
            <el-switch v-model="form.thesisDownloadEnabled"></el-switch>
            <span class="form-tip">开启后，用户下载论文需要支付费用</span>
          </el-form-item>
          
          <el-form-item label="论文下载价格">
            <el-input-number 
              v-model="form.thesisDownloadPrice" 
              :min="0.01" 
              :max="999.99" 
              :step="0.01"
              :precision="2"
              :disabled="!form.thesisDownloadEnabled">
            </el-input-number>
            <span class="form-tip">单位：元</span>
          </el-form-item>
          
          <el-form-item label="VIP用户免费下载">
            <el-switch v-model="form.thesisDownloadVipFree" :disabled="!form.thesisDownloadEnabled"></el-switch>
            <span class="form-tip">开启后，VIP用户可以免费下载论文</span>
          </el-form-item>
          
          <el-form-item label="首次下载免费">
            <el-switch v-model="form.thesisDownloadFirstFree" :disabled="!form.thesisDownloadEnabled"></el-switch>
            <span class="form-tip">开启后，用户首次下载论文免费</span>
          </el-form-item>
        </el-card>
      </div>
      
      <el-form-item>
        <el-button type="primary" @click="onSave">保存设置</el-button>
        <el-button @click="resetForm">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { adminSettings } from '@/api/admin'

export default {
  name: 'BaseConfig',
  data() {
    return {
      form: {
        systemName: '',
        logoUrl: '',
        description: '',
        // 论文下载价格设置
        thesisDownloadEnabled: false,
        thesisDownloadPrice: 10.00,
        thesisDownloadVipFree: true,
        thesisDownloadFirstFree: true
      },
      loading: false
    }
  },
  created() {
    this.loadConfig()
  },
  methods: {
    // 加载配置
    async loadConfig() {
      try {
        this.loading = true
        
        // 先尝试从本地存储加载
        const localData = localStorage.getItem('baseConfig')
        if (localData) {
          const parsedData = JSON.parse(localData)
          this.form = { ...this.form, ...parsedData }
        }
        
        // 从后端加载
        try {
          const response = await adminSettings.getSettings()
          if (response.success && response.data) {
            // 基础配置
            if (response.data.system) {
              this.form.systemName = response.data.system.name || this.form.systemName
              this.form.logoUrl = response.data.system.logo || this.form.logoUrl
              this.form.description = response.data.system.description || this.form.description
            }
            
            // 论文下载配置
            if (response.data.thesis_download) {
              this.form.thesisDownloadEnabled = response.data.thesis_download.is_active === 'true'
              this.form.thesisDownloadPrice = parseFloat(response.data.thesis_download.price) || this.form.thesisDownloadPrice
              this.form.thesisDownloadVipFree = response.data.thesis_download.vip_free === 'true'
              this.form.thesisDownloadFirstFree = response.data.thesis_download.first_free === 'true'
            }
          }
        } catch (error) {
          console.warn('从后端加载配置失败，使用本地配置', error)
        }
      } finally {
        this.loading = false
      }
    },
    
    // 保存配置
    async onSave() {
      try {
        this.loading = true
        
        // 保存到本地存储
        localStorage.setItem('baseConfig', JSON.stringify(this.form))
        
        // 保存到后端
        try {
          const configData = {
            system: {
              name: this.form.systemName,
              logo: this.form.logoUrl,
              description: this.form.description
            },
            thesis_download: {
              is_active: this.form.thesisDownloadEnabled ? 'true' : 'false',
              price: this.form.thesisDownloadPrice.toString(),
              vip_free: this.form.thesisDownloadVipFree ? 'true' : 'false',
              first_free: this.form.thesisDownloadFirstFree ? 'true' : 'false'
            }
          }
          
          const response = await adminSettings.saveSettings(configData)
          
          if (response.success) {
            this.$message.success('设置保存成功')
          } else {
            this.$message.warning('保存到后端失败: ' + (response.message || '未知错误'))
          }
        } catch (error) {
          console.error('保存到后端失败', error)
          this.$message.warning('保存到后端失败，但已保存到本地')
        }
      } finally {
        this.loading = false
      }
    },
    
    // 重置表单
    resetForm() {
      this.loadConfig()
    },
    
    beforeLogoUpload(file) {
      const isImage = file.type.startsWith('image/')
      if (!isImage) {
        this.$message.error('只能上传图片文件！')
      }
      return isImage
    },
    handleLogoChange(file) {
      const reader = new FileReader()
      reader.onload = e => {
        this.form.logoUrl = e.target.result
      }
      reader.readAsDataURL(file.raw)
    }
  }
}
</script>

<style scoped>
.base-config {
  padding: 20px;
}
.card-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 20px;
}
.config-card {
  flex: 1;
  min-width: 300px;
}
.logo-uploader {
  display: inline-block;
}
.logo-img {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  border: 1px solid #eee;
  object-fit: contain;
}
.logo-upload-icon {
  font-size: 32px;
  color: #bbb;
  width: 80px;
  height: 80px;
  line-height: 80px;
  text-align: center;
  border: 1px dashed #ccc;
  border-radius: 8px;
  background: #fafbfc;
}
.form-tip {
  margin-left: 10px;
  color: #909399;
  font-size: 12px;
}
</style> 