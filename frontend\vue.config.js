const { defineConfig } = require('@vue/cli-service')
module.exports = defineConfig({
  filenameHashing: true,
  productionSourceMap: false,
  outputDir:'../EarlyBird/resources/ui',
  devServer: {
    host: '0.0.0.0',
    port: 8080,
    historyApiFallback: true,
    allowedHosts: ['127.0.0.1', 'ai.zaoniao.vip', 'localhost'],
    client: {
      overlay: false,
    },

    proxy: {
      '/api': {
        target: 'http://127.0.0.1:3301',
        changeOrigin: true,
      },
    }
  }
})
