<template>
  <div class="admin-stats-users">
    <el-card>
      <div slot="header" class="clearfix">
        <span>用户统计</span>
        <div class="header-right">
          <el-radio-group v-model="period" size="small" @change="fetchUserStats">
            <el-radio-button label="7d">最近7天</el-radio-button>
            <el-radio-button label="30d">最近30天</el-radio-button>
            <el-radio-button label="90d">最近90天</el-radio-button>
          </el-radio-group>
          <el-button style="margin-left: 10px;" size="small" type="primary" icon="el-icon-refresh" @click="fetchUserStats">
            刷新
          </el-button>
        </div>
      </div>
      
      <div v-loading="loading">
        <!-- 数据卡片 -->
        <div class="stat-cards">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-card shadow="hover" class="stat-card">
                <div class="stat-card-title">总用户数</div>
                <div class="stat-card-value">{{ statsData.total_users || 0 }}</div>
              </el-card>
            </el-col>
            <el-col :span="6">
              <el-card shadow="hover" class="stat-card">
                <div class="stat-card-title">VIP用户</div>
                <div class="stat-card-value">{{ statsData.vip_users || 0 }}</div>
              </el-card>
            </el-col>
            <el-col :span="6">
              <el-card shadow="hover" class="stat-card">
                <div class="stat-card-title">新增用户</div>
                <div class="stat-card-value">{{ statsData.new_users || 0 }}</div>
              </el-card>
            </el-col>
            <el-col :span="6">
              <el-card shadow="hover" class="stat-card">
                <div class="stat-card-title">锁定用户</div>
                <div class="stat-card-value">{{ statsData.locked_users || 0 }}</div>
              </el-card>
            </el-col>
          </el-row>
        </div>

        <!-- 图表区域 -->
        <div class="chart-container">
          <el-row :gutter="20">
            <!-- 每日新增用户趋势图 -->
            <el-col :span="16">
              <el-card shadow="hover" class="chart-card">
                <div slot="header" class="clearfix">
                  <span>每日新增用户趋势</span>
                </div>
                <div class="chart" id="daily-users-chart" style="height: 300px;"></div>
              </el-card>
            </el-col>
            
            <!-- 用户性别分布饼图 -->
            <el-col :span="8">
              <el-card shadow="hover" class="chart-card">
                <div slot="header" class="clearfix">
                  <span>用户性别分布</span>
                </div>
                <div class="chart" id="gender-chart" style="height: 300px;"></div>
              </el-card>
            </el-col>
          </el-row>

          <el-row :gutter="20" style="margin-top: 20px;">
            <!-- VIP等级分布图 -->
            <el-col :span="12">
              <el-card shadow="hover" class="chart-card">
                <div slot="header" class="clearfix">
                  <span>VIP等级分布</span>
                </div>
                <div class="chart" id="vip-level-chart" style="height: 300px;"></div>
              </el-card>
            </el-col>
            
            <!-- 用户增长率 -->
            <el-col :span="12">
              <el-card shadow="hover" class="chart-card">
                <div slot="header" class="clearfix">
                  <span>用户数据表格</span>
                </div>
                <el-table :data="tableData" style="width: 100%">
                  <el-table-column prop="date" label="日期" width="180"></el-table-column>
                  <el-table-column prop="count" label="新增用户数"></el-table-column>
                </el-table>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { adminStats } from '@/api/admin'
import * as echarts from 'echarts'

export default {
  name: 'AdminStatsUsers',
  data() {
    return {
      loading: false,
      period: '7d',
      statsData: {},
      tableData: [],
      charts: {
        dailyUsersChart: null,
        genderChart: null,
        vipLevelChart: null
      }
    }
  },
  mounted() {
    this.fetchUserStats()
    // 窗口大小变化时重绘图表
    window.addEventListener('resize', this.resizeCharts)
  },
  beforeDestroy() {
    // 移除事件监听
    window.removeEventListener('resize', this.resizeCharts)
    // 销毁图表实例
    Object.keys(this.charts).forEach(key => {
      if (this.charts[key]) {
        this.charts[key].dispose()
      }
    })
  },
  methods: {
    async fetchUserStats() {
      this.loading = true
      try {
        const response = await adminStats.getUserStats({ period: this.period })
        if (response.success) {
          this.statsData = response.data
          this.tableData = this.statsData.daily_stats || []
          this.$nextTick(() => {
            this.initCharts()
          })
        } else {
          this.$message.error(response.message || '获取用户统计数据失败')
        }
      } catch (error) {
        this.$message.error('获取用户统计数据失败：' + error.message)
      } finally {
        this.loading = false
      }
    },
    initCharts() {
      this.initDailyUsersChart()
      this.initGenderChart()
      this.initVipLevelChart()
    },
    initDailyUsersChart() {
      const chartDom = document.getElementById('daily-users-chart')
      if (!chartDom) return
      
      if (this.charts.dailyUsersChart) {
        this.charts.dailyUsersChart.dispose()
      }
      this.charts.dailyUsersChart = echarts.init(chartDom)
      
      const dailyStats = this.statsData.daily_stats || []
      const dates = dailyStats.map(item => item.date)
      const counts = dailyStats.map(item => item.count)
      
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: [{
          type: 'category',
          data: dates,
          axisLabel: {
            rotate: 45
          }
        }],
        yAxis: [{
          type: 'value'
        }],
        series: [{
          name: '新增用户',
          data: counts,
          type: 'line',
          smooth: true,
          itemStyle: {
            color: '#409EFF'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [{
                offset: 0, color: 'rgba(64, 158, 255, 0.7)'
              }, {
                offset: 1, color: 'rgba(64, 158, 255, 0.1)'
              }]
            }
          }
        }]
      }
      
      this.charts.dailyUsersChart.setOption(option)
    },
    initGenderChart() {
      const chartDom = document.getElementById('gender-chart')
      if (!chartDom) return
      
      if (this.charts.genderChart) {
        this.charts.genderChart.dispose()
      }
      this.charts.genderChart = echarts.init(chartDom)
      
      const genderStats = this.statsData.gender_stats || []
      const data = genderStats.map(item => {
        let name = item.gender || '未知'
        if (name === 'male') name = '男'
        if (name === 'female') name = '女'
        return {
          name: name,
          value: item.count
        }
      })
      
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          right: 10,
          top: 'center',
          data: data.map(item => item.name)
        },
        series: [
          {
            name: '性别分布',
            type: 'pie',
            radius: ['50%', '70%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '16',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: data
          }
        ]
      }
      
      this.charts.genderChart.setOption(option)
    },
    initVipLevelChart() {
      const chartDom = document.getElementById('vip-level-chart')
      if (!chartDom) return
      
      if (this.charts.vipLevelChart) {
        this.charts.vipLevelChart.dispose()
      }
      this.charts.vipLevelChart = echarts.init(chartDom)
      
      const vipLevelStats = this.statsData.vip_level_stats || []
      const data = vipLevelStats.map(item => {
        return {
          name: item.level ? `VIP${item.level}` : '普通用户',
          value: item.count
        }
      })
      
      const option = {
        tooltip: {
          trigger: 'item'
        },
        legend: {
          top: '5%',
          left: 'center'
        },
        series: [
          {
            name: 'VIP等级',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '16',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: data
          }
        ]
      }
      
      this.charts.vipLevelChart.setOption(option)
    },
    resizeCharts() {
      Object.keys(this.charts).forEach(key => {
        if (this.charts[key]) {
          this.charts[key].resize()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.admin-stats-users {
  .header-right {
    float: right;
  }
  
  .stat-cards {
    margin-bottom: 20px;
    
    .stat-card {
      text-align: center;
      
      .stat-card-title {
        font-size: 14px;
        color: #606266;
      }
      
      .stat-card-value {
        font-size: 24px;
        font-weight: bold;
        margin-top: 10px;
        color: #303133;
      }
    }
  }
  
  .chart-container {
    margin-top: 20px;
    
    .chart-card {
      margin-bottom: 20px;
    }
  }
}
</style> 