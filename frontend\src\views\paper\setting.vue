<template>
  <div class="setting-container">
    <div class="setting-card">
      <div class="card-header">
        <i class="el-icon-setting"></i>
        <h2>系统设置</h2>
      </div>

      <div class="setting-disabled">
        <div class="disabled-icon">
          <i class="el-icon-warning-outline"></i>
        </div>
        <div class="disabled-content">
          <h3>设置功能已迁移</h3>
          <p>系统设置功能已迁移到后台管理系统，用户端不再提供设置功能。</p>
          <p>如需修改系统配置，请联系管理员在后台管理系统中进行操作。</p>
          <div class="action-buttons">
            <el-button type="primary" @click="goBack">
              <i class="el-icon-back"></i>
              返回首页
            </el-button>
            <el-button @click="goToAdmin">
              <i class="el-icon-s-custom"></i>
              管理员登录
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "Setting",
  methods: {
    goBack() {
      this.$router.push('/')
    },
    goToAdmin() {
      this.$router.push('/admin/login')
    }
  }
};
</script>

<style scoped lang="scss">
.setting-container {
  min-height: 100vh;
  padding: 40px 20px;
  background: #f5f7fa;
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

.setting-card {
  width: 100%;
  max-width: 600px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;

  .card-header {
    padding: 24px;
    background: linear-gradient(135deg, #0083b0 0%, #00b4db 100%);
    color: white;
    display: flex;
    align-items: center;
    gap: 12px;

    i {
      font-size: 24px;
    }

    h2 {
      margin: 0;
      font-size: 20px;
      font-weight: 600;
    }
  }
}

.setting-disabled {
  padding: 60px 40px;
  text-align: center;

  .disabled-icon {
    margin-bottom: 30px;
    
    i {
      font-size: 80px;
      color: #e6a23c;
    }
  }

  .disabled-content {
    h3 {
      font-size: 24px;
      color: #303133;
      margin-bottom: 20px;
      font-weight: 600;
    }

    p {
      font-size: 16px;
      color: #606266;
      line-height: 1.6;
      margin-bottom: 15px;
    }

    .action-buttons {
      margin-top: 40px;
      
      .el-button {
        margin: 0 10px;
        padding: 12px 24px;
        font-size: 15px;
        
        i {
          margin-right: 6px;
        }
      }
    }
  }
}
</style>
