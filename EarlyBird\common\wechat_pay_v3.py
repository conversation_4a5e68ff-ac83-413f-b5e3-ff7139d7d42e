#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
作者名：EarlyBird
作者邮箱：<EMAIL>
官方网址：https://blog.zaoniao.vip

微信支付V3工具类
实现微信支付V3的签名、下单、查单、回调验证等功能
"""

import json
import time
import uuid
import base64
import hashlib
import requests
from datetime import datetime
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import padding, rsa
from cryptography.hazmat.backends import default_backend
import logging

logger = logging.getLogger(__name__)

class WeChatPayV3:
    """微信支付V3工具类"""
    
    def __init__(self, appid, mchid, private_key_path, serial_no, api_v3_key):
        """
        初始化微信支付V3
        
        Args:
            appid: 微信支付应用ID
            mchid: 微信支付商户号
            private_key_path: 商户私钥文件路径
            serial_no: 商户证书序列号
            api_v3_key: APIv3密钥
        """
        self.appid = appid
        self.mchid = mchid
        self.serial_no = serial_no
        self.api_v3_key = api_v3_key
        
        # 加载商户私钥
        try:
            # 处理相对路径，将其转换为绝对路径
            import os
            if not os.path.isabs(private_key_path):
                # 如果是相对路径，基于项目根目录进行解析
                project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
                private_key_path = os.path.join(project_root, private_key_path)
            
            logger.info(f"尝试加载私钥文件: {private_key_path}")
            
            with open(private_key_path, 'rb') as f:
                self.private_key = serialization.load_pem_private_key(
                    f.read(),
                    password=None,
                    backend=default_backend()
                )
            logger.info("商户私钥加载成功")
        except Exception as e:
            logger.error(f"加载商户私钥失败: {str(e)}")
            logger.error(f"尝试的路径: {private_key_path}")
            raise
        
        # 微信支付V3 API地址
        self.base_url = "https://api.mch.weixin.qq.com"
        
    def generate_signature(self, method, url_path, body, timestamp, nonce):
        """
        生成微信支付V3签名
        
        Args:
            method: HTTP方法 (GET, POST, PUT, DELETE, PATCH)
            url_path: 请求路径，不包含域名
            body: 请求体
            timestamp: 时间戳
            nonce: 随机字符串
            
        Returns:
            签名字符串
        """
        # 构造签名串
        signature_string = f"{method}\n{url_path}\n{timestamp}\n{nonce}\n{body}\n"
        
        # 使用商户私钥签名
        signature = self.private_key.sign(
            signature_string.encode('utf-8'),
            padding.PKCS1v15(),
            hashes.SHA256()
        )
        
        # Base64编码
        return base64.b64encode(signature).decode('utf-8')
    
    def generate_authorization(self, method, url_path, body):
        """
        生成Authorization头
        
        Args:
            method: HTTP方法
            url_path: 请求路径
            body: 请求体
            
        Returns:
            Authorization头值
        """
        timestamp = str(int(time.time()))
        nonce = str(uuid.uuid4()).replace('-', '')
        
        signature = self.generate_signature(method, url_path, body, timestamp, nonce)
        
        # 构造Authorization头
        authorization = (
            f'WECHATPAY2-SHA256-RSA2048 '
            f'mchid="{self.mchid}",'
            f'nonce_str="{nonce}",'
            f'timestamp="{timestamp}",'
            f'serial_no="{self.serial_no}",'
            f'signature="{signature}"'
        )
        
        return authorization, timestamp, nonce
    
    def create_native_order(self, out_trade_no, description, amount, notify_url, client_ip=None):
        """
        创建Native支付订单（生成二维码）
        
        Args:
            out_trade_no: 商户订单号
            description: 商品描述
            amount: 金额（元）
            notify_url: 回调通知地址
            client_ip: 客户端IP（可选）
            
        Returns:
            包含code_url的响应数据
        """
        url = f"{self.base_url}/v3/pay/transactions/native"
        
        # 构造请求体
        data = {
            "appid": self.appid,
            "mchid": self.mchid,
            "description": description,
            "out_trade_no": out_trade_no,
            "notify_url": notify_url,
            "amount": {
                "total": int(amount * 100),  # 转换为分
                "currency": "CNY"
            }
        }
        
        # 注意：Native支付不需要payer.client_ip参数，该参数仅在H5支付等场景中使用
        
        body = json.dumps(data, separators=(',', ':'))
        
        # 生成Authorization头
        authorization, timestamp, nonce = self.generate_authorization("POST", "/v3/pay/transactions/native", body)
        
        # 发送请求
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json",
            "Authorization": authorization
        }
        
        try:
            response = requests.post(url, headers=headers, data=body, timeout=30)
            result = response.json()
            
            # 微信支付下单响应
            
            if response.status_code == 200 and result.get('code_url'):
                return {
                    'success': True,
                    'code_url': result['code_url'],
                    'prepay_id': result.get('prepay_id'),
                    'out_trade_no': out_trade_no
                }
            else:
                logger.error(f"微信支付下单失败: {result}")
                return {
                    'success': False,
                    'error_code': result.get('code'),
                    'error_message': result.get('message')
                }
                
        except Exception as e:
            logger.error(f"微信支付下单异常: {str(e)}")
            return {
                'success': False,
                'error_message': f"请求异常: {str(e)}"
            }
    
    def query_order(self, out_trade_no):
        """
        查询订单状态
        
        Args:
            out_trade_no: 商户订单号
            
        Returns:
            订单状态信息
        """
        url = f"{self.base_url}/v3/pay/transactions/out-trade-no/{out_trade_no}?mchid={self.mchid}"
        
        # 生成Authorization头
        authorization, timestamp, nonce = self.generate_authorization("GET", f"/v3/pay/transactions/out-trade-no/{out_trade_no}?mchid={self.mchid}", "")
        
        headers = {
            "Accept": "application/json",
            "Authorization": authorization
        }
        
        try:
            response = requests.get(url, headers=headers, timeout=30)
            result = response.json()
            
            # 微信支付查单响应
            
            if response.status_code == 200:
                return {
                    'success': True,
                    'trade_state': result.get('trade_state'),
                    'trade_state_desc': result.get('trade_state_desc'),
                    'amount': result.get('amount', {}).get('total', 0) / 100,  # 转换为元
                    'success_time': result.get('success_time'),
                    'transaction_id': result.get('transaction_id')
                }
            else:
                logger.error(f"微信支付查单失败: {result}")
                return {
                    'success': False,
                    'error_code': result.get('code'),
                    'error_message': result.get('message')
                }
                
        except Exception as e:
            logger.error(f"微信支付查单异常: {str(e)}")
            return {
                'success': False,
                'error_message': f"请求异常: {str(e)}"
            }
    
    def verify_notify_signature(self, headers, body):
        """
        验证回调通知签名
        
        Args:
            headers: 请求头
            body: 请求体
            
        Returns:
            验证结果
        """
        try:
            # 获取签名信息
            timestamp = headers.get('Wechatpay-Timestamp')
            nonce = headers.get('Wechatpay-Nonce')
            signature = headers.get('Wechatpay-Signature')
            serial = headers.get('Wechatpay-Serial')
            
            if not all([timestamp, nonce, signature, serial]):
                logger.error("回调通知缺少必要的签名信息")
                return False
            
            # 构造验签串
            sign_string = f"{timestamp}\n{nonce}\n{body}\n"
            
            # 这里需要加载微信支付平台证书来验签
            # 为了简化，这里先返回True，实际使用时需要完整的验签逻辑
            # 回调通知签名验证通过
            return True
            
        except Exception as e:
            logger.error(f"回调通知签名验证失败: {str(e)}")
            return False
    
    def decrypt_notify_data(self, encrypted_data, associated_data, nonce):
        """
        解密回调通知数据
        
        Args:
            encrypted_data: 加密数据
            associated_data: 附加数据
            nonce: 随机串
            
        Returns:
            解密后的数据
        """
        try:
            # 使用APIv3密钥解密
            # 这里需要实现AES-GCM解密
            # 为了简化，这里先返回原始数据，实际使用时需要完整的解密逻辑
            # 回调通知数据解密成功
            return encrypted_data
            
        except Exception as e:
            logger.error(f"回调通知数据解密失败: {str(e)}")
            return None 