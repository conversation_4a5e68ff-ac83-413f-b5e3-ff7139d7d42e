<template>
  <!-- 生成论文标题的VUE子模块，这个模块定义了调用服务器的方法和路由，具体在/api/generate中进行了定义 -->
  <div class="chat-layout">
    <!-- 左侧区域：公告和输入框 -->
    <div class="chat-left">
      <!-- 使用说明公告 -->
      <div class="announcement">
        <div class="announcement-title">
          <i class="el-icon-info-filled"></i>
          <span>使用说明</span>
        </div>
        <div class="announcement-content">
          <div class="step-list">
            <div class="step-item">
              <div class="step-number">1</div>
              <div class="step-text">在设置页面选择并配置您想使用的 AI 模型（支持 Kimi、千问、DeepSeek 等）</div>
            </div>
            <div class="step-item">
              <div class="step-number">2</div>
              <div class="step-text">在下方输入框中输入您的问题或要求，按发送按钮或回车键发送</div>
            </div>
            <div class="step-item">
              <div class="step-number">3</div>
              <div class="step-text">AI 会根据您的输入给出相应的回复</div>
            </div>
          </div>
          
          <div class="features-section">
            <div class="features-title">您可以通过此功能：</div>
            <div class="features-grid">
              <div class="feature-item">
                <i class="el-icon-edit-outline"></i>
                <span>咨询论文写作相关问题</span>
              </div>
              <div class="feature-item">
                <i class="el-icon-refresh"></i>
                <span>请求修改或优化论文内容</span>
              </div>
              <div class="feature-item">
                <i class="el-icon-reading"></i>
                <span>获取写作建议和指导</span>
              </div>
              <div class="feature-item">
                <i class="el-icon-check"></i>
                <span>验证 API 配置是否正确</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部操作区域 -->
      <div class="chat-bottom-area">
        <!-- 模型状态栏 -->
        <div class="model-status">
          <div class="model-selector">
            <i class="el-icon-cpu"></i>
            <span class="label">当前AI模型：</span>
            <el-select 
              v-model="modelName" 
              size="small" 
              @change="handleModelChange"
              placeholder="请选择AI模型"
            >
              <el-option
                v-for="model in modelList"
                :key="model.id"
                :label="model.name"
                :value="model.id"
              >
                <span class="model-option">
                  <i :class="model.icon || 'el-icon-cpu'" class="model-icon"></i>
                  {{ model.name }}
                  <span class="model-desc" v-if="model.description">{{ model.description }}</span>
                </span>
              </el-option>
            </el-select>
          </div>
          <div class="loading-status" v-if="isLoading">
            <i class="el-icon-loading"></i>
            正在获取结果，请稍等...
          </div>
        </div>

        <!-- 输入区域 -->
        <div class="chat-input">
          <input
            ref="chatInput"
            type="text"
            v-model="userMessage"
            :disabled="isLoading || !modelName"
            @keydown.enter.prevent="handleSend"
            placeholder="请输入您的问题或要求，按回车发送"
            class="chat-text-input"
          />
          <el-button 
            type="primary" 
            :loading="isLoading"
            :disabled="!modelName"
            @click="handleSend"
            icon="el-icon-s-promotion"
          >
            发送
          </el-button>
        </div>
      </div>
    </div>

    <!-- 右侧聊天记录区域 -->
    <div class="chat-right">
      <div class="chat-container">
        <div class="chat-header">
          <span>聊天记录</span>
          <el-button 
            type="text" 
            size="mini" 
            @click="clearChatLog" 
            v-if="chatLog && chatLog.length > 0"
          >
            <i class="el-icon-delete"></i> 清空记录
          </el-button>
        </div>
        <div class="chat-messages-wrapper">
          <div class="chat-list" id="chatListBox">
            <div class="chat-more" @click.stop="handleLoadMore" v-if="hasMore">
              加载更早消息
            </div>
            <div class="chat-more" v-else>没有更多了</div>
            <template v-for="chat in chatLog">
              <div
                :key="chat.id"
                :class="['chat', chat.type == 'recv' ? 'recv' : 'send']"
              >
                <div class="chat-title">
                  <span class="chat-avatar">
                    <i :class="chat.type == 'recv' ? 'el-icon-service' : 'el-icon-user'"></i>
                  </span>
                  <span class="chat-name">{{ chat.type == "send" ? "我" : getModelDisplayName(chat.modelName) }}</span>
                  <span class="chat-time">{{ chat.createAt }}</span>
                  <span class="chat-actions">
                    <el-button type="text" size="mini" @click.stop="handleDelete(chat.id)">
                      <i class="el-icon-delete"></i>
                    </el-button>
                  </span>
                </div>
                <div class="chat-body">{{ chat.msg }}</div>
              </div>
            </template>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import Date from "@/utils/datetime";
import { chat, getChatLog, delChat } from "@/api/chat";
import { getSetting, saveSetting } from "@/api/setting";
import { getModelList } from "@/utils/model";
const modelList = getModelList();
export default {
  name: "智能互动",

  data() {
    return {
      modelList: modelList,
      userMessage: "",
      isLoading: false,
      modelName: "",
      chatLog: [],
      pageNo: 1,
      pageSize: 9,
      hasMore: true,
    };
  },
  mounted() {
    // 获取设置
    getSetting({}).then((res) => {
      this.modelName = res.data["modelName"];
      
      // 设置加载完毕后聚焦输入框
      this.$nextTick(() => {
        if (this.$refs.chatInput) {
          this.$refs.chatInput.focus();
        }
      });
    });
    
    // 加载聊天记录
    this.handleLoadMore(true);
    
    // 添加点击事件监听器
    document.addEventListener('click', this.handleDocumentClick);
    
    // 添加定时器，定期尝试聚焦输入框
    this.focusInterval = setInterval(() => {
      if (this.$refs.chatInput && !this.isLoading && this.modelName) {
        this.$refs.chatInput.focus();
      }
    }, 1000);
  },
  beforeDestroy() {
    // 组件销毁前移除事件监听器和定时器
    document.removeEventListener('click', this.handleDocumentClick);
    if (this.focusInterval) {
      clearInterval(this.focusInterval);
    }
  },
  methods: {
    clearChatLog() {
      // 显示确认对话框
      this.$confirm('确定要清空所有聊天记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 用户确认清空
        this.chatLog = [];
        this.$message({
          type: 'success',
          message: '聊天记录已清空'
        });
      }).catch(() => {
        // 用户取消操作
      });
    },
    handleDocumentClick() {
      // 点击页面任何位置都会聚焦到输入框
      if (this.$refs.chatInput && !this.isLoading && this.modelName) {
        this.$refs.chatInput.focus();
      }
    },
    handleScrollTop() {
      this.$nextTick(function () {
        var container = document.querySelector(".chat-messages-wrapper");
        if (container) {
          container.scrollTop = 0;
        }
      });
    },
    getModelDisplayName(modelId) {
      const model = this.modelList.find(m => m.id === modelId);
      return model ? model.name : modelId;
    },
    handleScrollButtom() {
      this.$nextTick(function () {
        var container = document.querySelector(".chat-messages-wrapper");
        if (container) {
          // 确保滚动到最底部
          container.scrollTop = container.scrollHeight + 1000;
        }
      });
    },
    handleLoadMore() {
      getChatLog({ pageNo: this.pageNo, pageSize: this.pageSize }).then((res) => {
        // 记录当前滚动位置和高度
        const container = document.querySelector(".chat-messages-wrapper");
        const oldScrollHeight = container ? container.scrollHeight : 0;
        const oldScrollTop = container ? container.scrollTop : 0;
        
        // 添加新消息
        var newChatLog = res.data.concat(this.chatLog);
        this.chatLog = newChatLog;
        this.pageNo += 1;

        if (res.data.length < this.pageSize) {
          this.hasMore = false;
        }
        
        // 在DOM更新后调整滚动位置
        this.$nextTick(() => {
          if (container) {
            // 保持相对位置不变
            const newScrollHeight = container.scrollHeight;
            const scrollDiff = newScrollHeight - oldScrollHeight;
            container.scrollTop = oldScrollTop + scrollDiff;
          }
        });
      });
    },
    handleDelete(id) {
      if (id < 0) {
        window.location.reload();
        return;
      }
      delChat({ chatId: id }).then((res) => {
        var chatLogs = JSON.parse(JSON.stringify(this.chatLog));
        var result = [];
        for (var i in chatLogs) {
          if (chatLogs[i]["id"] == id) {
            continue;
          }
          result.push(chatLogs[i]);
        }
        this.chatLog = result;
      });
    },
    appendChatLog(type, msg, id = -1) {
      var data = JSON.parse(JSON.stringify(this.chatLog));
      data.push({
        id: id,
        type: type,
        msg: msg,
        modelName: this.modelName,
        createAt: new Date().strftime(),
      });
      this.chatLog = data;
    },

    handleSend() {
      if (!this.modelName) {
        this.$message({ type: "error", message: "请在设置界面选择AI模型" });
        return;
      }
      if (this.userMessage == "") {
        this.$message({ type: "error", message: "请填写输入内容" });
        return;
      }
      
      // 先添加用户消息到聊天记录
      this.appendChatLog("send", this.userMessage);
      
      // 清空输入框
      const sentMessage = this.userMessage;
      this.userMessage = "";
      
      // 聚焦输入框，方便继续输入
      this.$nextTick(() => {
        if (this.$refs.chatInput) {
          this.$refs.chatInput.focus();
        }
      });
      
      // 滚动到底部
      this.handleScrollButtom();

      // 设置加载状态
      this.isLoading = true;
      
      // 发送请求到后端
      chat({ userMessage: sentMessage, modelName: this.modelName })
        .then((res) => {
          this.isLoading = false;
          console.log("API响应:", res);

          // 检查响应格式
          if (res && (res.is_success || res.code === 0)) {
            // 成功响应
            var data = res.data || {};
            this.appendChatLog("recv", data.msg || "无回复内容", data.chatId || -1);
          } else {
            // 显示错误消息
            const errorMsg = res.message || res.msg || "请求失败，请重试";
            this.appendChatLog("recv", errorMsg, -1);
          }
          
          // 滚动到底部显示新消息
          this.handleScrollButtom();
        })
        .catch((e) => {
          this.isLoading = false;
          this.appendChatLog("recv", "请求出错，请检查网络连接", -1);
          this.handleScrollButtom();
          console.error("聊天请求出错:", e);
        });
    },
    handleModelChange(modelName) {
      // 保存用户选择的模型
      this.modelName = modelName;
      // 保存到设置中，修改为正确的参数格式 { setting: { modelName: modelName } }
      saveSetting({ setting: { modelName: modelName } }).then(() => {
        this.$message.success('AI模型已切换');
      }).catch((e) => {
        this.$message.error('AI模型切换失败');
      });
    },
  },
};
</script>

<style scoped lang="scss">
.chat-layout {
  display: flex;
  height: calc(100vh - 70px); /* 进一步减少顶部和底部空间，从80px改为70px */
  padding: 8px 20px; /* 进一步减少顶部和底部间距 */
  background: #f5f7fa;
  overflow: hidden;
  gap: 20px;
}

.chat-left {
  width: 40%;
  min-width: 400px;
  display: flex;
  flex-direction: column;
  gap: 10px; /* 减少组件间距，从15px改为10px */
  max-height: 100%;
}

.chat-right {
  width: 60%;
  flex: 1;
  display: flex;
  flex-direction: column;
  max-height: 100%;
}

.chat-container {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  height: 100%;

  .chat-header {
    padding: 8px 15px; /* 进一步减少内边距，从10px改为8px */
    font-size: 16px;
    font-weight: bold;
    color: #409EFF;
    border-bottom: 1px solid #ebeef5;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .chat-messages-wrapper {
    flex: 1;
    overflow-y: auto;
    padding: 0;
    display: flex;
    flex-direction: column;
    max-height: calc(100% - 40px); /* 减去header的高度，从45px改为40px */
    scrollbar-width: thin; /* Firefox */
    scrollbar-color: #c0c4cc #f5f7fa; /* Firefox */
    
    &::-webkit-scrollbar {
      width: 6px;
    }
    
    &::-webkit-scrollbar-track {
      background: #f5f7fa;
      border-radius: 3px;
    }
    
    &::-webkit-scrollbar-thumb {
      background-color: #c0c4cc;
      border-radius: 3px;
      
      &:hover {
        background-color: #909399;
      }
    }
  }
  
  .chat-list {
    padding: 10px; /* 减少内边距，从12px改为10px */
    display: flex;
    flex-direction: column;
    width: 100%;
  }
}

.chat-list {
  .chat-more {
    text-align: center;
    color: #909399;
    padding: 10px;
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
      color: #409EFF;
    }
  }

  .chat {
    margin-bottom: 12px; /* 减少消息间距，从15px改为12px */
    border-radius: 8px;
    overflow: hidden;
    width: 100%;

    .chat-title {
      padding: 8px 12px; /* 减少内边距，从10px 15px改为8px 12px */
      display: flex;
      align-items: center;
      gap: 8px; /* 减少间距，从10px改为8px */
      border-bottom: 1px solid #ebeef5;

      .chat-avatar {
        width: 22px; /* 减小头像尺寸，从24px改为22px */
        height: 22px; /* 减小头像尺寸，从24px改为22px */
        border-radius: 50%;
        background: #409EFF;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .chat-name {
        font-weight: 500;
        flex: 1;
      }

      .chat-time {
        color: #909399;
        font-size: 12px;
      }
    }

    .chat-body {
      padding: 10px; /* 减少内边距，从12px改为10px */
      line-height: 1.5; /* 减少行高，从1.6改为1.5 */
      white-space: pre-wrap;
      word-break: break-all;
    }

    &.send {
      background: #ecf5ff;

      .chat-title {
        background: #d9ecff;
      }
    }

    &.recv {
      background: #fff;

      .chat-title {
        background: #f5f7fa;
      }
    }
  }
}

.model-status {
  padding: 6px 12px; /* 减少内边距，从8px 15px改为6px 12px */
  background: #fff;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  font-size: 13px;

  .model-info {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #409EFF;

    &.error {
      color: #f56c6c;
    }
  }

  .loading-status {
    color: #409EFF;
    display: flex;
    align-items: center;
    gap: 8px;
  }
}

.chat-input {
  background: #fff;
  border-radius: 8px;
  padding: 8px 10px; /* 调整内边距，从10px改为8px 10px */
  display: flex;
  gap: 10px; /* 减少间距，从12px改为10px */
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  min-height: 54px; /* 减少高度，从56px改为54px */

  .chat-text-input {
    flex: 1;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 0 12px; /* 减少内边距，从0 15px改为0 12px */
    font-size: 14px;
    line-height: 38px; /* 减少行高，从40px改为38px */
    height: 38px; /* 减少高度，从40px改为38px */
    color: #606266;
    transition: border-color 0.2s;
    outline: none;
    font-family: inherit;
    
    &:focus {
      border-color: #409EFF;
    }
    
    &:disabled {
      background-color: #f5f7fa;
      cursor: not-allowed;
    }
  }
  
  .el-button {
    align-self: center;
    height: 38px; /* 减少高度，从40px改为38px */
    padding: 0 18px; /* 减少内边距，从0 20px改为0 18px */
  }
}

.chat-bottom-area {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: auto; /* 将底部区域推到底部 */
}

.announcement {
  background: #ffffff;
  border-radius: 12px;
  padding: 15px; /* 减少内边距，从20px改为15px */
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  margin-bottom: 0;
  transition: all 0.3s ease;
  flex: 1;
  overflow-y: auto;
  max-height: calc(100% - 130px); /* 进一步调整高度，从140px改为130px */
  scrollbar-width: thin; /* Firefox */
  scrollbar-color: #c0c4cc #f5f7fa; /* Firefox */
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f5f7fa;
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background-color: #c0c4cc;
    border-radius: 3px;
    
    &:hover {
      background-color: #909399;
    }
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.08);
  }

  .announcement-title {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px; /* 减少标题下方间距，从20px改为15px */
    padding-bottom: 10px; /* 减少底部padding，从12px改为10px */
    border-bottom: 2px solid #f0f2f5;

    i {
      font-size: 24px;
      color: #409EFF;
    }

    span {
      font-size: 18px;
      font-weight: 600;
      color: #2c3e50;
    }
  }

  .announcement-content {
    color: #4c5667;
  }

  .step-list {
    margin-bottom: 20px; /* 减少底部间距，从24px改为20px */

    .step-item {
      display: flex;
      align-items: flex-start;
      margin-bottom: 12px; /* 减少步骤间距，从16px改为12px */
      padding: 10px; /* 减少内边距，从12px改为10px */
      background: #f8fafc;
      border-radius: 8px;
      transition: all 0.2s ease;

      &:hover {
        background: #f1f5f9;
        transform: translateX(5px);
      }

      .step-number {
        width: 28px;
        height: 28px;
        background: #409EFF;
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        margin-right: 12px;
        flex-shrink: 0;
      }

      .step-text {
        font-size: 14px;
        line-height: 1.6;
        padding-top: 4px;
      }
    }
  }

  .features-section {
    .features-title {
      font-size: 16px;
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 16px;
    }

    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 16px;

      .feature-item {
        display: flex;
        align-items: center;
        padding: 12px;
        background: #f8fafc;
        border-radius: 8px;
        transition: all 0.2s ease;

        &:hover {
          background: #f1f5f9;
          transform: translateX(5px);
        }

        i {
          font-size: 20px;
          color: #409EFF;
          margin-right: 12px;
        }

        span {
          font-size: 14px;
          color: #4c5667;
        }
      }
    }
  }
}
</style>

<style lang="scss">
/* 移除全局样式，因为我们已经不使用el-textarea了 */
</style>
