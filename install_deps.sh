#!/bin/bash

echo "================================================"
echo "🚀 早鸟论文系统 - Python依赖快速安装"
echo "================================================"
echo

# 检查Python版本
echo "📋 检查Python环境..."
if ! command -v python3 &> /dev/null; then
    echo "❌ 错误: 未找到Python3，请先安装Python 3.8+"
    exit 1
fi

PYTHON_VERSION=$(python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
echo "✅ Python版本: $PYTHON_VERSION"
echo

# 检查pip
echo "📋 检查pip..."
if ! python3 -m pip --version &> /dev/null; then
    echo "❌ 错误: pip不可用，请先安装pip"
    exit 1
fi
echo "✅ pip检查通过"
echo

# 升级pip
echo "📦 升级pip..."
python3 -m pip install --upgrade pip
echo

# 安装依赖
echo "📦 安装项目依赖..."
echo "选择安装方式:"
echo "1. 使用默认源安装"
echo "2. 使用清华镜像源安装（推荐，速度更快）"
echo
read -p "请选择 (1 或 2): " choice

if [ "$choice" = "2" ]; then
    echo "使用清华镜像源安装..."
    python3 -m pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
else
    echo "使用默认源安装..."
    python3 -m pip install -r requirements.txt
fi

if [ $? -ne 0 ]; then
    echo
    echo "❌ 依赖安装失败，请检查错误信息"
    exit 1
fi

echo
echo "✅ 依赖安装完成！"
echo
echo "🎉 现在可以启动项目了："
echo "   python3 EarlyBird/server.py"
echo "   或"
echo "   python3 EarlyBird/web_server.py"
echo
