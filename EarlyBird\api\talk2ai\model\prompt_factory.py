import json
import logging
from flask import g
from EarlyBird.api.talk2ai.dantic import (
    ParamOutline,
    ParamTitle,
    ParamGetContentFromOutline,
    ParamGenerateSingleParagraph,
    ParamRegenDigest,
)
from EarlyBird.api.talk2ai.model.data_enhanced_prompts import DataEnhancedPrompts


LOGGER = logging.getLogger(__name__)


def getTitle(param: ParamTitle):

    LOGGER.info(f"getTitle 参数: {param}")
    if param.isVipUser:
        param.size = 6
        keywordNumbser = 10
    else:
        param.size = 2
        keywordNumbser = 4
    user_message = f"你是一名{param.level}学生，要拟定{param.size}个论文的标题.论文是关于'{param.domain}'领域,具体与'{param.topic}'有关。"
    if param.keyword != "":

        user_message += f"论文内容应包含关键字'{param.keyword}'。"

    #      // tt 是英文，kk是用户选择的，默认是中文。大模型跟随问题，不得不这么做。

    user_message += f"论文标题的语言是 {param.lang}。"
    user_message += f"请返回json数据，包含一个名为'titles'的数组,数组中每一项都包含3个字段 :'tt','kk','kws'，分别表示标题的英文版本、{param.lang}版本、{keywordNumbser}个相关的关键字"

    return user_message


def getOutline(param: ParamOutline):

    user_message = f"角色:你是一位{param.level}学生."
    user_message += f"任务:要为一篇题目为'{param.title}'的论文拟定提纲."
    user_message += f"语言:使用写论文的正式语言,{param.style}."
    user_message += f"约束:写作语言是{param.lang},论文包含{param.paragraphCount}段落,每段包含{param.secondParagraphCount}子段落,首先是'绪论',不要'参考文献'和'致谢'."
    
    # 增强提纲的数据丰富性要求
    user_message += f"\n\n【数据丰富性要求】\n"
    user_message += f"1. 提纲结构要体现数据支撑：\n"
    user_message += f"   - 每个段落标题要体现具体的数据分析方向\n"
    user_message += f"   - 子段落要包含数据收集、数据分析、数据对比等环节\n"
    user_message += f"   - 确保有专门的段落用于数据展示和分析\n"
    
    user_message += f"2. 段落内容要求：\n"
    user_message += f"   - 绪论：包含研究背景、数据现状、研究意义\n"
    user_message += f"   - 理论分析：包含理论基础、数据模型、分析方法\n"
    user_message += f"   - 实证研究：包含数据收集、统计分析、结果展示\n"
    user_message += f"   - 案例分析：包含具体案例、数据对比、效果评估\n"
    user_message += f"   - 结论：包含数据总结、研究发现、政策建议\n"
    
    user_message += f"3. 子段落要求：\n"
    user_message += f"   - 每个子段落都要有明确的数据分析目标\n"
    user_message += f"   - 包含数据来源、分析方法、结果展示等要素\n"
    user_message += f"   - 确保逻辑清晰，数据支撑充分\n"
    
    user_message += (
        "格式:返回json格式,json数据中，只有一个'subtitle'字段,这个字段的值是数组。"
    )
    user_message += f"数组中每个元素，代表论文的一个段落。每段是一个json字典,其中包含一个'title'字段,表示段落标题。"
    user_message += f"每段包含1个'subtitle'数组,表示该段的子段落,是一个数组,其中每个元素是一个字典,键是'title',值是标题。不要加入'content'字段,不要省略段落"

    return user_message


def generateSingleParagraph(param: ParamGenerateSingleParagraph):
    user_message = f"你是一位专业的学术论文写作专家，正在撰写一篇标题为《{param.title}》的论文。"
    
    # 根据字数控制模式设置字数要求
    if param.lengthMode == "fixed":
        user_message += f"当前需要生成的是第'{param.paragraphTitle}'段落，此段内容必须控制在{param.length}字左右。"
    elif param.lengthMode == "range":
        user_message += f"当前需要生成的是第'{param.paragraphTitle}'段落，此段内容字数应在{param.minLength}-{param.maxLength}字之间。"
    else:  # auto模式
        user_message += f"当前需要生成的是第'{param.paragraphTitle}'段落，此段内容大约{param.length}字，可根据内容重要性适当调整。"
    
    user_message += f"写作语言是{param.lang}，需要使用正式的非生活化的论文语言。"
    
    # 添加内容风格要求
    style_instructions = {
        "academic": "使用严谨的学术表达方式，注重逻辑性和专业性",
        "popular": "使用通俗易懂的表达方式，便于读者理解",
        "concise": "内容简洁明了，避免冗余表达",
        "detailed": "内容详细深入，充分展开论述",
        "case_rich": "多举具体案例和实例来支撑论点"
    }
    
    if param.contentStyle in style_instructions:
        user_message += f"\n内容风格要求：{style_instructions[param.contentStyle]}。"
    
    # 添加上下文信息
    if hasattr(param, 'context') and param.context:
        user_message += f"\n\n【论文上下文信息】\n"
        user_message += f"论文整体结构：{param.context.get('outline', '')}\n"
        
        # 添加已生成段落的内容作为上下文
        if param.context.get('previous_paragraphs'):
            user_message += f"\n【已生成段落内容】\n"
            for i, prev_para in enumerate(param.context['previous_paragraphs'], 1):
                user_message += f"{i}. {prev_para['title']}：{prev_para['content'][:200]}...\n"
        
        # 添加后续段落标题作为参考
        if param.context.get('next_paragraphs'):
            user_message += f"\n【后续段落标题】\n"
            for i, next_para in enumerate(param.context['next_paragraphs'], 1):
                user_message += f"{i}. {next_para['title']}\n"
    
    # 使用数据增强功能
    user_message = DataEnhancedPrompts.enhance_paragraph_prompt(user_message, param.paragraphTitle)
    
    # 增强数据内容要求
    user_message += f"\n\n【数据丰富性要求】\n"
    user_message += f"1. 必须包含具体的数据支撑，如：\n"
    user_message += f"   - 百分比数据（如：增长25%、下降30%、占比60%等）\n"
    user_message += f"   - 具体数值（如：销售额100万、用户数5000、成本降低20万等）\n"
    user_message += f"   - 时间数据（如：2023年、近5年、第一季度等）\n"
    user_message += f"   - 对比数据（如：相比去年同期、与行业平均水平相比等）\n"
    user_message += f"   - 排名数据（如：位居第一、排名第三等）\n"
    
    user_message += f"2. 数据使用要求：\n"
    user_message += f"   - 数据要真实可信，符合实际情况\n"
    user_message += f"   - 数据要有来源说明（如：根据XX报告显示、XX研究指出等）\n"
    user_message += f"   - 数据要有分析解读，不能只是罗列数字\n"
    user_message += f"   - 数据要与段落主题高度相关\n"
    
    user_message += f"3. 案例和实例要求：\n"
    user_message += f"   - 提供具体的案例或实例来支撑论点\n"
    user_message += f"   - 案例要包含具体的时间、地点、人物或组织\n"
    user_message += f"   - 实例要有详细的过程描述和结果分析\n"
    
    user_message += f"4. 研究数据要求：\n"
    user_message += f"   - 引用相关的研究数据或调查报告\n"
    user_message += f"   - 提供权威机构发布的数据（如：统计局、行业协会等）\n"
    user_message += f"   - 包含学术研究的最新发现和结论\n"
    
    user_message += f"\n【写作要求】\n"
    user_message += f"1. 确保与已生成段落的内容逻辑连贯，避免重复和矛盾\n"
    user_message += f"2. 为后续段落做好铺垫，保持论文整体结构的完整性\n"
    user_message += f"3. 使用学术性的表达方式，避免口语化表达\n"
    user_message += f"4. 句首不要使用'首先'、'其次'、'最后'等关联词\n"
    user_message += f"5. 每个论点都要有数据或案例支撑\n"
    user_message += f"6. 数据要准确、具体、有说服力\n"
    
    if param.instruction != "":
        user_message += f"7. 特殊要求：{param.instruction}\n"
    
    user_message += f"\n【输出格式】\n"
    user_message += f"只输出段落正文内容，不要包含段落标题，不要使用markdown语法，不要有子段落。"
    user_message += f"内容必须包含丰富的数据、案例和实例，确保每个论点都有具体的数据支撑。"

    return user_message


def generateReference(param: ParamRegenDigest):
    user_message = f"角色:你是一名专家学者."
    user_message += (
        f"任务:给一篇标题为'{param.title}'的论文搜集参考文献清单,中英文文献都可以"
    )
    user_message += f"约束:大约需要10偏左右."
    user_message += (
        "返回结果为json,格式如下--\n[{title:'',author:'',year:'','journal':''}]"
    )

    return user_message


def generateThanks(param: ParamRegenDigest):
    user_message = f"角色:你是一名专家学者."
    user_message += (
        f"任务:给一篇标题为'{param.title}'的论文写致谢词，写作语言是{param.lang}。"
    )
    user_message += f"约束:长度500字，使用正式论文语言。不要使用转折词和连词。"
    user_message += "返回结果为json格式，如下---\n{thanks:'内容'}"

    return user_message


def generateDigest(param: ParamRegenDigest):
    user_message = f"角色:你是一名专家学者."
    user_message += f"任务:给一篇标题为《{param.title}》的论文写一段摘要和10个关键词,写作语言是{param.lang}。"
    user_message += f"约束:长度大于500字，使用正式论文语言。不要使用转折词和连词。"
    
    # 增强摘要的数据丰富性要求
    user_message += f"\n\n【数据丰富性要求】\n"
    user_message += f"1. 摘要内容必须包含：\n"
    user_message += f"   - 研究背景和现状的具体数据\n"
    user_message += f"   - 研究方法的关键数据指标\n"
    user_message += f"   - 主要发现和结论的具体数据\n"
    user_message += f"   - 研究意义和价值的量化描述\n"
    
    user_message += f"2. 数据要求：\n"
    user_message += f"   - 包含百分比、增长率、市场规模等关键数据\n"
    user_message += f"   - 提供时间范围、样本数量、研究周期等具体信息\n"
    user_message += f"   - 突出最重要的数据发现和结论\n"
    user_message += f"   - 数据要准确、有说服力\n"
    
    user_message += f"3. 关键词要求：\n"
    user_message += f"   - 包含数据相关的关键词（如：数据分析、统计、实证等）\n"
    user_message += f"   - 包含研究方法的关键词\n"
    user_message += f"   - 包含研究领域和主题的关键词\n"
    
    user_message += (
        "返回结果为json格式，如下---\n{digest:'摘要',keywords:['词1','词2']}"
    )

    return user_message


def generateDigestEnglish(param: ParamRegenDigest):
    user_message = f"角色:你是一名专家学者."
    user_message += f"任务:给一篇标题为《{param.title}》的论文写一段摘要和10个关键词,写作语言是英语。"
    user_message += f"约束:长度大于500字，使用正式论文语言。不要使用转折词和连词。"
    user_message += (
        "返回结果为json格式，如下---\n{digest:'text',keywords:['word1','word2']}"
    )

    return user_message
