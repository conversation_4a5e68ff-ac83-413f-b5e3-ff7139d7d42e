"use strict";(self["webpackChunkopen_essay_gui"]=self["webpackChunkopen_essay_gui"]||[]).push([[344],{5344:(s,t,a)=>{a.r(t),a.d(t,{default:()=>n});var i=function(){var s=this,t=s._self._c;return t("div",{staticClass:"admin-stats-overview"},[t("div",{staticClass:"page-header"},[t("h2",[s._v("数据概览")]),t("div",{staticClass:"header-actions"},[t("el-button",{attrs:{type:"primary",icon:"el-icon-refresh",loading:s.loading},on:{click:s.refreshData}},[s._v(" 刷新数据 ")])],1)]),t("el-row",{staticClass:"stats-cards",attrs:{gutter:20}},[t("el-col",{attrs:{span:6}},[t("el-card",{staticClass:"stats-card",attrs:{shadow:"hover"}},[t("div",{staticClass:"card-content"},[t("div",{staticClass:"card-icon users-icon"},[t("i",{staticClass:"el-icon-user"})]),t("div",{staticClass:"card-info"},[t("div",{staticClass:"card-title"},[s._v("总用户数")]),t("div",{staticClass:"card-value"},[s._v(s._s(s.overview.total_users||0))]),t("div",{staticClass:"card-trend"},[t("span",{staticClass:"trend-label"},[s._v("今日新增:")]),t("span",{staticClass:"trend-value positive"},[s._v("+"+s._s(s.today.new_users||0))])])])])])],1),t("el-col",{attrs:{span:6}},[t("el-card",{staticClass:"stats-card",attrs:{shadow:"hover"}},[t("div",{staticClass:"card-content"},[t("div",{staticClass:"card-icon thesis-icon"},[t("i",{staticClass:"el-icon-document"})]),t("div",{staticClass:"card-info"},[t("div",{staticClass:"card-title"},[s._v("总论文数")]),t("div",{staticClass:"card-value"},[s._v(s._s(s.overview.total_thesis||0))]),t("div",{staticClass:"card-trend"},[t("span",{staticClass:"trend-label"},[s._v("今日新增:")]),t("span",{staticClass:"trend-value positive"},[s._v("+"+s._s(s.today.new_thesis||0))])])])])])],1),t("el-col",{attrs:{span:6}},[t("el-card",{staticClass:"stats-card",attrs:{shadow:"hover"}},[t("div",{staticClass:"card-content"},[t("div",{staticClass:"card-icon chat-icon"},[t("i",{staticClass:"el-icon-chat-dot-round"})]),t("div",{staticClass:"card-info"},[t("div",{staticClass:"card-title"},[s._v("聊天记录")]),t("div",{staticClass:"card-value"},[s._v(s._s(s.overview.total_chat_logs||0))]),t("div",{staticClass:"card-trend"},[t("span",{staticClass:"trend-label"},[s._v("今日新增:")]),t("span",{staticClass:"trend-value positive"},[s._v("+"+s._s(s.today.chat_logs||0))])])])])])],1),t("el-col",{attrs:{span:6}},[t("el-card",{staticClass:"stats-card",attrs:{shadow:"hover"}},[t("div",{staticClass:"card-content"},[t("div",{staticClass:"card-icon vip-icon"},[t("i",{staticClass:"el-icon-medal"})]),t("div",{staticClass:"card-info"},[t("div",{staticClass:"card-title"},[s._v("VIP用户")]),t("div",{staticClass:"card-value"},[s._v(s._s(s.overview.vip_users||0))]),t("div",{staticClass:"card-trend"},[t("span",{staticClass:"trend-label"},[s._v("活跃用户:")]),t("span",{staticClass:"trend-value"},[s._v(s._s(s.overview.active_users||0))])])])])])],1)],1),t("el-row",{staticClass:"stats-details",attrs:{gutter:20}},[t("el-col",{attrs:{span:12}},[t("el-card",{staticClass:"detail-card"},[t("div",{attrs:{slot:"header"},slot:"header"},[t("span",[s._v("用户统计详情")])]),t("div",{staticClass:"detail-content"},[t("div",{staticClass:"detail-item"},[t("span",{staticClass:"item-label"},[s._v("VIP用户:")]),t("span",{staticClass:"item-value"},[s._v(s._s(s.overview.vip_users||0))])]),t("div",{staticClass:"detail-item"},[t("span",{staticClass:"item-label"},[s._v("过期VIP:")]),t("span",{staticClass:"item-value"},[s._v(s._s(s.overview.expired_vip_users||0))])]),t("div",{staticClass:"detail-item"},[t("span",{staticClass:"item-label"},[s._v("活跃用户(7天):")]),t("span",{staticClass:"item-value"},[s._v(s._s(s.overview.active_users||0))])]),t("div",{staticClass:"detail-item"},[t("span",{staticClass:"item-label"},[s._v("昨日新增用户:")]),t("span",{staticClass:"item-value"},[s._v(s._s(s.yesterday.new_users||0))])]),t("div",{staticClass:"detail-item"},[t("span",{staticClass:"item-label"},[s._v("昨日新增论文:")]),t("span",{staticClass:"item-value"},[s._v(s._s(s.yesterday.new_thesis||0))])])])])],1),t("el-col",{attrs:{span:12}},[t("el-card",{staticClass:"detail-card"},[t("div",{attrs:{slot:"header"},slot:"header"},[t("span",[s._v("系统状态")])]),t("div",{staticClass:"detail-content"},[t("div",{staticClass:"status-item"},[t("div",{staticClass:"status-icon success"},[t("i",{staticClass:"el-icon-success"})]),t("div",{staticClass:"status-info"},[t("div",{staticClass:"status-title"},[s._v("系统运行正常")]),t("div",{staticClass:"status-desc"},[s._v("所有服务运行正常")])])]),t("div",{staticClass:"status-item"},[t("div",{staticClass:"status-icon info"},[t("i",{staticClass:"el-icon-info"})]),t("div",{staticClass:"status-info"},[t("div",{staticClass:"status-title"},[s._v("数据库连接")]),t("div",{staticClass:"status-desc"},[s._v("连接正常")])])]),t("div",{staticClass:"status-item"},[t("div",{staticClass:"status-icon warning"},[t("i",{staticClass:"el-icon-warning"})]),t("div",{staticClass:"status-info"},[t("div",{staticClass:"status-title"},[s._v("存储空间")]),t("div",{staticClass:"status-desc"},[s._v("使用率 75%")])])])])])],1)],1)],1)},e=[],l=a(9192);const c={name:"AdminStatsOverview",data(){return{loading:!1,overview:{},today:{},yesterday:{}}},mounted(){this.loadData()},methods:{async loadData(){try{this.loading=!0;const s=await l.bk.getOverview();s.success?(this.overview=s.data.overview||{},this.today=s.data.today||{},this.yesterday=s.data.yesterday||{}):this.$message.error(s.message||"获取数据失败")}catch(s){console.error("加载数据概览失败:",s),this.$message.error("加载数据失败")}finally{this.loading=!1}},refreshData(){this.loadData()}}},d=c;var v=a(1656),r=(0,v.A)(d,i,e,!1,null,"9d1d41c6",null);const n=r.exports}}]);