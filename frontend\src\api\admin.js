import request from '@/utils/request'
import Cookies from 'js-cookie'

// 管理员认证相关API
export const adminAuth = {
  // 管理员登录
  login(data) {
    console.log('发起管理员登录请求')
    return request({
      url: '/api/admin/auth/login',
      method: 'post',
      data
    }).then(response => {
      console.log('管理员登录API响应:', response)
      // 直接返回响应，不进行额外处理
      return response
    }).catch(error => {
      // 处理登录错误
      console.error('登录请求失败:', error)
      // 返回一个标准格式的错误响应
      return { success: false, message: error.message || '登录失败，请稍后重试' }
    })
  },
  
  // 管理员登出
  logout() {
    console.log('调用管理员登出API')

    // 获取最新的管理员token
    const adminToken = Cookies.get('admin_token') || localStorage.getItem('admin_token')

    return request({
      url: '/api/admin/auth/logout',
      method: 'post',
      timeout: 5000, // 设置较短的超时时间
      headers: adminToken ? {
        'Authorization': `Bearer ${adminToken}`
      } : {},
      // 不重试登出请求，避免循环
      retry: 0
    }).then(response => {
      console.log('登出API调用成功:', response)
      return response
    }).catch(error => {
      // 处理登出错误，但不阻止登出流程
      console.warn('登出API调用失败，但将继续清理本地状态:', error.message)
      // 返回一个成功响应，确保前端继续登出流程
      return { success: true, message: '已退出登录' }
    })
  },
  
  // 获取管理员信息
  getProfile() {
    return request({
      url: '/api/admin/auth/profile',
      method: 'get'
    })
  },
  
  // 修改密码
  changePassword(data) {
    return request({
      url: '/api/admin/auth/change-password',
      method: 'post',
      data
    })
  }
}

// 用户管理相关API
export const adminUser = {
  // 获取用户列表
  getList(params) {
    return request({
      url: '/api/admin/users/list',
      method: 'get',
      params
    })
  },
  
  // 获取用户详情
  getDetail(userId) {
    return request({
      url: `/api/admin/users/${userId}`,
      method: 'get'
    })
  },
  
  // 创建用户
  create(data) {
    return request({
      url: '/api/admin/users',
      method: 'post',
      data
    })
  },
  
  // 更新用户信息
  update(userId, data) {
    return request({
      url: `/api/admin/users/${userId}`,
      method: 'put',
      data
    })
  },
  
  // 删除用户
  delete(userId) {
    return request({
      url: `/api/admin/users/${userId}`,
      method: 'delete'
    })
  },
  
  // 锁定/解锁用户
  toggleLock(userId, data) {
    return request({
      url: `/api/admin/users/${userId}/lock`,
      method: 'post',
      data
    })
  },
  
  // VIP管理
  vipManage(userId, data) {
    return request({
      url: `/api/admin/users/${userId}/vip`,
      method: 'post',
      data
    })
  },
  
  // 重置用户密码
  resetPassword(userId, data) {
    return request({
      url: `/api/admin/users/${userId}/reset-password`,
      method: 'post',
      data
    })
  },
  
  // 批量删除用户
  batchDelete(data) {
    return request({
      url: '/api/admin/users/batch/delete',
      method: 'post',
      data
    })
  },
  
  // 批量锁定/解锁用户
  batchLock(data) {
    return request({
      url: '/api/admin/users/batch/lock',
      method: 'post',
      data
    })
  },
  
  // 批量VIP管理
  batchVip(data) {
    return request({
      url: '/api/admin/users/batch/vip',
      method: 'post',
      data
    })
  },
  
  // 用户统计
  getStats(params) {
    return request({
      url: '/api/admin/users/stats',
      method: 'get',
      params
    })
  }
}

// 论文管理相关API
export const adminThesis = {
  // 获取论文列表
  getList(params) {
    return request({
      url: '/api/admin/thesis/list',
      method: 'get',
      params
    })
  },
  
  // 获取论文详情
  getDetail(thesisId) {
    return request({
      url: `/api/admin/thesis/detail/${thesisId}`,
      method: 'get'
    })
  },
  
  // 删除论文
  delete(thesisId) {
    return request({
      url: `/api/admin/thesis/delete/${thesisId}`,
      method: 'delete'
    })
  },
  
  // 论文统计
  getStats(params) {
    return request({
      url: '/api/admin/thesis/stats',
      method: 'get',
      params
    })
  },
  
  // 获取用户论文列表
  getUserThesis(userId, params) {
    return request({
      url: `/api/admin/thesis/user/${userId}`,
      method: 'get',
      params
    })
  }
}

// 系统统计相关API
export const adminStats = {
  // 获取数据概览
  getOverview() {
    return request({
      url: '/api/admin/stats/overview',
      method: 'get'
    })
  },
  
  // 获取用户统计
  getUserStats(params) {
    return request({
      url: '/api/admin/stats/users',
      method: 'get',
      params
    })
  },
  
  // 获取论文统计
  getThesisStats(params) {
    return request({
      url: '/api/admin/stats/thesis',
      method: 'get',
      params
    })
  },
  
  // 获取聊天统计
  getChatStats(params) {
    return request({
      url: '/api/admin/stats/chat',
      method: 'get',
      params
    })
  }
}

// 设置管理相关API
export const adminSettings = {
  // 获取系统设置
  getSettings() {
    return request({
      url: '/api/admin/settings',
      method: 'get'
    })
  },
  
  // 保存系统设置
  saveSettings(data) {
    return request({
      url: '/api/admin/settings',
      method: 'post',
      data
    })
  },
  
  // 测试API连接
  testApiConnection(data) {
    return request({
      url: '/api/admin/settings/test-api',
      method: 'post',
      data
    })
  },
  
  // 重置系统设置
  resetSettings() {
    return request({
      url: '/api/admin/settings/reset',
      method: 'post'
    })
  }
}

// 管理员账号相关API
export const adminAccount = {
  // 获取管理员列表
  getList(params) {
    console.log('调用管理员列表API，参数:', params);
    return request({
      url: '/api/admin/accounts/list',
      method: 'get',
      params,
      timeout: 10000 // 10秒超时
    }).catch(error => {
      console.error('获取管理员列表失败:', error);
      return { 
        success: false, 
        message: '获取管理员列表失败，请稍后重试',
        data: {
          list: [],
          total: 0
        }
      };
    });
  },
  
  // 获取管理员详情
  getDetail(adminId) {
    return request({
      url: `/api/admin/accounts/${adminId}`,
      method: 'get',
      timeout: 10000 // 10秒超时
    }).catch(error => {
      console.error('获取管理员详情失败:', error);
      return { 
        success: false, 
        message: '获取管理员详情失败，请稍后重试',
        data: null
      };
    });
  },
  
  // 创建管理员
  create(data) {
    return request({
      url: '/api/admin/accounts',
      method: 'post',
      data,
      timeout: 10000 // 10秒超时
    }).catch(error => {
      console.error('创建管理员失败:', error);
      return { 
        success: false, 
        message: '创建管理员失败，请稍后重试',
        data: null
      };
    });
  },
  
  // 更新管理员
  update(adminId, data) {
    return request({
      url: `/api/admin/accounts/${adminId}`,
      method: 'put',
      data,
      timeout: 10000 // 10秒超时
    }).catch(error => {
      console.error('更新管理员失败:', error);
      return { 
        success: false, 
        message: '更新管理员失败，请稍后重试',
        data: null
      };
    });
  },
  
  // 删除管理员
  delete(adminId) {
    return request({
      url: `/api/admin/accounts/${adminId}`,
      method: 'delete',
      timeout: 10000 // 10秒超时
    }).catch(error => {
      console.error('删除管理员失败:', error);
      return { 
        success: false, 
        message: '删除管理员失败，请稍后重试',
        data: null
      };
    });
  },
  
  // 重置管理员密码
  resetPassword(adminId, data) {
    return request({
      url: `/api/admin/accounts/${adminId}/reset-password`,
      method: 'post',
      data,
      timeout: 10000 // 10秒超时
    }).catch(error => {
      console.error('重置密码失败:', error);
      return { 
        success: false, 
        message: '重置密码失败，请稍后重试',
        data: null
      };
    });
  }
}

// 支付订单管理相关API
export const adminPayment = {
  // 获取支付订单列表
  getOrders(params) {
    return request({
      url: '/api/admin/settings/payment/orders',
      method: 'get',
      params
    })
  },
  
  // 获取支付订单详情
  getOrderDetail(orderId) {
    return request({
      url: `/api/admin/settings/payment/order/${orderId}`,
      method: 'get'
    })
  },
  
  // 获取支付统计数据
  getPaymentStats(params) {
    return request({
      url: '/api/admin/settings/payment/stats',
      method: 'get',
      params
    })
  }
}

// 默认导出
export default {
  ...adminAuth,
  ...adminUser,
  ...adminThesis,
  ...adminStats,
  ...adminSettings,
  ...adminAccount,
  ...adminPayment
} 