from EarlyBird.api.admin.app import admin_app_bp, init_admin_api
from EarlyBird.api.home import home_bp
from EarlyBird.api.user import bp as user_bp
from EarlyBird.api.thesis import bp as thesis_bp
from EarlyBird.api.talk2ai import bp as talk2ai_bp
from EarlyBird.api.generate import bp as generate_bp
from EarlyBird.api.pay import bp as pay_bp
from EarlyBird.api.admin import admin_bp  # 导入admin_bp（包含wechat_pay_config子蓝图）

def register_bp(app):
    # 注册管理后台API蓝图
    init_admin_api(app)
    
    # 注册微信支付配置API蓝图（这个很重要，包含/api/admin/wechat_pay_config路由）
    app.register_blueprint(admin_bp)
    
    # 注册前台API蓝图
    app.register_blueprint(home_bp)
    app.register_blueprint(user_bp)
    app.register_blueprint(thesis_bp)
    app.register_blueprint(talk2ai_bp)
    app.register_blueprint(generate_bp)
    app.register_blueprint(pay_bp)  # 注册支付API蓝图
