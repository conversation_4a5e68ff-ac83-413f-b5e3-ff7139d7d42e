<template>
  <div class="debug"  v-if="!isProductionEnv && isShow">
    <div v-for="(value,key) in debugInfo" :key="key">
      <el-tag>{{ key }}:{{ value }}</el-tag>
    </div>
    <el-button small @click="isShow = false">关闭debug</el-button>
  </div>
</template>
<script>
import { mapGetters } from "vuex";
export default {
  name: "Debug",
  data() {
    return {
      isShow: true,
    };
  },
  computed: {
    ...mapGetters(["user", "token",'device']),
    debugInfo() {
      return {
        user: this.user,
        token: this.token,
        device: this.device,
        env: process.env.NODE_ENV,
      };
    },
    isProductionEnv() {
      return process.env.NODE_ENV === "production";
    },
  },
  created() {
    console.log(this.user);
    console.log(this.debugInfo);
  },
};
</script>
<style scoped lang="scss">
.debug {
  padding: 10px;
  background: rgb(140, 197, 255);
  display: flex;
  .el-tag {
    margin-left: 10px;
  }
}
</style>