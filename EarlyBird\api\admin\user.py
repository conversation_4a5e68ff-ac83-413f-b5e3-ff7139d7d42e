from flask import Blueprint, request, jsonify
from EarlyBird.common.libs.api_result import ApiResult
from EarlyBird.model.admin import Admin, AdminLog
from EarlyBird.model.user import User
from EarlyBird.common.libs.BaseModel import db
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

# 创建蓝图
admin_user_bp = Blueprint('admin_user', __name__)

# 导入认证装饰器
from .auth import admin_auth_required


@admin_user_bp.route('/list', methods=['GET'])
@admin_auth_required
def user_list():
    """获取用户列表"""
    try:
        admin = request.admin
        logger.info(f"管理员 {admin.username} 请求用户列表")
        
        # 检查权限
        if not admin.has_permission('user', 'view'):
            logger.warning(f"管理员 {admin.username} 没有查看用户权限")
            return ApiResult.error("没有查看用户的权限")
        
        # 获取查询参数
        page = int(request.args.get('page', 1))
        size = int(request.args.get('size', 20))
        keyword = request.args.get('keyword', '')
        vip_status = request.args.get('vip_status', '')
        is_lock = request.args.get('is_lock', '')
        
        logger.info(f"查询参数: page={page}, size={size}, keyword={keyword}, vip_status={vip_status}, is_lock={is_lock}")
        
        # 构建查询
        query = User.query.filter(User.is_deleted == 0)  # 只查询未删除的用户
        
        # 关键词搜索
        if keyword:
            query = query.filter(
                (User.username.contains(keyword)) |
                (User.nickname.contains(keyword)) |
                (User.realname.contains(keyword)) |
                (User.email.contains(keyword))
            )
        
        # VIP状态筛选
        if vip_status == 'vip':
            query = query.filter(User.vip_expire_at > datetime.now())
        elif vip_status == 'expired':
            query = query.filter(
                (User.vip_expire_at <= datetime.now()) |
                (User.vip_expire_at.is_(None))
            )
        
        # 锁定状态筛选
        if is_lock == 'true':
            query = query.filter(User.is_lock == True)
        elif is_lock == 'false':
            query = query.filter(User.is_lock == False)
        
        # 分页
        total = query.count()
        logger.info(f"查询到总用户数: {total}")
        
        users = query.order_by(User.create_time.desc()).offset((page - 1) * size).limit(size).all()
        logger.info(f"当前页用户数: {len(users)}")
        
        # 格式化数据
        user_list = []
        for user in users:
            try:
                user_info = user.get_info()
                user_list.append(user_info)
            except Exception as e:
                logger.error(f"格式化用户 {user.id} 信息失败: {str(e)}")
                # 添加基本信息，避免整个请求失败
                user_list.append({
                    'id': user.id,
                    'username': user.username,
                    'nickname': user.nickname or '未设置',
                    'email': user.email or '未设置',
                    'create_time': str(user.create_time) if user.create_time else '',
                    'is_lock': user.is_lock,
                    'vip_level': user.vip_level or 1,
                    'vip_expire_at': str(user.vip_expire_at) if user.vip_expire_at else None
                })
        
        # 记录操作日志
        try:
            AdminLog.create_log(
                admin_id=admin.id,
                admin_username=admin.username,
                action="view",
                resource="user",
                description=f"查看用户列表，页码:{page}，关键词:{keyword}",
                ip_address=request.remote_addr,
                user_agent=request.headers.get('User-Agent', '')
            )
        except Exception as e:
            logger.error(f"记录操作日志失败: {str(e)}")
        
        result = {
            "list": user_list,
            "total": total,
            "page": page,
            "size": size
        }
        
        logger.info(f"用户列表查询成功，返回 {len(user_list)} 条记录")
        return ApiResult.success(result)
        
    except Exception as e:
        logger.error(f"获取用户列表失败: {str(e)}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        return ApiResult.error(f"获取用户列表失败: {str(e)}")


@admin_user_bp.route('/detail/<int:user_id>', methods=['GET'])
@admin_auth_required
def user_detail(user_id):
    """获取用户详情"""
    try:
        admin = request.admin
        
        # 检查权限
        if not admin.has_permission('user', 'view'):
            return ApiResult.error("没有查看用户的权限")
        
        # 查找用户
        user = User.query.filter(User.is_deleted == 0).filter_by(id=user_id).first()
        if not user:
            return ApiResult.error("用户不存在")
        
        # 记录操作日志
        AdminLog.create_log(
            admin_id=admin.id,
            admin_username=admin.username,
            action="view",
            resource="user",
            resource_id=user_id,
            description=f"查看用户详情: {user.username}",
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', '')
        )
        
        return ApiResult.success(user.get_info())
        
    except Exception as e:
        logger.error(f"获取用户详情失败: {str(e)}")
        return ApiResult.error("获取用户详情失败")


@admin_user_bp.route('/update/<int:user_id>', methods=['PUT'])
@admin_auth_required
def user_update(user_id):
    """更新用户信息"""
    try:
        admin = request.admin
        
        # 检查权限
        if not admin.has_permission('user', 'edit'):
            return ApiResult.error("没有编辑用户的权限")
        
        # 查找用户
        user = User.query.filter(User.is_deleted == 0).filter_by(id=user_id).first()
        if not user:
            return ApiResult.error("用户不存在")
        
        data = request.get_json()
        
        # 更新字段
        if 'username' in data:
            # 检查用户名是否已存在
            existing_user = User.query.filter_by(username=data['username']).first()
            if existing_user and existing_user.id != user_id:
                return ApiResult.error("用户名已存在")
            user.username = data['username']
        
        if 'realname' in data:
            user.realname = data['realname']
        
        if 'email' in data:
            user.email = data['email']
        
        if 'phone' in data:
            user.phone = data['phone']
        
        if 'is_lock' in data:
            user.is_lock = data['is_lock']
        
        if 'vip_level' in data:
            user.vip_level = data['vip_level']
        
        # 保存
        user.save()
        
        # 记录操作日志
        AdminLog.create_log(
            admin_id=admin.id,
            admin_username=admin.username,
            action="update",
            resource="user",
            resource_id=user_id,
            description=f"更新用户信息: {user.username}",
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', ''),
            request_data=data
        )
        
        return ApiResult.success(user.get_info(), "更新成功")
        
    except Exception as e:
        logger.error(f"更新用户信息失败: {str(e)}")
        return ApiResult.error("更新用户信息失败")


@admin_user_bp.route('/delete/<int:user_id>', methods=['DELETE'])
@admin_auth_required
def user_delete(user_id):
    """删除用户"""
    try:
        admin = request.admin
        
        # 检查权限
        if not admin.has_permission('user', 'delete'):
            return ApiResult.error("没有删除用户的权限")
        
        # 查找用户
        user = User.query.filter(User.is_deleted == 0).filter_by(id=user_id).first()
        if not user:
            return ApiResult.error("用户不存在")
        
        username = user.username
        
        # 物理删除用户及其相关数据
        try:
            # 1. 删除用户的论文
            from EarlyBird.model.thesis import Thesis
            from EarlyBird.model.paragraph import Paragraph
            
            # 删除用户的论文段落
            paragraphs = Paragraph.query.filter_by(thesisId=user_id).all()
            for paragraph in paragraphs:
                db.session.delete(paragraph)
            
            # 删除用户的论文
            theses = Thesis.query.filter_by(uid=user_id).all()
            for thesis in theses:
                db.session.delete(thesis)
            
            # 2. 删除用户的聊天记录
            from EarlyBird.model.chat_log import ChatLog
            chat_logs = ChatLog.query.filter_by(uid=user_id).all()
            for chat_log in chat_logs:
                db.session.delete(chat_log)
            
            # 3. 删除用户的报告历史
            from EarlyBird.model.report_history import ReportHistory
            report_histories = ReportHistory.query.filter_by(uid=user_id).all()
            for report in report_histories:
                db.session.delete(report)
            
            # 4. 删除用户的其他相关数据
            # 可以根据需要添加更多相关表的删除
            
            # 5. 最后删除用户本身
            db.session.delete(user)
            
            # 提交所有删除操作
            db.session.commit()
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"删除用户相关数据失败: {str(e)}")
            return ApiResult.error("删除用户相关数据失败")
        
        # 记录操作日志
        AdminLog.create_log(
            admin_id=admin.id,
            admin_username=admin.username,
            action="delete",
            resource="user",
            resource_id=user_id,
            description=f"物理删除用户: {username}",
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', '')
        )
        
        return ApiResult.success(None, "删除成功")
        
    except Exception as e:
        logger.error(f"删除用户失败: {str(e)}")
        return ApiResult.error("删除用户失败")


@admin_user_bp.route('/vip/<int:user_id>', methods=['POST'])
@admin_auth_required
def user_vip_manage(user_id):
    """VIP管理"""
    try:
        admin = request.admin
        
        # 检查权限
        if not admin.has_permission('user', 'vip'):
            return ApiResult.error("没有VIP管理的权限")
        
        # 查找用户
        user = User.query.filter(User.is_deleted == 0).filter_by(id=user_id).first()
        if not user:
            return ApiResult.error("用户不存在")
        
        data = request.get_json()
        action = data.get('action')
        days = int(data.get('days', 0))
        hours = int(data.get('hours', 0))
        
        if action == 'extend':
            # 延长VIP
            user.extendVip(days, hours)
            message = f"延长VIP {days}天{hours}小时"
        elif action == 'set':
            # 设置VIP
            now = datetime.now()
            user.vip_start_at = now
            user.vip_expire_at = now + timedelta(days=days, hours=hours)
            user.save()
            message = f"设置VIP {days}天{hours}小时"
        elif action == 'cancel':
            # 取消VIP
            user.vip_start_at = None
            user.vip_expire_at = None
            user.save()
            message = "取消VIP"
        else:
            return ApiResult.error("无效的操作")
        
        # 记录操作日志
        AdminLog.create_log(
            admin_id=admin.id,
            admin_username=admin.username,
            action="vip_manage",
            resource="user",
            resource_id=user_id,
            description=f"VIP管理: {message} - {user.username}",
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', ''),
            request_data=data
        )
        
        return ApiResult.success(user.get_info(), message + "成功")
        
    except Exception as e:
        logger.error(f"VIP管理失败: {str(e)}")
        return ApiResult.error("VIP管理失败")


@admin_user_bp.route('/stats', methods=['GET'])
@admin_auth_required
def user_stats():
    """用户统计"""
    try:
        admin = request.admin
        
        # 检查权限
        if not admin.has_permission('user', 'view'):
            return ApiResult.error("没有查看用户的权限")
        
        # 获取统计参数
        period = request.args.get('period', '7d')  # 7d, 30d, 90d
        
        # 计算时间范围
        now = datetime.now()
        if period == '7d':
            start_date = now - timedelta(days=7)
        elif period == '30d':
            start_date = now - timedelta(days=30)
        elif period == '90d':
            start_date = now - timedelta(days=90)
        else:
            start_date = now - timedelta(days=7)
        
        # 统计数据
        total_users = User.query.filter(User.is_deleted == 0).count()
        vip_users = User.query.filter(User.is_deleted == 0).filter(User.vip_expire_at > now).count()
        new_users = User.query.filter(User.is_deleted == 0).filter(User.create_time >= start_date).count()
        locked_users = User.query.filter(User.is_deleted == 0).filter(User.is_lock == True).count()
        
        # 记录操作日志
        AdminLog.create_log(
            admin_id=admin.id,
            admin_username=admin.username,
            action="stats",
            resource="user",
            description=f"查看用户统计，周期:{period}",
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', '')
        )
        
        return ApiResult.success({
            "total_users": total_users,
            "vip_users": vip_users,
            "new_users": new_users,
            "locked_users": locked_users,
            "period": period
        })
        
    except Exception as e:
        logger.error(f"获取用户统计失败: {str(e)}")
        return ApiResult.error("获取用户统计失败")


@admin_user_bp.route('/create', methods=['POST'])
@admin_auth_required
def user_create():
    """新增用户"""
    try:
        admin = request.admin
        
        # 检查权限
        if not admin.has_permission('user', 'create'):
            return ApiResult.error("没有创建用户的权限")
        
        data = request.get_json()
        
        # 验证必填字段
        username = data.get('username')
        password = data.get('password')
        email = data.get('email')
        
        if not username or not password:
            return ApiResult.error("用户名和密码不能为空")
        
        # 检查用户名是否已存在
        existing_user = User.query.filter_by(username=username).first()
        if existing_user:
            return ApiResult.error("用户名已存在")
        
        # 检查邮箱是否已存在
        if email:
            existing_email = User.query.filter_by(email=email).first()
            if existing_email:
                return ApiResult.error("邮箱已存在")
        
        # 创建新用户
        user = User()
        user.username = username
        user.password = password  # 会自动加密
        user.email = email
        user.nickname = data.get('nickname', '')
        user.realname = data.get('realname', '')
        user.phone = data.get('phone', '')
        user.is_lock = data.get('is_lock', False)
        user.vip_level = data.get('vip_level', 1)
        
        # 保存用户
        user.save()
        
        # 记录操作日志
        AdminLog.create_log(
            admin_id=admin.id,
            admin_username=admin.username,
            action="create",
            resource="user",
            resource_id=user.id,
            description=f"创建新用户: {username}",
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', ''),
            request_data=data
        )
        
        return ApiResult.success(user.get_info(), "用户创建成功")
        
    except Exception as e:
        logger.error(f"创建用户失败: {str(e)}")
        return ApiResult.error("创建用户失败")


@admin_user_bp.route('/lock/<int:user_id>', methods=['POST'])
@admin_auth_required
def user_toggle_lock(user_id):
    """锁定/解锁用户"""
    try:
        admin = request.admin
        
        # 检查权限
        if not admin.has_permission('user', 'edit'):
            return ApiResult.error("没有编辑用户的权限")
        
        # 查找用户
        user = User.query.filter(User.is_deleted == 0).filter_by(id=user_id).first()
        if not user:
            return ApiResult.error("用户不存在")
        
        data = request.get_json()
        is_lock = data.get('is_lock', not user.is_lock)
        
        # 更新锁定状态
        user.is_lock = is_lock
        user.save()
        
        action = "锁定" if is_lock else "解锁"
        
        # 记录操作日志
        AdminLog.create_log(
            admin_id=admin.id,
            admin_username=admin.username,
            action="toggle_lock",
            resource="user",
            resource_id=user_id,
            description=f"{action}用户: {user.username}",
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', ''),
            request_data=data
        )
        
        return ApiResult.success(user.get_info(), f"用户{action}成功")
        
    except Exception as e:
        logger.error(f"锁定/解锁用户失败: {str(e)}")
        return ApiResult.error("锁定/解锁用户失败")


@admin_user_bp.route('/batch/delete', methods=['POST'])
@admin_auth_required
def user_batch_delete():
    """批量删除用户"""
    try:
        admin = request.admin
        
        # 检查权限
        if not admin.has_permission('user', 'delete'):
            return ApiResult.error("没有删除用户的权限")
        
        data = request.get_json()
        user_ids = data.get('user_ids', [])
        
        if not user_ids:
            return ApiResult.error("请选择要删除的用户")
        
        deleted_count = 0
        deleted_usernames = []
        failed_users = []
        
        for user_id in user_ids:
            try:
                user = User.query.filter(User.is_deleted == 0).filter_by(id=user_id).first()
                if user:
                    username = user.username
                    
                    # 物理删除用户及其相关数据
                    try:
                        # 1. 删除用户的论文
                        from EarlyBird.model.thesis import Thesis
                        from EarlyBird.model.paragraph import Paragraph
                        
                        # 删除用户的论文段落
                        paragraphs = Paragraph.query.filter_by(thesisId=user_id).all()
                        for paragraph in paragraphs:
                            db.session.delete(paragraph)
                        
                        # 删除用户的论文
                        theses = Thesis.query.filter_by(uid=user_id).all()
                        for thesis in theses:
                            db.session.delete(thesis)
                        
                        # 2. 删除用户的聊天记录
                        from EarlyBird.model.chat_log import ChatLog
                        chat_logs = ChatLog.query.filter_by(uid=user_id).all()
                        for chat_log in chat_logs:
                            db.session.delete(chat_log)
                        
                        # 3. 删除用户的报告历史
                        from EarlyBird.model.report_history import ReportHistory
                        report_histories = ReportHistory.query.filter_by(uid=user_id).all()
                        for report in report_histories:
                            db.session.delete(report)
                        
                        # 4. 最后删除用户本身
                        db.session.delete(user)
                        
                        # 提交删除操作
                        db.session.commit()
                        
                        deleted_count += 1
                        deleted_usernames.append(username)
                        
                    except Exception as e:
                        db.session.rollback()
                        logger.error(f"删除用户 {username} 相关数据失败: {str(e)}")
                        failed_users.append(f"{username}(数据删除失败)")
                        
                else:
                    failed_users.append(f"用户ID:{user_id}(不存在)")
                    
            except Exception as e:
                logger.error(f"删除用户ID {user_id} 失败: {str(e)}")
                failed_users.append(f"用户ID:{user_id}(删除异常)")
        
        # 记录操作日志
        AdminLog.create_log(
            admin_id=admin.id,
            admin_username=admin.username,
            action="batch_delete",
            resource="user",
            description=f"批量物理删除用户: {', '.join(deleted_usernames)}",
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', ''),
            request_data=data
        )
        
        message = f"成功删除 {deleted_count} 个用户"
        if failed_users:
            message += f"，失败 {len(failed_users)} 个用户: {', '.join(failed_users)}"
        
        return ApiResult.success({
            "deleted_count": deleted_count,
            "deleted_usernames": deleted_usernames,
            "failed_users": failed_users
        }, message)
        
    except Exception as e:
        logger.error(f"批量删除用户失败: {str(e)}")
        return ApiResult.error("批量删除用户失败")


@admin_user_bp.route('/batch/lock', methods=['POST'])
@admin_auth_required
def user_batch_lock():
    """批量锁定/解锁用户"""
    try:
        admin = request.admin
        
        # 检查权限
        if not admin.has_permission('user', 'edit'):
            return ApiResult.error("没有编辑用户的权限")
        
        data = request.get_json()
        user_ids = data.get('user_ids', [])
        is_lock = data.get('is_lock', True)
        
        if not user_ids:
            return ApiResult.error("请选择要操作的用户")
        
        updated_count = 0
        updated_usernames = []
        
        for user_id in user_ids:
            user = User.query.filter(User.is_deleted == 0).filter_by(id=user_id).first()
            if user:
                user.is_lock = is_lock
                user.save()
                updated_count += 1
                updated_usernames.append(user.username)
        
        action = "锁定" if is_lock else "解锁"
        
        # 记录操作日志
        AdminLog.create_log(
            admin_id=admin.id,
            admin_username=admin.username,
            action="batch_lock",
            resource="user",
            description=f"批量{action}用户: {', '.join(updated_usernames)}",
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', ''),
            request_data=data
        )
        
        return ApiResult.success({
            "updated_count": updated_count,
            "updated_usernames": updated_usernames
        }, f"成功{action} {updated_count} 个用户")
        
    except Exception as e:
        logger.error(f"批量锁定/解锁用户失败: {str(e)}")
        return ApiResult.error("批量锁定/解锁用户失败")


@admin_user_bp.route('/batch/vip', methods=['POST'])
@admin_auth_required
def user_batch_vip():
    """批量设置VIP"""
    try:
        admin = request.admin
        
        # 检查权限
        if not admin.has_permission('user', 'vip'):
            return ApiResult.error("没有VIP管理的权限")
        
        data = request.get_json()
        user_ids = data.get('user_ids', [])
        vip_level = data.get('vip_level', 1)
        days = data.get('days', 30)
        action = data.get('action', 'add')
        
        if not user_ids:
            return ApiResult.error("请选择要操作的用户")
        
        updated_count = 0
        updated_usernames = []
        
        for user_id in user_ids:
            user = User.query.filter(User.is_deleted == 0).filter_by(id=user_id).first()
            if user:
                if action == 'add':
                    # 开通VIP
                    now = datetime.now()
                    user.vip_start_at = now
                    user.vip_expire_at = now + timedelta(days=days)
                    user.vip_level = vip_level
                elif action == 'extend':
                    # 续费VIP
                    user.extendVip(days, 0)
                    user.vip_level = vip_level
                elif action == 'cancel':
                    # 取消VIP
                    user.vip_start_at = None
                    user.vip_expire_at = None
                    user.vip_level = 1
                
                user.save()
                updated_count += 1
                updated_usernames.append(user.username)
        
        # 记录操作日志
        AdminLog.create_log(
            admin_id=admin.id,
            admin_username=admin.username,
            action="batch_vip",
            resource="user",
            description=f"批量VIP管理: {action} - {', '.join(updated_usernames)}",
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', ''),
            request_data=data
        )
        
        return ApiResult.success({
            "updated_count": updated_count,
            "updated_usernames": updated_usernames
        }, f"成功处理 {updated_count} 个用户的VIP")
        
    except Exception as e:
        logger.error(f"批量VIP管理失败: {str(e)}")
        return ApiResult.error("批量VIP管理失败")


@admin_user_bp.route('/reset-password/<int:user_id>', methods=['POST'])
@admin_auth_required
def reset_user_password(user_id):
    """重置用户密码"""
    try:
        admin = request.admin
        
        # 检查权限
        if not admin.has_permission('user', 'edit'):
            return ApiResult.error("没有重置用户密码的权限")
        
        # 查找用户
        user = User.query.filter(User.is_deleted == 0).filter_by(id=user_id).first()
        if not user:
            return ApiResult.error("用户不存在")
        
        data = request.get_json() or {}
        new_password = data.get('new_password', '123456')
        
        # 重置密码
        user.password = new_password
        user.save()
        
        # 记录操作日志
        AdminLog.create_log(
            admin_id=admin.id,
            admin_username=admin.username,
            action="reset_password",
            resource="user",
            resource_id=user.id,
            description=f"重置用户密码: {user.username}",
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', ''),
            request_data=data
        )
        
        return ApiResult.success(None, f"用户 {user.username} 密码已重置为：{new_password}")
        
    except Exception as e:
        logger.error(f"重置用户密码失败: {str(e)}")
        return ApiResult.error("重置用户密码失败") 