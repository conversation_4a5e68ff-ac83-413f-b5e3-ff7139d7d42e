"use strict";(self["webpackChunkopen_essay_gui"]=self["webpackChunkopen_essay_gui"]||[]).push([[22],{8022:(e,s,t)=>{t.r(s),t.d(s,{default:()=>d});var a=function(){var e=this,s=e._self._c;return s("div",{staticClass:"admin-users"},[s("div",{staticClass:"page-header"},[s("div",{staticClass:"header-content"},[e._m(0),s("div",{staticClass:"header-actions"},[s("el-button",{attrs:{type:"primary",icon:"el-icon-plus",size:"medium"},on:{click:e.handleAddUser}},[e._v(" 新增用户 ")]),s("el-button",{attrs:{type:"success",icon:"el-icon-refresh",size:"medium"},on:{click:e.refreshData}},[e._v(" 刷新数据 ")])],1)])]),s("div",{staticClass:"search-section"},[s("el-card",{staticClass:"search-card",attrs:{shadow:"hover"}},[s("div",{staticClass:"search-header"},[s("h3",{staticClass:"search-title"},[s("i",{staticClass:"el-icon-search"}),e._v(" 搜索筛选 ")])]),s("el-form",{staticClass:"search-form",attrs:{inline:!0,model:e.searchForm}},[s("el-form-item",{attrs:{label:"关键词"}},[s("el-input",{staticClass:"search-input",attrs:{placeholder:"用户名/昵称/邮箱",clearable:"","prefix-icon":"el-icon-search"},nativeOn:{keyup:function(s){return!s.type.indexOf("key")&&e._k(s.keyCode,"enter",13,s.key,"Enter")?null:e.handleSearch.apply(null,arguments)}},model:{value:e.searchForm.keyword,callback:function(s){e.$set(e.searchForm,"keyword",s)},expression:"searchForm.keyword"}})],1),s("el-form-item",{attrs:{label:"VIP状态"}},[s("el-select",{staticClass:"search-select",attrs:{placeholder:"全部",clearable:""},model:{value:e.searchForm.vip_status,callback:function(s){e.$set(e.searchForm,"vip_status",s)},expression:"searchForm.vip_status"}},[s("el-option",{attrs:{label:"VIP用户",value:"vip"}}),s("el-option",{attrs:{label:"已过期",value:"expired"}}),s("el-option",{attrs:{label:"普通用户",value:"normal"}})],1)],1),s("el-form-item",{attrs:{label:"锁定状态"}},[s("el-select",{staticClass:"search-select",attrs:{placeholder:"全部",clearable:""},model:{value:e.searchForm.is_lock,callback:function(s){e.$set(e.searchForm,"is_lock",s)},expression:"searchForm.is_lock"}},[s("el-option",{attrs:{label:"已锁定",value:"true"}}),s("el-option",{attrs:{label:"正常",value:"false"}})],1)],1),s("el-form-item",{attrs:{label:"注册时间"}},[s("el-date-picker",{staticClass:"search-date",attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd"},model:{value:e.searchForm.date_range,callback:function(s){e.$set(e.searchForm,"date_range",s)},expression:"searchForm.date_range"}})],1),s("el-form-item",{staticClass:"search-buttons"},[s("el-button",{staticClass:"search-btn",attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.handleSearch}},[e._v(" 搜索 ")]),s("el-button",{staticClass:"reset-btn",attrs:{icon:"el-icon-refresh"},on:{click:e.resetSearch}},[e._v(" 重置 ")])],1)],1)],1)],1),e.selectedUsers.length>0?s("div",{staticClass:"batch-toolbar"},[s("el-card",{staticClass:"batch-card",attrs:{shadow:"hover"}},[s("div",{staticClass:"toolbar-content"},[s("div",{staticClass:"toolbar-left"},[s("i",{staticClass:"el-icon-info"}),s("span",{staticClass:"selection-info"},[e._v("已选择 "+e._s(e.selectedUsers.length)+" 个用户")])]),s("div",{staticClass:"toolbar-actions"},[s("el-button",{attrs:{size:"small",type:"primary",icon:"el-icon-star-on"},on:{click:e.handleBatchVip}},[e._v(" 批量设置VIP ")]),s("el-button",{attrs:{size:"small",type:"warning",icon:"el-icon-lock"},on:{click:e.handleBatchLock}},[e._v(" 批量锁定 ")]),s("el-button",{attrs:{size:"small",type:"danger",icon:"el-icon-delete"},on:{click:e.handleBatchDelete}},[e._v(" 批量删除 ")]),s("el-button",{attrs:{size:"small",icon:"el-icon-close"},on:{click:e.clearSelection}},[e._v(" 取消选择 ")])],1)])])],1):e._e(),s("div",{staticClass:"table-section"},[s("el-card",{staticClass:"table-card",attrs:{shadow:"hover"}},[s("div",{staticClass:"table-header"},[s("h3",{staticClass:"table-title"},[s("i",{staticClass:"el-icon-document"}),e._v(" 用户列表 ")]),s("div",{staticClass:"table-actions"},[s("el-button",{attrs:{size:"small",icon:"el-icon-download"},on:{click:e.exportData}},[e._v(" 导出数据 ")])],1)]),s("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.userLoading,expression:"userLoading"}],ref:"userTable",staticClass:"user-table",attrs:{data:e.userList,border:"",stripe:"","header-cell-style":{background:"#f5f7fa",color:"#606266"}},on:{"selection-change":e.handleSelectionChange}},[s("el-table-column",{attrs:{type:"selection",width:"55"}}),s("el-table-column",{attrs:{prop:"id",label:"ID",width:"80",align:"center"}}),s("el-table-column",{attrs:{label:"用户信息","min-width":"250"},scopedSlots:e._u([{key:"default",fn:function(t){return[s("div",{staticClass:"user-info"},[s("div",{staticClass:"user-avatar"},[s("el-avatar",{attrs:{size:45,src:t.row.headimg,icon:"el-icon-user"}})],1),s("div",{staticClass:"user-details"},[s("div",{staticClass:"username"},[e._v(e._s(t.row.username))]),s("div",{staticClass:"nickname"},[e._v(e._s(t.row.nickname||"未设置昵称"))]),s("div",{staticClass:"email"},[e._v(e._s(t.row.email||"未设置邮箱"))])])])]}}])}),s("el-table-column",{attrs:{label:"VIP状态",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.vip_expire_at&&new Date(t.row.vip_expire_at)>new Date?s("el-tag",{attrs:{type:"success",size:"small",effect:"dark"}},[s("i",{staticClass:"el-icon-star-on"}),e._v(" VIP"+e._s(t.row.vip_level)+" ")]):s("el-tag",{attrs:{type:"info",size:"small",effect:"plain"}},[s("i",{staticClass:"el-icon-user"}),e._v(" 普通用户 ")])]}}])}),s("el-table-column",{attrs:{label:"状态",width:"100",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.is_lock?s("el-tag",{attrs:{type:"danger",size:"small",effect:"dark"}},[s("i",{staticClass:"el-icon-lock"}),e._v(" 已锁定 ")]):s("el-tag",{attrs:{type:"success",size:"small",effect:"dark"}},[s("i",{staticClass:"el-icon-check"}),e._v(" 正常 ")])]}}])}),s("el-table-column",{attrs:{prop:"create_time",label:"注册时间",width:"160",align:"center"}}),s("el-table-column",{attrs:{prop:"last_login_time",label:"最后登录",width:"160",align:"center"}}),s("el-table-column",{attrs:{label:"操作",width:"300",fixed:"right",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[s("div",{staticClass:"action-buttons"},[s("el-button",{attrs:{size:"mini",type:"info",icon:"el-icon-view"},on:{click:function(s){return e.handleView(t.row)}}},[e._v(" 查看 ")]),s("el-button",{attrs:{size:"mini",type:"primary",icon:"el-icon-edit"},on:{click:function(s){return e.handleEdit(t.row)}}},[e._v(" 编辑 ")]),s("el-button",{attrs:{size:"mini",type:"warning",icon:"el-icon-star-on"},on:{click:function(s){return e.handleVip(t.row)}}},[e._v(" VIP ")]),s("el-button",{attrs:{size:"mini",type:t.row.is_lock?"success":"warning",icon:t.row.is_lock?"el-icon-unlock":"el-icon-lock"},on:{click:function(s){return e.handleToggleLock(t.row)}}},[e._v(" "+e._s(t.row.is_lock?"解锁":"锁定")+" ")]),s("el-button",{attrs:{size:"mini",type:"info",icon:"el-icon-key"},on:{click:function(s){return e.handleResetPassword(t.row)}}},[e._v(" 重置密码 ")]),s("el-button",{attrs:{size:"mini",type:"danger",icon:"el-icon-delete"},on:{click:function(s){return e.handleDelete(t.row)}}},[e._v(" 删除 ")])],1)]}}])})],1),s("div",{staticClass:"pagination-section"},[s("el-pagination",{staticClass:"pagination",attrs:{"current-page":e.pagination.page,"page-sizes":[10,20,50,100],"page-size":e.pagination.size,total:e.userTotal,layout:"total, sizes, prev, pager, next, jumper",background:""},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1)],1),s("el-dialog",{staticClass:"detail-dialog",attrs:{title:"用户详情",visible:e.detailDialogVisible,width:"700px"},on:{"update:visible":function(s){e.detailDialogVisible=s}}},[e.currentUser?s("div",{staticClass:"user-detail"},[s("el-descriptions",{attrs:{column:2,border:""}},[s("el-descriptions-item",{attrs:{label:"用户ID"}},[e._v(e._s(e.currentUser.id))]),s("el-descriptions-item",{attrs:{label:"用户名"}},[e._v(e._s(e.currentUser.username))]),s("el-descriptions-item",{attrs:{label:"昵称"}},[e._v(e._s(e.currentUser.nickname))]),s("el-descriptions-item",{attrs:{label:"邮箱"}},[e._v(e._s(e.currentUser.email))]),s("el-descriptions-item",{attrs:{label:"手机号"}},[e._v(e._s(e.currentUser.phone||"未设置"))]),s("el-descriptions-item",{attrs:{label:"真实姓名"}},[e._v(e._s(e.currentUser.realname||"未设置"))]),s("el-descriptions-item",{attrs:{label:"VIP等级"}},[e._v(e._s(e.currentUser.vip_level||1))]),s("el-descriptions-item",{attrs:{label:"VIP到期时间"}},[e._v(" "+e._s(e.currentUser.vip_expire_at||"未开通")+" ")]),s("el-descriptions-item",{attrs:{label:"注册时间"}},[e._v(e._s(e.currentUser.create_time))]),s("el-descriptions-item",{attrs:{label:"最后登录"}},[e._v(e._s(e.currentUser.last_login_time||"未登录"))]),s("el-descriptions-item",{attrs:{label:"状态"}},[e.currentUser.is_lock?s("el-tag",{attrs:{type:"danger"}},[e._v("已锁定")]):s("el-tag",{attrs:{type:"success"}},[e._v("正常")])],1)],1)],1):e._e()]),s("el-dialog",{staticClass:"edit-dialog",attrs:{title:e.isEditMode?"编辑用户":"新增用户",visible:e.editDialogVisible,width:"600px"},on:{"update:visible":function(s){e.editDialogVisible=s},close:e.resetEditForm}},[s("el-form",{ref:"editForm",attrs:{model:e.editForm,rules:e.editRules,"label-width":"100px"}},[s("el-form-item",{attrs:{label:"用户名",prop:"username"}},[s("el-input",{attrs:{disabled:e.isEditMode},model:{value:e.editForm.username,callback:function(s){e.$set(e.editForm,"username",s)},expression:"editForm.username"}})],1),e.isEditMode?e._e():s("el-form-item",{attrs:{label:"密码",prop:"password"}},[s("el-input",{attrs:{type:"password","show-password":""},model:{value:e.editForm.password,callback:function(s){e.$set(e.editForm,"password",s)},expression:"editForm.password"}})],1),s("el-form-item",{attrs:{label:"昵称",prop:"nickname"}},[s("el-input",{model:{value:e.editForm.nickname,callback:function(s){e.$set(e.editForm,"nickname",s)},expression:"editForm.nickname"}})],1),s("el-form-item",{attrs:{label:"邮箱",prop:"email"}},[s("el-input",{model:{value:e.editForm.email,callback:function(s){e.$set(e.editForm,"email",s)},expression:"editForm.email"}})],1),s("el-form-item",{attrs:{label:"手机号",prop:"phone"}},[s("el-input",{model:{value:e.editForm.phone,callback:function(s){e.$set(e.editForm,"phone",s)},expression:"editForm.phone"}})],1),s("el-form-item",{attrs:{label:"真实姓名",prop:"realname"}},[s("el-input",{model:{value:e.editForm.realname,callback:function(s){e.$set(e.editForm,"realname",s)},expression:"editForm.realname"}})],1),s("el-form-item",{attrs:{label:"锁定状态",prop:"is_lock"}},[s("el-switch",{model:{value:e.editForm.is_lock,callback:function(s){e.$set(e.editForm,"is_lock",s)},expression:"editForm.is_lock"}})],1),e.isEditMode?e._e():s("el-form-item",{attrs:{label:"VIP等级",prop:"vip_level"}},[s("el-select",{attrs:{placeholder:"选择VIP等级"},model:{value:e.editForm.vip_level,callback:function(s){e.$set(e.editForm,"vip_level",s)},expression:"editForm.vip_level"}},[s("el-option",{attrs:{label:"普通用户",value:1}}),s("el-option",{attrs:{label:"VIP1",value:2}}),s("el-option",{attrs:{label:"VIP2",value:3}}),s("el-option",{attrs:{label:"VIP3",value:4}})],1)],1)],1),s("div",{attrs:{slot:"footer"},slot:"footer"},[s("el-button",{on:{click:function(s){e.editDialogVisible=!1}}},[e._v("取消")]),s("el-button",{attrs:{type:"primary",loading:e.editLoading},on:{click:e.handleSaveEdit}},[e._v(" "+e._s(e.isEditMode?"保存":"创建")+" ")])],1)],1),s("el-dialog",{staticClass:"vip-dialog",attrs:{title:"VIP管理",visible:e.vipDialogVisible,width:"500px"},on:{"update:visible":function(s){e.vipDialogVisible=s},close:e.resetVipForm}},[s("el-form",{ref:"vipForm",attrs:{model:e.vipForm,rules:e.vipRules,"label-width":"100px"}},[s("el-form-item",{attrs:{label:"VIP等级",prop:"vip_level"}},[s("el-select",{attrs:{placeholder:"选择VIP等级"},model:{value:e.vipForm.vip_level,callback:function(s){e.$set(e.vipForm,"vip_level",s)},expression:"vipForm.vip_level"}},[s("el-option",{attrs:{label:"VIP1",value:1}}),s("el-option",{attrs:{label:"VIP2",value:2}}),s("el-option",{attrs:{label:"VIP3",value:3}})],1)],1),s("el-form-item",{attrs:{label:"操作类型",prop:"action"}},[s("el-radio-group",{model:{value:e.vipForm.action,callback:function(s){e.$set(e.vipForm,"action",s)},expression:"vipForm.action"}},[s("el-radio",{attrs:{label:"add"}},[e._v("开通VIP")]),s("el-radio",{attrs:{label:"extend"}},[e._v("续费VIP")]),s("el-radio",{attrs:{label:"cancel"}},[e._v("取消VIP")])],1)],1),"cancel"!==e.vipForm.action?s("el-form-item",{attrs:{label:"时长(天)",prop:"days"}},[s("el-input-number",{attrs:{min:1,max:365},model:{value:e.vipForm.days,callback:function(s){e.$set(e.vipForm,"days",s)},expression:"vipForm.days"}})],1):e._e()],1),s("div",{attrs:{slot:"footer"},slot:"footer"},[s("el-button",{on:{click:function(s){e.vipDialogVisible=!1}}},[e._v("取消")]),s("el-button",{attrs:{type:"primary",loading:e.vipLoading},on:{click:e.handleSaveVip}},[e._v("确认")])],1)],1),s("el-dialog",{staticClass:"reset-password-dialog",attrs:{title:"重置用户密码",visible:e.resetPasswordDialogVisible,width:"500px"},on:{"update:visible":function(s){e.resetPasswordDialogVisible=s},close:e.resetPasswordForm}},[s("div",{staticClass:"reset-password-info"},[s("el-alert",{attrs:{title:"重置密码说明",type:"info",closable:!1,"show-icon":""}},[s("div",{attrs:{slot:"description"},slot:"description"},[s("p",[e._v("• 如果不填写新密码，将使用默认密码："),s("strong",[e._v("123456")])]),s("p",[e._v("• 重置后请及时通知用户修改密码")]),s("p",[e._v("• 此操作会记录在管理员操作日志中")])])])],1),s("el-form",{ref:"resetPasswordForm",staticStyle:{"margin-top":"20px"},attrs:{model:e.resetPasswordForm,rules:e.resetPasswordRules,"label-width":"100px"}},[s("el-form-item",{attrs:{label:"用户名"}},[s("el-input",{attrs:{disabled:""},model:{value:e.resetPasswordForm.username,callback:function(s){e.$set(e.resetPasswordForm,"username",s)},expression:"resetPasswordForm.username"}})],1),s("el-form-item",{attrs:{label:"新密码",prop:"new_password"}},[s("el-input",{attrs:{type:"password","show-password":"",placeholder:"留空则使用默认密码：123456"},model:{value:e.resetPasswordForm.new_password,callback:function(s){e.$set(e.resetPasswordForm,"new_password",s)},expression:"resetPasswordForm.new_password"}})],1),s("el-form-item",{attrs:{label:"确认密码",prop:"confirm_password"}},[s("el-input",{attrs:{type:"password","show-password":"",placeholder:"请再次输入新密码"},model:{value:e.resetPasswordForm.confirm_password,callback:function(s){e.$set(e.resetPasswordForm,"confirm_password",s)},expression:"resetPasswordForm.confirm_password"}})],1)],1),s("div",{attrs:{slot:"footer"},slot:"footer"},[s("el-button",{on:{click:function(s){e.resetPasswordDialogVisible=!1}}},[e._v("取消")]),s("el-button",{attrs:{type:"primary",loading:e.resetPasswordLoading},on:{click:e.handleSaveResetPassword}},[e._v(" 确认重置 ")])],1)],1)],1)},i=[function(){var e=this,s=e._self._c;return s("div",{staticClass:"header-left"},[s("div",{staticClass:"title-section"},[s("h1",{staticClass:"page-title"},[s("i",{staticClass:"el-icon-user-solid"}),e._v(" 用户管理 ")]),s("p",{staticClass:"page-subtitle"},[e._v("管理系统中的所有用户账户和权限")])])])}],r=t(5353);const l={name:"AdminUsers",data(){return{searchForm:{keyword:"",vip_status:"",is_lock:""},pagination:{page:1,size:20},selectedUsers:[],detailDialogVisible:!1,editDialogVisible:!1,vipDialogVisible:!1,batchVipDialogVisible:!1,currentUser:null,editLoading:!1,vipLoading:!1,isEditMode:!1,editForm:{username:"",password:"",nickname:"",email:"",phone:"",realname:"",is_lock:!1,vip_level:1},editRules:{username:[{required:!0,message:"请输入用户名",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"}],email:[{type:"email",message:"请输入正确的邮箱格式",trigger:"blur"}]},vipForm:{vip_level:1,action:"add",days:30},vipRules:{vip_level:[{required:!0,message:"请选择VIP等级",trigger:"change"}],action:[{required:!0,message:"请选择操作类型",trigger:"change"}],days:[{required:!0,message:"请输入时长",trigger:"blur"}]},resetPasswordDialogVisible:!1,resetPasswordForm:{username:"",new_password:"",confirm_password:""},resetPasswordRules:{confirm_password:[{validator:(e,s,t)=>{this.resetPasswordForm.new_password&&s!==this.resetPasswordForm.new_password?t(new Error("两次输入的密码不一致")):t()},trigger:"blur"}]},resetPasswordLoading:!1}},computed:{...(0,r.L8)("admin",["userList","userTotal","userLoading","vipUserCount","activeUserCount","lockedUserCount"])},mounted(){this.loadData()},methods:{...(0,r.i0)("admin",["getUserList","getUserDetail","createUser","updateUser","deleteUser","toggleUserLock","vipManage","batchDeleteUsers","batchLockUsers","batchVipUsers","resetUserPassword"]),async loadData(){try{console.log("开始加载用户数据...");const e={...this.searchForm,...this.pagination};console.log("请求参数:",e);const s=await this.getUserList(e);console.log("API响应结果:",s),s.success?console.log("用户数据加载成功，用户数量:",this.userTotal):(console.error("获取用户列表失败:",s.message),this.$message.error(s.message||"获取用户列表失败"))}catch(e){console.error("加载用户数据时发生错误:",e),this.$message.error("加载用户数据失败")}},handleAddUser(){this.currentUser=null,this.editForm={username:"",password:"",nickname:"",email:"",phone:"",realname:"",is_lock:!1,vip_level:1},this.editDialogVisible=!0,this.isEditMode=!1},handleSearch(){this.pagination.page=1,this.loadData()},resetSearch(){this.searchForm={keyword:"",vip_status:"",is_lock:""},this.pagination.page=1,this.loadData()},async refreshData(){await this.loadData(),this.$message.success("数据刷新成功")},handleSizeChange(e){this.pagination.size=e,this.loadData()},handleCurrentChange(e){this.pagination.page=e,this.loadData()},async handleView(e){try{const s=await this.getUserDetail(e.id);s.success?(this.currentUser=s.data,this.detailDialogVisible=!0):this.$message.error(s.message||"获取用户详情失败")}catch(s){console.error("获取用户详情失败:",s),this.$message.error("获取用户详情失败")}},handleEdit(e){this.currentUser=e,this.editForm={username:e.username,password:"",nickname:e.nickname,email:e.email,phone:e.phone,realname:e.realname,is_lock:e.is_lock},this.editDialogVisible=!0,this.isEditMode=!0},async handleSaveEdit(){try{const e=await this.$refs.editForm.validate();if(!e)return;if(this.editLoading=!0,this.isEditMode){const e=await this.updateUser({userId:this.currentUser.id,data:this.editForm});e.success?(this.$message.success(e.message||"更新成功"),this.editDialogVisible=!1,this.loadData()):this.$message.error(e.message||"更新失败")}else{const e=await this.createUser(this.editForm);e.success?(this.$message.success(e.message||"创建成功"),this.editDialogVisible=!1,this.loadData()):this.$message.error(e.message||"创建失败")}}catch(e){console.error("保存用户失败:",e),this.$message.error("保存用户失败")}finally{this.editLoading=!1}},handleVip(e){this.currentUser=e,this.vipForm={vip_level:e.vip_level||1,action:"add",days:30},this.vipDialogVisible=!0},async handleSaveVip(){try{const e=await this.$refs.vipForm.validate();if(!e)return;this.vipLoading=!0;const s=await this.vipManage({userId:this.currentUser.id,data:this.vipForm});s.success?(this.$message.success(s.message||"VIP管理成功"),this.vipDialogVisible=!1,this.loadData()):this.$message.error(s.message||"VIP管理失败")}catch(e){console.error("VIP管理失败:",e),this.$message.error("VIP管理失败")}finally{this.vipLoading=!1}},async handleDelete(e){try{await this.$confirm(`确定要删除用户 "${e.username}" 吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const s=await this.deleteUser(e.id);s.success?(this.$message.success(s.message||"删除成功"),this.loadData()):this.$message.error(s.message||"删除失败")}catch(s){"cancel"!==s&&(console.error("删除用户失败:",s),this.$message.error("删除用户失败"))}},async handleToggleLock(e){try{const s=e.is_lock?"解锁":"锁定";await this.$confirm(`确定要${s}用户 "${e.username}" 吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const t=await this.toggleUserLock({userId:e.id,isLock:!e.is_lock});t.success?(this.$message.success(t.message||`${s}成功`),this.loadData()):this.$message.error(t.message||`${s}失败`)}catch(s){"cancel"!==s&&(console.error("锁定/解锁用户失败:",s),this.$message.error("锁定/解锁用户失败"))}},resetEditForm(){this.editForm={username:"",password:"",nickname:"",email:"",phone:"",realname:"",is_lock:!1,vip_level:1},this.$refs.editForm&&this.$refs.editForm.resetFields()},resetVipForm(){this.vipForm={vip_level:1,action:"add",days:30},this.$refs.vipForm&&this.$refs.vipForm.resetFields()},handleSelectionChange(e){this.selectedUsers=e.map((e=>e.id))},async handleBatchVip(){0!==this.selectedUsers.length?(this.vipForm={vip_level:1,action:"add",days:30},this.batchVipDialogVisible=!0):this.$message.warning("请先选择用户")},async handleBatchLock(){if(0!==this.selectedUsers.length)try{await this.$confirm(`确定要锁定选中的 ${this.selectedUsers.length} 个用户吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=await this.batchLockUsers({userIds:this.selectedUsers,isLock:!0});e.success?(this.$message.success(e.message||"批量锁定成功"),this.clearSelection(),this.loadData()):this.$message.error(e.message||"批量锁定失败")}catch(e){"cancel"!==e&&(console.error("批量锁定失败:",e),this.$message.error("批量锁定失败"))}else this.$message.warning("请先选择用户")},async handleBatchDelete(){if(0!==this.selectedUsers.length)try{await this.$confirm(`确定要删除选中的 ${this.selectedUsers.length} 个用户吗？此操作不可恢复！`,"警告",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"error"});const e=await this.batchDeleteUsers(this.selectedUsers);e.success?(this.$message.success(e.message||"批量删除成功"),this.clearSelection(),this.loadData()):this.$message.error(e.message||"批量删除失败")}catch(e){"cancel"!==e&&(console.error("批量删除失败:",e),this.$message.error("批量删除失败"))}else this.$message.warning("请先选择用户")},clearSelection(){this.selectedUsers=[],this.$refs.userTable&&this.$refs.userTable.clearSelection()},exportData(){this.$message.info("导出功能开发中...")},handleResetPassword(e){this.currentUser=e,this.resetPasswordForm={username:e.username,new_password:"",confirm_password:""},this.resetPasswordDialogVisible=!0},async handleSaveResetPassword(){try{const e=await this.$refs.resetPasswordForm.validate();if(!e)return;this.resetPasswordLoading=!0;const s=await this.resetUserPassword({userId:this.currentUser.id,newPassword:this.resetPasswordForm.new_password||"123456"});s.success?(this.$message.success(s.message||"重置密码成功"),this.resetPasswordDialogVisible=!1,this.loadData()):this.$message.error(s.message||"重置密码失败")}catch(e){console.error("重置密码失败:",e),this.$message.error("重置密码失败")}finally{this.resetPasswordLoading=!1}},resetPasswordForm(){this.resetPasswordForm={username:"",new_password:"",confirm_password:""},this.$refs.resetPasswordForm&&this.$refs.resetPasswordForm.resetFields()}}},o=l;var n=t(1656),c=(0,n.A)(o,a,i,!1,null,"be6867be",null);const d=c.exports}}]);