const paragraphBox = {
    'newParaDialog': {
        'title': '填写新段落内容',
        'formContent': '段落内容',
        'formTitle': "段落标题",

        'btnContinue': '继续填写',
        'btnCancel': '取消',
        'btnSubmit': '提交',
        'tipCancel': '内容尚未保存，确定要取消吗？'
        // 'tipCancel':'',
        // 'btnContinue':'',
        // 'btnContinue':'',
    }
}

// Element UI 分页组件的翻译
const elementUI = {
    'el': {
        'pagination': {
            'goto': '前往',
            'pagesize': '条/页',
            'total': '共 {total} 条',
            'pageClassifier': '页',
            'prev': '上一页',
            'next': '下一页',
            'first': '首页',
            'last': '尾页',
            'pager': '分页',
            'sizes': '条/页'
        },
        'table': {
            'emptyText': '暂无数据',
            'confirmFilter': '筛选',
            'resetFilter': '重置',
            'clearFilter': '全部',
            'sumText': '合计'
        },
        'select': {
            'loading': '加载中',
            'noMatch': '无匹配数据',
            'noData': '无数据',
            'placeholder': '请选择'
        },
        'datepicker': {
            'now': '此刻',
            'today': '今天',
            'cancel': '取消',
            'clear': '清空',
            'confirm': '确定',
            'selectDate': '选择日期',
            'selectTime': '选择时间',
            'startDate': '开始日期',
            'startTime': '开始时间',
            'endDate': '结束日期',
            'endTime': '结束时间',
            'prevYear': '前一年',
            'nextYear': '后一年',
            'prevMonth': '上个月',
            'nextMonth': '下个月',
            'year': '年',
            'month1': '1 月',
            'month2': '2 月',
            'month3': '3 月',
            'month4': '4 月',
            'month5': '5 月',
            'month6': '6 月',
            'month7': '7 月',
            'month8': '8 月',
            'month9': '9 月',
            'month10': '10 月',
            'month11': '11 月',
            'month12': '12 月',
            'weeks': {
                'sun': '日',
                'mon': '一',
                'tue': '二',
                'wed': '三',
                'thu': '四',
                'fri': '五',
                'sat': '六'
            },
            'months': {
                'jan': '一月',
                'feb': '二月',
                'mar': '三月',
                'apr': '四月',
                'may': '五月',
                'jun': '六月',
                'jul': '七月',
                'aug': '八月',
                'sep': '九月',
                'oct': '十月',
                'nov': '十一月',
                'dec': '十二月'
            }
        },
        'dialog': {
            'close': '关闭此对话框',
            'confirm': '确定',
            'cancel': '取消'
        },
        'messagebox': {
            'title': '提示',
            'confirm': '确定',
            'cancel': '取消',
            'error': '输入的数据不合法!'
        },
        'upload': {
            'deleteTip': '按 delete 键可删除',
            'delete': '删除',
            'preview': '查看图片',
            'continue': '继续上传'
        },
        'popconfirm': {
            'confirmButtonText': '确定',
            'cancelButtonText': '取消'
        },
        'colorpicker': {
            'confirm': '确定',
            'clear': '清空'
        },
        'image': {
            'error': '加载失败'
        }
    }
}

const education = {
    '中学': '中学',
    '大专': '大专',
    '本科': '本科',
    '硕士': '硕士',
    '博士': '博士',
}

const userRankPage = {
    'freeLimit1': '免费用户每两次AI请求之间需间隔1分钟',
    'freeLimit2': '免费用户需要逐段手动生成内容',
    'vipFeature1': 'VIP用户无需等待',
    'vipFeature2': 'VIP用户可一键自动生成全文',

    'features': 'AI功能',
    'featuresTitle': '题目生成',
    'featuresOutline': '提纲生成',
    'featuresContent': '内容生成',
    'freeUser': '普通用户',
    'freeUserFeatures': '可免费使用下列勾选功能',
    'vipUser': 'VIP用户',
    'vipUserFeatures': '可使用下列完整功能',
    'toBuy': '手机淘宝APP扫码购买VIP',
    'tipPrice': '价格',
    'tipFree': '免费',

}
const userRank = {
    'titleRules': [
        { name: "论文题目AI自动生成", vip: true, normal: true },
        { name: "可选择任意学科", vip: true, normal: true },
        { name: "支持关键字自定义", vip: true, normal: true },
        { name: "中英双语题目自动翻译", vip: true, normal: true },
        { name: "单次最多推荐2个题目", vip: true, normal: true },
        { name: "单题目最多推荐2个关键字", vip: true, normal: true },
        { name: "单次最多推荐6个题目", vip: true, normal: false },
        { name: "单题目最多推荐10个关键字", vip: true, normal: false },
        { name: "生成的论文题目数据一键下载", vip: true, normal: false },
    ],
    'outlineRules': [
        { name: "论文提纲AI自动生成", normal: true },
        { name: "支持选择任意学科", normal: true },
        { name: "支持大专、本科水平论文提纲", normal: true },
        { name: "支持全部学术水平论文提纲", normal: false },
        { name: "生成最多6个一级章节的提纲", normal: true },
        { name: "生成最多9个一级章节的提纲", normal: false },
        { name: "论文提纲数据一键下载", normal: false },
    ],
    'contentRules': [
        { name: "论文内容AI自动生成", normal: true },
        { name: "自动生成摘要", normal: false },
        { name: "单独段落由AI重新生成", normal: true },
        { name: "全部段落由AI批量重新生成", normal: false },
        { name: "段落标题可单独手动编辑", normal: false },
        { name: "段落内容可单独手动编辑", normal: true },
        { name: "可生成单段最长200字", normal: true },
        { name: "可生成单段最长800字", normal: false },
        { name: "可一键下载为word格式", normal: false },
        { name: "可一键导出json格式数据", normal: false },
    ],

}

const helpPage = {
    'title': '让AI助你写好论文！高效、专业、便捷<br>早鸟论文，你的论文写作助手！',
    'slogan': '三步完成论文，智能高效！',
    'letsGo': '开始你的写作之旅',

    'step1': {
        'title': '智能选题',
        'desc': '基于AI分析，推荐热门研究方向，生成专业的论文题目。',
    },
    'step2': {
        'title': '提纲规划',
        'desc': '智能生成专业的论文框架，合理的章节结构让论文更有条理。',
    },
    'step3': {
        'title': '内容创作',
        'desc': 'AI辅助生成专业内容，支持实时修改和优化，让论文更加完善。',
    },
    'step4': {
        'title': '一键导出',
        'desc': '自动排版，标准格式，直接导出即可使用的论文文档。',
    }
}



const menu = {

    'categoryPaper':'论文写作',
    'categoryWork':'工作助手',
    'categoryTutorial':'使用教程',

    'myId':'我的ID:{id}',
    'vipBalance':'还有{balanceDay}',
    'homepage': '首页',
    'postReading': '读后感',
    'brainStorming': '头脑风暴',
    'workSummary': '工作总结',
    'weeklyReport': '周报',

    'rewrite': '改写',

    'polish': '润色',

    'slogan': 'AI论文生成只需三步',
    'title': '智能选题',
    'outline': '提纲拟定',
    'content': '内容生成',
    'paperGeneration': '论文生成',
    'smartInteraction': '智能互动',
    'aicgApp': 'AICG应用',
    'reportGeneration': '报告生成',
    'dailyReport': '日报生成',
    'weeklyReport': '周报生成',
    'monthlyReport': '月报生成',
    'summary': '总结生成',
    'help': '使用教程',
    'member': '升级VIP',
    'userData': '用量统计',
    'userInfo': '我的信息',
    'loginTip': '登录体验全部功能',
    'loginOut': '退出',
    'advise': '意见建议',
    'rate': '体验评分',
    'tipFreeUser': '免费用户',
    'tipVipUser': 'VIP 用户',
    'tipPrice': '产看价格',
    'tipExpireAt': '到期',

    'tipLogout': '已退出登录，欢迎再次使用',
    'tipRemind': '提醒'
}
const titlePage = {
    'major': '研究专业',
    'topic': '研究课题',
    'lang': '写作语言',
    'level': '学历',
    'keyword': '关键字',
    'btnSubmit': '推荐论文题目',
    'title': '题目',
    'btnSelectMajor': '点击选择学科',
    'btnSelectTitle': '选择此标题',
    'btnClose': '关闭',

    'placeholderMajor': '请填写或选择学科',
    'placeholderTopic': '请填写细分研究方向',


    'tipSelectThis': 'AI建议标题如下，你可以选择合适的标题，进一步生成提纲:',
    'relativeKeyword': '相关关键词',

}
const outlinePage = {
    'btnSubmit': '生成提纲',
    'title': '论文标题',
    'level': '对应学历',
    'lang': '写作语言',
    'length': '篇幅长度',
    'btnSelectOutline': '选此提纲',
    'btnReSubmit': '重新生成',
    'btnExportWord': '导出为word文档',
}
const contentPage = {
    'title': '标题',
    'lang': '语言',
    'length': '篇幅',
    'level': '水平',
    'createTime': '时间',

    'btnOutlineEditMode':'进入大纲精修模式',
    'btnReturnTxtMode':'返回内容编辑模式',
    'btnRunning': 'AI引擎书写中，请稍等',
    'btnSubmit': '一键全生成',
    'btnDownload': '下载WORD文档',
    'btnProgress': '可在右侧大纲查看书写进度',

    'leftTitleThesisList': '我所有论文',
    'leftTitleOutline': '论文大纲',


    'notifyStartGeneratTask': 'ai引擎将逐个段落，单独生成，可在提纲中查看进度'

}

const languageList = [
    '中文',
    '英语',
    '法语',
    '俄语',
    '日语',
    '葡萄牙语',
]

const errorMsg = {
    'needLogin': '需要登录后才能体验所有功能',
    'serverError': '服务器请求失败',
}
export default {
    paragraphBox,
    languageList,
    education,
    userRankPage,
    userRank,
    helpPage,
    menu,
    titlePage,
    outlinePage,
    contentPage,
    errorMsg,
    elementUI
}