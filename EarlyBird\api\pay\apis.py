from EarlyBird.api.pay import bp
import logging
from flask import jsonify, g, request
from flask_pydantic import validate
from EarlyBird.common.libs.api_result import ApiResponse, ApiResult
from EarlyBird.common import Result
from EarlyBird.common.pay_utils import PaymentUtil
from EarlyBird.common.wechat_pay_v3 import WeChatPayV3
from EarlyBird.config.wechat_pay_config import WeChatPayConfig
from EarlyBird.model.payment import Payment, PaymentConfig
from EarlyBird.common.database import db
from EarlyBird.api.pay.qr_generator import QRCodeGenerator
import uuid
import time
import json
import urllib.parse

LOGGER = logging.getLogger(__name__)

# 全局变量，用于缓存微信支付V3实例
_wechat_pay_instance = None
_wechat_config_cache = None

def get_wechat_pay():
    """获取微信支付V3实例，延迟初始化"""
    global _wechat_pay_instance, _wechat_config_cache
    
    try:
        # 获取最新配置
        wechat_config = WeChatPayConfig.get_config()
        
        # 检查配置是否有效
        if not wechat_config or not wechat_config.get('appid') or wechat_config['appid'].startswith('your_'):
            LOGGER.warning("微信支付配置无效或为默认值")
            return None
        
        # 检查是否需要重新初始化（配置变更）
        if _wechat_pay_instance is None or _wechat_config_cache != wechat_config:
            LOGGER.info("初始化微信支付V3实例")
            
            _wechat_pay_instance = WeChatPayV3(
                appid=wechat_config['appid'],
                mchid=wechat_config['mchid'],
                private_key_path=wechat_config['private_key_path'],
                serial_no=wechat_config['serial_no'],
                api_v3_key=wechat_config['api_v3_key']
            )
            _wechat_config_cache = wechat_config.copy()
            LOGGER.info("微信支付V3初始化成功")
        
        return _wechat_pay_instance
        
    except Exception as e:
        LOGGER.error(f"微信支付V3初始化失败: {str(e)}")
        _wechat_pay_instance = None
        _wechat_config_cache = None
        return None

@bp.route("/create", methods=["POST"])
def create_payment():
    """创建支付订单"""
    try:
        data = request.get_json()
        if not data:
            return ApiResult.error("请求数据为空")
        
        # 验证必填参数
        required_fields = ['user_id', 'product_id', 'pay_type', 'amount']
        for field in required_fields:
            if field not in data:
                return ApiResult.error(f"缺少必填参数: {field}")
        
        user_id = data['user_id']
        product_id = data['product_id']
        pay_type = data['pay_type']
        amount = float(data['amount'])
        thesis_id = data.get('thesis_id')
        thesis_title = data.get('thesis_title', '论文下载')
        
        # 只支持微信支付
        if pay_type != 'wxpay':
            return ApiResult.error("当前仅支持微信支付")
        
        # 检查微信支付是否可用
        wechat_pay = get_wechat_pay()
        if not wechat_pay:
            return ApiResult.error("微信支付服务不可用，请联系管理员")
        
        # 生成订单号
        out_trade_no = PaymentUtil.generate_order_no()
        
        # 创建支付记录
        payment = Payment(
            user_id=user_id,
            product_id=product_id,
            amount=amount,
            out_trade_no=out_trade_no,
            status=0,  # 未支付
            pay_type=pay_type
        )
        
        db.session.add(payment)
        db.session.commit()
        
        # 创建支付订单成功
        
        # 调用微信支付V3下单接口
        client_ip = request.remote_addr
        wechat_config = WeChatPayConfig.get_config()
        notify_url = wechat_config['notify_url']
        
        result = wechat_pay.create_native_order(
            out_trade_no=out_trade_no,
            description=thesis_title,
            amount=amount,
            notify_url=notify_url,
            client_ip=client_ip
        )
        
        if result['success']:
            # 使用本地二维码生成器
            wechat_code_url = result['code_url']
            qr_code_base64 = QRCodeGenerator.generate_qr_base64(wechat_code_url, size=200)
            
            # 优先使用本地生成的base64二维码，失败时回退到在线服务
            qr_code_url = qr_code_base64 if qr_code_base64 else f"https://api.qrserver.com/v1/create-qr-code/?size=200x200&data={urllib.parse.quote(wechat_code_url)}"
            
            # 返回支付信息
            result_data = {
                'out_trade_no': out_trade_no,
                'order_id': out_trade_no,
                'qr_code_url': qr_code_url,  # 使用二维码图片（优先本地生成）
                'qr_url': qr_code_url,
                'pay_url': wechat_code_url,  # 保留原始微信支付URL
                'redirect_url': wechat_code_url,
                'wechat_code_url': wechat_code_url,  # 原始微信支付URL
                'amount': amount,
                'pay_type': pay_type,
                'status': 0
            }
            
            return ApiResult.success(result_data, "支付订单创建成功")
        else:
            # 删除失败的支付记录
            db.session.delete(payment)
            db.session.commit()
            
            error_msg = result.get('error_message', '微信支付下单失败')
            return ApiResult.error(error_msg)
        
    except Exception as e:
        LOGGER.exception(f"创建支付订单失败: {str(e)}")
        return ApiResult.error(f"创建支付订单失败: {str(e)}")

@bp.route("/query", methods=["POST"])
def query_payment():
    """查询支付订单状态"""
    try:
        data = request.get_json()
        if not data:
            return ApiResult.error("请求数据为空")
        
        out_trade_no = data.get('out_trade_no')
        if not out_trade_no:
            return ApiResult.error("缺少订单号")
        
        # 查询数据库中的支付记录
        payment = Payment.query.filter_by(out_trade_no=out_trade_no).first()
        if not payment:
            return ApiResult.error("订单不存在")
        
        # 如果数据库中状态是未支付，则查询微信支付平台
        wechat_pay = get_wechat_pay()
        if payment.status == 0 and wechat_pay:
            # 查询微信支付平台状态
            query_result = wechat_pay.query_order(out_trade_no)
            
            if query_result['success']:
                trade_state = query_result['trade_state']
                
                # 更新数据库中的支付状态
                if trade_state == 'SUCCESS':
                    payment.status = 1  # 已支付
                    payment.trade_no = query_result.get('transaction_id', '')
                    payment.pay_time = query_result.get('success_time', '')
                    payment.notify_data = json.dumps(query_result)
                    db.session.commit()
                    
                    # 支付成功
                elif trade_state in ['CLOSED', 'REVOKED']:
                    payment.status = 2  # 已关闭/已撤销
                    db.session.commit()
        
        # 返回订单状态
        result_data = {
            'out_trade_no': payment.out_trade_no,
            'trade_no': payment.trade_no,
            'status': payment.status,
            'amount': float(payment.amount),
            'pay_type': payment.pay_type,
            'pay_time': payment.pay_time,
            'create_time': payment.create_time.strftime('%Y-%m-%d %H:%M:%S') if payment.create_time else ''
        }
        
        return ApiResult.success(result_data, "查询成功")
        
    except Exception as e:
        LOGGER.exception(f"查询支付订单失败: {str(e)}")
        return ApiResult.error(f"查询支付订单失败: {str(e)}")

@bp.route("/wechat_notify", methods=["POST"])
def wechat_payment_notify():
    """微信支付回调通知处理"""
    try:
        # 获取微信支付实例
        wechat_pay = get_wechat_pay()
        if not wechat_pay:
            LOGGER.error("微信支付服务不可用")
            return "FAIL", 400
        
        # 获取通知数据
        notify_data = request.get_data(as_text=True)
        # 收到微信支付通知
        
        # 验证签名（简化处理，实际使用时需要完整验签）
        if not wechat_pay.verify_notify_signature(request.headers, notify_data):
            LOGGER.error("微信支付回调签名验证失败")
            return "FAIL", 400
        
        # 解析通知数据
        try:
            data = json.loads(notify_data)
        except:
            LOGGER.error("微信支付回调数据解析失败")
            return "FAIL", 400
        
        # 解密通知数据（简化处理，实际使用时需要完整解密）
        resource = data.get('resource', {})
        encrypted_data = resource.get('ciphertext', '')
        associated_data = resource.get('associated_data', '')
        nonce = resource.get('nonce', '')
        
        decrypted_data = wechat_pay.decrypt_notify_data(encrypted_data, associated_data, nonce)
        if not decrypted_data:
            LOGGER.error("微信支付回调数据解密失败")
            return "FAIL", 400
        
        # 解析解密后的数据
        try:
            notify_info = json.loads(decrypted_data)
        except:
            LOGGER.error("微信支付回调解密数据解析失败")
            return "FAIL", 400
        
        out_trade_no = notify_info.get('out_trade_no')
        trade_state = notify_info.get('trade_state')
        
        if not out_trade_no:
            LOGGER.error("微信支付回调缺少订单号")
            return "FAIL", 400
        
        # 查询支付记录
        payment = Payment.query.filter_by(out_trade_no=out_trade_no).first()
        if not payment:
            LOGGER.error(f"微信支付回调：订单不存在 {out_trade_no}")
            return "FAIL", 400
        
        # 验证支付状态
        if trade_state == 'SUCCESS':  # 支付成功
            payment.status = 1
            payment.trade_no = notify_info.get('transaction_id', '')
            payment.pay_time = notify_info.get('success_time', '')
            payment.notify_data = notify_data
            db.session.commit()
            
            # 微信支付回调处理成功
            return "SUCCESS"
        else:
            LOGGER.warning(f"微信支付回调：支付失败 {out_trade_no}, 状态: {trade_state}")
            return "FAIL"
            
    except Exception as e:
        LOGGER.exception(f"处理微信支付回调失败: {str(e)}")
        return "FAIL"

@bp.route("/notify", methods=["POST"])
def payment_notify():
    """通用支付异步通知处理（兼容旧接口）"""
    return wechat_payment_notify()

@bp.route("/products", methods=["GET"])
def get_payment_products():
    """获取支付商品列表"""
    try:
        # 这里可以返回论文下载相关的商品信息
        products = [
            {
                'id': 1,
                'name': '论文下载服务',
                'product_id': 'thesis_download',
                'price': 10.00,
                'description': '下载单篇论文',
                'is_active': True
            }
        ]
        
        return ApiResult.success(products, "获取商品列表成功")
        
    except Exception as e:
        LOGGER.exception(f"获取商品列表失败: {str(e)}")
        return ApiResult.error(f"获取商品列表失败: {str(e)}")

@bp.route("/test", methods=["POST"])
def test_payment():
    """测试支付接口"""
    try:
        data = request.get_json()
        if not data:
            return ApiResult.error("请求数据为空")
        
        pay_type = data.get('pay_type', 'wxpay')
        amount = data.get('amount', 0.01)
        
        if pay_type != 'wxpay':
            return ApiResult.error("当前仅支持微信支付")
        
        # 生成测试订单号
        test_order_no = f"TEST_{int(time.time())}"
        
        # 检查微信支付是否可用
        wechat_pay = get_wechat_pay()
        if not wechat_pay:
            LOGGER.warning("微信支付服务不可用，使用模拟测试模式")
            
            # 模拟测试模式 - 生成一个模拟的二维码
            mock_qr_data = f"weixin://wxpay/bizpayurl?pr=test_{test_order_no}"
            
            # 优先使用本地二维码生成器
            qr_code_base64 = QRCodeGenerator.generate_qr_base64(mock_qr_data, size=200)
            qr_code_url = qr_code_base64 if qr_code_base64 else f"https://api.qrserver.com/v1/create-qr-code/?size=200x200&data={urllib.parse.quote(mock_qr_data)}"
            
            result_data = {
                'out_trade_no': test_order_no,
                'qr_code_url': qr_code_url,
                'amount': amount,
                'pay_type': pay_type,
                'is_mock': True,  # 标识这是模拟测试
                'message': '这是模拟测试二维码，不会产生真实支付'
            }
            
            LOGGER.info(f"生成模拟测试支付二维码: {test_order_no}")
            return ApiResult.success(result_data, "模拟测试支付接口成功（仅用于测试界面功能）")
        
        # 真实微信支付测试
        try:
            # 创建测试支付参数
            client_ip = request.remote_addr
            wechat_config = WeChatPayConfig.get_config()
            notify_url = wechat_config['notify_url']
            
            result = wechat_pay.create_native_order(
                out_trade_no=test_order_no,
                description="测试商品",
                amount=amount,
                notify_url=notify_url,
                client_ip=client_ip
            )
            
            if result['success']:
                # 使用本地二维码生成器
                wechat_code_url = result['code_url']
                qr_code_base64 = QRCodeGenerator.generate_qr_base64(wechat_code_url, size=200)
                
                if qr_code_base64:
                    result_data = {
                        'out_trade_no': test_order_no,
                        'qr_code_url': qr_code_base64,  # 使用base64二维码图片
                        'wechat_code_url': wechat_code_url,  # 保留原始微信支付URL
                        'amount': amount,
                        'pay_type': pay_type,
                        'is_mock': False  # 标识这是真实支付
                    }
                    
                    LOGGER.info(f"生成真实测试支付二维码: {test_order_no}, 微信URL: {wechat_code_url}")
                    return ApiResult.success(result_data, "测试支付接口成功")
                else:
                    # 二维码生成失败，回退到在线服务
                    qr_code_img_url = f"https://api.qrserver.com/v1/create-qr-code/?size=200x200&data={urllib.parse.quote(wechat_code_url)}"
                    result_data = {
                        'out_trade_no': test_order_no,
                        'qr_code_url': qr_code_img_url,
                        'wechat_code_url': wechat_code_url,
                        'amount': amount,
                        'pay_type': pay_type,
                        'is_mock': False
                    }
                    return ApiResult.success(result_data, "测试支付接口成功")
            else:
                error_message = result.get('error_message', '测试支付接口失败')
                LOGGER.error(f"微信支付测试失败: {error_message}")
                
                # 如果真实支付失败，也提供模拟测试
                mock_qr_data = f"weixin://wxpay/bizpayurl?pr=test_{test_order_no}"
                qr_code_base64 = QRCodeGenerator.generate_qr_base64(mock_qr_data, size=200)
                qr_code_url = qr_code_base64 if qr_code_base64 else f"https://api.qrserver.com/v1/create-qr-code/?size=200x200&data={urllib.parse.quote(mock_qr_data)}"
                
                result_data = {
                    'out_trade_no': test_order_no,
                    'qr_code_url': qr_code_url,
                    'amount': amount,
                    'pay_type': pay_type,
                    'is_mock': True,
                    'message': f'微信支付测试失败：{error_message}，已切换到模拟模式',
                    'error_details': error_message
                }
                
                return ApiResult.success(result_data, f"微信支付测试失败，提供模拟测试: {error_message}")
                
        except Exception as e:
            LOGGER.exception(f"微信支付测试异常: {str(e)}")
            
            # 异常时也提供模拟测试
            mock_qr_data = f"weixin://wxpay/bizpayurl?pr=test_{test_order_no}"
            qr_code_base64 = QRCodeGenerator.generate_qr_base64(mock_qr_data, size=200)
            qr_code_url = qr_code_base64 if qr_code_base64 else f"https://api.qrserver.com/v1/create-qr-code/?size=200x200&data={urllib.parse.quote(mock_qr_data)}"
            
            result_data = {
                'out_trade_no': test_order_no,
                'qr_code_url': qr_code_url,
                'amount': amount,
                'pay_type': pay_type,
                'is_mock': True,
                'message': f'微信支付测试异常：{str(e)}，已切换到模拟模式',
                'error_details': str(e)
            }
            
            return ApiResult.success(result_data, f"微信支付测试异常，提供模拟测试: {str(e)}")
        
    except Exception as e:
        LOGGER.exception(f"测试支付接口失败: {str(e)}")
        return ApiResult.error(f"测试支付接口失败: {str(e)}") 