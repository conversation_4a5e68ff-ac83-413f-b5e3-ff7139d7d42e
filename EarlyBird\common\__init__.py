from .libs.api_result import Result, PageNav, ApiResponse, ApiResult
from sqlalchemy import Enum
import os
import time
import logging
import re

LOGGER = logging.getLogger(__name__)

def purgeJsonPrefix(s: str):
    if s.startswith("```json"):
        s = s[8:]
    endpos = s.find("```")
    if endpos < 0:
        return s
    return s[0:endpos]


## 数一段话中的字数，1个汉字算1个，单词算1个，标点算1个。其余忽略
def countCharacters(text):
    if text is None:
        return 0
    text = re.sub("\n", " ", text)
    pattern = re.compile(r"[\u4e00-\u9fa5]")
    chinese_characters = re.findall(pattern, text)
    chineseCount = len(chinese_characters)

    restString = re.sub(pattern, " ", text)
    restString = re.sub(r"\s+", " ", restString)
    restString = restString.strip()
    restStringSplit = restString.split(" ")

    return chineseCount + len(restStringSplit)


def isDevEnv():
    try:
        return os.environ.get("FLASK_ENV") == "development"
    except Exception as e:
        return False


def canDirectAccessNet():
    return True

def isDeplyOnAws():
    return True



class TaskStatus(Enum):
    BEFORE_INIT = 1
    INIT = 11
    RUNING = 12
    SUCCESS = 13
    ERROR = 14

    @staticmethod
    def getName(s):
        m = {
            TaskStatus.INIT: "等待中",
            TaskStatus.RUNING: "生成中",
            TaskStatus.SUCCESS: "生成完毕",
            TaskStatus.ERROR: "生成错误",
        }
        return m[s] if s in m else "-"
