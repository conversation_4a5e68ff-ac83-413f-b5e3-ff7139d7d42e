from abc import ABCMeta, abstractmethod
import json
import time
import pymysql
import logging
import traceback
import threading
from flask import g
from EarlyBird.api.talk2ai.dantic import (
    ParamOutline,
    ParamTitle,
    ParamGetContentFromOutline,
    ParamGenerateSingleParagraph,
    ParamRegenDigest,
)
from EarlyBird.ExtendRegister.db_register import db
from EarlyBird.ExtendRegister.model_register import ApiLog
from typing import Union, List


LOGGER = logging.getLogger(__name__)


def purgeJsonPrefix(s: str):
    if s.startswith("```json"):
        s = s[8:]
    endpos = s.find("```")
    if endpos < 0:
        return s
    return s[0:endpos]


class InvokeResult:
    def __init__(self, isSuccess=True, message="", tokenSize=0, data="") -> None:
        self.isSuccess: bool = isSuccess
        self.message: str = message
        self.tokenSize: int = tokenSize
        self.data: Union[str, List] = data

    def isSucc(self):
        return self.isSuccess

    def __repr__(self) -> str:
        return json.dumps(
            {
                "tokenSize": self.tokenSize,
                "isSuccess": self.isSuccess,
                "message": self.message,
                "data": self.data,
            }
        )


class BaseAiModel(object):
    __metaclass__ = ABCMeta

    @abstractmethod
    def getTitle(self, param: ParamTitle) -> InvokeResult:
        pass

    @abstractmethod
    def getOutline(self, param: ParamOutline) -> InvokeResult:
        pass

    @abstractmethod
    def generateParagraph(self, param: ParamGenerateSingleParagraph) -> InvokeResult:
        pass

    @abstractmethod
    def generateDigest(self, param: ParamRegenDigest) -> InvokeResult:
        pass

    @abstractmethod
    def generateDigestEnglish(self, param: ParamRegenDigest) -> InvokeResult:
        pass
