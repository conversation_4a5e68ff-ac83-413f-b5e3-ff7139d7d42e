<template>
  <div class="admin-accounts-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <div class="title-section">
            <h1 class="page-title">
              <i class="el-icon-user"></i>
              管理员账户
            </h1>
            <p class="page-subtitle">管理系统中的管理员账户和权限</p>
          </div>
        </div>
        <div class="header-actions">
          <el-button type="primary" icon="el-icon-plus" size="medium" @click="handleCreate">
            新增管理员
          </el-button>
          <el-button type="success" icon="el-icon-refresh" size="medium" @click="fetchData">
            刷新数据
          </el-button>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="search-card" shadow="hover">
      <div class="search-header">
        <h3 class="search-title">
          <i class="el-icon-search"></i>
          搜索筛选
        </h3>
      </div>
      <el-form :inline="true" :model="listQuery" class="search-form">
        <el-form-item label="关键词">
          <el-input
            v-model="listQuery.keyword"
            placeholder="用户名/姓名/邮箱"
            clearable
            prefix-icon="el-icon-search"
            @keyup.enter.native="handleFilter"
            class="search-input"
          />
        </el-form-item>
        <el-form-item class="search-buttons">
          <el-button type="primary" icon="el-icon-search" @click="handleFilter" class="search-btn">
            搜索
          </el-button>
          <el-button icon="el-icon-refresh" @click="resetSearch" class="reset-btn">
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card" shadow="hover" v-loading="loading">
      <div class="table-header">
        <h3 class="table-title">
          <i class="el-icon-document"></i>
          管理员列表
        </h3>
        <div class="table-actions">
          <el-button size="small" icon="el-icon-download" @click="exportData">
            导出数据
          </el-button>
        </div>
      </div>

      <!-- 错误提示 -->
      <el-alert
        v-if="error"
        title="数据加载失败"
        type="error"
        :description="error"
        show-icon
        :closable="false"
        class="error-alert"
      >
        <el-button size="small" type="primary" @click="fetchData" class="retry-btn">重试</el-button>
      </el-alert>

      <el-table
        v-if="!error"
        :data="list"
        border
        stripe
        style="width: 100%"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
      >
        <el-table-column prop="id" label="ID" width="80" align="center" />
        <el-table-column prop="username" label="用户名" min-width="120" />
        <el-table-column prop="realname" label="姓名" min-width="120">
          <template slot-scope="scope">
            {{ scope.row.realname || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="email" label="邮箱" min-width="180">
          <template slot-scope="scope">
            {{ scope.row.email || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="角色" width="120" align="center">
          <template slot-scope="scope">
            <el-tag type="success" effect="dark" v-if="scope.row.is_superadmin">
              超级管理员
            </el-tag>
            <el-tag type="info" effect="plain" v-else>
              管理员
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100" align="center">
          <template slot-scope="scope">
            <el-tag type="success" v-if="scope.row.is_active">正常</el-tag>
            <el-tag type="danger" v-else>禁用</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="160" align="center">
          <template slot-scope="scope">
            {{ scope.row.created_at || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="280" fixed="right" align="center">
          <template slot-scope="scope">
            <el-button size="mini" type="primary" icon="el-icon-edit" @click="handleUpdate(scope.row)">
              编辑
            </el-button>
            <el-button size="mini" type="warning" icon="el-icon-key" @click="handleResetPassword(scope.row)">
              重置密码
            </el-button>
            <el-button
              size="mini"
              type="danger"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              :disabled="scope.row.is_superadmin"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
        <template slot="empty">
          <div class="empty-data">
            <i class="el-icon-document"></i>
            <p>暂无数据</p>
          </div>
        </template>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :current-page.sync="listQuery.page"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="listQuery.size"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 重置密码对话框 -->
    <el-dialog
      title="重置密码"
      :visible.sync="passwordDialogVisible"
      width="400px"
      :close-on-click-modal="false"
    >
      <el-form :model="passwordForm" ref="passwordForm" :rules="passwordRules" label-width="100px">
        <el-form-item label="新密码" prop="new_password">
          <el-input
            v-model="passwordForm.new_password"
            type="password"
            show-password
            placeholder="请输入新密码"
          />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirm_password">
          <el-input
            v-model="passwordForm.confirm_password"
            type="password"
            show-password
            placeholder="请再次输入密码"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="passwordDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmResetPassword" :loading="passwordLoading">
          确 定
        </el-button>
      </div>
    </el-dialog>

    <!-- 删除确认对话框 -->
    <el-dialog
      title="删除确认"
      :visible.sync="deleteDialogVisible"
      width="400px"
      :close-on-click-modal="false"
    >
      <div class="delete-confirm-content">
        <i class="el-icon-warning warning-icon"></i>
        <div class="confirm-text">
          <p>确定要删除管理员 "{{ currentAdmin ? currentAdmin.username : '' }}" 吗？</p>
          <p class="warning-text">此操作不可逆，请谨慎操作！</p>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="deleteDialogVisible = false">取 消</el-button>
        <el-button type="danger" @click="confirmDelete" :loading="deleteLoading">
          确认删除
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { adminAccount } from '@/api/admin'

export default {
  name: 'AdminAccountsList',
  data() {
    // 密码验证规则
    const validatePass = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入密码'));
      } else if (value.length < 6) {
        callback(new Error('密码长度不能少于6个字符'));
      } else {
        if (this.passwordForm.confirm_password !== '') {
          this.$refs.passwordForm.validateField('confirm_password');
        }
        callback();
      }
    };
    const validatePass2 = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'));
      } else if (value !== this.passwordForm.new_password) {
        callback(new Error('两次输入密码不一致'));
      } else {
        callback();
      }
    };
    
    return {
      loading: false,
      error: null,
      listQuery: {
        page: 1,
        size: 10,
        keyword: ''
      },
      list: [],
      total: 0,
      passwordDialogVisible: false,
      passwordLoading: false,
      passwordForm: {
        new_password: '',
        confirm_password: ''
      },
      passwordRules: {
        new_password: [{ validator: validatePass, trigger: 'blur' }],
        confirm_password: [{ validator: validatePass2, trigger: 'blur' }]
      },
      currentAdmin: null,
      deleteDialogVisible: false,
      deleteLoading: false
    }
  },
  computed: {
    totalPages() {
      return Math.ceil(this.total / this.listQuery.size) || 1;
    }
  },
  created() {
    this.fetchData();
  },
  methods: {
    fetchData() {
      this.loading = true;
      this.error = null;
      
      console.log('开始获取管理员列表数据...');
      adminAccount.getList(this.listQuery)
        .then(response => {
          this.loading = false;
          console.log('管理员列表API响应:', response);
          
          if (response.success) {
            this.list = response.data.list || [];
            this.total = response.data.total || 0;
            console.log(`管理员列表数据加载成功，共${this.total}条记录，当前显示${this.list.length}条`);
          } else {
            this.error = response.message || '获取管理员列表失败';
            console.error('管理员列表API业务错误:', this.error);
            
            // 如果API返回错误但有模拟数据，使用模拟数据
            if (response.data && response.data.list) {
              this.list = response.data.list;
              this.total = response.data.total || 0;
              this.error = null;
            }
          }
        })
        .catch(error => {
          this.loading = false;
          this.error = '网络错误，请稍后重试';
          console.error('获取管理员列表失败:', error);
          
          // 使用模拟数据作为备用
          this.useMockData();
        });
    },
    useMockData() {
      console.log('使用模拟数据');
      // 模拟数据，当API调用失败时使用
      const mockData = [
        { 
          id: 1, 
          username: 'admin', 
          realname: '系统管理员',
          email: '<EMAIL>',
          phone: '13800138000',
          is_superadmin: true,
          is_active: true,
          created_at: '2025-06-21 08:59:17',
          last_login_at: '2025-06-25 15:30:00'
        },
        { 
          id: 2, 
          username: 'ceshiadmin', 
          realname: '测试管理员',
          email: '<EMAIL>',
          phone: '13800138001',
          is_superadmin: false,
          is_active: true,
          created_at: '2025-06-25 11:26:30',
          last_login_at: null
        }
      ];
      
      this.list = mockData;
      this.total = mockData.length;
      this.error = null;
    },
    handleFilter() {
      this.listQuery.page = 1;
      this.fetchData();
    },
    resetSearch() {
      this.listQuery.keyword = '';
      this.handleFilter();
    },
    handleSizeChange(size) {
      this.listQuery.size = size;
      this.fetchData();
    },
    handleCurrentChange(page) {
      this.listQuery.page = page;
      this.fetchData();
    },
    handleCreate() {
      this.$router.push({ name: 'AdminAccountsCreate' });
    },
    handleUpdate(row) {
      this.$router.push({ name: 'AdminAccountsEdit', params: { id: row.id } });
    },
    handleResetPassword(row) {
      this.currentAdmin = row;
      this.passwordForm.new_password = '';
      this.passwordForm.confirm_password = '';
      this.passwordDialogVisible = true;
      
      // 重置表单验证
      if (this.$refs.passwordForm) {
        this.$refs.passwordForm.resetFields();
      }
    },
    confirmResetPassword() {
      this.$refs.passwordForm.validate(valid => {
        if (valid) {
          this.passwordLoading = true;
          adminAccount.resetPassword(this.currentAdmin.id, {
            new_password: this.passwordForm.new_password
          })
            .then(response => {
              this.passwordLoading = false;
              if (response.success) {
                this.passwordDialogVisible = false;
                this.$message({
                  type: 'success',
                  message: '密码重置成功'
                });
              } else {
                this.$message.error(response.message || '密码重置失败');
              }
            })
            .catch(error => {
              this.passwordLoading = false;
              this.$message.error('网络错误，请稍后重试');
              console.error('重置密码失败:', error);
            });
        }
      });
    },
    handleDelete(row) {
      if (row.is_superadmin) {
        this.$message.warning('不能删除超级管理员');
        return;
      }
      
      this.currentAdmin = row;
      this.deleteDialogVisible = true;
    },
    confirmDelete() {
      this.deleteLoading = true;
      adminAccount.delete(this.currentAdmin.id)
        .then(response => {
          this.deleteLoading = false;
          if (response.success) {
            this.deleteDialogVisible = false;
            this.$message({
              type: 'success',
              message: '删除成功'
            });
            this.fetchData();
          } else {
            this.$message.error(response.message || '删除失败');
          }
        })
        .catch(error => {
          this.deleteLoading = false;
          this.$message.error('网络错误，请稍后重试');
          console.error('删除管理员失败:', error);
        });
    },
    exportData() {
      this.$message({
        type: 'info',
        message: '导出功能开发中'
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.admin-accounts-container {
  padding: 20px;
  background-color: #f0f2f5;
  min-height: calc(100vh - 60px);
}

// 页面头部样式
.page-header {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  padding: 20px;
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-section {
  .page-title {
    font-size: 20px;
    color: #303133;
    margin: 0 0 8px 0;
    display: flex;
    align-items: center;
    
    i {
      margin-right: 10px;
      font-size: 22px;
      color: #409EFF;
    }
  }
  
  .page-subtitle {
    font-size: 14px;
    color: #909399;
    margin: 0;
  }
}

// 搜索卡片样式
.search-card {
  margin-bottom: 20px;
}

.search-header {
  margin-bottom: 15px;
  
  .search-title {
    font-size: 16px;
    color: #303133;
    margin: 0;
    display: flex;
    align-items: center;
    
    i {
      margin-right: 8px;
      color: #409EFF;
    }
  }
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  
  .search-input {
    width: 220px;
  }
  
  .search-buttons {
    margin-left: 10px;
    margin-bottom: 18px;
  }
}

// 表格卡片样式
.table-card {
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  
  .table-title {
    font-size: 16px;
    color: #303133;
    margin: 0;
    display: flex;
    align-items: center;
    
    i {
      margin-right: 8px;
      color: #409EFF;
    }
  }
}

// 错误提示样式
.error-alert {
  margin-bottom: 15px;
  
  .retry-btn {
    margin-left: 15px;
  }
}

// 空数据样式
.empty-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  color: #909399;
  
  i {
    font-size: 48px;
    margin-bottom: 15px;
  }
  
  p {
    font-size: 14px;
    margin: 0;
  }
}

// 分页容器样式
.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

// 删除确认对话框样式
.delete-confirm-content {
  display: flex;
  align-items: flex-start;
  padding: 10px 0;
  
  .warning-icon {
    font-size: 24px;
    color: #E6A23C;
    margin-right: 15px;
  }
  
  .confirm-text {
    flex: 1;
    
    p {
      margin: 0 0 10px 0;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
    
    .warning-text {
      color: #F56C6C;
      font-size: 13px;
    }
  }
}

// 对话框底部样式
.dialog-footer {
  text-align: right;
}
</style> 