import logging
import dashscope
import random
import time
from dashscope import Generation
from http import HTTPStatus
from .. import AiAdapter, AiQuery, AiQueryResult


LOGGER = logging.getLogger(__name__)


class Mock(AiAdapter):
    def query(self, query: AiQuery) -> AiQueryResult:
        try:
            LOGGER.info(query.userMessage)
            LOGGER.info("Mock AiAdapter Sleep 10")
            time.sleep(10)
            mockResult = """在过去的一段时间里，作为产品经理，我主要专注于产品设计和用户反馈的跟进工作，力求打造更符合 用户需求的产品体验。\n
            \n在产品设计方面，我主导了新功能的设计和优化流程。我们对现有产品的各项功能进行了深度分析，明确了改进点，旨在提升产品的易用性和功能性。
            我与设计团队紧密合作，提出并实施了一系列创新设计方案，包括界面优化、操作流程简化以及用户体验提升等。同时，我也积极参与到原型制作和测
            试阶段，确保每个细节都符合我们的设计初衷和用户期待。\n\n用户反馈跟进是我工作的另一重要部分。我定期收集和整理来自各个渠道的用户反馈，无
            论是应用商店的评价，还是用户的邮件、电话咨询，我都逐一查阅并记录。通过这些反馈，我深入理解了用户的需求、痛点以及对我们产品的建议。对于
            集中反映的问题，我及时协调团队进行修复和改进；对于有价值的建议，我将其融入到产品迭代计划中，以提升产品的满意度和用户粘性。\n\n在这个过
            程中，我深刻体会到，用户是产品的最好导师。他们的声音不仅推动我们改进产品，也启发我们不断创新。未来，我将继续以用户为中心，致力于打造更
            优质、更人性化的产品，以满足用户的期待并超越他们的想象。"""
            return AiQueryResult(text=mockResult, totalToken=9527)
        except Exception as e:
            LOGGER.exception(e)
            return AiQueryResult(isValid=False, errMessage=str(e))
    
    def generate(self, prompt: str) -> str:
        """生成方法，用于表格生成等场景
        
        Args:
            prompt: 提示词
            
        Returns:
            str: 生成的文本
        """
        try:
            LOGGER.info(f"Mock generate called with prompt: {prompt[:100]}...")
            time.sleep(2)  # 模拟AI处理时间
            
            # 根据提示词内容生成相应的表格数据
            if "表格" in prompt or "列" in prompt:
                # 生成表格数据
                return self._generate_mock_table_data(prompt)
            else:
                # 生成普通文本
                return self._generate_mock_text(prompt)
                
        except Exception as e:
            LOGGER.exception(f"Mock generate error: {e}")
            return "生成失败，请重试"
    
    def _generate_mock_table_data(self, prompt: str) -> str:
        """生成模拟表格数据"""
        # 从提示词中提取行数和列数
        rows = 3
        columns = 3
        
        if "行" in prompt:
            try:
                import re
                row_match = re.search(r'(\d+)行', prompt)
                if row_match:
                    rows = int(row_match.group(1))
            except:
                pass
        
        if "列" in prompt:
            try:
                import re
                col_match = re.search(r'(\d+)列', prompt)
                if col_match:
                    columns = int(col_match.group(1))
            except:
                pass
        
        # 分析段落内容，提取关键信息
        content_analysis = self._analyze_content(prompt)
        
        # 根据内容分析生成更相关的表格
        if content_analysis['has_data']:
            # 有数据的情况
            headers = content_analysis['headers']
            data_rows = content_analysis['data_rows']
        else:
            # 没有明确数据的情况，生成基于主题的表格
            headers = content_analysis['headers']
            data_rows = content_analysis['data_rows']
        
        # 确保表格大小符合要求
        while len(headers) < columns:
            headers.append(f"列{len(headers)+1}")
        if len(headers) > columns:
            headers = headers[:columns]
        
        # 生成数据行
        table_data = [",".join(headers)]
        
        for i in range(rows - 1):  # 减去表头行
            if i < len(data_rows):
                row = data_rows[i]
                # 确保行数据长度匹配列数
                while len(row) < columns:
                    row.append(f"数据{i+1}-{len(row)+1}")
                if len(row) > columns:
                    row = row[:columns]
                table_data.append(",".join(row))
            else:
                # 生成默认行
                row = [f"项目{i+1}"]
                for j in range(1, columns):
                    row.append(f"数值{i+1}-{j}")
                table_data.append(",".join(row))
        
        return "\n".join(table_data)
    
    def _analyze_content(self, prompt: str) -> dict:
        """分析段落内容，提取关键信息"""
        # 提取段落内容
        content = ""
        
        # 尝试多种方式提取内容
        content_markers = ["内容：", "内容:", "内容：\n", "内容:\n"]
        for marker in content_markers:
            if marker in prompt:
                content_start = prompt.find(marker) + len(marker)
                # 找到内容开始位置后，提取到下一个换行符或特殊标记
                remaining_text = prompt[content_start:]
                # 查找额外要求的开始位置
                extra_start = remaining_text.find("额外要求：")
                if extra_start != -1:
                    content = remaining_text[:extra_start].strip()
                else:
                    content = remaining_text.strip()
                break
        
        # 如果还是没找到，尝试提取引号内的内容
        if not content:
            import re
            quotes = re.findall(r'[""](.*?)[""]', prompt)
            if quotes:
                content = quotes[0]
        
        # 如果还是没找到，尝试提取"内容："后面的所有文本直到"额外要求"
        if not content:
            if "内容：" in prompt:
                content_part = prompt.split("内容：")[1]
                if "额外要求" in content_part:
                    content = content_part.split("额外要求")[0].strip()
                else:
                    content = content_part.strip()
        
        print(f"提取到的内容: {content[:100]}...")  # 调试信息
        
        analysis = {
            'has_data': False,
            'headers': ['项目', '数值', '说明'],
            'data_rows': []
        }
        
        if not content:
            return analysis
        
        # 分析内容类型
        content_lower = content.lower()
        
        # 检查是否包含数据相关词汇
        data_keywords = ['数据', '分析', '结果', '统计', '实验', '研究', '测试', '对比', '比较']
        has_data_keywords = any(keyword in content_lower for keyword in data_keywords)
        
        # 检查是否包含数字
        import re
        numbers = re.findall(r'\d+', content)
        percentages = re.findall(r'\d+%', content)
        
        print(f"检测到数字: {numbers}, 百分比: {percentages}")  # 调试信息
        
        if has_data_keywords or numbers or percentages:
            analysis['has_data'] = True
            
            # 根据内容类型生成相应的表格，优先检查材料相关内容
            if '材料' in content or '复合' in content or '碳纤维' in content or '环氧树脂' in content:
                analysis['headers'] = ['材料类型', '强度', '密度', '应用领域']
                analysis['data_rows'] = [
                    ['碳纤维增强环氧树脂', '高强度', '低密度', '航空航天'],
                    ['玻璃纤维复合材料', '中等强度', '中等密度', '汽车制造'],
                    ['芳纶纤维复合材料', '高韧性', '低密度', '防护装备']
                ]
            elif '算法' in content or '性能' in content:
                analysis['headers'] = ['算法名称', '准确率', '处理速度', '优缺点']
                analysis['data_rows'] = [
                    ['算法A', '85%', '1000次/秒', '准确率高，速度中等'],
                    ['算法B', '78%', '1200次/秒', '速度较快，准确率一般'],
                    ['算法C', '92%', '600次/秒', '准确率最高，速度较慢']
                ]
            elif '实验' in content or '测试' in content:
                analysis['headers'] = ['实验项目', '测试结果', '标准值', '结论']
                analysis['data_rows'] = [
                    ['拉伸强度测试', '450MPa', '400MPa', '符合标准'],
                    ['弯曲强度测试', '380MPa', '350MPa', '超出标准'],
                    ['冲击韧性测试', '25kJ/m²', '20kJ/m²', '性能优异']
                ]
            elif '调查' in content or '问卷' in content:
                analysis['headers'] = ['调查项目', '选择人数', '百分比', '主要反馈']
                analysis['data_rows'] = [
                    ['满意度调查', '45人', '45%', '非常满意'],
                    ['功能评价', '30人', '30%', '基本满意'],
                    ['改进建议', '25人', '25%', '需要优化']
                ]
            else:
                # 通用数据分析表格
                analysis['headers'] = ['分析项目', '数值', '单位', '说明']
                analysis['data_rows'] = [
                    ['主要指标', numbers[0] if numbers else '85', '%' if percentages else '单位', '核心性能指标'],
                    ['次要指标', numbers[1] if len(numbers) > 1 else '1200', '%' if percentages else '单位', '辅助性能指标'],
                    ['综合评估', numbers[2] if len(numbers) > 2 else '92', '%' if percentages else '单位', '整体评价结果']
                ]
        else:
            # 没有明确数据的情况，生成基于主题的表格
            if '研究' in content or '分析' in content:
                analysis['headers'] = ['研究内容', '方法', '结果', '意义']
                analysis['data_rows'] = [
                    ['数据收集', '统计学方法', '完整数据集', '为分析提供基础'],
                    ['数据处理', '高级算法', '清洗后数据', '确保分析准确性'],
                    ['结果分析', '综合评估', '重要发现', '提供参考依据']
                ]
            elif '技术' in content or '工艺' in content:
                analysis['headers'] = ['技术环节', '工艺参数', '控制要点', '优化方向']
                analysis['data_rows'] = [
                    ['材料制备', '温度控制', '均匀性', '提高效率'],
                    ['成型工艺', '压力参数', '密实度', '降低成本'],
                    ['质量检测', '检测标准', '一致性', '保证品质']
                ]
            else:
                # 通用内容分析表格
                analysis['headers'] = ['内容要点', '重要性', '详细说明', '应用价值']
                analysis['data_rows'] = [
                    ['核心概念', '高', '主要内容概述', '理论基础'],
                    ['关键方法', '中', '具体实施步骤', '实践指导'],
                    ['预期效果', '高', '预期达到目标', '价值体现']
                ]
        
        return analysis
    
    def _generate_mock_text(self, prompt: str) -> str:
        """生成模拟文本"""
        return f"基于您的提示'{prompt[:50]}...'，我生成了相应的内容。这是一个模拟的AI生成结果。"