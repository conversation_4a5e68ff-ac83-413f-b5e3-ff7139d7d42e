import logging
import json
import os
from typing import List
import time
from threading import Event, Thread
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from sqlalchemy import or_
import threading
from datetime import datetime
import traceback
from sqlalchemy.exc import OperationalError, PendingRollbackError

from EarlyBird.common.libs.BaseModel import db
from EarlyBird.model.generate_task import GenerateTask
from .handler_generate_thesis import HandlerGenerateThesis
from EarlyBird.common import task_queue

from EarlyBird.common import TaskStatus, isDevEnv
from EarlyBird.ExtendRegister.model_register import Thesis

# 数据库连接重试配置
DB_MAX_RETRIES = 3
DB_RETRY_DELAY = 5  # 秒

logger = logging.getLogger(__name__)


class TaskPaperGenerator(threading.Thread):
    """论文生成任务处理线程"""
    
    def __init__(self, stop_event, app):
        super().__init__()
        self.daemon = True
        self.stop_event = stop_event
        self.app = app
        self.logger = logging.getLogger(__name__)
        self.logger.info("论文生成任务处理线程初始化")
        
    def _safe_db_operation(self, operation, *args, **kwargs):
        """安全执行数据库操作，带有重试机制"""
        retries = 0
        last_error = None
        
        while retries < DB_MAX_RETRIES:
            try:
                result = operation(*args, **kwargs)
                return result, None
            except (OperationalError, PendingRollbackError) as e:
                last_error = e
                self.logger.warning(f"数据库操作失败 (尝试 {retries+1}/{DB_MAX_RETRIES}): {str(e)}")
                
                # 尝试回滚事务
                try:
                    db.session.rollback()
                    self.logger.info("数据库会话已回滚")
                except Exception as rollback_error:
                    self.logger.error(f"回滚事务失败: {str(rollback_error)}")
                
                # 等待后重试
                retries += 1
                if retries < DB_MAX_RETRIES:
                    time.sleep(DB_RETRY_DELAY)
            except Exception as e:
                # 其他非数据库连接相关的错误，不重试
                return None, e
        
        return None, last_error
        
    def run(self):
        """线程运行函数"""
        self.logger.info("论文生成任务处理线程启动")
        
        # 创建应用上下文
        with self.app.app_context():
            while not self.stop_event.is_set():
                try:
                    # 获取待处理任务，使用正确的字段名
                    task, error = self._safe_db_operation(
                        lambda: GenerateTask.query.filter_by(status=1).order_by(GenerateTask.id.asc()).first()
                    )
                    
                    if error:
                        self.logger.error(f"获取待处理任务失败: {str(error)}")
                        time.sleep(10)  # 出错后等待较长时间
                        continue
                    
                    if task:
                        self.logger.info(f"开始处理论文生成任务: {task.id}")
                        try:
                            # 更新任务状态为处理中
                            task.status = 2  # RUNNING
                            _, commit_error = self._safe_db_operation(lambda: db.session.commit())
                            
                            if commit_error:
                                self.logger.error(f"更新任务状态失败: {str(commit_error)}")
                                continue
                            
                            # 处理任务
                            self._process_task(task)
                            
                        except Exception as e:
                            self.logger.error(f"处理论文生成任务失败: {str(e)}")
                            traceback.print_exc()
                            
                            # 更新任务状态为失败
                            task.status = 4  # ERROR
                            task.msg = str(e)
                            _, update_error = self._safe_db_operation(lambda: db.session.commit())
                            
                            if update_error:
                                self.logger.error(f"更新任务状态为失败时出错: {str(update_error)}")
                    
                    # 休眠一段时间
                    time.sleep(5)
                    
                except Exception as e:
                    self.logger.error(f"论文生成任务处理线程异常: {str(e)}")
                    traceback.print_exc()
                    
                    # 尝试回滚任何未完成的事务
                    try:
                        db.session.rollback()
                    except:
                        pass
                        
                    time.sleep(10)  # 发生异常时休眠时间更长
        
        self.logger.info("论文生成任务处理线程退出")

    def _process_task(self, task):
        """处理生成任务"""
        try:
            self.logger.info(f"开始处理任务 ID: {task.id}, 论文 ID: {task.thesisId}")
            
            # 获取论文信息
            thesis, error = self._safe_db_operation(
                lambda: Thesis.query.filter_by(id=task.thesisId).first()
            )
            
            if error or not thesis:
                error_msg = f"找不到论文信息 ID: {task.thesisId}" if not thesis else str(error)
                self.logger.error(error_msg)
                task.status = 4  # ERROR
                task.msg = error_msg
                _, commit_error = self._safe_db_operation(lambda: db.session.commit())
                if commit_error:
                    self.logger.error(f"更新任务状态失败: {str(commit_error)}")
                return
            
            # 创建处理器并执行
            handler = HandlerGenerateThesis(self.app, task.thesisId, self.stop_event)
            result = handler.dispatch()
            
            # 更新任务状态
            if result.get('success', False):
                task.status = 3  # SUCCESS
                task.msg = "论文生成成功"
            else:
                task.status = 4  # ERROR
                task.msg = result.get('message', '论文生成失败')
            
            # 保存任务状态
            _, commit_error = self._safe_db_operation(lambda: db.session.commit())
            if commit_error:
                self.logger.error(f"更新任务完成状态失败: {str(commit_error)}")
            
            self.logger.info(f"任务处理完成 ID: {task.id}, 状态: {task.status}")
            
        except Exception as e:
            self.logger.error(f"处理任务异常 ID: {task.id}: {str(e)}")
            traceback.print_exc()
            
            # 更新任务状态为失败
            try:
                task.status = 4  # ERROR
                task.msg = f"处理异常: {str(e)}"
                db.session.commit()
            except Exception as commit_error:
                self.logger.error(f"更新任务失败状态出错: {str(commit_error)}")
                try:
                    db.session.rollback()
                except:
                    pass

    def loadOldTask(self):

        class LoadTask(Thread):
            def __init__(self, flaskApp):
                super().__init__()
                self.flaskApp = flaskApp

            def run(self):
                # 等主线程监听任务队列
                time.sleep(4)
                with self.flaskApp.app_context():
                    thesisList: List[Thesis] = Thesis.query.filter(
                        or_(
                            Thesis.status == TaskStatus.INIT,
                            Thesis.status == TaskStatus.RUNING,
                        )
                    ).all()
                    if len(thesisList) == 0:
                        logger.info("db中未找到堆积的任务需要处理")
                        return
                    logger.info(f"db中找到{len(thesisList)} 个论文正在生成，恢复任务")
                    for t in thesisList:
                        task_queue.pubGenerateThesisTask(t.id)

        LoadTask(self.app).start()
