<!--
作者名：EarlyBird
作者邮箱：<EMAIL>
官方网址：https://blog.zaoniao.vip

支付配置页面 - 简化版本
-->
<template>
  <div class="payment-config">
    <div class="page-header">
      <h2>支付配置管理</h2>
      <div class="header-actions">
        <el-button type="primary" @click="loadConfig" icon="el-icon-refresh">刷新配置</el-button>
        <el-button type="success" @click="testPayment" :loading="testing" icon="el-icon-connection">
          测试连接
        </el-button>
      </div>
    </div>

    <div v-loading="loading" class="config-container">
      <!-- 微信支付配置 -->
      <el-card class="config-card">
        <div slot="header">
          <span>微信支付配置</span>
        </div>

        <el-form :model="configForm" ref="configForm" label-width="140px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="应用ID" prop="app_id">
                <el-input v-model="configForm.app_id" placeholder="请输入微信AppID"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="商户号" prop="mch_id">
                <el-input v-model="configForm.mch_id" placeholder="请输入微信商户号"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="API V3密钥" prop="api_v3_key">
                <el-input 
                  v-model="configForm.api_v3_key" 
                  type="password" 
                  placeholder="请输入API V3密钥"
                  show-password
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="证书序列号" prop="serial_no">
                <el-input v-model="configForm.serial_no" placeholder="请输入证书序列号"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="私钥文件路径" prop="private_key_path">
                <el-input v-model="configForm.private_key_path" placeholder="请输入私钥文件路径"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="回调通知地址" prop="notify_url">
                <el-input v-model="configForm.notify_url" placeholder="请输入回调通知地址"></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="备注">
                <el-input 
                  v-model="configForm.remark" 
                  type="textarea" 
                  placeholder="请输入配置备注"
                  :rows="3"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item>
            <el-button type="primary" @click="saveConfig" :loading="saving">保存配置</el-button>
            <el-button @click="resetForm">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 测试结果弹框 -->
    <el-dialog 
      title="支付测试结果" 
      :visible.sync="testDialogVisible" 
      width="600px"
      :close-on-click-modal="false"
    >
      <div v-if="testResult.success">
        <el-alert type="success" :title="testResult.message" :closable="false" show-icon></el-alert>
        
        <div v-if="testResult.data && testResult.data.qr_code_url" class="qr-code-container">
          <h4>测试支付二维码 (0.01元)</h4>
          <div class="qr-code-wrapper">
            <img :src="testResult.data.qr_code_url" alt="支付二维码" class="qr-code">
          </div>
          <p class="order-info">订单号: {{ testResult.data.out_trade_no }}</p>
          
          <div v-if="testResult.data.is_mock" class="mock-warning">
            <el-alert 
              type="warning" 
              title="模拟测试模式" 
              description="当前为模拟测试，无法进行真实支付。请配置正确的证书文件后重试。"
              :closable="false"
              show-icon
            ></el-alert>
            <div v-if="testResult.data.message" style="margin-top: 10px;">
              <p><strong>错误信息:</strong> {{ testResult.data.message }}</p>
            </div>
            <div v-if="testResult.data.error_details" style="margin-top: 5px;">
              <p><strong>详细错误:</strong> {{ testResult.data.error_details }}</p>
            </div>
          </div>
          
          <div v-else class="real-payment-info">
            <el-alert 
              type="success" 
              title="真实支付模式" 
              description="这是真实的微信支付二维码，扫码后会产生0.01元的实际支付。"
              :closable="false"
              show-icon
            ></el-alert>
          </div>
        </div>
      </div>
      
      <div v-else>
        <el-alert type="error" :title="testResult.message" :closable="false" show-icon></el-alert>
        <p v-if="testResult.details" class="error-details">{{ testResult.details }}</p>
      </div>

      <div slot="footer">
        <el-button @click="testDialogVisible = false">关闭</el-button>
        <el-button v-if="testResult.success && testResult.data" type="primary" @click="checkPaymentStatus">
          检查支付状态
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getPaymentConfig, updatePaymentConfig } from '@/api/payment'
import request from '@/utils/request'

export default {
  name: 'PaymentConfigSimple',
  data() {
    return {
      loading: false,
      saving: false,
      testing: false,
      testDialogVisible: false,
      
      // 微信支付配置表单
      configForm: {
        app_id: '',
        mch_id: '',
        api_v3_key: '',
        serial_no: '',
        private_key_path: '',
        notify_url: '',
        is_enabled: true,
        is_test_mode: false,
        remark: ''
      },
      
      // 测试结果
      testResult: {
        success: false,
        message: '',
        data: null,
        details: ''
      }
    }
  },
  
  created() {
    this.loadConfig()
  },
  
  methods: {
    /**
     * 加载配置数据
     */
    async loadConfig() {
      try {
        this.loading = true
        const response = await getPaymentConfig()
        
        if (response.success && response.data) {
          const data = response.data
          
          // 直接加载配置数据（API返回的是配置对象，不是嵌套结构）
          this.configForm = {
            ...this.configForm,
            app_id: data.appid || data.app_id || '',
            mch_id: data.mchid || data.mch_id || '',
            api_v3_key: data.api_v3_key || '',
            serial_no: data.serial_no || '',
            private_key_path: data.private_key_path || '',
            notify_url: data.notify_url || '',
            is_enabled: data.is_enabled !== undefined ? data.is_enabled : true,
            is_test_mode: data.is_test_mode !== undefined ? data.is_test_mode : false,
            remark: data.remark || ''
          }
          
          this.$message.success('配置加载成功')
        } else {
          this.$message.error(response.message || '加载配置失败')
        }
      } catch (error) {
        console.error('加载配置失败:', error)
        this.$message.error('加载配置失败: ' + (error.message || '未知错误'))
      } finally {
        this.loading = false
      }
    },
    
    /**
     * 保存配置
     */
    async saveConfig() {
      try {
        this.saving = true
        
        const response = await updatePaymentConfig(this.configForm)
        
        if (response.success) {
          this.$message.success('配置保存成功')
          this.loadConfig() // 重新加载配置
        } else {
          this.$message.error(response.message || '保存配置失败')
        }
      } catch (error) {
        console.error('保存配置失败:', error)
        this.$message.error('保存配置失败: ' + (error.message || '未知错误'))
      } finally {
        this.saving = false
      }
    },
    
    /**
     * 重置表单
     */
    resetForm() {
      this.$refs.configForm.resetFields()
    },
    
    /**
     * 测试支付连接
     */
    async testPayment() {
      try {
        this.testing = true
        
        const response = await request({
          url: '/api/pay/test',
          method: 'post',
          data: {
            pay_type: 'wxpay',
            amount: 0.01
          }
        })
        
        this.testResult = {
          success: response.success,
          message: response.message || (response.success ? '支付测试成功' : '支付测试失败'),
          data: response.data,
          details: response.details || ''
        }
        
        this.testDialogVisible = true
        
      } catch (error) {
        console.error('测试支付失败:', error)
        this.testResult = {
          success: false,
          message: '测试支付失败',
          data: null,
          details: error.message || '网络错误'
        }
        this.testDialogVisible = true
      } finally {
        this.testing = false
      }
    },
    
    /**
     * 检查支付状态
     */
    async checkPaymentStatus() {
      if (!this.testResult.data || !this.testResult.data.out_trade_no) {
        this.$message.warning('没有可查询的订单')
        return
      }
      
      try {
        const response = await request({
          url: '/api/pay/query',
          method: 'post',
          data: {
            out_trade_no: this.testResult.data.out_trade_no
          }
        })
        
        if (response.success) {
          this.$message.success('订单状态: ' + (response.data.status || '未知'))
        } else {
          this.$message.error('查询失败: ' + response.message)
        }
      } catch (error) {
        console.error('查询支付状态失败:', error)
        this.$message.error('查询失败: ' + error.message)
      }
    }
  }
}
</script>

<style scoped>
.payment-config {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.config-card {
  margin-bottom: 20px;
}

.qr-code-container {
  text-align: center;
  margin-top: 20px;
}

.qr-code-wrapper {
  margin: 20px 0;
}

.qr-code {
  max-width: 200px;
  height: auto;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.order-info {
  color: #606266;
  font-size: 14px;
  margin: 10px 0;
}

.mock-warning {
  margin-top: 20px;
}

.error-details {
  color: #606266;
  font-size: 14px;
  margin-top: 10px;
}
</style> 