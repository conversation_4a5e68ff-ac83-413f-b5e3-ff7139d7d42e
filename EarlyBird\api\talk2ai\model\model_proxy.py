import logging
import json


from EarlyBird.api.talk2ai.dantic import (
    ParamOutline,
    ParamTitle,
    ParamGetContentFromOutline,
    ParamGenerateSingleParagraph,
    ParamRegenDigest,
)
from EarlyBird.api.talk2ai.model.base import BaseAiModel, purgeJsonPrefix, InvokeResult
from EarlyBird.api.talk2ai.model import prompt_factory


from EarlyBird.common import ai
from EarlyBird.api.home.service import HomeService


LOGGER = logging.getLogger(__name__)


class ModelProxy(BaseAiModel):

    def __init__(self, modelName: str) -> None:
        super().__init__()
        self.modelName = modelName
        LOGGER.info(f"ModelProxy 实例化.模型:{modelName}")

    def _sendRequest(self, userMessage: str) -> InvokeResult:
        try:
            # Get current model from settings
            current_model = HomeService().getModelName().lower()
            LOGGER.info(f"Using model: {current_model}")
            
            aiModel = ai.getAdapter(current_model)
            queryResult: ai.AiQueryResult = aiModel.query(
                ai.AiQuery(userMessage=userMessage)
            )

            if not queryResult.isValid:
                return InvokeResult(isSuccess=False, message=queryResult.errMessage)

            return InvokeResult(
                isSuccess=True, data=queryResult.text, tokenSize=queryResult.totalToken
            )
        except Exception as e:
            LOGGER.exception(e)
            return InvokeResult(isSuccess=False, message=str(e))

    def getTitle(self, param: ParamTitle) -> InvokeResult:

        result: InvokeResult = self._sendRequest(prompt_factory.getTitle(param))
        if result.isSuccess:
            result.data = purgeJsonPrefix(result.data)
        return result

    def getOutline(self, param: ParamOutline) -> InvokeResult:
        """
        生成论文提纲，调用三次AI模型，分别生成三种风格的提纲
        
        Args:
            param: 提纲生成参数
            
        Returns:
            InvokeResult: 包含三种风格提纲的结果
        """
        # 这里需要考虑调用三次GPT，把三次返回的提纲按照格式，一次性的放到返回文件中发送到前端。
        finalResult = []
        finalTokenSize = 0
        thesis_styles = [
            {"author": "行业专家", "style": "加入更多的对该论题的深度理解和前沿内容"},
            {
                "author": "大学学生",
                "style": "从基础理论入手，多分析现有数据，展示自己的学习成果",
            },
            {
                "author": "研究学者",
                "style": "注重理论框架的严谨性，强调方法论和创新点",
            },
        ]

        # 验证和修复提纲数据结构的函数
        def validate_and_fix_outline(outline_data):
            """验证并修复提纲数据结构"""
            try:
                if not isinstance(outline_data, dict):
                    LOGGER.warning(f"提纲数据不是字典类型: {type(outline_data)}")
                    if isinstance(outline_data, str):
                        try:
                            outline_data = json.loads(outline_data)
                            LOGGER.info("成功将字符串转换为字典")
                        except:
                            LOGGER.error("无法将字符串转换为字典")
                            return {"title": param.title, "subtitle": []}
                    else:
                        return {"title": param.title, "subtitle": []}
                
                # 确保提纲有title字段
                if "title" not in outline_data or not outline_data["title"]:
                    LOGGER.warning("提纲缺少title字段，使用论文标题")
                    outline_data["title"] = param.title
                
                # 确保提纲有subtitle字段且为数组
                if "subtitle" not in outline_data:
                    LOGGER.warning("提纲缺少subtitle字段，尝试从其他字段获取")
                    # 尝试从其他常见字段获取subtitle内容
                    for key in ["children", "subheadings", "sections", "chapters", "content"]:
                        if key in outline_data and isinstance(outline_data[key], list):
                            LOGGER.info(f"使用'{key}'字段作为subtitle")
                            outline_data["subtitle"] = outline_data[key]
                            break
                    
                    # 如果仍然没有找到，创建一个空的subtitle字段
                    if "subtitle" not in outline_data:
                        LOGGER.warning("无法找到subtitle内容，创建空subtitle字段")
                        outline_data["subtitle"] = []
                
                # 确保subtitle是列表
                if not isinstance(outline_data["subtitle"], list):
                    LOGGER.warning(f"subtitle不是列表类型: {type(outline_data['subtitle'])}")
                    if isinstance(outline_data["subtitle"], dict):
                        outline_data["subtitle"] = [outline_data["subtitle"]]
                    else:
                        outline_data["subtitle"] = []
                
                # 处理subtitle中的每个节点
                def process_node(node):
                    if not isinstance(node, dict):
                        LOGGER.warning(f"节点不是字典类型: {type(node)}")
                        return {"title": "格式错误的节点", "subtitle": []}
                    
                    # 确保节点有title
                    if "title" not in node or not node["title"]:
                        # 尝试从其他字段获取标题
                        for field in ["name", "heading", "text"]:
                            if field in node and node[field]:
                                node["title"] = node[field]
                                break
                        
                        # 如果仍然没有找到标题，使用默认值
                        if "title" not in node or not node["title"]:
                            node["title"] = "无标题章节"
                    
                    # 确保节点有subtitle字段
                    if "subtitle" not in node:
                        node["subtitle"] = []
                    elif not isinstance(node["subtitle"], list):
                        if isinstance(node["subtitle"], dict):
                            node["subtitle"] = [node["subtitle"]]
                        else:
                            node["subtitle"] = []
                    
                    # 递归处理子节点
                    node["subtitle"] = [process_node(subnode) for subnode in node["subtitle"]]
                    
                    return node
                
                # 处理所有节点
                outline_data["subtitle"] = [process_node(node) for node in outline_data["subtitle"]]
                
                return outline_data
            except Exception as e:
                LOGGER.exception(f"验证和修复提纲数据结构时出错: {e}")
                return {"title": param.title, "subtitle": []}

        for style in thesis_styles:
            try:
                param.author = style["author"]
                param.style = style["style"]
                user_message = prompt_factory.getOutline(param)

                result: InvokeResult = self._sendRequest(user_message)
                if result.isSuccess:
                    resultData = purgeJsonPrefix(str(result.data))
                    
                    try:
                        # 解析JSON数据
                        outline_data = json.loads(resultData)
                        
                        # 验证和修复提纲数据结构
                        fixed_outline = validate_and_fix_outline(outline_data)
                        
                        # 添加到结果列表
                        finalResult.append(fixed_outline)
                        finalTokenSize += result.tokenSize
                        
                        LOGGER.info(f"成功生成并修复 {param.author} 风格的提纲")
                    except json.JSONDecodeError as e:
                        LOGGER.error(f"解析提纲JSON数据失败: {e}")
                        # 创建一个基本的提纲结构
                        finalResult.append({
                            "title": param.title,
                            "subtitle": [],
                            "error": f"解析提纲数据失败: {str(e)}"
                        })
                else:
                    LOGGER.error(f"生成 {param.author} 风格的提纲失败: {result.message}")
                    # 添加一个错误提示的提纲
                    finalResult.append({
                        "title": param.title,
                        "subtitle": [],
                        "error": f"生成提纲失败: {result.message}"
                    })
            except Exception as e:
                LOGGER.exception(f"生成 {param.author} 风格的提纲时发生错误: {e}")
                # 添加一个错误提示的提纲
                finalResult.append({
                    "title": param.title,
                    "subtitle": [],
                    "error": f"生成提纲出错: {str(e)}"
                })

        # 确保至少返回一个有效的提纲
        if not finalResult:
            LOGGER.warning("未能生成任何提纲，创建默认提纲")
            finalResult = [{
                "title": param.title,
                "subtitle": []
            }]

        return InvokeResult(data=finalResult, tokenSize=finalTokenSize)
    
    def generateParagraph(self, param: ParamGenerateSingleParagraph) -> InvokeResult:
        result: InvokeResult = self._sendRequest(
            prompt_factory.generateSingleParagraph(param)
        )
        if result.isSuccess:
            result.data = purgeJsonPrefix(result.data)
        return result

    def generateDigest(self, param: ParamRegenDigest) -> InvokeResult:

        result: InvokeResult = self._sendRequest(prompt_factory.generateDigest(param))
        if result.isSuccess:
            resBody = purgeJsonPrefix(result.data)
            resBody = resBody.replace("\n", "")
            resBody = resBody.replace("\r", "")
            try:
                resultJson = json.loads(resBody)
                keywords = resultJson["keywords"]
                if keywords is not None and type(keywords) == list:
                    resultJson["keywords"] = ",".join(keywords)
                result.data = resultJson
            except Exception as e:
                LOGGER.exception(e)
        return result

    def generateDigestEnglish(self, param: ParamRegenDigest) -> InvokeResult:

        result: InvokeResult = self._sendRequest(
            prompt_factory.generateDigestEnglish(param)
        )
        if result.isSuccess:
            resBody = purgeJsonPrefix(result.data)
            resBody = resBody.replace("\n", "")
            resBody = resBody.replace("\r", "")
            try:
                # userMessage 里要求输出 digest的json额结构，在这里解除一下结构
                resultJson = json.loads(resBody)
                # 如果关键词给出的是list，变成str，
                keywords = resultJson["keywords"]
                if keywords is not None and type(keywords) == list:
                    resultJson["keywords"] = ",".join(keywords)
                result.data = resultJson
            except Exception as e:
                LOGGER.exception(e)
        return result


