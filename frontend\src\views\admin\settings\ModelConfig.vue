<!-- 
作者名：EarlyBird
作者邮箱：<EMAIL>
官方网址：https://blog.zaoniao.vip
-->
<template>
  <div class="model-config">
    <h2>大模型密钥配置</h2>
    <el-form :model="form" label-width="140px" style="max-width: 600px;">
      <el-form-item label="当前模型">
        <el-select v-model="form.modelName" placeholder="请选择当前模型">
          <el-option label="千问 (qianwen)" value="qianwen"></el-option>
          <el-option label="Kimi (kimi)" value="kimi"></el-option>
          <el-option label="DeepSeek (deepseek)" value="deepseek"></el-option>
          <el-option label="豆包 (doubao)" value="doubao"></el-option>
          <el-option label="OpenAI (openai)" value="openai"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="千问API Key">
        <el-input v-model="form.apikeyQianwen" clearable placeholder="请输入千问API Key"></el-input>
      </el-form-item>
      <el-form-item label="DeepSeek API Key">
        <el-input v-model="form.apikeyDeepSeekR1" clearable placeholder="请输入DeepSeek API Key"></el-input>
      </el-form-item>
      <el-form-item label="Kimi API Key">
        <el-input v-model="form.apikeyKimi" clearable placeholder="请输入Kimi API Key"></el-input>
      </el-form-item>
      <el-form-item label="豆包API Key">
        <el-input v-model="form.apikeyDoubao" clearable placeholder="请输入豆包API Key"></el-input>
      </el-form-item>
      <el-form-item label="OpenAI API Key">
        <el-input v-model="form.apikeyOpenai" clearable placeholder="请输入OpenAI API Key"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSave" :loading="saving">保存大模型密钥</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { getSetting, saveSetting } from '@/api/setting'

export default {
  name: 'ModelConfig',
  data() {
    return {
      form: {
        modelName: '',
        apikeyQianwen: '',
        apikeyDeepSeekR1: '',
        apikeyKimi: '',
        apikeyDoubao: '',
        apikeyOpenai: ''
      },
      saving: false
    }
  },
  created() {
    this.loadConfig()
  },
  methods: {
    // 加载大模型密钥配置
    async loadConfig() {
      try {
        const res = await getSetting()
        if (res && (res.success || res.is_success) && res.data) {
          const data = res.data
          this.form.modelName = data.modelName || ''
          this.form.apikeyQianwen = data.apikeyQianwen || ''
          this.form.apikeyDeepSeekR1 = data.apikeyDeepSeekR1 || ''
          this.form.apikeyKimi = data.apikeyKimi || ''
          this.form.apikeyDoubao = data.apikeyDoubao || ''
          this.form.apikeyOpenai = data.apikeyOpenai || ''
        }
      } catch (e) {
        this.$message.error('加载大模型密钥配置失败')
      }
    },
    // 保存大模型密钥配置
    async onSave() {
      this.saving = true
      try {
        const params = {
          setting: {
            modelName: this.form.modelName,
            apikeyQianwen: this.form.apikeyQianwen,
            apikeyDeepSeekR1: this.form.apikeyDeepSeekR1,
            apikeyKimi: this.form.apikeyKimi,
            apikeyDoubao: this.form.apikeyDoubao,
            apikeyOpenai: this.form.apikeyOpenai
          }
        }
        const res = await saveSetting(params)
        if (res && (res.success || res.is_success)) {
          this.$message.success('大模型密钥保存成功')
          this.loadConfig()
        } else {
          this.$message.error(res.message || '大模型密钥保存失败')
        }
      } catch (e) {
        this.$message.error('大模型密钥保存异常')
      } finally {
        this.saving = false
      }
    }
  }
}
</script>

<style scoped>
.model-config {
  padding: 20px;
}
</style> 