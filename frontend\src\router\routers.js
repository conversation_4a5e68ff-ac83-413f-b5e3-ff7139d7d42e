import Vue from 'vue'
import Router from 'vue-router'
import MainLayout from '@/components/layout/MainLayout'
import AdminLayout from '@/components/layout/AdminLayout'
import UserLogin from '@/views/user/Login'
import UserRegister from '@/views/user/Register'
import UserProfile from '@/views/user/Profile'
import AdminLogin from '@/views/admin/Login'
import menuList from "@/utils/menu"

Vue.use(Router)
//定义了起始路由,起始路由的情况下转向getOutline,然后加载@/views/getOutline模块
export const constantRouterMap = [
  // 用户登录和注册页面（独立路由，不使用主布局）
  {
    path: '/user/login',
    name: 'UserLogin',
    component: UserLogin,
    meta: { title: '用户登录' }
  },
  {
    path: '/user/register',
    name: 'UserRegister',
    component: UserRegister,
    meta: { title: '用户注册' }
  },
  {
    path: '/user/profile',
    name: 'UserProfile',
    component: UserProfile,
    meta: { title: '个人资料' }
  },
  
  // 后台管理系统路由 - 放在主布局路由之前
  {
    path: '/admin/login',
    name: 'AdminLogin',
    component: AdminLogin,
    meta: { title: '管理员登录' }
  },
  {
    path: '/admin',
    component: AdminLayout,
    redirect: '/admin/dashboard',
    children: [
      {
        path: 'dashboard',
        name: 'AdminDashboard',
        component: () => import('@/views/admin/Dashboard'),
        meta: { title: '数据概览', requiresAuth: true }
      },
      {
        path: 'users',
        name: 'AdminUsers',
        component: () => import('@/views/admin/Users'),
        meta: { title: '用户管理', requiresAuth: true }
      },
      {
        path: 'users/accounts',
        name: 'AdminAccountsList',
        component: () => import('@/views/admin/accounts/List'),
        meta: { title: '账号管理', requiresAuth: true, superAdmin: true }
      },
      {
        path: 'users/accounts/create',
        name: 'AdminAccountsCreate',
        component: () => import('@/views/admin/accounts/Edit'),
        meta: { title: '创建账号', requiresAuth: true, superAdmin: true, hidden: true }
      },
      {
        path: 'users/accounts/edit/:id',
        name: 'AdminAccountsEdit',
        component: () => import('@/views/admin/accounts/Edit'),
        meta: { title: '编辑账号', requiresAuth: true, superAdmin: true, hidden: true }
      },
      {
        path: 'thesis',
        name: 'AdminThesis',
        component: () => import('@/views/admin/Thesis'),
        meta: { title: '论文管理', requiresAuth: true }
      },
      {
        path: 'thesis/list',
        name: 'AdminThesisList',
        component: () => import('@/views/admin/Thesis'),
        meta: { title: '论文列表', requiresAuth: true, parent: 'AdminThesis' }
      },
      {
        path: 'settings/orders',
        name: 'AdminPaymentOrders',
        component: () => import('@/views/admin/settings/PaymentOrders'),
        meta: { title: '订单管理', requiresAuth: true, parent: 'AdminSettings' }
      },
      {
        path: 'thesis/order/:id',
        name: 'AdminPaymentOrderDetail',
        component: () => import('@/views/admin/settings/PaymentOrderDetail.vue'),
        meta: { title: '订单详情', requiresAuth: true }
      },
      {
        path: 'stats/overview',
        name: 'AdminStatsOverview',
        component: () => import('@/views/admin/StatsOverview'),
        meta: { title: '数据概览', requiresAuth: true }
      },
      {
        path: 'stats/users',
        name: 'AdminStatsUsers',
        component: () => import('@/views/admin/StatsUsers'),
        meta: { title: '用户统计', requiresAuth: true }
      },
      {
        path: 'stats/thesis',
        name: 'AdminStatsThesis',
        component: () => import('@/views/admin/StatsThesis'),
        meta: { title: '论文统计', requiresAuth: true }
      },
      {
        path: 'stats/chat',
        name: 'AdminStatsChat',
        component: () => import('@/views/admin/StatsChat'),
        meta: { title: '聊天统计', requiresAuth: true }
      },
      {
        path: 'settings',
        name: 'AdminSettings',
        component: () => import('@/views/admin/settings/BaseConfig.vue'),
        meta: { title: '系统设置', requiresAuth: true }
      },
      {
        path: 'settings/payment',
        name: 'AdminPaymentConfig',
        component: () => import('@/views/admin/settings/PaymentConfigSimple.vue'),
        meta: { title: '支付配置', requiresAuth: true }
      },
      {
        path: 'settings/model',
        name: 'AdminModelConfig',
        component: () => import('@/views/admin/settings/ModelConfig.vue'),
        meta: { title: '大模型配置', requiresAuth: true }
      }
    ]
  }
]

const routerChildren = []
// 递归处理菜单项，包括子菜单
function processMenuItems(items) {
  items.forEach(link => {
    // 添加当前菜单路由
    if (link.path) {
      routerChildren.push({
        path: link.path, 
        component: (resolve) => require(['@/views' + link.path], resolve),
      })
    }
    
    // 处理子菜单
    if (link.children && link.children.length > 0) {
      processMenuItems(link.children)
    }
  })
}

// 处理所有菜单项
processMenuItems(menuList)

// 主布局路由 - 只匹配根路径，避免与管理员路由冲突
constantRouterMap.push({
  path: '/',
  component: MainLayout,
  redirect: '/help/guide',
  children: routerChildren
})

//将定义好的路由参入放入路由实例
export default new Router({
  mode: 'history',
  // mode: 'hash',
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRouterMap
})
