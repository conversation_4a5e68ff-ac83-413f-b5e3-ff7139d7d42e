<template>
  <div :index="index" @click="handlerPage" :class="['menu-item', isSelf ? 'selected' : '']">
    <slot></slot>
  </div>
</template>
<script>
export default {
  props: {
    index: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      isSelf: false,
    };
  },

  watch: {
    $route(n, o) {
      if (n.path == this.index) {
        this.isSelf = true;
      } else {
        this.isSelf = false;
      }
    },
  },
  created() {
    if (this.$route.path == this.index) {
      this.isSelf = true;
    }
  },
  methods: {
    handlerPage() {
      this.$router.push({
        path: this.index,
      });
    },
  },
};
</script>
<style scoped>
.selected {
  color: #1890ff !important;
  font-weight: 500 !important;
  background-color: rgba(24, 144, 255, 0.1) !important;
}
</style>