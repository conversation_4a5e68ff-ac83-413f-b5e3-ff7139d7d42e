<template>
  <div class="admin-users">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <div class="title-section">
            <h1 class="page-title">
              <i class="el-icon-user-solid"></i>
              用户管理
            </h1>
            <p class="page-subtitle">管理系统中的所有用户账户和权限</p>
          </div>
        </div>
        <div class="header-actions">
          <el-button type="primary" icon="el-icon-plus" size="medium" @click="handleAddUser">
            新增用户
          </el-button>
          <el-button type="success" icon="el-icon-refresh" size="medium" @click="refreshData">
            刷新数据
          </el-button>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="search-section">
      <el-card class="search-card" shadow="hover">
        <div class="search-header">
          <h3 class="search-title">
            <i class="el-icon-search"></i>
            搜索筛选
          </h3>
        </div>
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="关键词">
            <el-input
              v-model="searchForm.keyword"
              placeholder="用户名/昵称/邮箱"
              clearable
              prefix-icon="el-icon-search"
              @keyup.enter.native="handleSearch"
              class="search-input"
            />
          </el-form-item>
          <el-form-item label="VIP状态">
            <el-select v-model="searchForm.vip_status" placeholder="全部" clearable class="search-select">
              <el-option label="VIP用户" value="vip" />
              <el-option label="已过期" value="expired" />
              <el-option label="普通用户" value="normal" />
            </el-select>
          </el-form-item>
          <el-form-item label="锁定状态">
            <el-select v-model="searchForm.is_lock" placeholder="全部" clearable class="search-select">
              <el-option label="已锁定" value="true" />
              <el-option label="正常" value="false" />
            </el-select>
          </el-form-item>
          <el-form-item label="注册时间">
            <el-date-picker
              v-model="searchForm.date_range"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              class="search-date"
            />
          </el-form-item>
          <el-form-item class="search-buttons">
            <el-button type="primary" icon="el-icon-search" @click="handleSearch" class="search-btn">
              搜索
            </el-button>
            <el-button icon="el-icon-refresh" @click="resetSearch" class="reset-btn">
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 批量操作工具栏 -->
    <div class="batch-toolbar" v-if="selectedUsers.length > 0">
      <el-card class="batch-card" shadow="hover">
        <div class="toolbar-content">
          <div class="toolbar-left">
            <i class="el-icon-info"></i>
            <span class="selection-info">已选择 {{ selectedUsers.length }} 个用户</span>
          </div>
          <div class="toolbar-actions">
            <el-button size="small" type="primary" icon="el-icon-star-on" @click="handleBatchVip">
              批量设置VIP
            </el-button>
            <el-button size="small" type="warning" icon="el-icon-lock" @click="handleBatchLock">
              批量锁定
            </el-button>
            <el-button size="small" type="danger" icon="el-icon-delete" @click="handleBatchDelete">
              批量删除
            </el-button>
            <el-button size="small" icon="el-icon-close" @click="clearSelection">
              取消选择
            </el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 用户列表 -->
    <div class="table-section">
      <el-card class="table-card" shadow="hover">
        <div class="table-header">
          <h3 class="table-title">
            <i class="el-icon-document"></i>
            用户列表
          </h3>
          <div class="table-actions">
            <el-button size="small" icon="el-icon-download" @click="exportData">
              导出数据
            </el-button>
          </div>
        </div>
        
        <el-table
          ref="userTable"
          v-loading="userLoading"
          :data="userList"
          @selection-change="handleSelectionChange"
          border
          stripe
          class="user-table"
          :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="id" label="ID" width="80" align="center" />
          <el-table-column label="用户信息" min-width="250">
            <template slot-scope="scope">
              <div class="user-info">
                <div class="user-avatar">
                  <el-avatar :size="45" :src="scope.row.headimg" icon="el-icon-user"></el-avatar>
                </div>
                <div class="user-details">
                  <div class="username">{{ scope.row.username }}</div>
                  <div class="nickname">{{ scope.row.nickname || '未设置昵称' }}</div>
                  <div class="email">{{ scope.row.email || '未设置邮箱' }}</div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="VIP状态" width="120" align="center">
            <template slot-scope="scope">
              <el-tag 
                v-if="scope.row.vip_expire_at && new Date(scope.row.vip_expire_at) > new Date()" 
                type="success"
                size="small"
                effect="dark"
              >
                <i class="el-icon-star-on"></i>
                VIP{{ scope.row.vip_level }}
              </el-tag>
              <el-tag v-else type="info" size="small" effect="plain">
                <i class="el-icon-user"></i>
                普通用户
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="状态" width="100" align="center">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.is_lock" type="danger" size="small" effect="dark">
                <i class="el-icon-lock"></i>
                已锁定
              </el-tag>
              <el-tag v-else type="success" size="small" effect="dark">
                <i class="el-icon-check"></i>
                正常
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="create_time" label="注册时间" width="160" align="center" />
          <el-table-column prop="last_login_time" label="最后登录" width="160" align="center" />
          <el-table-column label="操作" width="300" fixed="right" align="center">
            <template slot-scope="scope">
              <div class="action-buttons">
                <el-button size="mini" type="info" icon="el-icon-view" @click="handleView(scope.row)">
                  查看
                </el-button>
                <el-button size="mini" type="primary" icon="el-icon-edit" @click="handleEdit(scope.row)">
                  编辑
                </el-button>
                <el-button size="mini" type="warning" icon="el-icon-star-on" @click="handleVip(scope.row)">
                  VIP
                </el-button>
                <el-button 
                  size="mini" 
                  :type="scope.row.is_lock ? 'success' : 'warning'"
                  :icon="scope.row.is_lock ? 'el-icon-unlock' : 'el-icon-lock'"
                  @click="handleToggleLock(scope.row)"
                >
                  {{ scope.row.is_lock ? '解锁' : '锁定' }}
                </el-button>
                <el-button size="mini" type="info" icon="el-icon-key" @click="handleResetPassword(scope.row)">
                  重置密码
                </el-button>
                <el-button size="mini" type="danger" icon="el-icon-delete" @click="handleDelete(scope.row)">
                  删除
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
        
        <!-- 分页 -->
        <div class="pagination-section">
          <el-pagination
            :current-page="pagination.page"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pagination.size"
            :total="userTotal"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            background
            class="pagination"
          />
        </div>
      </el-card>
    </div>

    <!-- 用户详情对话框 -->
    <el-dialog
      title="用户详情"
      :visible.sync="detailDialogVisible"
      width="700px"
      class="detail-dialog"
    >
      <div v-if="currentUser" class="user-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="用户ID">{{ currentUser.id }}</el-descriptions-item>
          <el-descriptions-item label="用户名">{{ currentUser.username }}</el-descriptions-item>
          <el-descriptions-item label="昵称">{{ currentUser.nickname }}</el-descriptions-item>
          <el-descriptions-item label="邮箱">{{ currentUser.email }}</el-descriptions-item>
          <el-descriptions-item label="手机号">{{ currentUser.phone || '未设置' }}</el-descriptions-item>
          <el-descriptions-item label="真实姓名">{{ currentUser.realname || '未设置' }}</el-descriptions-item>
          <el-descriptions-item label="VIP等级">{{ currentUser.vip_level || 1 }}</el-descriptions-item>
          <el-descriptions-item label="VIP到期时间">
            {{ currentUser.vip_expire_at || '未开通' }}
          </el-descriptions-item>
          <el-descriptions-item label="注册时间">{{ currentUser.create_time }}</el-descriptions-item>
          <el-descriptions-item label="最后登录">{{ currentUser.last_login_time || '未登录' }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag v-if="currentUser.is_lock" type="danger">已锁定</el-tag>
            <el-tag v-else type="success">正常</el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>

    <!-- 编辑用户对话框 -->
    <el-dialog
      :title="isEditMode ? '编辑用户' : '新增用户'"
      :visible.sync="editDialogVisible"
      width="600px"
      @close="resetEditForm"
      class="edit-dialog"
    >
      <el-form
        ref="editForm"
        :model="editForm"
        :rules="editRules"
        label-width="100px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="editForm.username" :disabled="isEditMode" />
        </el-form-item>
        <el-form-item label="密码" prop="password" v-if="!isEditMode">
          <el-input v-model="editForm.password" type="password" show-password />
        </el-form-item>
        <el-form-item label="昵称" prop="nickname">
          <el-input v-model="editForm.nickname" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="editForm.email" />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="editForm.phone" />
        </el-form-item>
        <el-form-item label="真实姓名" prop="realname">
          <el-input v-model="editForm.realname" />
        </el-form-item>
        <el-form-item label="锁定状态" prop="is_lock">
          <el-switch v-model="editForm.is_lock" />
        </el-form-item>
        <el-form-item label="VIP等级" prop="vip_level" v-if="!isEditMode">
          <el-select v-model="editForm.vip_level" placeholder="选择VIP等级">
            <el-option label="普通用户" :value="1" />
            <el-option label="VIP1" :value="2" />
            <el-option label="VIP2" :value="3" />
            <el-option label="VIP3" :value="4" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="editLoading" @click="handleSaveEdit">
          {{ isEditMode ? '保存' : '创建' }}
        </el-button>
      </div>
    </el-dialog>

    <!-- VIP管理对话框 -->
    <el-dialog
      title="VIP管理"
      :visible.sync="vipDialogVisible"
      width="500px"
      @close="resetVipForm"
      class="vip-dialog"
    >
      <el-form
        ref="vipForm"
        :model="vipForm"
        :rules="vipRules"
        label-width="100px"
      >
        <el-form-item label="VIP等级" prop="vip_level">
          <el-select v-model="vipForm.vip_level" placeholder="选择VIP等级">
            <el-option label="VIP1" :value="1" />
            <el-option label="VIP2" :value="2" />
            <el-option label="VIP3" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="操作类型" prop="action">
          <el-radio-group v-model="vipForm.action">
            <el-radio label="add">开通VIP</el-radio>
            <el-radio label="extend">续费VIP</el-radio>
            <el-radio label="cancel">取消VIP</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="vipForm.action !== 'cancel'" label="时长(天)" prop="days">
          <el-input-number v-model="vipForm.days" :min="1" :max="365" />
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="vipDialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="vipLoading" @click="handleSaveVip">确认</el-button>
      </div>
    </el-dialog>

    <!-- 重置密码对话框 -->
    <el-dialog
      title="重置用户密码"
      :visible.sync="resetPasswordDialogVisible"
      width="500px"
      @close="resetPasswordForm"
      class="reset-password-dialog"
    >
      <div class="reset-password-info">
        <el-alert
          title="重置密码说明"
          type="info"
          :closable="false"
          show-icon
        >
          <div slot="description">
            <p>• 如果不填写新密码，将使用默认密码：<strong>123456</strong></p>
            <p>• 重置后请及时通知用户修改密码</p>
            <p>• 此操作会记录在管理员操作日志中</p>
          </div>
        </el-alert>
      </div>
      
      <el-form
        ref="resetPasswordForm"
        :model="resetPasswordForm"
        :rules="resetPasswordRules"
        label-width="100px"
        style="margin-top: 20px;"
      >
        <el-form-item label="用户名">
          <el-input v-model="resetPasswordForm.username" disabled />
        </el-form-item>
        <el-form-item label="新密码" prop="new_password">
          <el-input 
            v-model="resetPasswordForm.new_password" 
            type="password" 
            show-password
            placeholder="留空则使用默认密码：123456"
          />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirm_password">
          <el-input 
            v-model="resetPasswordForm.confirm_password" 
            type="password" 
            show-password
            placeholder="请再次输入新密码"
          />
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="resetPasswordDialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="resetPasswordLoading" @click="handleSaveResetPassword">
          确认重置
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex'

export default {
  name: 'AdminUsers',
  data() {
    return {
      searchForm: {
        keyword: '',
        vip_status: '',
        is_lock: ''
      },
      pagination: {
        page: 1,
        size: 20
      },
      selectedUsers: [],
      detailDialogVisible: false,
      editDialogVisible: false,
      vipDialogVisible: false,
      batchVipDialogVisible: false,
      currentUser: null,
      editLoading: false,
      vipLoading: false,
      isEditMode: false,
      editForm: {
        username: '',
        password: '',
        nickname: '',
        email: '',
        phone: '',
        realname: '',
        is_lock: false,
        vip_level: 1
      },
      editRules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' }
        ],
        email: [
          { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
        ]
      },
      vipForm: {
        vip_level: 1,
        action: 'add',
        days: 30
      },
      vipRules: {
        vip_level: [
          { required: true, message: '请选择VIP等级', trigger: 'change' }
        ],
        action: [
          { required: true, message: '请选择操作类型', trigger: 'change' }
        ],
        days: [
          { required: true, message: '请输入时长', trigger: 'blur' }
        ]
      },
      resetPasswordDialogVisible: false,
      resetPasswordForm: {
        username: '',
        new_password: '',
        confirm_password: ''
      },
      resetPasswordRules: {
        confirm_password: [
          { 
            validator: (rule, value, callback) => {
              if (this.resetPasswordForm.new_password && value !== this.resetPasswordForm.new_password) {
                callback(new Error('两次输入的密码不一致'))
              } else {
                callback()
              }
            }, 
            trigger: 'blur' 
          }
        ]
      },
      resetPasswordLoading: false
    }
  },
  computed: {
    ...mapGetters('admin', ['userList', 'userTotal', 'userLoading', 'vipUserCount', 'activeUserCount', 'lockedUserCount'])
  },
  mounted() {
    this.loadData()
  },
  methods: {
    ...mapActions('admin', [
      'getUserList', 
      'getUserDetail', 
      'createUser',
      'updateUser', 
      'deleteUser', 
      'toggleUserLock',
      'vipManage',
      'batchDeleteUsers',
      'batchLockUsers',
      'batchVipUsers',
      'resetUserPassword'
    ]),
    
    // 加载数据
    async loadData() {
      try {
        console.log('开始加载用户数据...')
        const params = {
          ...this.searchForm,
          ...this.pagination
        }
        console.log('请求参数:', params)
        
        const result = await this.getUserList(params)
        console.log('API响应结果:', result)
        
        if (!result.success) {
          console.error('获取用户列表失败:', result.message)
          this.$message.error(result.message || '获取用户列表失败')
        } else {
          console.log('用户数据加载成功，用户数量:', this.userTotal)
        }
      } catch (error) {
        console.error('加载用户数据时发生错误:', error)
        this.$message.error('加载用户数据失败')
      }
    },
    
    // 新增用户
    handleAddUser() {
      this.currentUser = null
      this.editForm = {
        username: '',
        password: '',
        nickname: '',
        email: '',
        phone: '',
        realname: '',
        is_lock: false,
        vip_level: 1
      }
      this.editDialogVisible = true
      this.isEditMode = false
    },
    
    // 搜索
    handleSearch() {
      this.pagination.page = 1
      this.loadData()
    },
    
    // 重置搜索
    resetSearch() {
      this.searchForm = {
        keyword: '',
        vip_status: '',
        is_lock: ''
      }
      this.pagination.page = 1
      this.loadData()
    },
    
    // 刷新数据
    async refreshData() {
      await this.loadData()
      this.$message.success('数据刷新成功')
    },
    
    // 分页大小改变
    handleSizeChange(size) {
      this.pagination.size = size
      this.loadData()
    },
    
    // 当前页改变
    handleCurrentChange(page) {
      this.pagination.page = page
      this.loadData()
    },
    
    // 查看用户详情
    async handleView(user) {
      try {
        const result = await this.getUserDetail(user.id)
        if (result.success) {
          this.currentUser = result.data
          this.detailDialogVisible = true
        } else {
          this.$message.error(result.message || '获取用户详情失败')
        }
      } catch (error) {
        console.error('获取用户详情失败:', error)
        this.$message.error('获取用户详情失败')
      }
    },
    
    // 编辑用户
    handleEdit(user) {
      this.currentUser = user
      this.editForm = {
        username: user.username,
        password: '', // 编辑时不显示密码
        nickname: user.nickname,
        email: user.email,
        phone: user.phone,
        realname: user.realname,
        is_lock: user.is_lock
      }
      this.editDialogVisible = true
      this.isEditMode = true
    },
    
    // 保存编辑
    async handleSaveEdit() {
      try {
        const valid = await this.$refs.editForm.validate()
        if (!valid) return
        
        this.editLoading = true
        
        if (this.isEditMode) {
          // 编辑模式
          const result = await this.updateUser({
            userId: this.currentUser.id,
            data: this.editForm
          })
          
          if (result.success) {
            this.$message.success(result.message || '更新成功')
            this.editDialogVisible = false
            this.loadData()
          } else {
            this.$message.error(result.message || '更新失败')
          }
        } else {
          // 新增模式
          const result = await this.createUser(this.editForm)
          
          if (result.success) {
            this.$message.success(result.message || '创建成功')
            this.editDialogVisible = false
            this.loadData()
          } else {
            this.$message.error(result.message || '创建失败')
          }
        }
      } catch (error) {
        console.error('保存用户失败:', error)
        this.$message.error('保存用户失败')
      } finally {
        this.editLoading = false
      }
    },
    
    // VIP管理
    handleVip(user) {
      this.currentUser = user
      this.vipForm = {
        vip_level: user.vip_level || 1,
        action: 'add',
        days: 30
      }
      this.vipDialogVisible = true
    },
    
    // 保存VIP设置
    async handleSaveVip() {
      try {
        const valid = await this.$refs.vipForm.validate()
        if (!valid) return
        
        this.vipLoading = true
        const result = await this.vipManage({
          userId: this.currentUser.id,
          data: this.vipForm
        })
        
        if (result.success) {
          this.$message.success(result.message || 'VIP管理成功')
          this.vipDialogVisible = false
          this.loadData()
        } else {
          this.$message.error(result.message || 'VIP管理失败')
        }
      } catch (error) {
        console.error('VIP管理失败:', error)
        this.$message.error('VIP管理失败')
      } finally {
        this.vipLoading = false
      }
    },
    
    // 删除用户
    async handleDelete(user) {
      try {
        await this.$confirm(`确定要删除用户 "${user.username}" 吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        const result = await this.deleteUser(user.id)
        if (result.success) {
          this.$message.success(result.message || '删除成功')
          this.loadData()
        } else {
          this.$message.error(result.message || '删除失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除用户失败:', error)
          this.$message.error('删除用户失败')
        }
      }
    },
    
    // 锁定/解锁用户
    async handleToggleLock(user) {
      try {
        const action = user.is_lock ? '解锁' : '锁定'
        await this.$confirm(`确定要${action}用户 "${user.username}" 吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        const result = await this.toggleUserLock({
          userId: user.id,
          isLock: !user.is_lock
        })
        
        if (result.success) {
          this.$message.success(result.message || `${action}成功`)
          this.loadData()
        } else {
          this.$message.error(result.message || `${action}失败`)
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('锁定/解锁用户失败:', error)
          this.$message.error('锁定/解锁用户失败')
        }
      }
    },
    
    // 重置编辑表单
    resetEditForm() {
      this.editForm = {
        username: '',
        password: '',
        nickname: '',
        email: '',
        phone: '',
        realname: '',
        is_lock: false,
        vip_level: 1
      }
      this.$refs.editForm && this.$refs.editForm.resetFields()
    },
    
    // 重置VIP表单
    resetVipForm() {
      this.vipForm = {
        vip_level: 1,
        action: 'add',
        days: 30
      }
      this.$refs.vipForm && this.$refs.vipForm.resetFields()
    },

    // 处理选择变化
    handleSelectionChange(selection) {
      this.selectedUsers = selection.map(user => user.id)
    },

    // 处理批量VIP设置
    async handleBatchVip() {
      if (this.selectedUsers.length === 0) {
        this.$message.warning('请先选择用户')
        return
      }
      
      this.vipForm = {
        vip_level: 1,
        action: 'add',
        days: 30
      }
      this.batchVipDialogVisible = true
    },

    // 处理批量锁定
    async handleBatchLock() {
      if (this.selectedUsers.length === 0) {
        this.$message.warning('请先选择用户')
        return
      }
      
      try {
        await this.$confirm(`确定要锁定选中的 ${this.selectedUsers.length} 个用户吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        const result = await this.batchLockUsers({
          userIds: this.selectedUsers,
          isLock: true
        })
        
        if (result.success) {
          this.$message.success(result.message || '批量锁定成功')
          this.clearSelection()
          this.loadData()
        } else {
          this.$message.error(result.message || '批量锁定失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('批量锁定失败:', error)
          this.$message.error('批量锁定失败')
        }
      }
    },

    // 处理批量删除
    async handleBatchDelete() {
      if (this.selectedUsers.length === 0) {
        this.$message.warning('请先选择用户')
        return
      }
      
      try {
        await this.$confirm(`确定要删除选中的 ${this.selectedUsers.length} 个用户吗？此操作不可恢复！`, '警告', {
          confirmButtonText: '确定删除',
          cancelButtonText: '取消',
          type: 'error'
        })
        
        const result = await this.batchDeleteUsers(this.selectedUsers)
        
        if (result.success) {
          this.$message.success(result.message || '批量删除成功')
          this.clearSelection()
          this.loadData()
        } else {
          this.$message.error(result.message || '批量删除失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('批量删除失败:', error)
          this.$message.error('批量删除失败')
        }
      }
    },

    // 处理选择清除
    clearSelection() {
      this.selectedUsers = []
      this.$refs.userTable && this.$refs.userTable.clearSelection()
    },

    // 导出数据
    exportData() {
      this.$message.info('导出功能开发中...')
    },

    // 处理重置密码
    handleResetPassword(user) {
      this.currentUser = user
      this.resetPasswordForm = {
        username: user.username,
        new_password: '',
        confirm_password: ''
      }
      this.resetPasswordDialogVisible = true
    },

    // 处理重置密码
    async handleSaveResetPassword() {
      try {
        const valid = await this.$refs.resetPasswordForm.validate()
        if (!valid) return
        
        this.resetPasswordLoading = true
        
        // 调用重置密码API
        const result = await this.resetUserPassword({
          userId: this.currentUser.id,
          newPassword: this.resetPasswordForm.new_password || '123456'
        })
        
        if (result.success) {
          this.$message.success(result.message || '重置密码成功')
          this.resetPasswordDialogVisible = false
          this.loadData()
        } else {
          this.$message.error(result.message || '重置密码失败')
        }
      } catch (error) {
        console.error('重置密码失败:', error)
        this.$message.error('重置密码失败')
      } finally {
        this.resetPasswordLoading = false
      }
    },

    // 处理重置密码对话框关闭
    resetPasswordForm() {
      this.resetPasswordForm = {
        username: '',
        new_password: '',
        confirm_password: ''
      }
      this.$refs.resetPasswordForm && this.$refs.resetPasswordForm.resetFields()
    }
  }
}
</script>

<style lang="scss" scoped>
.admin-users {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;

  // 页面头部
  .page-header {
    margin-bottom: 24px;
    
    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      padding: 24px 32px;
      border-radius: 12px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      
      .header-left {
        .title-section {
          .page-title {
            color: white;
            font-size: 28px;
            font-weight: 600;
            margin: 0 0 8px 0;
            display: flex;
            align-items: center;
            gap: 12px;
            
            i {
              font-size: 32px;
            }
          }
          
          .page-subtitle {
            color: rgba(255, 255, 255, 0.9);
            font-size: 16px;
            margin: 0;
          }
        }
      }
      
      .header-actions {
        display: flex;
        gap: 12px;
        
        .el-button {
          border-radius: 8px;
          font-weight: 500;
          padding: 12px 20px;
          border: none;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          
          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          }
        }
      }
    }
  }

  // 搜索区域
  .search-section {
    margin-bottom: 24px;
    
    .search-card {
      border-radius: 12px;
      border: none;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
      
      .search-header {
        margin-bottom: 20px;
        
        .search-title {
          font-size: 18px;
          font-weight: 600;
          color: #303133;
          margin: 0;
          display: flex;
          align-items: center;
          gap: 8px;
          
          i {
            color: #409eff;
          }
        }
      }
      
      .search-form {
        .el-form-item {
          margin-bottom: 16px;
          margin-right: 20px;
          
          .el-form-item__label {
            font-weight: 500;
            color: #606266;
          }
        }
        
        .search-input, .search-select, .search-date {
          width: 200px;
        }
        
        .search-buttons {
          margin-left: 20px;
          
          .search-btn, .reset-btn {
            border-radius: 6px;
            padding: 10px 20px;
          }
        }
      }
    }
  }

  // 批量操作工具栏
  .batch-toolbar {
    margin-bottom: 24px;
    
    .batch-card {
      border-radius: 12px;
      border: none;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
      background: linear-gradient(135deg, #fff5f5, #f0f9ff);
      
      .toolbar-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .toolbar-left {
          display: flex;
          align-items: center;
          gap: 8px;
          
          i {
            color: #409eff;
            font-size: 16px;
          }
          
          .selection-info {
            font-weight: 500;
            color: #303133;
          }
        }
        
        .toolbar-actions {
          display: flex;
          gap: 8px;
          
          .el-button {
            border-radius: 6px;
            font-weight: 500;
          }
        }
      }
    }
  }

  // 表格区域
  .table-section {
    .table-card {
      border-radius: 12px;
      border: none;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
      
      .table-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 16px;
        border-bottom: 1px solid #ebeef5;
        
        .table-title {
          font-size: 18px;
          font-weight: 600;
          color: #303133;
          margin: 0;
          display: flex;
          align-items: center;
          gap: 8px;
          
          i {
            color: #409eff;
          }
        }
        
        .table-actions {
          .el-button {
            border-radius: 6px;
            font-weight: 500;
          }
        }
      }
      
      .user-table {
        border-radius: 8px;
        overflow: hidden;
        
        .user-info {
          display: flex;
          align-items: center;
          gap: 12px;
          
          .user-avatar {
            flex-shrink: 0;
          }
          
          .user-details {
            .username {
              font-weight: 600;
              color: #303133;
              margin-bottom: 4px;
            }
            
            .nickname {
              font-size: 12px;
              color: #909399;
              margin-bottom: 2px;
            }
            
            .email {
              font-size: 12px;
              color: #c0c4cc;
            }
          }
        }
        
        .action-buttons {
          display: flex;
          gap: 4px;
          flex-wrap: wrap;
          
          .el-button {
            border-radius: 4px;
            font-size: 12px;
            padding: 6px 10px;
          }
        }
      }
      
      .pagination-section {
        margin-top: 20px;
        display: flex;
        justify-content: center;
        
        .pagination {
          .el-pagination.is-background .el-pager li:not(.disabled).active {
            background-color: #409eff;
          }
        }
      }
    }
  }

  // 对话框样式
  .detail-dialog, .edit-dialog, .vip-dialog {
    .el-dialog__header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border-radius: 8px 8px 0 0;
      
      .el-dialog__title {
        color: white;
        font-weight: 600;
      }
    }
    
    .el-dialog__body {
      padding: 24px;
    }
    
    .el-dialog__footer {
      border-top: 1px solid #ebeef5;
      padding: 16px 24px;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .admin-users {
    .stats-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }
}

@media (max-width: 768px) {
  .admin-users {
    padding: 16px;
    
    .page-header {
      .header-content {
        flex-direction: column;
        gap: 16px;
        text-align: center;
        
        .header-actions {
          width: 100%;
          justify-content: center;
        }
      }
    }
    
    .stats-grid {
      grid-template-columns: 1fr;
    }
    
    .search-section {
      .search-form {
        .el-form-item {
          display: block;
          margin-bottom: 16px;
          
          .search-input, .search-select, .search-date {
            width: 100%;
          }
        }
        
        .search-buttons {
          margin-left: 0;
          margin-top: 16px;
        }
      }
    }
    
    .batch-toolbar {
      .toolbar-content {
        flex-direction: column;
        gap: 16px;
        
        .toolbar-actions {
          width: 100%;
          justify-content: center;
          flex-wrap: wrap;
        }
      }
    }
    
    .table-section {
      .table-header {
        flex-direction: column;
        gap: 16px;
        text-align: center;
      }
      
      .user-table {
        .action-buttons {
          justify-content: center;
        }
      }
    }
  }
}
</style> 