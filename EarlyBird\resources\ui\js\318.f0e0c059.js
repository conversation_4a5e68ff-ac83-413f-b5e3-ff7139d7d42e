"use strict";(self["webpackChunkopen_essay_gui"]=self["webpackChunkopen_essay_gui"]||[]).push([[318],{9318:(t,s,a)=>{a.r(s),a.d(s,{default:()=>o});var i=function(){var t=this,s=t._self._c;return s("div",{staticClass:"dashboard"},[s("div",{staticClass:"welcome-banner"},[s("div",{staticClass:"welcome-content"},[s("div",{staticClass:"welcome-text"},[s("h1",[t._v("欢迎回来，"+t._s(t.adminInfo?.username||"管理员"))]),s("p",[t._v("今天是 "+t._s(t.currentDate)+"，祝您工作愉快！")])]),s("div",{staticClass:"welcome-actions"},[s("el-button",{attrs:{type:"primary",icon:"el-icon-refresh"},on:{click:t.refreshData}},[t._v(" 刷新数据 ")])],1)])]),s("el-row",{staticClass:"stats-cards",attrs:{gutter:24}},[s("el-col",{attrs:{xs:24,sm:12,md:6}},[s("div",{staticClass:"stats-card user-card"},[s("div",{staticClass:"stats-icon"},[s("i",{staticClass:"el-icon-user"})]),s("div",{staticClass:"stats-content"},[s("div",{staticClass:"stats-number"},[t._v(t._s(t.overviewData?.overview?.total_users||0))]),s("div",{staticClass:"stats-label"},[t._v("总用户数")]),s("div",{staticClass:"stats-trend"},[s("i",{staticClass:"el-icon-arrow-up"}),t._v(" 今日新增: "+t._s(t.overviewData?.today?.new_users||0)+" ")])])])]),s("el-col",{attrs:{xs:24,sm:12,md:6}},[s("div",{staticClass:"stats-card thesis-card"},[s("div",{staticClass:"stats-icon"},[s("i",{staticClass:"el-icon-document"})]),s("div",{staticClass:"stats-content"},[s("div",{staticClass:"stats-number"},[t._v(t._s(t.overviewData?.overview?.total_thesis||0))]),s("div",{staticClass:"stats-label"},[t._v("总论文数")]),s("div",{staticClass:"stats-trend"},[s("i",{staticClass:"el-icon-arrow-up"}),t._v(" 今日新增: "+t._s(t.overviewData?.today?.new_thesis||0)+" ")])])])]),s("el-col",{attrs:{xs:24,sm:12,md:6}},[s("div",{staticClass:"stats-card vip-card"},[s("div",{staticClass:"stats-icon"},[s("i",{staticClass:"el-icon-star-on"})]),s("div",{staticClass:"stats-content"},[s("div",{staticClass:"stats-number"},[t._v(t._s(t.overviewData?.overview?.vip_users||0))]),s("div",{staticClass:"stats-label"},[t._v("VIP用户")]),s("div",{staticClass:"stats-trend"},[s("i",{staticClass:"el-icon-warning"}),t._v(" 过期: "+t._s(t.overviewData?.overview?.expired_vip_users||0)+" ")])])])]),s("el-col",{attrs:{xs:24,sm:12,md:6}},[s("div",{staticClass:"stats-card chat-card"},[s("div",{staticClass:"stats-icon"},[s("i",{staticClass:"el-icon-chat-dot-round"})]),s("div",{staticClass:"stats-content"},[s("div",{staticClass:"stats-number"},[t._v(t._s(t.overviewData?.overview?.total_chat_logs||0))]),s("div",{staticClass:"stats-label"},[t._v("聊天记录")]),s("div",{staticClass:"stats-trend"},[s("i",{staticClass:"el-icon-arrow-up"}),t._v(" 今日: "+t._s(t.overviewData?.today?.chat_logs||0)+" ")])])])])],1),s("el-row",{staticClass:"charts-section",attrs:{gutter:24}},[s("el-col",{attrs:{xs:24,lg:16}},[s("div",{staticClass:"chart-card"},[s("div",{staticClass:"chart-header"},[s("h3",[t._v("用户增长趋势")]),s("div",{staticClass:"chart-actions"},[s("el-button",{attrs:{size:"small",type:"text"},on:{click:t.refreshData}},[s("i",{staticClass:"el-icon-refresh"})])],1)]),s("div",{staticClass:"chart-container"},[t.userStats?s("div",{staticClass:"chart-content"},t._l(t.userStats.daily_stats,(function(a,i){return s("div",{key:i,staticClass:"chart-item"},[s("div",{staticClass:"chart-bar"},[s("div",{staticClass:"chart-bar-fill",style:{height:t.getBarHeight(a.count,t.userStats.daily_stats)}})]),s("div",{staticClass:"chart-label"},[t._v(t._s(a.date))]),s("div",{staticClass:"chart-value"},[t._v(t._s(a.count))])])})),0):s("div",{staticClass:"chart-loading"},[s("div",{staticClass:"loading-spinner"}),s("p",[t._v("加载中...")])])])])]),s("el-col",{attrs:{xs:24,lg:8}},[s("div",{staticClass:"chart-card"},[s("div",{staticClass:"chart-header"},[s("h3",[t._v("VIP用户分布")])]),s("div",{staticClass:"chart-container"},[t.userStats?s("div",{staticClass:"vip-distribution"},t._l(t.userStats.vip_level_stats,(function(a,i){return s("div",{key:i,staticClass:"vip-item"},[s("div",{staticClass:"vip-info"},[s("div",{staticClass:"vip-level"},[t._v("VIP "+t._s(a.level))]),s("div",{staticClass:"vip-count"},[t._v(t._s(a.count)+"人")])]),s("div",{staticClass:"vip-progress"},[s("div",{staticClass:"progress-bar"},[s("div",{staticClass:"progress-fill",style:{width:a.count/t.userStats.total_users*100+"%"}})]),s("div",{staticClass:"vip-percentage"},[t._v(" "+t._s((a.count/t.userStats.total_users*100).toFixed(1))+"% ")])])])})),0):s("div",{staticClass:"chart-loading"},[s("div",{staticClass:"loading-spinner"}),s("p",[t._v("加载中...")])])])])])],1),s("el-row",{staticClass:"bottom-section",attrs:{gutter:24}},[s("el-col",{attrs:{xs:24,md:8}},[s("div",{staticClass:"action-card"},[s("div",{staticClass:"card-header"},[s("h3",[t._v("快速操作")])]),s("div",{staticClass:"action-grid"},[s("div",{staticClass:"action-item",on:{click:function(s){return t.$router.push("/admin/users")}}},[s("div",{staticClass:"action-icon"},[s("i",{staticClass:"el-icon-user"})]),s("div",{staticClass:"action-text"},[t._v("用户管理")])]),s("div",{staticClass:"action-item",on:{click:function(s){return t.$router.push("/admin/thesis")}}},[s("div",{staticClass:"action-icon"},[s("i",{staticClass:"el-icon-document"})]),s("div",{staticClass:"action-text"},[t._v("论文管理")])]),s("div",{staticClass:"action-item",on:{click:function(s){return t.$router.push("/admin/stats/overview")}}},[s("div",{staticClass:"action-icon"},[s("i",{staticClass:"el-icon-s-data"})]),s("div",{staticClass:"action-text"},[t._v("详细统计")])]),s("div",{staticClass:"action-item",on:{click:function(s){return t.$router.push("/admin/settings")}}},[s("div",{staticClass:"action-icon"},[s("i",{staticClass:"el-icon-setting"})]),s("div",{staticClass:"action-text"},[t._v("系统设置")])])])])]),s("el-col",{attrs:{xs:24,md:16}},[s("div",{staticClass:"system-card"},[s("div",{staticClass:"card-header"},[s("h3",[t._v("系统信息")])]),s("div",{staticClass:"system-grid"},[s("div",{staticClass:"system-item"},[s("div",{staticClass:"system-label"},[t._v("系统名称")]),s("div",{staticClass:"system-value"},[t._v("早鸟论文后台管理系统")])]),s("div",{staticClass:"system-item"},[s("div",{staticClass:"system-label"},[t._v("当前管理员")]),s("div",{staticClass:"system-value"},[t._v(t._s(t.adminInfo?.username||"未知"))])]),s("div",{staticClass:"system-item"},[s("div",{staticClass:"system-label"},[t._v("最后登录")]),s("div",{staticClass:"system-value"},[t._v(t._s(t.adminInfo?.last_login_time||"未知"))])]),s("div",{staticClass:"system-item"},[s("div",{staticClass:"system-label"},[t._v("系统时间")]),s("div",{staticClass:"system-value"},[t._v(t._s(t.currentTime))])])])])])],1)],1)},e=[],c=a(5353);const l={name:"AdminDashboard",data(){return{currentTime:"",currentDate:"",timer:null}},computed:{...(0,c.L8)("admin",["overviewData","userStats","adminInfo"])},mounted(){this.$nextTick((()=>{setTimeout((()=>{this.loadData()}),100)})),this.startTimer()},beforeDestroy(){this.timer&&clearInterval(this.timer)},methods:{...(0,c.i0)("admin",["getOverview","getUserStats"]),async loadData(){try{await Promise.all([this.getOverview(),this.getUserStats({period:"7d"})])}catch(t){console.error("加载数据失败:",t),this.$message.error("加载数据失败，请稍后重试")}},async refreshData(){this.$message.info("正在刷新数据..."),await this.loadData(),this.$message.success("数据刷新成功")},getBarHeight(t,s){if(!s||0===s.length)return"0%";const a=Math.max(...s.map((t=>t.count)));return a>0?t/a*100+"%":"0%"},startTimer(){this.updateTime(),this.timer=setInterval(this.updateTime,1e3)},updateTime(){const t=new Date;this.currentTime=t.toLocaleString("zh-CN"),this.currentDate=t.toLocaleDateString("zh-CN",{year:"numeric",month:"long",day:"numeric",weekday:"long"})}}},r=l;var v=a(1656),n=(0,v.A)(r,i,e,!1,null,"01383205",null);const o=n.exports}}]);