#!/usr/bin/env python3
import os
import sys
from flask import send_from_directory, redirect, Flask
from datetime import timedelta
from threading import Event

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入日志配置
from config.logging_config import logger

def create_app():
    """创建Flask应用（服务器模式）"""
    from web_server import create_custom_app
    return create_custom_app()

def run_server():
    """运行服务器（无GUI模式）"""
    try:
        # 设置环境变量
        os.environ["FLASK_ENV"] = "production"
        
        # 打印环境信息
        from common.init_flask import printEnvInfo
        printEnvInfo()
        
        # 创建应用
        app = create_app()
        
        if app is None:
            logger.error("Failed to create Flask app")
            return

        # 确保数据目录存在
        base_dir = os.path.dirname(os.path.abspath(__file__))
        data_dir = os.path.join(base_dir, "resources", "data")
        os.makedirs(data_dir, exist_ok=True)
        logger.info(f"确保数据目录存在: {data_dir}")
        
        # 执行数据库迁移
        try:
            logger.info("开始执行数据库迁移...")
            
            # 直接使用SQLAlchemy执行SQL语句添加字段
            try:
                from sqlalchemy import create_engine, text
                from config.config import AppConfig
                
                # 获取数据库连接
                db_uri = AppConfig.SQLALCHEMY_DATABASE_URI
                logger.info(f"数据库连接URI: {db_uri}")
                
                engine = create_engine(db_uri)
                conn = engine.connect()
                
                # 检查表是否存在 - 使用MySQL语法
                check_table_sql = """
                SELECT COUNT(*) 
                FROM information_schema.tables 
                WHERE table_schema = 'zndata' 
                AND table_name = 'earlybird_chat_log'
                """
                table_exists = conn.execute(text(check_table_sql)).fetchone()
                
                if table_exists and table_exists[0] > 0:
                    logger.info("ChatLog表存在，继续检查字段")
                    
                    # 检查uid字段是否已存在 - 使用MySQL语法
                    check_column_sql = """
                    SELECT COLUMN_NAME 
                    FROM INFORMATION_SCHEMA.COLUMNS 
                    WHERE TABLE_SCHEMA = 'zndata' 
                    AND TABLE_NAME = 'earlybird_chat_log' 
                    AND COLUMN_NAME = 'uid'
                    """
                    columns = conn.execute(text(check_column_sql)).fetchall()
                    
                    has_uid = len(columns) > 0
                    
                    if not has_uid:
                        logger.info("ChatLog表不存在uid字段，开始添加...")
                        
                        # 添加uid字段
                        add_column_sql = """
                        ALTER TABLE earlybird_chat_log 
                        ADD COLUMN uid INT DEFAULT 0
                        """
                        conn.execute(text(add_column_sql))
                        conn.commit()
                        
                        logger.info("成功为ChatLog表添加uid字段")
                    else:
                        logger.info("ChatLog表已存在uid字段，无需迁移")
                else:
                    logger.warning("ChatLog表不存在，将在应用启动时自动创建")
                
                conn.close()
            except Exception as e:
                logger.error(f"直接执行SQL迁移失败: {str(e)}")
                logger.exception(e)
                
            # 尝试使用迁移脚本（作为备选方案）
            try:
                from migrations.add_uid_to_chatlog import migrate
                migrate_success = migrate()
                if migrate_success:
                    logger.info("使用迁移脚本成功完成数据库迁移")
            except Exception as e:
                logger.warning(f"使用迁移脚本执行数据库迁移失败: {str(e)}")
        except Exception as e:
            logger.error(f"执行数据库迁移时出错: {str(e)}")
            logger.exception(e)
        
        # 启动任务处理线程
        from task.task_api_server import TaskApiServer
        from task.task_paper_generator import TaskPaperGenerator
        
        stop_event = Event()
        TaskApiServer(stop_event, app).start()
        TaskPaperGenerator(stop_event, app).start()

        # 打印服务器信息
        host = app.config.get("RUN_HOST", "127.0.0.1")
        port = app.config.get("RUN_PORT", 3301)
        print("\n=== 服务器已启动（服务器模式）===")
        print(f"请在浏览器中访问: http://{host}:{port}")
        print("=================\n")
        
        # 使用waitress作为生产服务器，配置日志
        from waitress import serve
        serve(
            app, 
            host=host, 
            port=port,
            # 配置waitress日志，减少混乱
            log_socket_errors=False,
            # 设置最大请求体大小
            max_request_body_size=100 * 1024 * 1024,  # 100MB
            # 设置连接超时
            connection_limit=1000,
            # 设置线程数
            threads=4
        )

    except Exception as e:
        logger.exception(e)
        logger.error(f"catch exception when running web server: {str(e)}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(run_server()) 