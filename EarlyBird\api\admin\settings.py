"""
作者名：EarlyBird
作者邮箱：<EMAIL>
官方网址：https://blog.zaoniao.vip

管理员系统设置API
"""
from flask import Blueprint, request, jsonify
from EarlyBird.common.libs.api_result import ApiResult
from EarlyBird.model.admin import Admin, AdminLog
from EarlyBird.model.payment import PaymentConfig, Payment, ThesisDownloadRecord, PaymentProduct
from EarlyBird.model.setting import Setting
from EarlyBird.common.libs.BaseModel import db
from sqlalchemy import desc, func, inspect
from datetime import datetime, timedelta
import logging
import json
import traceback

logger = logging.getLogger(__name__)

# 创建蓝图
admin_settings_bp = Blueprint('admin_settings', __name__)

# 导入认证装饰器
from .auth import admin_auth_required

@admin_settings_bp.route('', methods=['GET'])
@admin_auth_required
def get_settings():
    """获取系统设置"""
    try:
        admin = request.admin
        
        # 检查权限 - 修复权限检查，使用正确的权限名称
        if not admin.has_permission('system', 'config'):
            logger.warning(f"管理员 {admin.username}(ID:{admin.id}) 没有查看系统设置的权限")
            return ApiResult.error("没有查看系统设置的权限")
        
        # 获取系统基础设置
        system_settings = {}
        system_config = Setting.query.filter_by(category='system').all()
        for config in system_config:
            system_settings[config.key] = config.value
        
        # 获取论文下载价格设置
        thesis_download_settings = {}
        thesis_download_configs = PaymentConfig.query.filter(
            PaymentConfig.config_key.like('thesis.download.%'),
            PaymentConfig.is_deleted == False
        ).all()
        
        for config in thesis_download_configs:
            # 去掉前缀，只保留最后一部分作为键
            key = config.config_key.split('.')[-1]
            thesis_download_settings[key] = config.config_value
        
        # 记录操作日志
        AdminLog.create_log(
            admin_id=admin.id,
            admin_username=admin.username,
            action="view",
            resource="settings",
            description="查看系统设置",
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', '')
        )
        
        return ApiResult.success({
            "system": system_settings,
            "thesis_download": thesis_download_settings
        })
        
    except Exception as e:
        logger.error(f"获取系统设置失败: {str(e)}")
        return ApiResult.error(f"获取系统设置失败: {str(e)}")

@admin_settings_bp.route('', methods=['POST'])
@admin_auth_required
def save_settings():
    """保存系统设置"""
    try:
        admin = request.admin
        
        # 检查权限 - 修复权限检查，使用正确的权限名称
        if not admin.has_permission('system', 'config'):
            logger.warning(f"管理员 {admin.username}(ID:{admin.id}) 没有编辑系统设置的权限")
            return ApiResult.error("没有编辑系统设置的权限")
        
        # 获取请求数据
        data = request.json
        if not data:
            return ApiResult.error("请提供设置数据")
        
        # 保存系统基础设置
        if 'system' in data:
            system_data = data['system']
            for key, value in system_data.items():
                # 查找或创建设置
                setting = Setting.query.filter_by(category='system', key=key).first()
                if setting:
                    setting.value = value
                else:
                    setting = Setting(category='system', key=key, value=value)
                    db.session.add(setting)
        
        # 保存论文下载价格设置
        if 'thesis_download' in data:
            thesis_download_data = data['thesis_download']

            # 配置键映射，确保与后端逻辑一致
            key_mapping = {
                'is_active': 'thesis.download.is_active',
                'enabled': 'thesis.download.is_active',  # 兼容旧的键名
                'price': 'thesis.download.price',
                'first_free': 'thesis.download.first_free',
                'vip_free': 'thesis.download.vip_free'
            }

            for key, value in thesis_download_data.items():
                # 使用映射表获取正确的配置键
                config_key = key_mapping.get(key, f"thesis.download.{key}")

                # 查找或创建配置
                config = PaymentConfig.query.filter_by(config_key=config_key, is_deleted=False).first()
                if config:
                    config.config_value = str(value)
                    logger.info(f"更新配置: {config_key} = {value}")
                else:
                    config = PaymentConfig(
                        name=f"论文下载{key}",
                        config_key=config_key,
                        config_value=str(value),
                        description=f"论文下载{key}配置"
                    )
                    db.session.add(config)
                    logger.info(f"创建配置: {config_key} = {value}")
        
        # 提交更改
        db.session.commit()
        
        # 记录操作日志
        AdminLog.create_log(
            admin_id=admin.id,
            admin_username=admin.username,
            action="edit",
            resource="settings",
            description="保存系统设置",
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', '')
        )
        
        return ApiResult.success(None, "系统设置保存成功")
        
    except Exception as e:
        logger.error(f"保存系统设置失败: {str(e)}")
        db.session.rollback()
        return ApiResult.error(f"保存系统设置失败: {str(e)}")


# 保留/get路由以兼容旧版本
@admin_settings_bp.route('/get', methods=['GET'])
@admin_auth_required
def get_settings_legacy():
    """获取系统设置（旧版本兼容）"""
    return get_settings()


# 保留/save路由以兼容旧版本
@admin_settings_bp.route('/save', methods=['POST'])
@admin_auth_required
def save_settings_legacy():
    """保存系统设置（旧版本兼容）"""
    return save_settings()


@admin_settings_bp.route('/test-api', methods=['POST'])
@admin_auth_required
def test_api_connection():
    """测试API连接"""
    try:
        admin = request.admin
        
        # 检查权限
        if not admin.has_permission('system', 'config'):
            return ApiResult.error("没有测试API连接的权限")
        
        data = request.get_json()
        api_type = data.get('api_type')
        api_key = data.get('api_key')
        api_url = data.get('api_url')
        
        if not api_type or not api_key:
            return ApiResult.error("API类型和密钥不能为空")
        
        # 测试API连接
        try:
            import requests
            
            # 根据API类型构建测试请求
            if api_type == 'qianwen':
                test_url = f"{api_url}/v1/services/aigc/text-generation/generation"
                headers = {
                    "Authorization": f"Bearer {api_key}",
                    "Content-Type": "application/json"
                }
                test_data = {
                    "model": "qwen-max",
                    "input": {
                        "messages": [
                            {"role": "user", "content": "你好"}
                        ]
                    }
                }
            elif api_type == 'kimi':
                test_url = f"{api_url}/v1/chat/completions"
                headers = {
                    "Authorization": f"Bearer {api_key}",
                    "Content-Type": "application/json"
                }
                test_data = {
                    "model": "moonshot-v1-8k",
                    "messages": [
                        {"role": "user", "content": "你好"}
                    ]
                }
            elif api_type == 'deepseek':
                test_url = f"{api_url}/v1/chat/completions"
                headers = {
                    "Authorization": f"Bearer {api_key}",
                    "Content-Type": "application/json"
                }
                test_data = {
                    "model": "deepseek-chat",
                    "messages": [
                        {"role": "user", "content": "你好"}
                    ]
                }
            else:
                return ApiResult.error("不支持的API类型")
            
            # 发送测试请求
            response = requests.post(
                test_url, 
                headers=headers, 
                json=test_data, 
                timeout=10
            )
            
            if response.status_code == 200:
                logger.info(f"API连接测试成功: {api_type}")
                return ApiResult.success(None, "API连接测试成功")
            else:
                logger.warning(f"API连接测试失败: {response.status_code} - {response.text}")
                return ApiResult.error(f"API连接测试失败: {response.status_code} - {response.text}")
                
        except requests.exceptions.RequestException as e:
            logger.error(f"API连接请求异常: {str(e)}")
            return ApiResult.error(f"API连接测试失败: {str(e)}")
        
    except Exception as e:
        logger.error(f"测试API连接失败: {str(e)}")
        logger.error(traceback.format_exc())
        return ApiResult.error(f"测试API连接失败: {str(e)}")


@admin_settings_bp.route('/reset', methods=['POST'])
@admin_auth_required
def reset_settings():
    """重置系统设置"""
    try:
        admin = request.admin
        
        # 检查权限
        if not admin.has_permission('system', 'config'):
            return ApiResult.error("没有重置系统设置的权限")
        
        # 删除所有自定义设置，恢复默认值
        Setting.query.delete()
        db.session.commit()
        
        # 记录操作日志
        AdminLog.create_log(
            admin_id=admin.id,
            admin_username=admin.username,
            action="reset",
            resource="settings",
            description="重置系统设置",
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', '')
        )
        
        logger.info("成功重置系统设置")
        return ApiResult.success(None, "设置重置成功")
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"重置系统设置失败: {str(e)}")
        logger.error(traceback.format_exc())
        return ApiResult.error(f"重置系统设置失败: {str(e)}")


# 支付配置相关API

@admin_settings_bp.route('/payment/config', methods=['GET'])
@admin_auth_required
def get_payment_config():
    """获取支付配置"""
    try:
        logger.info("开始获取支付配置...")
        admin = request.admin
        
        # 检查权限
        if not admin.has_permission('system', 'config'):
            logger.warning(f"管理员 {admin.username}(ID:{admin.id}) 没有查看支付配置的权限")
            return ApiResult.error("没有查看支付配置的权限")
        
        logger.info(f"管理员 {admin.username}(ID:{admin.id}) 正在获取支付配置")
        
        # 尝试获取微信支付配置（新版配置）
        try:
            from EarlyBird.api.admin.wechat_pay_config import get_current_config
            from EarlyBird.model.wechat_pay_config import WeChatPayConfig
            
            logger.info("尝试获取微信支付V3配置...")
            wechat_config_response = get_current_config()
            
            # 如果是ApiResult对象，直接获取数据
            if isinstance(wechat_config_response, dict) and 'success' in wechat_config_response:
                wechat_config_data = wechat_config_response.get('data')
                logger.info(f"成功获取微信支付V3配置: {wechat_config_data is not None}")
                
                # 如果获取到了配置，直接返回
                if wechat_config_data:
                    # 构建响应数据结构
                    response_data = {
                        'wechat_pay_v3': wechat_config_data,
                        'thesis_download': {
                            'is_active': True,
                            'first_free': True,
                            'vip_free': True,
                            'price': 10
                        }
                    }
                    logger.info("返回微信支付V3配置")
                    return ApiResult.success(data=response_data)
            
            logger.info("未找到微信支付V3配置，将尝试获取旧版配置")
        except Exception as e:
            logger.warning(f"获取微信支付V3配置失败: {str(e)}")
            # 继续使用旧版配置
        
        # 检查表是否存在（旧版配置）
        inspector = inspect(db.engine)
        if not inspector.has_table(PaymentConfig.__tablename__):
            logger.warning(f"表 {PaymentConfig.__tablename__} 不存在，将创建该表")
            # 创建表
            PaymentConfig.__table__.create(db.engine)
            logger.info(f"成功创建表 {PaymentConfig.__tablename__}")
            
            # 添加默认配置
            default_configs = [
                # 微信支付配置
                PaymentConfig(name='微信商户ID', config_key='wxpay.pid', config_value='', description='微信支付平台分配的商户ID'),
                PaymentConfig(name='微信异步通知地址', config_key='wxpay.notify_url', config_value='', description='微信支付结果异步通知地址'),
                PaymentConfig(name='微信同步跳转地址', config_key='wxpay.return_url', config_value='', description='微信支付完成后跳转地址'),
                PaymentConfig(name='微信商户私钥', config_key='wxpay.merchant_private_key', config_value='', description='RSA私钥，用于签名'),
                PaymentConfig(name='微信平台公钥', config_key='wxpay.platform_public_key', config_value='', description='RSA公钥，用于验签'),
                PaymentConfig(name='微信AppID', config_key='wxpay.app_id', config_value='', description='微信支付平台分配的AppID'),
                PaymentConfig(name='微信AppSecret', config_key='wxpay.app_secret', config_value='', description='微信支付平台分配的AppSecret'),
                PaymentConfig(name='微信是否启用', config_key='wxpay.is_active', config_value='false', description='是否启用微信支付'),
                
                # 论文下载收费配置
                PaymentConfig(name='论文下载收费是否启用', config_key='thesis.download.is_active', config_value='false', description='是否启用论文下载收费功能'),
                PaymentConfig(name='首次下载是否免费', config_key='thesis.download.first_free', config_value='true', description='用户首次下载论文是否免费'),
                PaymentConfig(name='论文下载收费金额', config_key='thesis.download.price', config_value='10', description='论文下载的收费金额(元)'),
                PaymentConfig(name='VIP用户是否免费下载', config_key='thesis.download.vip_free', config_value='true', description='VIP用户是否可以免费下载论文')
            ]
            
            db.session.bulk_save_objects(default_configs)
            db.session.commit()
            logger.info("成功添加默认支付配置")
        
        # 查询所有支付配置
        configs = PaymentConfig.query.filter_by(is_deleted=False).all()
        
        # 按支付方式分组
        result = {}
        for config in configs:
            parts = config.config_key.split('.')
            if len(parts) >= 2:
                payment_method = parts[0]  # 如 alipay, wxpay, thesis
                config_name = parts[1]     # 如 pid, notify_url, download
                
                if payment_method not in result:
                    result[payment_method] = {}
                
                # 检查是否是JSON格式的嵌套配置
                try:
                    import json
                    json_data = json.loads(config.config_value)
                    if isinstance(json_data, dict):
                        for sub_key, sub_value in json_data.items():
                            if isinstance(sub_value, dict) and 'value' in sub_value:
                                result[payment_method][sub_key] = {
                                    'name': sub_value.get('name', sub_key),
                                    'value': sub_value.get('value', ''),
                                    'description': sub_value.get('description', f'{sub_key} 配置')
                                }
                            else:
                                result[payment_method][sub_key] = {
                                    'name': sub_key,
                                    'value': str(sub_value),
                                    'description': f'{sub_key} 配置'
                                }
                    else:
                        result[payment_method][config_name] = {
                            'name': config.name,
                            'value': config.config_value,
                            'description': config.description
                        }
                except (json.JSONDecodeError, TypeError):
                    result[payment_method][config_name] = {
                        'name': config.name,
                        'value': config.config_value,
                        'description': config.description
                    }
        
        # 确保wxpay包含所有必要的字段
        if 'wxpay' not in result:
            result['wxpay'] = {}
        
        # 确保wxpay包含所有必要的字段
        wxpay_defaults = {
            'app_id': {'name': '微信AppID', 'value': '', 'description': '微信支付平台分配的AppID'},
            'app_secret': {'name': '微信AppSecret', 'value': '', 'description': '微信支付平台分配的AppSecret'},
            'pid': {'name': '微信商户ID', 'value': '', 'description': '微信支付平台分配的商户ID'},
            'merchant_private_key': {'name': '微信商户私钥', 'value': '', 'description': 'RSA私钥，用于签名'},
            'platform_public_key': {'name': '微信平台公钥', 'value': '', 'description': 'RSA公钥，用于验签'},
            'notify_url': {'name': '微信异步通知地址', 'value': '', 'description': '微信支付结果异步通知地址'},
            'return_url': {'name': '微信同步跳转地址', 'value': '', 'description': '微信支付完成后跳转地址'},
            'is_active': {'name': '微信是否启用', 'value': 'false', 'description': '是否启用微信支付'}
        }
        
        for key, default_value in wxpay_defaults.items():
            if key not in result['wxpay']:
                result['wxpay'][key] = default_value
        
        # 兼容前端期望格式
        thesis_cfg = result.get('thesis', {})
        thesis_download = {
            'is_active': thesis_cfg.get('download', {}).get('value', thesis_cfg.get('is_active', {}).get('value', False)),
            'first_free': thesis_cfg.get('first_free', {}).get('value', False),
            'vip_free': thesis_cfg.get('vip_free', {}).get('value', False),
            'price': thesis_cfg.get('price', {}).get('value', 0)
        }
        # 类型转换
        thesis_download['is_active'] = thesis_download['is_active'] in [True, 'true', 'True', 1, '1']
        thesis_download['first_free'] = thesis_download['first_free'] in [True, 'true', 'True', 1, '1']
        thesis_download['vip_free'] = thesis_download['vip_free'] in [True, 'true', 'True', 1, '1']
        try:
            thesis_download['price'] = float(thesis_download['price'])
        except Exception:
            thesis_download['price'] = 0
        
        # 确保返回的数据结构完整
        response_data = {
            'wxpay': result['wxpay'],
            'thesis_download': thesis_download
        }
        
        logger.info(f"支付配置获取成功，wxpay字段包含 {len(result['wxpay'])} 个配置项")
        return ApiResult.success(data=response_data)
        
    except Exception as e:
        logger.error(f"获取支付配置失败: {str(e)}", exc_info=True)
        return ApiResult.error(f"获取支付配置失败: {str(e)}")


@admin_settings_bp.route('/payment/config', methods=['POST'])
@admin_auth_required
def save_payment_config():
    """保存支付配置"""
    try:
        admin = request.admin
        
        # 检查权限
        if not admin.has_permission('system', 'config'):
            logger.warning(f"管理员 {admin.username}(ID:{admin.id}) 没有修改支付配置的权限")
            return ApiResult.error("没有修改支付配置的权限")
        
        # 获取请求数据
        data = request.get_json()
        if not data:
            return ApiResult.error("请求数据为空")
        
        logger.info(f"管理员 {admin.username}(ID:{admin.id}) 正在保存支付配置: {data}")
        
        # 检查表是否存在
        inspector = inspect(db.engine)
        if not inspector.has_table(PaymentConfig.__tablename__):
            logger.warning(f"表 {PaymentConfig.__tablename__} 不存在，将创建该表")
            # 创建表
            PaymentConfig.__table__.create(db.engine)
            logger.info(f"成功创建表 {PaymentConfig.__tablename__}")
        
        # 处理嵌套配置
        def process_nested_config(prefix, config_data):
            """递归处理嵌套配置数据，将嵌套字典序列化为JSON字符串"""
            if isinstance(config_data, dict):
                # 检查是否是配置项对象 (有name, value, description)
                if 'name' in config_data and 'value' in config_data:
                    # 这是一个配置项，直接保存
                    config = PaymentConfig.query.filter_by(config_key=prefix, is_deleted=False).first()
                    
                    if config:
                        # 更新配置
                        config.config_value = config_data['value']
                        logger.info(f"更新配置: {prefix} = {config_data['value']}")
                    else:
                        # 添加配置
                        name = config_data.get('name', prefix)
                        description = config_data.get('description', f"{prefix} 配置")
                        
                        new_config = PaymentConfig(
                            name=name,
                            config_key=prefix,
                            config_value=config_data['value'],
                            description=description
                        )
                        db.session.add(new_config)
                        logger.info(f"添加配置: {prefix} = {config_data['value']}")
                else:
                    # 这是一个嵌套配置，有两种处理方式:
                    # 1. 将整个嵌套配置序列化为JSON字符串保存
                    # 2. 递归处理每个子配置项
                    
                    # 方式1: 序列化为JSON字符串
                    import json
                    config = PaymentConfig.query.filter_by(config_key=prefix, is_deleted=False).first()
                    json_value = json.dumps(config_data, ensure_ascii=False)
                    
                    if config:
                        # 更新配置
                        config.config_value = json_value
                        logger.info(f"更新配置: {prefix} = {json_value}")
                    else:
                        # 添加配置
                        new_config = PaymentConfig(
                            name=prefix.split('.')[-1] if '.' in prefix else prefix,
                            config_key=prefix,
                            config_value=json_value,
                            description=f"{prefix} 配置"
                        )
                        db.session.add(new_config)
                        logger.info(f"添加配置: {prefix} = {json_value}")
                    
                    # 方式2: 递归处理每个子配置项
                    for key, value in config_data.items():
                        new_prefix = f"{prefix}.{key}" if prefix else key
                        process_nested_config(new_prefix, value)
            else:
                # 其他类型直接转字符串
                value = str(config_data)
                config = PaymentConfig.query.filter_by(config_key=prefix, is_deleted=False).first()
                
                if config:
                    config.config_value = value
                    logger.info(f"更新配置: {prefix} = {value}")
                else:
                    new_config = PaymentConfig(
                        name=prefix.split('.')[-1] if '.' in prefix else prefix,
                        config_key=prefix,
                        config_value=value,
                        description=f"{prefix} 配置"
                    )
                    db.session.add(new_config)
                    logger.info(f"添加配置: {prefix} = {value}")
        
        # 处理根级配置
        for key, value in data.items():
            process_nested_config(key, value)
        
        db.session.commit()
        logger.info("保存支付配置成功")
        
        return ApiResult.success(message="保存支付配置成功")
    except Exception as e:
        db.session.rollback()
        logger.error(f"保存支付配置失败: {str(e)}", exc_info=True)
        return ApiResult.error(f"保存支付配置失败: {str(e)}")


@admin_settings_bp.route('/payment/products', methods=['GET'])
@admin_auth_required
def get_payment_products():
    """获取支付商品列表"""
    try:
        admin = request.admin
        
        # 检查权限
        if not admin.has_permission('system', 'config'):
            return ApiResult.error("没有查看支付商品的权限")
        
        # 查询参数
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 10))
        
        # 检查表是否存在
        inspector = inspect(db.engine)
        if not inspector.has_table(PaymentProduct.__tablename__):
            logger.warning(f"表 {PaymentProduct.__tablename__} 不存在，将创建该表")
            # 创建表
            PaymentProduct.__table__.create(db.engine)
            logger.info(f"成功创建表 {PaymentProduct.__tablename__}")
            
            # 添加示例商品
            default_products = [
                PaymentProduct(
                    name='标准会员月卡',
                    product_id='VIP_MONTH',
                    price=39.90,
                    discount_price=29.90,
                    description='标准会员月卡，享受30天会员权益',
                    vip_level=1,
                    vip_days=30,
                    is_active=True,
                    sort_order=1
                ),
                PaymentProduct(
                    name='标准会员季卡',
                    product_id='VIP_QUARTER',
                    price=99.90,
                    discount_price=89.90,
                    description='标准会员季卡，享受90天会员权益',
                    vip_level=1,
                    vip_days=90,
                    is_active=True,
                    sort_order=2
                ),
                PaymentProduct(
                    name='标准会员年卡',
                    product_id='VIP_YEAR',
                    price=298.00,
                    discount_price=258.00,
                    description='标准会员年卡，享受365天会员权益',
                    vip_level=1,
                    vip_days=365,
                    is_active=True,
                    sort_order=3
                )
            ]
            
            for product in default_products:
                db.session.add(product)
            
            db.session.commit()
            logger.info("成功添加示例商品数据")
        
        # 查询商品列表
        query = PaymentProduct.query
        
        # 排序
        query = query.order_by(PaymentProduct.sort_order.asc(), PaymentProduct.id.asc())
        
        # 分页
        total = query.count()
        products = query.offset((page - 1) * limit).limit(limit).all()
        
        # 转换为字典列表
        product_list = [product.to_dict() for product in products]
        
        # 记录操作日志
        AdminLog.create_log(
            admin_id=admin.id,
            admin_username=admin.username,
            action="view",
            resource="payment_products",
            description="查看支付商品列表",
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', '')
        )
        
        logger.info(f"成功获取支付商品列表，返回{len(product_list)}个商品")
        return ApiResult.success({
            'list': product_list,
            'total': total
        })
        
    except Exception as e:
        logger.error(f"获取支付商品列表失败: {str(e)}")
        logger.error(traceback.format_exc())
        return ApiResult.error(f"获取支付商品列表失败: {str(e)}")


@admin_settings_bp.route('/payment/products', methods=['POST'])
@admin_auth_required
def create_payment_product():
    """创建支付商品"""
    try:
        admin = request.admin
        
        # 检查权限
        if not admin.has_permission('system', 'config'):
            return ApiResult.error("没有创建支付商品的权限")
        
        data = request.get_json()
        if not data:
            return ApiResult.error("请求数据不能为空")
        
        # 验证必填字段
        required_fields = ['name', 'product_id', 'price']
        for field in required_fields:
            if field not in data or not data[field]:
                return ApiResult.error(f"缺少必填字段: {field}")
        
        # 检查商品ID是否已存在
        if PaymentProduct.query.filter_by(product_id=data['product_id']).first():
            return ApiResult.error(f"商品ID已存在: {data['product_id']}")
        
        # 创建商品
        product = PaymentProduct()
        product.name = data['name']
        product.product_id = data['product_id']
        product.price = data['price']
        product.discount_price = data.get('discount_price')
        product.description = data.get('description', '')
        product.vip_level = data.get('vip_level')
        product.vip_days = data.get('vip_days')
        product.is_active = data.get('is_active', True)
        product.sort_order = data.get('sort_order', 0)
        
        db.session.add(product)
        db.session.commit()
        
        # 记录操作日志
        AdminLog.create_log(
            admin_id=admin.id,
            admin_username=admin.username,
            action="create",
            resource="payment_product",
            description=f"创建支付商品: {product.name}",
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', '')
        )
        
        logger.info(f"成功创建支付商品: {product.name}")
        return ApiResult.success(product.to_dict(), "商品创建成功")
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"创建支付商品失败: {str(e)}")
        logger.error(traceback.format_exc())
        return ApiResult.error(f"创建支付商品失败: {str(e)}")


@admin_settings_bp.route('/payment/products/<int:product_id>', methods=['PUT'])
@admin_auth_required
def update_payment_product(product_id):
    """更新支付商品"""
    try:
        admin = request.admin
        
        # 检查权限
        if not admin.has_permission('system', 'config'):
            return ApiResult.error("没有修改支付商品的权限")
        
        # 查找商品
        product = PaymentProduct.query.get(product_id)
        if not product:
            return ApiResult.error("商品不存在")
        
        data = request.get_json()
        if not data:
            return ApiResult.error("请求数据不能为空")
        
        # 更新商品信息
        if 'name' in data:
            product.name = data['name']
        if 'price' in data:
            product.price = data['price']
        if 'discount_price' in data:
            product.discount_price = data['discount_price']
        if 'description' in data:
            product.description = data['description']
        if 'vip_level' in data:
            product.vip_level = data['vip_level']
        if 'vip_days' in data:
            product.vip_days = data['vip_days']
        if 'is_active' in data:
            product.is_active = data['is_active']
        if 'sort_order' in data:
            product.sort_order = data['sort_order']
        
        db.session.commit()
        
        # 记录操作日志
        AdminLog.create_log(
            admin_id=admin.id,
            admin_username=admin.username,
            action="update",
            resource="payment_product",
            description=f"更新支付商品: {product.name}",
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', '')
        )
        
        logger.info(f"成功更新支付商品: {product.name}")
        return ApiResult.success(product.to_dict(), "商品更新成功")
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"更新支付商品失败: {str(e)}")
        logger.error(traceback.format_exc())
        return ApiResult.error(f"更新支付商品失败: {str(e)}")


@admin_settings_bp.route('/payment/products/<int:product_id>', methods=['DELETE'])
@admin_auth_required
def delete_payment_product(product_id):
    """删除支付商品"""
    try:
        admin = request.admin
        
        # 检查权限
        if not admin.has_permission('system', 'config'):
            return ApiResult.error("没有删除支付商品的权限")
        
        # 查找商品
        product = PaymentProduct.query.get(product_id)
        if not product:
            return ApiResult.error("商品不存在")
        
        product_name = product.name
        
        # 删除商品
        db.session.delete(product)
        db.session.commit()
        
        # 记录操作日志
        AdminLog.create_log(
            admin_id=admin.id,
            admin_username=admin.username,
            action="delete",
            resource="payment_product",
            description=f"删除支付商品: {product_name}",
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', '')
        )
        
        logger.info(f"成功删除支付商品: {product_name}")
        return ApiResult.success(None, "商品删除成功")
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"删除支付商品失败: {str(e)}")
        logger.error(traceback.format_exc())
        return ApiResult.error(f"删除支付商品失败: {str(e)}")


@admin_settings_bp.route('/payment/test', methods=['POST'])
@admin_auth_required
def test_payment_connection():
    """测试支付连接"""
    try:
        admin = request.admin
        
        # 检查权限
        if not admin.has_permission('system', 'config'):
            logger.warning(f"管理员 {admin.username}(ID:{admin.id}) 没有测试支付连接的权限")
            return ApiResult.error("没有测试支付连接的权限")
        
        # 获取请求数据
        data = request.get_json()
        if not data:
            return ApiResult.error("请求数据为空")
        
        payment_method = data.get('payment_method', 'wxpay')
        config = data.get('config')
        
        if not config:
            return ApiResult.error("参数错误，缺少配置信息")
        
        logger.info(f"管理员 {admin.username}(ID:{admin.id}) 正在测试支付连接: {payment_method}")
        
        # 只支持微信支付测试
        if payment_method == 'wxpay':
            return test_wxpay_connection(config)
        else:
            return ApiResult.error(f"不支持的支付方式: {payment_method}")
    except Exception as e:
        logger.error(f"测试支付连接失败: {str(e)}", exc_info=True)
        return ApiResult.error(f"测试支付连接失败: {str(e)}")


def test_wxpay_connection(config):
    """测试微信支付连接"""
    try:
        # 检查必要的配置项
        required_fields = ['app_id', 'app_secret', 'pid']
        for field in required_fields:
            if not config.get(field):
                return ApiResult.error(f"微信支付配置缺少必要字段: {field}")
        
        # 这里可以添加实际的微信支付连接测试逻辑
        # 例如：调用微信支付API验证配置是否正确
        
        return ApiResult.success("微信支付连接测试成功", {
            'url': 'https://pay.weixin.qq.com/v3/partner/transactions/jsapi',
            'message': '配置验证通过，可以正常使用微信支付'
        })
    except Exception as e:
        logger.error(f"微信支付连接测试失败: {str(e)}")
        return ApiResult.error(f"微信支付连接测试失败: {str(e)}")


# 移除其他支付方式的测试函数
# def test_v990r_connection(config):
# def test_alipay_connection(config):
# def test_unionpay_connection(config):
# def test_paypal_connection(config): 

@admin_settings_bp.route('/payment/orders', methods=['GET'])
@admin_auth_required
def get_payment_orders():
    """获取支付订单列表"""
    try:
        admin = request.admin
        
        # 检查权限
        if not admin.has_permission('system', 'config'):
            return ApiResult.error("没有查看支付订单的权限")
        
        # 获取查询参数
        page = int(request.args.get('page', 1))
        size = int(request.args.get('size', 20))
        status = request.args.get('status', '')
        user_id = request.args.get('user_id', '')
        product_id = request.args.get('product_id', '')
        start_date = request.args.get('start_date', '')
        end_date = request.args.get('end_date', '')
        
        # 构建查询
        query = Payment.query
        
        # 筛选条件
        if status:
            try:
                status_int = int(status)
                query = query.filter(Payment.status == status_int)
            except ValueError:
                pass
                
        if user_id:
            try:
                user_id_int = int(user_id)
                query = query.filter(Payment.user_id == user_id_int)
            except ValueError:
                pass
                
        if product_id:
            query = query.filter(Payment.product_id.like(f"%{product_id}%"))
            
        if start_date:
            try:
                start_datetime = datetime.strptime(start_date, "%Y-%m-%d")
                query = query.filter(Payment.create_time >= start_datetime)
            except ValueError:
                pass
                
        if end_date:
            try:
                end_datetime = datetime.strptime(end_date, "%Y-%m-%d") + timedelta(days=1)
                query = query.filter(Payment.create_time < end_datetime)
            except ValueError:
                pass
        
        # 总数
        total = query.count()
        
        # 分页
        orders = query.order_by(desc(Payment.create_time)).offset((page - 1) * size).limit(size).all()
        
        # 转换为字典列表
        result = []
        for order in orders:
            # 查找关联的下载记录
            download_records = ThesisDownloadRecord.query.filter_by(payment_order_id=order.out_trade_no).all()
            download_info = []
            for record in download_records:
                download_info.append({
                    'id': record.id,
                    'thesis_id': record.thesis_id,
                    'is_paid': record.is_paid,
                    'payment_time': record.payment_time.strftime('%Y-%m-%d %H:%M:%S') if record.payment_time else '',
                    'create_time': record.create_time.strftime('%Y-%m-%d %H:%M:%S') if record.create_time else ''
                })
            
            # 订单数据
            order_data = order.to_dict()
            order_data['download_records'] = download_info
            result.append(order_data)
        
        return ApiResult.success({
            'list': result,
            'total': total,
            'page': page,
            'size': size
        }, "获取支付订单列表成功")
        
    except Exception as e:
        logger.error(f"获取支付订单列表失败: {str(e)}")
        logger.error(traceback.format_exc())
        return ApiResult.error(f"获取支付订单列表失败: {str(e)}")

@admin_settings_bp.route('/payment/order/<int:order_id>', methods=['GET'])
@admin_auth_required
def get_payment_order_detail(order_id):
    """获取支付订单详情"""
    try:
        admin = request.admin
        
        # 检查权限
        if not admin.has_permission('system', 'config'):
            return ApiResult.error("没有查看支付订单的权限")
        
        # 查询订单
        order = Payment.query.get(order_id)
        if not order:
            return ApiResult.error("订单不存在")
        
        # 查找关联的下载记录
        download_records = ThesisDownloadRecord.query.filter_by(payment_order_id=order.out_trade_no).all()
        download_info = []
        for record in download_records:
            download_info.append({
                'id': record.id,
                'thesis_id': record.thesis_id,
                'is_paid': record.is_paid,
                'payment_time': record.payment_time.strftime('%Y-%m-%d %H:%M:%S') if record.payment_time else '',
                'create_time': record.create_time.strftime('%Y-%m-%d %H:%M:%S') if record.create_time else ''
            })
        
        # 订单数据
        order_data = order.to_dict()
        order_data['download_records'] = download_info
        
        # 如果有通知数据，解析JSON
        if order.notify_data:
            try:
                import json
                order_data['notify_data_parsed'] = json.loads(order.notify_data)
            except:
                order_data['notify_data_parsed'] = None
        
        return ApiResult.success(order_data, "获取支付订单详情成功")
        
    except Exception as e:
        logger.error(f"获取支付订单详情失败: {str(e)}")
        logger.error(traceback.format_exc())
        return ApiResult.error(f"获取支付订单详情失败: {str(e)}")

@admin_settings_bp.route('/payment/stats', methods=['GET'])
@admin_auth_required
def get_payment_stats():
    """获取支付统计数据"""
    try:
        admin = request.admin
        
        # 检查权限
        if not admin.has_permission('system', 'config'):
            return ApiResult.error("没有查看支付统计的权限")
        
        # 获取时间范围
        days = int(request.args.get('days', 30))
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        # 总支付订单数
        total_orders = Payment.query.count()
        
        # 成功支付订单数
        paid_orders = Payment.query.filter(Payment.status == 1).count()
        
        # 总支付金额
        from sqlalchemy import func
        total_amount = db.session.query(func.sum(Payment.amount)).filter(Payment.status == 1).scalar() or 0
        
        # 时间段内的支付订单数
        period_orders = Payment.query.filter(
            Payment.create_time >= start_date,
            Payment.create_time <= end_date
        ).count()
        
        # 时间段内的成功支付订单数
        period_paid_orders = Payment.query.filter(
            Payment.create_time >= start_date,
            Payment.create_time <= end_date,
            Payment.status == 1
        ).count()
        
        # 时间段内的支付金额
        period_amount = db.session.query(func.sum(Payment.amount)).filter(
            Payment.create_time >= start_date,
            Payment.create_time <= end_date,
            Payment.status == 1
        ).scalar() or 0
        
        # 按日期统计支付订单数和金额
        daily_stats = []
        current_date = start_date
        while current_date <= end_date:
            next_date = current_date + timedelta(days=1)
            
            # 当日订单数
            day_orders = Payment.query.filter(
                Payment.create_time >= current_date,
                Payment.create_time < next_date
            ).count()
            
            # 当日成功支付订单数
            day_paid_orders = Payment.query.filter(
                Payment.create_time >= current_date,
                Payment.create_time < next_date,
                Payment.status == 1
            ).count()
            
            # 当日支付金额
            day_amount = db.session.query(func.sum(Payment.amount)).filter(
                Payment.create_time >= current_date,
                Payment.create_time < next_date,
                Payment.status == 1
            ).scalar() or 0
            
            daily_stats.append({
                'date': current_date.strftime('%Y-%m-%d'),
                'orders': day_orders,
                'paid_orders': day_paid_orders,
                'amount': float(day_amount)
            })
            
            current_date = next_date
        
        return ApiResult.success({
            'total_orders': total_orders,
            'paid_orders': paid_orders,
            'total_amount': float(total_amount),
            'period_orders': period_orders,
            'period_paid_orders': period_paid_orders,
            'period_amount': float(period_amount),
            'daily_stats': daily_stats
        }, "获取支付统计数据成功")
        
    except Exception as e:
        logger.error(f"获取支付统计数据失败: {str(e)}")
        logger.error(traceback.format_exc())
        return ApiResult.error(f"获取支付统计数据失败: {str(e)}") 