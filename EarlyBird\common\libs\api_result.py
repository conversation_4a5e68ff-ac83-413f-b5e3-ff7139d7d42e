from typing import Any
import math


class PageNav:
    def __init__(self, data=[], pageNo=0, pageSize=10, totalCount=0) -> None:
        self.data = data
        self.pageNo = pageNo
        self.pageSize = pageSize
        self.totalPage = (0,)
        self.totalCount = totalCount

    def dict(self):
        return {
            "data": self.data,
            "pageNo": self.pageNo,
            "pageSize": self.pageSize,
            "totalPage": math.ceil(self.totalCount / self.pageSize),
            "totalCount": self.totalCount,
        }


class Result:
    def __init__(self, is_ok=True, message="") -> None:
        self.is_ok: bool = is_ok  # 表示是否成功，用来判断
        self.message: str = message  # 如果错误，这是错误信息，
        self.data: Any = None  # 如果要携带返回数据，放在这个字段里

    def error(self, message=""):
        """设置错误信息并返回新的Result对象"""
        return Result(False, message)

    def setData(self, data):
        self.data = data
        return self

    def is_success(self) -> bool:
        return self.is_ok

    def isSucc(self) -> bool:
        return self.is_ok

    @staticmethod
    def success(data=None, message=""):
        """创建成功结果"""
        result = Result(True, message)
        result.data = data
        return result

    @staticmethod
    def fail(message=""):
        """创建失败结果"""
        return Result(False, message)

    def __str__(self):
        return f"[isSuc:{self.is_ok},message:{self.message},data:{self.data}]"


class ApiResponse:
    CODE_OK = 0
    CODE_ERROR = 1
    CODE_NEED_LOGIN = 99

    def __init__(self, code=CODE_OK, message="", data=None, ext=None) -> None:
        self.code = code
        self.message = message
        self.data = data if data is not None else []
        self.ext = ext

    def needLogin(self):
        self.code = self.CODE_NEED_LOGIN
        self.message = "请先登录成功后再调用此函数"
        return self

    def error(self, msessage=""):
        self.code = self.CODE_ERROR
        self.message = msessage
        return self

    def set_data(self, data):
        self.data = data if data is not None else []
        return self

    def set_ext(self, ext):
        self.ext = ext
        return self

    def json(self):
        result = {
            "is_success": True if self.code == self.CODE_OK else False,
            "code": self.code,
            "message": self.message,
            "data": self.data if self.data is not None else []
        }

        if self.ext:
            result.update(self.ext)

        return result


class ApiResult:
    """后台管理API统一响应格式"""
    
    @staticmethod
    def success(data=None, message="操作成功"):
        """成功响应"""
        return {
            "success": True,
            "code": 200,
            "message": message,
            "data": data
        }
    
    @staticmethod
    def error(message="操作失败", code=400):
        """错误响应"""
        # 确保message不为空
        if not message:
            message = "操作失败"
        
        return {
            "success": False,
            "code": code,
            "message": message,
            "data": None
        }
    
    @staticmethod
    def to_response(result_dict):
        """将结果字典转换为Flask响应对象，支持自定义响应头"""
        from flask import jsonify, make_response
        response = make_response(jsonify(result_dict))
        return response
