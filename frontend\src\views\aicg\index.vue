<template>
  <div class="aicg-container">
    <iframe 
      src="https://ai.zaoniao.vip/" 
      class="aicg-iframe"
      ref="aicgIframe"
      frameborder="0" 
      allowfullscreen
    ></iframe>
  </div>
</template>

<script>
export default {
  name: "AICG应用",
  data() {
    return {
      isLoading: true
    };
  },
  mounted() {
    // 监听iframe加载完成事件
    if (this.$refs.aicgIframe) {
      this.$refs.aicgIframe.onload = () => {
        this.isLoading = false;
      };
    }
  }
};
</script>

<style lang="scss" scoped>
.aicg-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
  
  .aicg-iframe {
    width: 100%;
    height: 100%;
    border: none;
    position: absolute;
    top: 0;
    left: 0;
  }
}
</style> 