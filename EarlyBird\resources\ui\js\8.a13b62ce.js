"use strict";(self["webpackChunkopen_essay_gui"]=self["webpackChunkopen_essay_gui"]||[]).push([[8],{5008:(e,a,i)=>{i.r(a),i.d(a,{default:()=>m});var o=function(){var e=this,a=e._self._c;return a("div",{staticClass:"model-config"},[a("h2",[e._v("大模型密钥配置")]),a("el-form",{staticStyle:{"max-width":"600px"},attrs:{model:e.form,"label-width":"140px"}},[a("el-form-item",{attrs:{label:"当前模型"}},[a("el-select",{attrs:{placeholder:"请选择当前模型"},model:{value:e.form.modelName,callback:function(a){e.$set(e.form,"modelName",a)},expression:"form.modelName"}},[a("el-option",{attrs:{label:"千问 (qianwen)",value:"qianwen"}}),a("el-option",{attrs:{label:"Kimi (kimi)",value:"kimi"}}),a("el-option",{attrs:{label:"DeepSeek (deepseek)",value:"deepseek"}}),a("el-option",{attrs:{label:"豆包 (doubao)",value:"doubao"}}),a("el-option",{attrs:{label:"OpenAI (openai)",value:"openai"}})],1)],1),a("el-form-item",{attrs:{label:"千问API Key"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入千问API Key"},model:{value:e.form.apikeyQianwen,callback:function(a){e.$set(e.form,"apikeyQianwen",a)},expression:"form.apikeyQianwen"}})],1),a("el-form-item",{attrs:{label:"DeepSeek API Key"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入DeepSeek API Key"},model:{value:e.form.apikeyDeepSeekR1,callback:function(a){e.$set(e.form,"apikeyDeepSeekR1",a)},expression:"form.apikeyDeepSeekR1"}})],1),a("el-form-item",{attrs:{label:"Kimi API Key"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入Kimi API Key"},model:{value:e.form.apikeyKimi,callback:function(a){e.$set(e.form,"apikeyKimi",a)},expression:"form.apikeyKimi"}})],1),a("el-form-item",{attrs:{label:"豆包API Key"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入豆包API Key"},model:{value:e.form.apikeyDoubao,callback:function(a){e.$set(e.form,"apikeyDoubao",a)},expression:"form.apikeyDoubao"}})],1),a("el-form-item",{attrs:{label:"OpenAI API Key"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入OpenAI API Key"},model:{value:e.form.apikeyOpenai,callback:function(a){e.$set(e.form,"apikeyOpenai",a)},expression:"form.apikeyOpenai"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",loading:e.saving},on:{click:e.onSave}},[e._v("保存大模型密钥")])],1)],1)],1)},l=[],t=i(663);const s={name:"ModelConfig",data(){return{form:{modelName:"",apikeyQianwen:"",apikeyDeepSeekR1:"",apikeyKimi:"",apikeyDoubao:"",apikeyOpenai:""},saving:!1}},created(){this.loadConfig()},methods:{async loadConfig(){try{const e=await(0,t.P)();if(e&&(e.success||e.is_success)&&e.data){const a=e.data;this.form.modelName=a.modelName||"",this.form.apikeyQianwen=a.apikeyQianwen||"",this.form.apikeyDeepSeekR1=a.apikeyDeepSeekR1||"",this.form.apikeyKimi=a.apikeyKimi||"",this.form.apikeyDoubao=a.apikeyDoubao||"",this.form.apikeyOpenai=a.apikeyOpenai||""}}catch(e){this.$message.error("加载大模型密钥配置失败")}},async onSave(){this.saving=!0;try{const e={setting:{modelName:this.form.modelName,apikeyQianwen:this.form.apikeyQianwen,apikeyDeepSeekR1:this.form.apikeyDeepSeekR1,apikeyKimi:this.form.apikeyKimi,apikeyDoubao:this.form.apikeyDoubao,apikeyOpenai:this.form.apikeyOpenai}},a=await(0,t.k)(e);a&&(a.success||a.is_success)?(this.$message.success("大模型密钥保存成功"),this.loadConfig()):this.$message.error(a.message||"大模型密钥保存失败")}catch(e){this.$message.error("大模型密钥保存异常")}finally{this.saving=!1}}}},r=s;var p=i(1656),n=(0,p.A)(r,o,l,!1,null,"4d59f30c",null);const m=n.exports}}]);