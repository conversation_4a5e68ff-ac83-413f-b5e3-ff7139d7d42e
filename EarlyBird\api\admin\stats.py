from flask import Blueprint, request, jsonify
from EarlyBird.common.libs.api_result import ApiResult
from EarlyBird.model.admin import Admin, AdminLog
from EarlyBird.model.user import User
from EarlyBird.model.thesis import Thesis
from EarlyBird.model.chat_log import ChatLog
from EarlyBird.common.libs.BaseModel import db
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

# 创建蓝图
admin_stats_bp = Blueprint('admin_stats', __name__)

# 导入认证装饰器
from .auth import admin_auth_required


@admin_stats_bp.route('/overview', methods=['GET'])
@admin_auth_required
def stats_overview():
    """数据概览"""
    try:
        admin = request.admin
        
        # 检查权限
        if not admin.has_permission('system', 'stats'):
            return ApiResult.error("没有查看系统统计的权限")
        
        # 获取时间范围
        now = datetime.now()
        today = now.replace(hour=0, minute=0, second=0, microsecond=0)
        yesterday = today - timedelta(days=1)
        week_ago = today - timedelta(days=7)
        month_ago = today - timedelta(days=30)
        
        # 基础统计数据
        total_users = User.query.count()
        total_thesis = Thesis.query.count()
        total_chat_logs = ChatLog.query.count()
        
        # 今日数据
        today_users = User.query.filter(User.create_time >= today).count()
        today_thesis = Thesis.query.filter(Thesis.create_time >= today).count()
        today_chat_logs = ChatLog.query.filter(ChatLog.create_time >= today).count()
        
        # 昨日数据
        yesterday_users = User.query.filter(
            User.create_time >= yesterday,
            User.create_time < today
        ).count()
        yesterday_thesis = Thesis.query.filter(
            Thesis.create_time >= yesterday,
            Thesis.create_time < today
        ).count()
        
        # VIP用户统计
        vip_users = User.query.filter(User.vip_expire_at > now).count()
        expired_vip_users = User.query.filter(
            (User.vip_expire_at <= now) | (User.vip_expire_at.is_(None))
        ).count()
        
        # 活跃用户（7天内有登录）
        active_users = User.query.filter(
            User.last_login_time >= week_ago
        ).count()
        
        # 记录操作日志
        AdminLog.create_log(
            admin_id=admin.id,
            admin_username=admin.username,
            action="view",
            resource="stats",
            description="查看数据概览",
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', '')
        )
        
        return ApiResult.success({
            "overview": {
                "total_users": total_users,
                "total_thesis": total_thesis,
                "total_chat_logs": total_chat_logs,
                "vip_users": vip_users,
                "expired_vip_users": expired_vip_users,
                "active_users": active_users
            },
            "today": {
                "new_users": today_users,
                "new_thesis": today_thesis,
                "chat_logs": today_chat_logs
            },
            "yesterday": {
                "new_users": yesterday_users,
                "new_thesis": yesterday_thesis
            }
        })
        
    except Exception as e:
        logger.error(f"获取数据概览失败: {str(e)}")
        return ApiResult.error("获取数据概览失败")


@admin_stats_bp.route('/users', methods=['GET'])
@admin_auth_required
def stats_users():
    """用户统计"""
    try:
        admin = request.admin
        
        # 检查权限
        if not admin.has_permission('user', 'view'):
            return ApiResult.error("没有查看用户统计的权限")
        
        # 获取统计参数
        period = request.args.get('period', '7d')  # 7d, 30d, 90d
        
        # 计算时间范围
        now = datetime.now()
        if period == '7d':
            start_date = now - timedelta(days=7)
            days = 7
        elif period == '30d':
            start_date = now - timedelta(days=30)
            days = 30
        elif period == '90d':
            start_date = now - timedelta(days=90)
            days = 90
        else:
            start_date = now - timedelta(days=7)
            days = 7
        
        # 基础统计
        total_users = User.query.count()
        vip_users = User.query.filter(User.vip_expire_at > now).count()
        new_users = User.query.filter(User.create_time >= start_date).count()
        locked_users = User.query.filter(User.is_lock == True).count()
        
        # 每日新增用户统计
        daily_stats = []
        for i in range(days):
            date = now - timedelta(days=i)
            day_start = date.replace(hour=0, minute=0, second=0, microsecond=0)
            day_end = day_start + timedelta(days=1)
            
            count = User.query.filter(
                User.create_time >= day_start,
                User.create_time < day_end
            ).count()
            
            daily_stats.append({
                "date": day_start.strftime("%Y-%m-%d"),
                "count": count
            })
        
        # 按性别统计
        gender_stats = db.session.query(
            User.gender,
            db.func.count(User.id).label('count')
        ).group_by(User.gender).all()
        
        # 按VIP等级统计
        vip_level_stats = db.session.query(
            User.vip_level,
            db.func.count(User.id).label('count')
        ).group_by(User.vip_level).all()
        
        # 记录操作日志
        AdminLog.create_log(
            admin_id=admin.id,
            admin_username=admin.username,
            action="stats",
            resource="user",
            description=f"查看用户统计，周期:{period}",
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', '')
        )
        
        return ApiResult.success({
            "total_users": total_users,
            "vip_users": vip_users,
            "new_users": new_users,
            "locked_users": locked_users,
            "daily_stats": list(reversed(daily_stats)),  # 按时间正序
            "gender_stats": [{"gender": gender, "count": count} for gender, count in gender_stats],
            "vip_level_stats": [{"level": level, "count": count} for level, count in vip_level_stats],
            "period": period
        })
        
    except Exception as e:
        logger.error(f"获取用户统计失败: {str(e)}")
        return ApiResult.error("获取用户统计失败")


@admin_stats_bp.route('/thesis', methods=['GET'])
@admin_auth_required
def stats_thesis():
    """论文统计"""
    try:
        admin = request.admin
        
        # 检查权限
        if not admin.has_permission('thesis', 'stats'):
            return ApiResult.error("没有查看论文统计的权限")
        
        # 获取统计参数
        period = request.args.get('period', '7d')  # 7d, 30d, 90d
        
        # 计算时间范围
        now = datetime.now()
        if period == '7d':
            start_date = now - timedelta(days=7)
            days = 7
        elif period == '30d':
            start_date = now - timedelta(days=30)
            days = 30
        elif period == '90d':
            start_date = now - timedelta(days=90)
            days = 90
        else:
            start_date = now - timedelta(days=7)
            days = 7
        
        # 基础统计
        total_thesis = Thesis.query.count()
        new_thesis = Thesis.query.filter(Thesis.create_time >= start_date).count()
        
        # 每日新增论文统计
        daily_stats = []
        for i in range(days):
            date = now - timedelta(days=i)
            day_start = date.replace(hour=0, minute=0, second=0, microsecond=0)
            day_end = day_start + timedelta(days=1)
            
            count = Thesis.query.filter(
                Thesis.create_time >= day_start,
                Thesis.create_time < day_end
            ).count()
            
            daily_stats.append({
                "date": day_start.strftime("%Y-%m-%d"),
                "count": count
            })
        
        # 按语言统计
        lang_stats = db.session.query(
            Thesis.lang,
            db.func.count(Thesis.id).label('count')
        ).group_by(Thesis.lang).all()
        
        # 按级别统计
        level_stats = db.session.query(
            Thesis.level,
            db.func.count(Thesis.id).label('count')
        ).group_by(Thesis.level).all()
        
        # 按长度统计
        length_stats = db.session.query(
            Thesis.length,
            db.func.count(Thesis.id).label('count')
        ).group_by(Thesis.length).all()
        
        # 记录操作日志
        AdminLog.create_log(
            admin_id=admin.id,
            admin_username=admin.username,
            action="stats",
            resource="thesis",
            description=f"查看论文统计，周期:{period}",
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', '')
        )
        
        return ApiResult.success({
            "total_thesis": total_thesis,
            "new_thesis": new_thesis,
            "daily_stats": list(reversed(daily_stats)),  # 按时间正序
            "lang_stats": [{"lang": lang, "count": count} for lang, count in lang_stats],
            "level_stats": [{"level": level, "count": count} for level, count in level_stats],
            "length_stats": [{"length": length, "count": count} for length, count in length_stats],
            "period": period
        })
        
    except Exception as e:
        logger.error(f"获取论文统计失败: {str(e)}")
        return ApiResult.error("获取论文统计失败")


@admin_stats_bp.route('/chat', methods=['GET'])
@admin_auth_required
def stats_chat():
    """聊天记录统计"""
    try:
        admin = request.admin
        
        # 检查权限
        if not admin.has_permission('system', 'stats'):
            return ApiResult.error("没有查看系统统计的权限")
        
        # 获取统计参数
        period = request.args.get('period', '7d')  # 7d, 30d, 90d
        
        # 计算时间范围
        now = datetime.now()
        if period == '7d':
            start_date = now - timedelta(days=7)
            days = 7
        elif period == '30d':
            start_date = now - timedelta(days=30)
            days = 30
        elif period == '90d':
            start_date = now - timedelta(days=90)
            days = 90
        else:
            start_date = now - timedelta(days=7)
            days = 7
        
        # 基础统计
        total_chat_logs = ChatLog.query.count()
        new_chat_logs = ChatLog.query.filter(ChatLog.create_time >= start_date).count()
        
        # 每日聊天记录统计
        daily_stats = []
        for i in range(days):
            date = now - timedelta(days=i)
            day_start = date.replace(hour=0, minute=0, second=0, microsecond=0)
            day_end = day_start + timedelta(days=1)
            
            count = ChatLog.query.filter(
                ChatLog.create_time >= day_start,
                ChatLog.create_time < day_end
            ).count()
            
            daily_stats.append({
                "date": day_start.strftime("%Y-%m-%d"),
                "count": count
            })
        
        # 按模型统计
        model_stats = db.session.query(
            ChatLog.modelName,
            db.func.count(ChatLog.id).label('count')
        ).group_by(ChatLog.modelName).all()
        
        # 按类型统计
        type_stats = db.session.query(
            ChatLog.type,
            db.func.count(ChatLog.id).label('count')
        ).group_by(ChatLog.type).all()
        
        # 记录操作日志
        AdminLog.create_log(
            admin_id=admin.id,
            admin_username=admin.username,
            action="stats",
            resource="chat",
            description=f"查看聊天记录统计，周期:{period}",
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', '')
        )
        
        return ApiResult.success({
            "total_chat_logs": total_chat_logs,
            "new_chat_logs": new_chat_logs,
            "daily_stats": list(reversed(daily_stats)),  # 按时间正序
            "model_stats": [{"model": model, "count": count} for model, count in model_stats],
            "type_stats": [{"type": type_name, "count": count} for type_name, count in type_stats],
            "period": period
        })
        
    except Exception as e:
        logger.error(f"获取聊天记录统计失败: {str(e)}")
        return ApiResult.error("获取聊天记录统计失败") 