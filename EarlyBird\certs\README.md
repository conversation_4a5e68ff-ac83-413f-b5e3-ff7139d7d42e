# 微信支付证书目录

## 文件说明

请将以下微信支付相关证书文件放置在此目录：

### 必需文件

1. **apiclient_key.pem** - 商户API私钥文件
   - 从微信商户平台下载
   - 用于API请求签名

2. **apiclient_cert.pem** - 商户API证书文件（可选）
   - 从微信商户平台下载
   - 用于API请求签名

### 可选文件

3. **platform_cert.pem** - 微信支付平台证书
   - 用于验证回调通知签名
   - 可通过API获取

## 获取证书步骤

1. 登录微信商户平台：https://pay.weixin.qq.com
2. 进入"账户中心" -> "API安全"
3. 下载API证书和私钥文件
4. 将文件重命名并放置在此目录

## 安全提醒

- 请妥善保管私钥文件，不要泄露给他人
- 建议设置适当的文件权限
- 不要将证书文件提交到版本控制系统

## 配置示例

在环境变量或配置文件中设置：

```bash
WECHAT_APPID=your_appid_here
WECHAT_MCHID=your_mchid_here
WECHAT_API_V3_KEY=your_api_v3_key_here
WECHAT_SERIAL_NO=your_serial_no_here
WECHAT_PRIVATE_KEY_PATH=EarlyBird/certs/apiclient_key.pem
WECHAT_NOTIFY_URL=https://your-domain.com/api/pay/wechat_notify
``` 