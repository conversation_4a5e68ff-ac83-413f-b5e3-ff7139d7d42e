"""
支付相关请求模型
定义支付API的request模型和schema
"""

from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
from decimal import Decimal


class PaymentCreateRequest(BaseModel):
    """创建支付订单请求模型"""
    user_id: int = Field(..., description="用户ID")
    product_id: str = Field(..., description="商品ID")
    pay_type: str = Field(..., description="支付方式")
    amount: float = Field(..., description="支付金额")
    thesis_id: Optional[int] = Field(None, description="论文ID")
    thesis_title: Optional[str] = Field("论文下载", description="论文标题")


class PaymentNotifyRequest(BaseModel):
    """支付回调通知请求模型"""
    out_trade_no: str = Field(..., description="商户订单号")
    trade_no: Optional[str] = Field(None, description="平台订单号")
    total_amount: Optional[str] = Field(None, description="订单金额")
    trade_status: Optional[str] = Field(None, description="交易状态")
    notify_time: Optional[str] = Field(None, description="通知时间")
    notify_type: Optional[str] = Field(None, description="通知类型")
    notify_id: Optional[str] = Field(None, description="通知ID")
    app_id: Optional[str] = Field(None, description="应用ID")
    charset: Optional[str] = Field(None, description="编码格式")
    version: Optional[str] = Field(None, description="接口版本")
    sign_type: Optional[str] = Field(None, description="签名类型")
    sign: Optional[str] = Field(None, description="签名")


class PaymentQueryRequest(BaseModel):
    """支付查询请求模型"""
    out_trade_no: Optional[str] = Field(None, description="商户订单号")
    trade_no: Optional[str] = Field(None, description="平台订单号")


class PaymentRefundRequest(BaseModel):
    """支付退款请求模型"""
    out_trade_no: str = Field(..., description="商户订单号")
    refund_amount: float = Field(..., description="退款金额")
    refund_reason: Optional[str] = Field("用户申请退款", description="退款原因")


class WeChatPayCreateRequest(BaseModel):
    """微信支付创建订单请求模型"""
    description: str = Field(..., description="商品描述")
    out_trade_no: str = Field(..., description="商户订单号")
    amount: int = Field(..., description="订单金额(分)")
    notify_url: Optional[str] = Field(None, description="回调地址")
    attach: Optional[str] = Field(None, description="附加数据")


class WeChatPayNotifyRequest(BaseModel):
    """微信支付回调通知请求模型"""
    id: str = Field(..., description="通知ID")
    create_time: str = Field(..., description="创建时间")
    event_type: str = Field(..., description="事件类型")
    resource_type: str = Field(..., description="资源类型")
    resource: Dict[str, Any] = Field(..., description="资源数据")
    summary: str = Field(..., description="回调摘要")


class V990rPayCreateRequest(BaseModel):
    """V990r支付创建订单请求模型"""
    merchant_id: str = Field(..., description="商户ID")
    out_trade_no: str = Field(..., description="商户订单号")
    subject: str = Field(..., description="订单标题")
    body: Optional[str] = Field(None, description="订单描述")
    total_amount: str = Field(..., description="订单金额")
    pay_type: str = Field(..., description="支付方式")
    notify_url: str = Field(..., description="异步通知地址")
    return_url: Optional[str] = Field(None, description="同步跳转地址")
    timestamp: str = Field(..., description="时间戳")
    version: str = Field("1.0", description="接口版本")
    sign_type: str = Field("MD5", description="签名类型")
    sign: str = Field(..., description="签名")


class V990rPayNotifyRequest(BaseModel):
    """V990r支付回调通知请求模型"""
    merchant_id: str = Field(..., description="商户ID")
    out_trade_no: str = Field(..., description="商户订单号")
    trade_no: str = Field(..., description="平台订单号")
    total_amount: str = Field(..., description="订单金额")
    trade_status: str = Field(..., description="交易状态")
    pay_time: str = Field(..., description="支付时间")
    sign: str = Field(..., description="签名")


class PaymentListRequest(BaseModel):
    """支付订单列表查询请求模型"""
    page: int = Field(1, description="页码")
    size: int = Field(10, description="每页数量")
    user_id: Optional[int] = Field(None, description="用户ID筛选")
    product_id: Optional[str] = Field(None, description="商品ID筛选")
    status: Optional[int] = Field(None, description="支付状态筛选")
    pay_type: Optional[str] = Field(None, description="支付方式筛选")
    start_date: Optional[str] = Field(None, description="开始日期")
    end_date: Optional[str] = Field(None, description="结束日期")


class PaymentStatsRequest(BaseModel):
    """支付统计请求模型"""
    start_date: Optional[str] = Field(None, description="开始日期")
    end_date: Optional[str] = Field(None, description="结束日期")
    group_by: Optional[str] = Field("day", description="分组方式: day, week, month")


class ThesisDownloadPayRequest(BaseModel):
    """论文下载支付请求模型"""
    thesis_id: int = Field(..., description="论文ID")
    payment_method: str = Field(..., description="支付方式")
    user_id: Optional[int] = Field(None, description="用户ID")


class PaymentStatusRequest(BaseModel):
    """支付状态查询请求模型"""
    out_trade_no: str = Field(..., description="商户订单号")
    user_id: Optional[int] = Field(None, description="用户ID")


class PaymentConfirmRequest(BaseModel):
    """支付确认请求模型"""
    out_trade_no: str = Field(..., description="商户订单号")
    user_id: Optional[int] = Field(None, description="用户ID")
