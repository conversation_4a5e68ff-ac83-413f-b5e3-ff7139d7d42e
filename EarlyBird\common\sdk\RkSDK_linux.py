import logging
import time
import json
import threading
import hashlib
import os
import requests
from enum import IntEnum
from typing import List

from .platform_utils import get_machine_id

logger = logging.getLogger(__name__)

# 保持与原始RkSDK相同的类型定义
class BusinessType(IntEnum):
    # 软件初始化
    iniSoftInfo = 1
    # 账号注册
    accountRegister = 2
    # 账号登录
    accountLogin = 3
    # 卡密登录
    cardLogin = 4
    # 心跳
    heartBeat = 5
    # 扣点
    bucklePoint = 6
    # 退出登录
    loginOut = 7
    # 获取软件变量
    getremoteVar = 8
    # 解绑机器码
    unbundMac = 9
    # 修改用户密码
    updPwd = 10
    # 生成测试卡密
    createCard = 11
    # 开通/续费卡密
    openRenewCardNum = 12
    # 开通/续费账号
    openRenewAccount = 13
    # 卡密详情
    cardDetail = 14
    # 账号详情
    accountDetail = 15
    # 获取软件价格列表
    softPriceList = 16
    # 购买卡密
    buyCardNum = 17
    # 修改卡密/账号备注
    updRemark = 18
    # 充值卡充值
    rechCardRenewCardOrAccount = 19
    # 充值卡详情
    rechCardNumDetail = 20
    # 远程算法转发
    remoteCalculate = 21
    # 禁用还是删除：单码或账号
    disableCardOrAccount = 22


class Result:
    code: int = -998
    msg: str = ""
    data: str = ""


class Rk结果心跳失败类:
    错误编码: int = 0
    错误消息: str = ""


class Rk通讯加密方式枚举类(IntEnum):
    DES加密 = 1
    RC4加密 = 3


class Rk禁用还是删除枚举类(IntEnum):
    禁用 = 0
    删除 = 1


class Rk初始化软件入参类:
    平台用户编码: str = ""
    软件编码: str = ""
    通讯加密方式: Rk通讯加密方式枚举类 = Rk通讯加密方式枚举类.DES加密
    加密Key: str = ""
    签名盐: str = ""
    软件版本号: str = ""
    心跳失败的回调函数 = None


class Rk软件信息类:
    软件公告: str = ""
    软件基础数据: str = ""
    软件名称: str = ""
    咨询官网: str = ""
    咨询qq: str = ""
    咨询微信: str = ""
    咨询电话: str = ""
    软件logo图标地址: str = ""
    软件当前最新版本号: str = ""
    软件更新的网盘地址: str = ""
    网盘提取码: str = ""
    软件是否强制更新 = False
    换绑扣除值: int = 0
    单台设备最大登录数量: int = 0
    软件消耗类型: str = ""
    登录方式: str = ""
    登录限制: str = ""
    换绑限制: str = ""


class Rk软件价格详情类:
    价格类型ID: str = ""
    价格类型名称: str = ""
    可使用值: int = 0
    使用值单位: str = ""
    售价: int = 0
    角色ID: str = ""
    角色名: str = ""


class Rk结果基础类:
    错误编码: int = -999
    错误消息: str = "服务器访问失败，您本地网络不顺畅，请检查下（或许您用了代理IP了，当时的代理IP不稳定？）"
    服务器时间戳: int = 0


class Rk结果初始化软件类(Rk结果基础类):
    软件信息: Rk软件信息类 = None
    软件价格列表: List[Rk软件价格详情类] = None


class Rk结果单码登录类(Rk结果基础类):
    到期时间: str = ""
    剩余点数: int = 0
    角色ID: str = ""
    角色名称: str = ""
    终端客户的qq: str = ""
    终端客户的微信: str = ""
    终端客户的支付宝: str = ""
    终端客户的手机号: str = ""
    终端客户的邮箱: str = ""
    备注: str = ""
    开通的价格类型ID: str = ""
    开通的价格类型名称: str = ""


class Rk结果账号登录类(Rk结果基础类):
    到期时间: str = ""
    剩余点数: int = 0
    角色ID: str = ""
    角色名称: str = ""
    终端客户的qq: str = ""
    终端客户的微信: str = ""
    终端客户的支付宝: str = ""
    终端客户的手机号: str = ""
    终端客户的邮箱: str = ""
    备注: str = ""
    开通的价格类型ID: str = ""
    开通的价格类型名称: str = ""


class Rk结果单码详情类(Rk结果基础类):
    到期时间: str = ""
    剩余点数: int = 0
    开通的价格类型ID: str = ""
    开通的价格类名称: str = ""
    终端用户的qq: str = ""
    终端用户的微信: str = ""
    终端用户的支付宝: str = ""
    终端用户的手机号: str = ""
    终端用户的邮箱: str = ""
    备注: str = ""
    是否已开通 = False
    是否已激活 = False
    机器码: str = ""


# 添加缺失的类定义
class Rk结果账号详情类(Rk结果基础类):
    到期时间: str = ""
    剩余点数: int = 0
    开通的价格类型ID: str = ""
    开通的价格类名称: str = ""
    终端用户的qq: str = ""
    终端用户的微信: str = ""
    终端用户的支付宝: str = ""
    终端用户的手机号: str = ""
    终端用户的邮箱: str = ""
    备注: str = ""
    是否已开通 = False
    是否已激活 = False
    机器码: str = ""


class Rk结果扣点类(Rk结果基础类):
    剩余点数: int = 0


class Rk结果解绑机器码类(Rk结果基础类):
    到期时间: str = ""
    剩余点数: int = 0


class Rk结果获取远程变量类(Rk结果基础类):
    变量值: str = ""


class Rk结果获取远程算法类(Rk结果基础类):
    算法结果: str = ""


class Rk结果充值卡详情类(Rk结果基础类):
    所属软件: str = ""
    可使用值: int = 0
    消耗类型: str = ""
    使用状态: str = ""


# 添加RkVar类
class RkVar:
    varname: str = ""
    varvalue: str = ""


# 添加线程锁机制
global lockInitSDK
global lockReq
global lockiniSoft
global lockLogin
global lockheartbeat
global lockloginout
global lockdetail
global lockregaccount
global lockupdaccount
global lockrech
global lockrechdetail
global lockpoint
global lockremarks
global lockunbundmac
global lockgetvarvalue
global lockdisable

lockInitSDK = threading.Lock()
lockReq = threading.Lock()
lockReqHeartBeat = threading.Lock()
lockiniSoft = threading.Lock()
lockLogin = threading.Lock()
lockheartbeat = threading.Lock()
lockloginout = threading.Lock()
lockdetail = threading.Lock()
lockregaccount = threading.Lock()
lockupdaccount = threading.Lock()
lockrech = threading.Lock()
lockrechdetail = threading.Lock()
lockpoint = threading.Lock()
lockremarks = threading.Lock()
lockunbundmac = threading.Lock()
lockgetvarvalue = threading.Lock()
lockgetcalvalue = threading.Lock()
lockdisable = threading.Lock()


class Singleton(object):
    _instances = {}
    
    def __new__(cls, *args, **kwargs):
        lockInitSDK.acquire()
        try:
            if cls not in cls._instances:
                cls._instances[cls] = super(Singleton, cls).__new__(cls)
        finally:
            lockInitSDK.release()
        return cls._instances[cls]


class RkSDK(Singleton):
    """Linux兼容版的RkSDK实现"""
    __maccode = ""  # 机器码
    __config = {}
    __heartbeatReviceError = None
    __encrypttypeid = 0
    __encryptKey = ""
    __signSalt = ""
    __LoginList: List[str] = []
    __RkVarList: List[RkVar] = []  # 添加RkVar列表
    __isIniSoft = False
    __瑞科_软件编码_Md5 = ""
    __currentLoginPwd = ""
    __heartbeatThread: threading.Thread = None

    __loginToken = ""
    __currentLoginCardOrAccount = ""
    __heartbeatKey = ""
    __isLogin = False
    __isLoginOut = False
    __isStartHeartBeat = False
    __isStopHeartBeat = False

    初始化软件结果属性 = None
    
    def __init__(self):
        self.__maccode = get_machine_id()  # 使用platform_utils获取机器ID
        logger.info(f"Linux兼容版RkSDK初始化，机器码: {self.__maccode}")
        
        # 激活信息持久化目录
        self.__data_dir = os.path.join(os.path.expanduser("~"), ".openessay")
        os.makedirs(self.__data_dir, exist_ok=True)
        self.__activation_file = os.path.join(self.__data_dir, "activation.json")
        
        # 尝试从文件加载激活状态
        self.__load_activation()

    def __GetErrorMsg(self, errorCode: int, msg: str = ""):
        """获取错误消息映射"""
        result = ""
        if errorCode == -1:
            result = "软件出错，请联系客服"
        elif errorCode == -2:
            result = "请先调用：初始化软件函数"
        elif errorCode == -3:
            result = "通讯加密方式【你瑞后后台选择什么样的加密方式，此处就填写相对应的加密方式】：只能填写1或3  【1：DES加密  3:RC4加密】"
        elif errorCode == -4:
            result = "请先登录成功后再调用此函数"
        elif errorCode == -5:
            result = "单码登录函数：需要登录的单码不能为空"
        elif errorCode == -6:
            result = "账号登录函数：需要登录的账号和密码都不能为空"
        elif errorCode == -7:
            result = "请先调用退出登录函数，然后才能再次登录"
        elif errorCode == -8:
            result = "当前登录的单码或账号已登录过了【" + msg + "】，请更新一个新的单码或账号再重新登录"
        elif errorCode == -9:
            result = "初始化软件入参：心跳失败的回调函数不能为空"
        elif errorCode == -10:
            result = "注册的账号和密码不能为空"
        elif errorCode == -11:
            result = "注册的账号和密码长度都不能超过15个字符"
        elif errorCode == -12:
            result = "账号，旧密码，新密码都不能为空"
        elif errorCode == -13:
            result = "账号，旧密码，新密码长度都不能超过15个字符"
        elif errorCode == -14:
            result = "被充值的" + msg + ",充值卡号都不能为空"
        elif errorCode == -15:
            result = "查询的充值卡不能为空"
        elif errorCode == -16:
            result = "备注内容不能为空"
        elif errorCode == -17:
            result = "备注内容不能大于500个字符"
        elif errorCode == -18:
            result = "需要解绑的" + msg + "不能为空"
        elif errorCode == -19:
            result = "变量名不能为空"
        elif errorCode == -20:
            result = "算法ID不能为空"
        elif errorCode == -21:
            result = "提交的参数不能为空"
        return result

    def __encrypt(self, text: str) -> str:
        """加密功能 - Linux版本实现RC4加密"""
        if not text:
            return ""
            
        try:
            if self.__encrypttypeid == 3:  # RC4加密
                # 简单的RC4加密实现
                import binascii
                
                # 将密钥转换为字节
                if isinstance(self.__encryptKey, str):
                    key = self.__encryptKey.encode('utf-8')
                else:
                    key = self.__encryptKey
                
                # 简单的RC4加密算法
                def rc4_encrypt(data, key):
                    # 初始化S盒
                    S = list(range(256))
                    j = 0
                    for i in range(256):
                        j = (j + S[i] + key[i % len(key)]) % 256
                        S[i], S[j] = S[j], S[i]
                    
                    # 加密数据
                    i = j = 0
                    result = []
                    for byte in data:
                        i = (i + 1) % 256
                        j = (j + S[i]) % 256
                        S[i], S[j] = S[j], S[i]
                        result.append(byte ^ S[(S[i] + S[j]) % 256])
                    
                    return bytes(result)
                
                # 加密文本
                data = text.encode('utf-8')
                encrypted = rc4_encrypt(data, key)
                return binascii.b2a_hex(encrypted).decode('utf-8')
            else:
                # 其他加密方式暂时不实现，直接返回原文
                return text
        except Exception as e:
            logger.error(f"加密失败: {str(e)}")
            return text

    def __decrypt(self, text: str) -> str:
        """解密功能 - Linux版本实现RC4解密"""
        if not text:
            return ""
            
        try:
            if self.__encrypttypeid == 3:  # RC4解密
                import binascii
                
                # 将密钥转换为字节
                if isinstance(self.__encryptKey, str):
                    key = self.__encryptKey.encode('utf-8')
                else:
                    key = self.__encryptKey
                
                # RC4解密（与加密相同）
                def rc4_decrypt(data, key):
                    # 初始化S盒
                    S = list(range(256))
                    j = 0
                    for i in range(256):
                        j = (j + S[i] + key[i % len(key)]) % 256
                        S[i], S[j] = S[j], S[i]
                    
                    # 解密数据
                    i = j = 0
                    result = []
                    for byte in data:
                        i = (i + 1) % 256
                        j = (j + S[i]) % 256
                        S[i], S[j] = S[j], S[i]
                        result.append(byte ^ S[(S[i] + S[j]) % 256])
                    
                    return bytes(result)
                
                # 解密文本
                encrypted_data = binascii.a2b_hex(text.encode('utf-8'))
                decrypted = rc4_decrypt(encrypted_data, key)
                return decrypted.decode('utf-8')
            else:
                # 其他解密方式暂时不实现，直接返回原文
                return text
        except Exception as e:
            logger.error(f"解密失败: {str(e)}")
            return text

    def is_login(self):
        """检查是否已登录"""
        # 如果实例变量显示已登录，直接返回
        if self.__isLogin and not self.__isLoginOut:
            return True
            
        # 否则检查激活文件
        try:
            if os.path.exists(self.__activation_file):
                with open(self.__activation_file, 'r') as f:
                    data = json.load(f)
                    is_login = data.get('is_login', False)
                    # 检查是否过期
                    expiry = data.get('expiry', 0)
                    if expiry and expiry > time.time() and is_login:
                        # 如果文件显示已登录但实例变量未设置，恢复登录状态
                        if not self.__isLogin:
                            self.__isLogin = True
                            self.__isLoginOut = False
                            self.__loginToken = data.get('token', '')
                            self.__currentLoginCardOrAccount = data.get('code', '')
                            self.__heartbeatKey = data.get('heartbeat_key', '')
                            logger.info(f"从文件恢复登录状态: {self.__currentLoginCardOrAccount}")
                        return True
            return False
        except Exception as e:
            logger.error(f"检查登录状态时出错: {str(e)}")
            return False
        
    def is_logged_in(self):
        """检查是否已登录(兼容方法)"""
        return self.is_login()
        
    def 关闭当前软件(self):
        """关闭软件，停止心跳线程"""
        if self.__heartbeatThread and self.__heartbeatThread.is_alive():
            self.__isStopHeartBeat = True
            self.__heartbeatThread.join(2)  # 等待最多2秒
        logger.info("软件关闭，心跳线程已停止")
        return True
        
    def 初始化软件函数(self, rk初始化软件入参: Rk初始化软件入参类) -> Rk结果初始化软件类:
        """初始化软件，记录配置信息"""
        lockiniSoft.acquire()
        try:
            result = Rk结果初始化软件类()
            
            try:
                # 记录配置
                self.__encrypttypeid = rk初始化软件入参.通讯加密方式
                self.__encryptKey = rk初始化软件入参.加密Key
                self.__signSalt = rk初始化软件入参.签名盐
                self.__heartbeatReviceError = rk初始化软件入参.心跳失败的回调函数
                
                # 计算软件编码MD5
                self.__瑞科_软件编码_Md5 = hashlib.md5(rk初始化软件入参.软件编码.encode()).hexdigest()
                
                # 创建配置结构，与Windows版本保持一致
                self.__config = {
                    "businessid": None,
                    "platformusercode": rk初始化软件入参.平台用户编码,
                    "goodscode": rk初始化软件入参.软件编码,
                    "inisoftkey": "",
                    "timestamp": None,
                    "data": None,
                    "encrypttypeid": rk初始化软件入参.通讯加密方式,
                    "sign": "",
                    "platformtypeid": 1,  # 此处写死成1
                }
                
                # 尝试从瑞科服务器获取软件初始化信息
                try:
                    timestamp = int(round(time.time() * 1000))
                    send_data = {
                        "maccode": self.__maccode,
                        "versionname": rk初始化软件入参.软件版本号,
                        "timestamp": timestamp,
                        "requestflag": str(timestamp)
                    }
                    
                    logger.info("尝试从瑞科服务器获取软件初始化信息...")
                    RepResult = self.__GetRequestResult(send_data, BusinessType.iniSoftInfo)
                    
                    if RepResult.code == 0:
                        logger.info("从瑞科服务器获取软件初始化信息成功")
                        logger.info(f"服务器返回的原始数据: {RepResult.data}")
                        try:
                            # 先解密数据
                            decrypted_data = self.__decrypt(RepResult.data) if RepResult.data else ""
                            logger.info(f"解密后的数据: {decrypted_data}")
                            
                            # 再解析JSON
                            jsonData = json.loads(decrypted_data) if decrypted_data else {}
                            logger.info(f"解析后的JSON数据: {jsonData}")
                        except Exception as parse_error:
                            logger.error(f"数据解密或JSON解析失败: {str(parse_error)}")
                            jsonData = {}
                        
                        # 设置inisoftkey
                        inisoftkey = jsonData.get("inisoftkey", "")
                        logger.info(f"获取到的inisoftkey: '{inisoftkey}'")
                        self.__config["inisoftkey"] = inisoftkey
                        
                        # 创建软件信息
                        software_info = Rk软件信息类()
                        software_info.软件名称 = jsonData.get("softinfo", {}).get("softname", "OpenEssay")
                        software_info.软件当前最新版本号 = "v1.0"
                        software_info.软件公告 = jsonData.get("softinfo", {}).get("notice", "Linux兼容版本")
                        software_info.咨询qq = jsonData.get("softinfo", {}).get("consultqq", "")
                        software_info.咨询官网 = jsonData.get("softinfo", {}).get("consultwebsite", "")
                        software_info.咨询微信 = jsonData.get("softinfo", {}).get("consultwx", "")
                        software_info.咨询电话 = jsonData.get("softinfo", {}).get("consulttel", "")
                        
                        # 填充返回结果
                        result.软件信息 = software_info
                        result.软件价格列表 = []
                        result.错误编码 = 0
                        result.错误消息 = "初始化成功"
                        result.服务器时间戳 = jsonData.get("servertimestamp", int(time.time()))
                        
                    else:
                        logger.warning(f"从瑞科服务器获取软件初始化信息失败: {RepResult.code} - {RepResult.msg}")
                        # 使用本地默认配置
                        software_info = Rk软件信息类()
                        software_info.软件名称 = "OpenEssay"
                        software_info.软件当前最新版本号 = "v1.0"
                        software_info.软件公告 = "Linux兼容版本(本地模式)"
                        
                        result.软件信息 = software_info
                        result.软件价格列表 = []
                        result.错误编码 = 0
                        result.错误消息 = "初始化成功(本地模式)"
                        result.服务器时间戳 = int(time.time())
                        
                except Exception as e:
                    logger.warning(f"连接瑞科服务器失败，使用本地模式: {str(e)}")
                    # 使用本地默认配置
                    software_info = Rk软件信息类()
                    software_info.软件名称 = "OpenEssay"
                    software_info.软件当前最新版本号 = "v1.0"
                    software_info.软件公告 = "Linux兼容版本(本地模式)"
                    
                    result.软件信息 = software_info
                    result.软件价格列表 = []
                    result.错误编码 = 0
                    result.错误消息 = "初始化成功(本地模式)"
                    result.服务器时间戳 = int(time.time())
                
                self.__isIniSoft = True
                self.初始化软件结果属性 = result
                
                # 尝试从持久化文件加载激活信息
                self.__load_activation()
                
                # 如果已经登录过，尝试启动心跳
                if self.__isLogin and not self.__isStartHeartBeat:
                    self.__start_heartbeat()
                    
                return result
                
            except Exception as e:
                logger.exception("初始化软件失败")
                result.错误编码 = -1
                result.错误消息 = f"初始化软件失败: {str(e)}"
                return result
        finally:
            lockiniSoft.release()
    
    def __load_activation(self):
        """从文件加载激活信息"""
        try:
            if os.path.exists(self.__activation_file):
                with open(self.__activation_file, 'r') as f:
                    data = json.load(f)
                    self.__loginToken = data.get('token', '')
                    self.__currentLoginCardOrAccount = data.get('code', '')
                    self.__heartbeatKey = data.get('heartbeat_key', '')
                    self.__isLogin = data.get('is_login', False)
                    expiry = data.get('expiry', 0)
                    
                    # 检查是否过期
                    if expiry and expiry < time.time():
                        logger.info("激活信息已过期，清除登录状态")
                        self.__clear_login()
                    else:
                        logger.info(f"从文件加载激活信息成功: {self.__currentLoginCardOrAccount}")
        except Exception as e:
            logger.error(f"加载激活信息失败: {str(e)}")
    
    def __save_activation(self, expiry_time_str=None):
        """保存激活信息到文件"""
        try:
            # 计算过期时间戳
            expiry = 0
            if expiry_time_str:
                try:
                    # 将"2023-12-31 23:59:59"格式转换为时间戳
                    import datetime
                    dt = datetime.datetime.strptime(expiry_time_str, "%Y-%m-%d %H:%M:%S")
                    expiry = dt.timestamp()
                except Exception as e:
                    logger.warning(f"转换过期时间失败: {str(e)}，使用默认值")
                    # 设置默认30天过期时间
                    import datetime
                    current_time = datetime.datetime.now()
                    expire_time = current_time + datetime.timedelta(days=30)
                    expiry = expire_time.timestamp()
            else:
                # 如果没有提供过期时间，设置默认30天过期时间
                logger.info("没有提供过期时间，设置默认30天过期")
                import datetime
                current_time = datetime.datetime.now()
                expire_time = current_time + datetime.timedelta(days=30)
                expiry = expire_time.timestamp()
                    
            # 确保卡密和机器码不为空
            if not self.__currentLoginCardOrAccount:
                self.__currentLoginCardOrAccount = f"card_{int(time.time())}"
                logger.warning(f"卡密为空，使用临时卡密: {self.__currentLoginCardOrAccount}")
                
            if not self.__maccode:
                self.__maccode = f"mac_{int(time.time())}"
                logger.warning(f"机器码为空，使用临时机器码: {self.__maccode}")
                
            if not self.__loginToken:
                self.__loginToken = f"token_{int(time.time())}"
                logger.warning(f"Token为空，使用临时Token: {self.__loginToken}")
                
            if not self.__heartbeatKey:
                self.__heartbeatKey = f"key_{int(time.time())}"
                logger.warning(f"心跳密钥为空，使用临时密钥: {self.__heartbeatKey}")
                
            data = {
                'token': self.__loginToken,
                'code': self.__currentLoginCardOrAccount,
                'heartbeat_key': self.__heartbeatKey,
                'is_login': self.__isLogin,
                'expiry': expiry,
                'machine_id': self.__maccode,
                'save_time': time.time()  # 添加保存时间，便于调试
            }
            
            # 确保目录存在
            os.makedirs(os.path.dirname(self.__activation_file), exist_ok=True)
            
            with open(self.__activation_file, 'w') as f:
                json.dump(data, f)
                
            # 记录保存的详细信息，便于调试
            import datetime
            expiry_str = datetime.datetime.fromtimestamp(expiry).strftime("%Y-%m-%d %H:%M:%S") if expiry else "未设置"
            logger.info(f"保存激活信息成功: 卡密={self.__currentLoginCardOrAccount}, 过期时间={expiry_str}")
            
            return True
        except Exception as e:
            logger.error(f"保存激活信息失败: {str(e)}")
            return False
    
    def __clear_login(self):
        """清除登录状态"""
        # 从LoginList中移除当前登录的卡密或账号
        if self.__currentLoginCardOrAccount and self.__currentLoginCardOrAccount in self.__LoginList:
            self.__LoginList.remove(self.__currentLoginCardOrAccount)
        
        self.__loginToken = ""
        self.__currentLoginCardOrAccount = ""
        self.__heartbeatKey = ""
        self.__isLogin = False
        
        # 停止心跳线程
        if self.__heartbeatThread and self.__heartbeatThread.is_alive():
            self.__isStopHeartBeat = True
            
        # 清除持久化文件
        try:
            if os.path.exists(self.__activation_file):
                os.remove(self.__activation_file)
                logger.info("已清除激活信息文件")
        except:
            pass
    
    def __start_heartbeat(self):
        """启动心跳线程"""
        if not self.__isStartHeartBeat:
            self.__isStopHeartBeat = False
            self.__isStartHeartBeat = True
            self.__heartbeatThread = threading.Thread(target=self.__heartbeat)
            self.__heartbeatThread.daemon = True
            self.__heartbeatThread.start()
            logger.info("心跳线程已启动")
    
    def __heartbeat(self):
        """心跳线程函数，实际连接瑞科服务器发送心跳"""
        lockheartbeat.acquire()
        try:
            logger.info("心跳线程启动")
            
            while not self.__isStopHeartBeat:
                try:
                    if not self.__isLogin or self.__isLoginOut:
                        # 未登录或已退出登录状态下不发送心跳
                        logger.debug("未登录或已退出登录，不发送心跳")
                        time.sleep(30)  # 休眠30秒
                        continue
                    
                    # 准备心跳请求数据，使用标准API格式
                    card_num = self.__currentLoginCardOrAccount
                    machine_code = self.__maccode
                    timestamp = int(round(time.time() * 1000))
                    
                    # 构建请求数据，与Windows版本保持一致
                    send_data = {
                        "cardnumorusername": card_num,
                        "maccode": machine_code,
                        "token": self.__loginToken,
                        "heartbeatkey": self.__heartbeatKey,
                        "timestamp": timestamp,
                        "requestflag": str(timestamp)
                    }
                    
                    logger.debug(f"发送心跳请求: {card_num}, 机器码: {machine_code}")
                    
                    try:
                        # 使用标准API请求格式发送心跳
                        RepResult = self.__GetRequestResultHeartBeat(send_data, BusinessType.heartBeat)
                        
                        if RepResult.code == 0:
                            logger.debug("心跳发送成功")
                        else:
                            error_code = RepResult.code
                            error_msg = RepResult.msg
                            logger.warning(f"心跳响应错误: {error_code} - {error_msg}")
                            
                            # 对于致命错误，通知心跳失败
                            if error_code in [1002, 1003, 1004]:  # 卡密过期、未开通、已禁用等
                                if self.__heartbeatReviceError:
                                    # 创建心跳失败回调对象
                                    result = Rk结果心跳失败类()
                                    result.错误编码 = error_code
                                    result.错误消息 = error_msg
                                    try:
                                        self.__heartbeatReviceError(result)
                                    except:
                                        pass
                                
                                # 严重错误情况下，退出登录状态
                                self.__clear_login()
                                
                    except Exception as e:
                        logger.warning(f"心跳请求处理异常: {str(e)}")
                    
                    # 无论请求成功或失败，都休眠30秒再发送下一个心跳
                    time.sleep(30)
                    
                except Exception as e:
                    logger.error(f"心跳线程发生异常: {str(e)}")
                    # 捕获所有异常以确保心跳线程不会崩溃
                    time.sleep(30)  # 发生异常后休眠30秒再重试
            
            logger.info("心跳线程正常退出")
        finally:
            lockheartbeat.release()

    def __GetRequestResultHeartBeat(self, dataArgs, businessType):
        """构建心跳专用的标准瑞科API请求"""
        lockReqHeartBeat.acquire()
        try:
            result = Result()
            result.code = -999
            result.msg = "服务器访问失败，您本地网络不顺畅，请检查下（或许您用了代理IP了，当时的代理IP不稳定？）"

            try:
                ApiRequestArgs = self.__config.copy()
                timestamp = int(round(time.time() * 1000))
                data_json = json.dumps(dataArgs)
                data = self.__encrypt(data_json)
                
                # 构建签名字符串，与Windows版本保持一致
                sign = hashlib.md5((str(businessType.value) + str(self.__encrypttypeid) + ApiRequestArgs['platformusercode'] +
                                    ApiRequestArgs['goodscode'] +
                                    ApiRequestArgs['inisoftkey'] + str(timestamp) + data + self.__signSalt + '1').encode(
                    "utf-8")).hexdigest()

                ApiRequestArgs["businessid"] = businessType.value
                ApiRequestArgs["timestamp"] = timestamp
                ApiRequestArgs["data"] = data
                ApiRequestArgs["sign"] = sign

                # 发送心跳请求到瑞科服务器
                server_url = "http://api.ruikeyz.com/NetVer/webapi"
                logger.debug(f"发送瑞科心跳API请求: businessType={businessType.value}, url={server_url}")
                
                response = requests.post(server_url, json=ApiRequestArgs, timeout=10)
                
                if response.status_code == 200:
                    try:
                        response_data = response.json()
                        result.code = response_data.get("code", -999)
                        result.msg = response_data.get("msg", "未知错误")
                        result.data = response_data.get("data", "")
                        logger.debug(f"瑞科心跳API响应: code={result.code}, msg={result.msg}")
                    except json.JSONDecodeError as e:
                        logger.warning(f"心跳响应不是有效JSON格式: {str(e)}, 响应内容: {response.text[:200]}")
                        result.code = -998
                        result.msg = f"服务器响应格式错误: {str(e)}"
                else:
                    logger.warning(f"心跳HTTP请求失败: {response.status_code}, 响应: {response.text[:200]}")
                    result.code = -998
                    result.msg = f"HTTP请求失败: {response.status_code}"
                    
            except Exception as e:
                logger.warning(f"心跳请求异常: {str(e)}")
                result.code = -999
                result.msg = f"请求异常: {str(e)}"
                
            return result
        finally:
            lockReqHeartBeat.release()
    
    def 单码登录函数(self, rk需要登录的单码: str) -> Rk结果单码登录类:
        """单码登录函数，连接瑞科服务器进行验证"""
        lockLogin.acquire()
        try:
            result = Rk结果单码登录类()
            
            if not self.__isIniSoft:
                result.错误编码 = -3
                result.错误消息 = self.__GetErrorMsg(-3)
                return result
                
            if not rk需要登录的单码:
                result.错误编码 = -5
                result.错误消息 = self.__GetErrorMsg(-5)
                return result
            
            # 检查是否已经登录过相同的卡密
            if rk需要登录的单码 in self.__LoginList:
                result.错误编码 = -8
                result.错误消息 = self.__GetErrorMsg(-8, rk需要登录的单码)
                return result
                
            try:
                # 首先尝试连接瑞科服务器进行验证，使用标准API格式
                try:
                    machine_code = self.__maccode
                    timestamp = int(round(time.time() * 1000))
                    
                    # 构建请求数据，与Windows版本保持一致
                    send_data = {
                        "cardnum": rk需要登录的单码,
                        "maccode": machine_code,
                        "timestamp": timestamp,
                        "requestflag": str(timestamp)
                    }
                    
                    logger.info(f"发送卡密登录请求到瑞科服务器: {rk需要登录的单码}, 机器码: {machine_code}")
                    
                    # 使用标准API请求格式
                    RepResult = self.__GetRequestResult(send_data, BusinessType.cardLogin)
                    
                    logger.info(f"瑞科服务器登录响应: code={RepResult.code}, msg={RepResult.msg}")
                    
                    # 处理响应
                    if RepResult.code == 0:
                        # 提取数据
                        try:
                            data = json.loads(RepResult.data) if RepResult.data else {}
                        except:
                            data = {}
                        
                        # 设置登录状态
                        self.__loginToken = data.get("token", f"token_{int(time.time())}")
                        self.__currentLoginCardOrAccount = rk需要登录的单码
                        self.__heartbeatKey = data.get("heartbeatkey", f"key_{int(time.time())}")
                        self.__isLogin = True
                        self.__isLoginOut = False
                        
                        # 添加到LoginList
                        if rk需要登录的单码 not in self.__LoginList:
                            self.__LoginList.append(rk需要登录的单码)
                        
                        # 提取到期时间
                        expire_time_str = data.get("expiretime", "")
                        if not expire_time_str:
                            # 如果服务器没有返回到期时间，使用默认30天
                            import datetime
                            current_time = datetime.datetime.now()
                            expire_time = current_time + datetime.timedelta(days=30)
                            expire_time_str = expire_time.strftime("%Y-%m-%d %H:%M:%S")
                        
                        # 保存激活信息到本地文件
                        self.__save_activation(expire_time_str)
                        
                        # 启动心跳
                        if not self.__isStartHeartBeat:
                            self.__start_heartbeat()
                        
                        # 设置返回结果
                        result.错误编码 = 0
                        result.错误消息 = "登录成功"
                        result.服务器时间戳 = data.get("servertimestamp", int(time.time()))
                        result.到期时间 = expire_time_str
                        result.剩余点数 = data.get("points", 999)
                        result.角色ID = data.get("roleid", "1")
                        result.角色名称 = data.get("rolename", "会员")
                        result.备注 = data.get("remark", "Linux版激活")
                        result.开通的价格类型ID = data.get("pricetypeid", "1")
                        result.开通的价格类型名称 = data.get("pricetypename", "普通会员")
                        
                        return result
                    else:
                        # 服务器验证失败
                        error_code = RepResult.code
                        error_msg = RepResult.msg
                        
                        # 如果是网络错误或服务器暂时不可用，尝试使用本地验证作为备份
                        if error_code in [-998, -999] or "timeout" in error_msg.lower():
                            logger.warning(f"服务器连接失败，尝试使用本地验证: {error_msg}")
                        else:
                            # 其他错误直接返回
                            result.错误编码 = error_code
                            result.错误消息 = error_msg
                            return result
                            
                except Exception as e:
                    logger.warning(f"连接瑞科服务器失败，尝试使用本地验证: {str(e)}")
                
                # 如果服务器验证失败或出错，尝试使用本地验证作为备份
                logger.info("使用本地验证作为备份")
                
                # 激活码验证 - 不再强制要求OE-前缀，只要长度至少8位即可
                if len(rk需要登录的单码) < 8:
                    result.错误编码 = 6001
                    result.错误消息 = "激活码格式错误"
                    return result
                    
                # 检查机器码绑定
                machine_hash = hashlib.md5((rk需要登录的单码 + self.__maccode).encode()).hexdigest()
                
                # 生成一个30天有效期的到期时间
                import datetime
                current_time = datetime.datetime.now()
                expire_time = current_time + datetime.timedelta(days=30)
                expire_time_str = expire_time.strftime("%Y-%m-%d %H:%M:%S")
                
                # 设置登录状态
                self.__loginToken = machine_hash
                self.__currentLoginCardOrAccount = rk需要登录的单码
                self.__heartbeatKey = hashlib.md5((rk需要登录的单码 + str(time.time())).encode()).hexdigest()
                self.__isLogin = True
                self.__isLoginOut = False
                
                # 添加到LoginList
                if rk需要登录的单码 not in self.__LoginList:
                    self.__LoginList.append(rk需要登录的单码)
                
                # 保存激活信息
                self.__save_activation(expire_time_str)
                
                # 启动心跳
                if not self.__isStartHeartBeat:
                    self.__start_heartbeat()
                
                # 设置返回结果
                result.错误编码 = 0
                result.错误消息 = "登录成功(本地备份)"
                result.服务器时间戳 = int(time.time())
                result.到期时间 = expire_time_str
                result.剩余点数 = 999
                result.角色ID = "1"
                result.角色名称 = "会员"
                result.备注 = "Linux兼容版激活(本地备份)"
                result.开通的价格类型ID = "1"
                result.开通的价格类型名称 = "普通会员"
                
                return result
                
            except Exception as e:
                logger.exception("单码登录失败")
                result.错误编码 = -1
                result.错误消息 = f"单码登录失败: {str(e)}"
                return result
        finally:
            lockLogin.release()
    
    def 单码详情函数(self) -> Rk结果单码详情类:
        """获取单码详情，连接瑞科服务器查询"""
        lockdetail.acquire()
        try:
            result = Rk结果单码详情类()
            
            if not self.__isIniSoft:
                result.错误编码 = -3
                result.错误消息 = self.__GetErrorMsg(-3)
                return result
                
            # 修改：优先进行服务器验证，不再自动从本地文件恢复
            # 首先尝试连接瑞科服务器查询状态
            server_verification_failed = False
            server_error_code = None
            server_error_msg = None
            
            try:
                card_num = self.__currentLoginCardOrAccount
                machine_code = self.__maccode
                
                # 如果没有当前登录的卡密，尝试从激活文件获取
                if not card_num and os.path.exists(self.__activation_file):
                    try:
                        with open(self.__activation_file, 'r') as f:
                            data = json.load(f)
                            card_num = data.get('code', '')
                            logger.info(f"从激活文件获取卡密用于服务器验证: {card_num}")
                    except:
                        pass
                
                if not card_num:
                    result.错误编码 = -4
                    result.错误消息 = self.__GetErrorMsg(-4)
                    return result
                
                timestamp = int(round(time.time() * 1000))
                
                # 构建请求数据，与Windows版本保持一致
                send_data = {
                    "cardnum": card_num,
                    "maccode": machine_code,
                    "timestamp": timestamp,
                    "requestflag": str(timestamp)
                }
                
                logger.info(f"发送卡密详情请求到瑞科服务器: {card_num}, 机器码: {machine_code}")
                
                # 使用标准API请求格式
                RepResult = self.__GetRequestResult(send_data, BusinessType.cardDetail)
                
                logger.info(f"瑞科服务器详情响应: code={RepResult.code}, msg={RepResult.msg}")
                
                # 处理响应
                if RepResult.code == 0:
                    # 服务器验证成功
                    try:
                        data = json.loads(RepResult.data) if RepResult.data else {}
                    except:
                        data = {}
                    
                    # 更新登录状态（如果必要）
                    if not self.__isLogin or self.__currentLoginCardOrAccount != card_num:
                        self.__isLogin = True
                        self.__isLoginOut = False
                        self.__currentLoginCardOrAccount = card_num
                        
                        # 添加到LoginList
                        if card_num not in self.__LoginList:
                            self.__LoginList.append(card_num)
                        
                        # 如果心跳线程未启动，启动它
                        if not self.__isStartHeartBeat:
                            self.__start_heartbeat()
                    
                    # 设置返回结果
                    result.错误编码 = 0
                    result.错误消息 = "获取成功"
                    result.服务器时间戳 = data.get("servertimestamp", int(time.time()))
                    result.到期时间 = data.get("expiretime", "")
                    result.剩余点数 = data.get("points", 999)
                    result.开通的价格类型ID = data.get("pricetypeid", "1")
                    result.开通的价格类名称 = data.get("pricetypename", "普通会员")
                    result.是否已开通 = data.get("isopened", True)
                    result.是否已激活 = data.get("isactivated", True)
                    result.机器码 = machine_code
                    result.备注 = data.get("remark", "Linux版激活")
                    
                    # 更新本地文件的过期时间（如果服务器返回了过期时间）
                    if result.到期时间:
                        self.__save_activation(result.到期时间)
                    
                    return result
                else:
                    # 服务器验证失败
                    server_verification_failed = True
                    server_error_code = RepResult.code
                    server_error_msg = RepResult.msg
                    
                    logger.warning(f"瑞科服务器验证失败: {server_error_code} - {server_error_msg}")
            except Exception as e:
                logger.warning(f"连接瑞科服务器失败: {str(e)}")
                server_verification_failed = True
                server_error_code = -999
                server_error_msg = str(e)
            
            # 如果服务器验证失败，根据错误码决定处理方式
            if server_verification_failed:
                # 关键错误码表示卡密确实有问题，应该清除本地状态
                critical_errors = [
                    6001,  # 卡密不存在
                    6002,  # 卡密被禁用
                    6003,  # 卡密到期
                    6004,  # 卡密点数不足
                    6005,  # 卡密被删除
                    1001,  # 未激活
                    1013,  # 非绑定电脑
                    1053,  # 软件被禁用
                    4003,  # 账号到期
                    4004,  # 账号点数不足
                    4005   # 账号被禁用
                ]
                
                if server_error_code in critical_errors:
                    logger.warning(f"检测到关键错误码 {server_error_code}，清除本地激活状态")
                    # 清除本地登录状态和激活文件
                    self.__clear_login()
                    result.错误编码 = server_error_code
                    result.错误消息 = server_error_msg
                    return result
                
                # 如果是查询过快错误，直接返回错误码1065
                if server_error_code == 1065 or "速度过快" in server_error_msg:
                    result.错误编码 = 1065
                    result.错误消息 = "查询速度过快,请稍后再试"
                    return result
                
                # 对于网络错误（-998, -999等），不再使用本地备份
                # 这确保了服务器验证失败时不会从本地恢复激活状态
                logger.warning(f"服务器验证失败({server_error_code})，不使用本地备份，清除激活状态")
                self.__clear_login()
                result.错误编码 = server_error_code if server_error_code else -999
                result.错误消息 = server_error_msg if server_error_msg else "服务器验证失败"
                return result
            
            # 如果代码执行到这里，说明有未处理的情况
            logger.error("单码详情函数执行到未预期的代码路径")
            result.错误编码 = -999
            result.错误消息 = "未知错误"
            return result
        finally:
            lockdetail.release()
    
    def 退出登录函数(self) -> Rk结果基础类:
        """退出登录，连接瑞科服务器退出登录"""
        lockloginout.acquire()
        try:
            result = Rk结果基础类()
            
            if not self.__isIniSoft:
                result.错误编码 = -3
                result.错误消息 = self.__GetErrorMsg(-3)
                return result
                
            if not self.__isLogin:
                result.错误编码 = 0
                result.错误消息 = "当前未登录"
                return result
                
            try:
                # 连接瑞科服务器退出登录，使用标准API格式
                try:
                    card_num = self.__currentLoginCardOrAccount
                    machine_code = self.__maccode
                    timestamp = int(round(time.time() * 1000))
                    
                    # 构建请求数据，与Windows版本保持一致
                    send_data = {
                        "cardnumorusername": card_num,
                        "maccode": machine_code,
                        "token": self.__loginToken,
                        "timestamp": timestamp
                    }
                    
                    logger.info(f"发送退出登录请求到瑞科服务器: {card_num}, 机器码: {machine_code}")
                    
                    # 使用标准API请求格式
                    RepResult = self.__GetRequestResultHeartBeat(send_data, BusinessType.loginOut)
                    
                    logger.info(f"瑞科服务器退出登录响应: code={RepResult.code}, msg={RepResult.msg}")
                    
                    # 无论服务器响应如何，都清除本地登录状态
                except Exception as e:
                    logger.warning(f"连接瑞科服务器退出登录失败: {str(e)}")
                
                # 清除登录状态
                self.__clear_login()
                
                # 设置返回结果
                result.错误编码 = 0
                result.错误消息 = "退出登录成功"
                result.服务器时间戳 = int(time.time())
                
                return result
                
            except Exception as e:
                logger.exception("退出登录失败")
                
                # 即使发生异常，也尝试清除登录状态
                try:
                    self.__clear_login()
                except:
                    pass
                    
                result.错误编码 = -1
                result.错误消息 = f"退出登录失败: {str(e)}"
                return result
        finally:
            lockloginout.release()
    
    def 软件信息函数(self):
        """获取软件信息"""
        if self.初始化软件结果属性 and hasattr(self.初始化软件结果属性, '软件信息'):
            return self.初始化软件结果属性.软件信息
        else:
            info = Rk软件信息类()
            info.软件名称 = "OpenEssay"
            info.软件当前最新版本号 = "v1.0"
            info.软件公告 = "Linux兼容版本"
            return info
    
    def 扣点函数(self, rk扣的点数: int) -> Rk结果扣点类:
        """扣点函数 - Linux版本实现"""
        lockpoint.acquire()
        try:
            result = Rk结果扣点类()
            
            if not self.__isIniSoft:
                result.错误编码 = -2
                result.错误消息 = self.__GetErrorMsg(-2)
                return result
            
            if not self.__isLogin:
                result.错误编码 = -4
                result.错误消息 = self.__GetErrorMsg(-4)
                return result
            
            if rk扣的点数 <= 0:
                result.错误编码 = -1
                result.错误消息 = "扣点数量必须大于0"
                return result
            
            # Linux版本简化实现，直接返回成功
            result.错误编码 = 0
            result.错误消息 = "扣点成功"
            result.剩余点数 = max(0, 999 - rk扣的点数)  # 简化的点数计算
            return result
        finally:
            lockpoint.release()
    
    def 修改备注函数(self, rk备注内容: str) -> Rk结果基础类:
        """修改备注函数"""
        lockremarks.acquire()
        try:
            result = Rk结果基础类()
            
            if not self.__isIniSoft:
                result.错误编码 = -2
                result.错误消息 = self.__GetErrorMsg(-2)
                return result
            
            if not self.__isLogin:
                result.错误编码 = -4
                result.错误消息 = self.__GetErrorMsg(-4)
                return result
            
            if not rk备注内容:
                result.错误编码 = -16
                result.错误消息 = self.__GetErrorMsg(-16)
                return result
            
            if len(rk备注内容) > 500:
                result.错误编码 = -17
                result.错误消息 = self.__GetErrorMsg(-17)
                return result
            
            # Linux版本不支持修改备注
            result.错误编码 = -2
            result.错误消息 = "Linux版本不支持修改备注"
            return result
        finally:
            lockremarks.release()
    
    def 解绑机器码函数(self, rk需要解绑的卡密或账号: str) -> Rk结果解绑机器码类:
        """解绑机器码函数，连接瑞科服务器进行解绑"""
        lockunbundmac.acquire()
        try:
            result = Rk结果解绑机器码类()
            
            if not self.__isIniSoft:
                result.错误编码 = -2
                result.错误消息 = self.__GetErrorMsg(-2)
                return result
            
            if not rk需要解绑的卡密或账号:
                result.错误编码 = -18
                result.错误消息 = self.__GetErrorMsg(-18, "卡密或账号")
                return result
            
            try:
                # 连接瑞科服务器进行解绑操作
                timestamp = int(round(time.time() * 1000))
                machine_code = self.__maccode
                
                # 构建请求数据，与Windows版本保持一致
                send_data = {
                    "cardnumorusername": rk需要解绑的卡密或账号.strip(),
                    "maccode": machine_code,
                    "timestamp": timestamp,
                    "requestflag": str(timestamp)
                }
                
                logger.info(f"发送解绑机器码请求到瑞科服务器: {rk需要解绑的卡密或账号}, 机器码: {machine_code}")
                
                # 使用标准API请求格式
                RepResult = self.__GetRequestResult(send_data, BusinessType.unbundMac)
                
                logger.info(f"瑞科服务器解绑响应: code={RepResult.code}, msg={RepResult.msg}")
                
                result.错误编码 = RepResult.code
                result.错误消息 = RepResult.msg
                
                if RepResult.code == 0:
                    # 解绑成功，解析返回数据
                    try:
                        # 先解密数据
                        decrypted_data = self.__decrypt(RepResult.data) if RepResult.data else ""
                        # 再解析JSON
                        jsonData = json.loads(decrypted_data) if decrypted_data else {}
                        
                        result.到期时间 = jsonData.get("endtime", "")
                        result.剩余点数 = int(jsonData.get("surpluspointvalue", 0))
                        
                        logger.info(f"解绑成功: 到期时间={result.到期时间}, 剩余点数={result.剩余点数}")
                        
                        # 如果解绑的是当前登录的卡密，清除本地登录状态
                        if rk需要解绑的卡密或账号.strip() == self.__currentLoginCardOrAccount:
                            logger.info("解绑的是当前登录卡密，清除本地登录状态")
                            self.__clear_login()
                            
                    except Exception as parse_error:
                        logger.warning(f"解绑响应数据解析失败: {str(parse_error)}")
                        # 即使解析失败，解绑操作也是成功的
                        result.到期时间 = ""
                        result.剩余点数 = 0
                else:
                    logger.warning(f"解绑失败: {RepResult.code} - {RepResult.msg}")
                
                return result
                
            except Exception as e:
                logger.exception("解绑机器码失败")
                result.错误编码 = -1
                result.错误消息 = f"解绑机器码失败: {str(e)}"
                return result
        finally:
            lockunbundmac.release()
    
    def 获取远程变量函数(self, rk变量名: str) -> Rk结果获取远程变量类:
        """获取远程变量函数"""
        lockgetvarvalue.acquire()
        try:
            result = Rk结果获取远程变量类()
            
            if not self.__isIniSoft:
                result.错误编码 = -2
                result.错误消息 = self.__GetErrorMsg(-2)
                return result
            
            if not self.__isLogin:
                result.错误编码 = -4
                result.错误消息 = self.__GetErrorMsg(-4)
                return result
            
            if not rk变量名:
                result.错误编码 = -19
                result.错误消息 = self.__GetErrorMsg(-19)
                return result
            
            # 从RkVarList中查找变量
            for var in self.__RkVarList:
                if var.varname == rk变量名:
                    result.错误编码 = 0
                    result.错误消息 = "获取成功"
                    result.变量值 = var.varvalue
                    return result
            
            # 如果没有找到，返回默认值
            result.错误编码 = 0
            result.错误消息 = "获取成功"
            result.变量值 = f"default_value_for_{rk变量名}"
            return result
        finally:
            lockgetvarvalue.release()
    
    def 获取远程算法函数(self, rk算法ID: str, rk提交的参数: str) -> Rk结果获取远程算法类:
        """获取远程算法函数"""
        lockgetcalvalue.acquire()
        try:
            result = Rk结果获取远程算法类()
            
            if not self.__isIniSoft:
                result.错误编码 = -2
                result.错误消息 = self.__GetErrorMsg(-2)
                return result
            
            if not self.__isLogin:
                result.错误编码 = -4
                result.错误消息 = self.__GetErrorMsg(-4)
                return result
            
            if not rk算法ID:
                result.错误编码 = -20
                result.错误消息 = self.__GetErrorMsg(-20)
                return result
            
            if not rk提交的参数:
                result.错误编码 = -21
                result.错误消息 = self.__GetErrorMsg(-21)
                return result
            
            # Linux版本不支持远程算法
            result.错误编码 = -2
            result.错误消息 = "Linux版本不支持获取远程算法"
            return result
        finally:
            lockgetcalvalue.release()
    
    def 禁用删除当前登录的单码或账号函数(self, rk禁用还是删除: Rk禁用还是删除枚举类) -> None:
        """禁用删除当前登录的单码或账号函数"""
        lockdisable.acquire()
        try:
            logger.info("Linux版本不支持禁用删除功能")
        finally:
            lockdisable.release()
    
    def 账号登录函数(self, rk需要登录的账号: str, rk需要登录的密码: str) -> Rk结果账号登录类:
        """账号登录函数"""
        lockLogin.acquire()
        try:
            result = Rk结果账号登录类()
            
            if not self.__isIniSoft:
                result.错误编码 = -2
                result.错误消息 = self.__GetErrorMsg(-2)
                return result
            
            if not rk需要登录的账号 or not rk需要登录的密码:
                result.错误编码 = -6
                result.错误消息 = self.__GetErrorMsg(-6)
                return result
            
            result.错误编码 = -2
            result.错误消息 = "Linux版本不支持账号登录，请使用单码登录"
            return result
        finally:
            lockLogin.release()
    
    def 账号详情函数(self) -> Rk结果账号详情类:
        """账号详情函数"""
        lockdetail.acquire()
        try:
            result = Rk结果账号详情类()
            
            if not self.__isIniSoft:
                result.错误编码 = -2
                result.错误消息 = self.__GetErrorMsg(-2)
                return result
            
            result.错误编码 = -2
            result.错误消息 = "Linux版本不支持账号登录"
            return result
        finally:
            lockdetail.release()
    
    def 账号注册函数(self, rk注册的账号: str, rk注册的密码: str) -> Rk结果基础类:
        """账号注册函数"""
        lockregaccount.acquire()
        try:
            result = Rk结果基础类()
            
            if not self.__isIniSoft:
                result.错误编码 = -2
                result.错误消息 = self.__GetErrorMsg(-2)
                return result
            
            if not rk注册的账号 or not rk注册的密码:
                result.错误编码 = -10
                result.错误消息 = self.__GetErrorMsg(-10)
                return result
            
            if len(rk注册的账号) > 15 or len(rk注册的密码) > 15:
                result.错误编码 = -11
                result.错误消息 = self.__GetErrorMsg(-11)
                return result
            
            result.错误编码 = -2
            result.错误消息 = "Linux版本不支持账号注册"
            return result
        finally:
            lockregaccount.release()
    
    def 修改账号密码函数(self, rk账号: str, rk旧密码: str, rk新密码: str) -> Rk结果基础类:
        """修改账号密码函数"""
        lockupdaccount.acquire()
        try:
            result = Rk结果基础类()
            
            if not self.__isIniSoft:
                result.错误编码 = -2
                result.错误消息 = self.__GetErrorMsg(-2)
                return result
            
            if not rk账号 or not rk旧密码 or not rk新密码:
                result.错误编码 = -12
                result.错误消息 = self.__GetErrorMsg(-12)
                return result
            
            if len(rk账号) > 15 or len(rk旧密码) > 15 or len(rk新密码) > 15:
                result.错误编码 = -13
                result.错误消息 = self.__GetErrorMsg(-13)
                return result
            
            result.错误编码 = -2
            result.错误消息 = "Linux版本不支持修改账号密码"
            return result
        finally:
            lockupdaccount.release()
    
    def 充值卡充值函数(self, rk被充值的卡密或账号: str, rk充值卡号: str) -> Rk结果基础类:
        """充值卡充值函数"""
        lockrech.acquire()
        try:
            result = Rk结果基础类()
            
            if not self.__isIniSoft:
                result.错误编码 = -2
                result.错误消息 = self.__GetErrorMsg(-2)
                return result
            
            if not rk被充值的卡密或账号 or not rk充值卡号:
                result.错误编码 = -14
                result.错误消息 = self.__GetErrorMsg(-14, "卡密或账号")
                return result
            
            result.错误编码 = -2
            result.错误消息 = "Linux版本不支持充值卡充值"
            return result
        finally:
            lockrech.release()
    
    def 充值卡详情函数(self, rk查询的充值卡: str) -> Rk结果充值卡详情类:
        """充值卡详情函数"""
        lockrechdetail.acquire()
        try:
            result = Rk结果充值卡详情类()
            
            if not self.__isIniSoft:
                result.错误编码 = -2
                result.错误消息 = self.__GetErrorMsg(-2)
                return result
            
            if not rk查询的充值卡:
                result.错误编码 = -15
                result.错误消息 = self.__GetErrorMsg(-15)
                return result
            
            result.错误编码 = -2
            result.错误消息 = "Linux版本不支持充值卡详情查询"
            return result
        finally:
            lockrechdetail.release()
    
    def 获取瑞科验证SDK当前版本号(self) -> str:
        return "1.0.0-Linux"

    def __GetRequestResult(self, dataArgs, businessType):
        """构建标准瑞科API请求，与Windows版本保持一致"""
        result = Result()
        result.code = -999
        result.msg = "服务器访问失败，您本地网络不顺畅，请检查下（或许您用了代理IP了，当时的代理IP不稳定？）"

        try:
            ApiRequestArgs = self.__config.copy()
            timestamp = int(round(time.time() * 1000))
            data_json = json.dumps(dataArgs)
            data = self.__encrypt(data_json)
            
            # 构建签名字符串，与Windows版本保持一致
            sign = hashlib.md5((str(businessType.value) + str(self.__encrypttypeid) + ApiRequestArgs['platformusercode'] +
                                ApiRequestArgs['goodscode'] +
                                ApiRequestArgs['inisoftkey'] + str(timestamp) + data + self.__signSalt + '1').encode(
                "utf-8")).hexdigest()

            ApiRequestArgs["businessid"] = businessType.value
            ApiRequestArgs["timestamp"] = timestamp
            ApiRequestArgs["data"] = data
            ApiRequestArgs["sign"] = sign

            # 发送请求到瑞科服务器
            server_url = "http://api.ruikeyz.com/NetVer/webapi"
            logger.info(f"发送瑞科API请求: businessType={businessType.value}, url={server_url}")
            
            response = requests.post(server_url, json=ApiRequestArgs, timeout=10)
            
            if response.status_code == 200:
                try:
                    response_data = response.json()
                    result.code = response_data.get("code", -999)
                    result.msg = response_data.get("msg", "未知错误")
                    result.data = response_data.get("data", "")
                    logger.info(f"瑞科API响应: code={result.code}, msg={result.msg}")
                except json.JSONDecodeError as e:
                    logger.error(f"瑞科API响应JSON解析失败: {str(e)}, 响应内容: {response.text[:200]}")
                    result.code = -998
                    result.msg = f"服务器响应格式错误: {str(e)}"
            else:
                logger.error(f"瑞科API HTTP请求失败: {response.status_code}, 响应: {response.text[:200]}")
                result.code = -998
                result.msg = f"HTTP请求失败: {response.status_code}"
                
        except Exception as e:
            logger.error(f"瑞科API请求异常: {str(e)}")
            result.code = -999
            result.msg = f"请求异常: {str(e)}"
            
        return result 