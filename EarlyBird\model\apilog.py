from werkzeug.security import generate_password_hash, check_password_hash

from EarlyBird.common.libs.BaseModel import BaseModel
from sqlalchemy import (
    Integer,
    String,
    Boolean,
    Column,
    JSON,
    Enum,
    SmallInteger,
    DateTime,
    Table,
    BIGINT,
    ForeignKey,
    MetaData,
    TEXT,
)
from sqlalchemy.orm import relationship
import json
from . import TABLE_PREFIX


class ApiLog(BaseModel):

    __tablename__ = f"{TABLE_PREFIX}api_log"
    __table_args__ = {"comment": "请求日志"}

    uid = Column(Integer, nullable=False, comment="用户id")
    modelName = Column(String(20), nullable=False, comment="模型名字")
    apiName = Column(String(50), nullable=False, comment="模型名字")
    isSuccess = Column(Boolean, nullable=False, comment="调用是否成功")
    param = Column(TEXT(65536), nullable=False, comment="返回原始结果")
    result = Column(TEXT(65536), nullable=False, comment="返回原始结果")
    size = Column(Integer, default="0", comment="token数量")
    timeSpan = Column(Integer, nullable=False, comment="耗时")

    def __repr__(self):
        return json.dumps(
            {
                "uid": self.uid,
                "modelName": self.modelName,
                "apiName": self.apiName,
                "isSuccess": self.isSuccess,
                "size": self.size,
                "timeSpan": self.timeSpan,
            }
        )
