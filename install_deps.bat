@echo off
chcp 65001 >nul
echo ================================================
echo 🚀 早鸟论文系统 - Python依赖快速安装
echo ================================================
echo.

echo 📋 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请先安装Python 3.8+
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
echo.

echo 📦 升级pip...
python -m pip install --upgrade pip
echo.

echo 📦 安装项目依赖...
echo 选择安装方式:
echo 1. 使用默认源安装
echo 2. 使用清华镜像源安装（推荐，速度更快）
echo.
set /p choice=请选择 (1 或 2): 

if "%choice%"=="2" (
    echo 使用清华镜像源安装...
    python -m pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
) else (
    echo 使用默认源安装...
    python -m pip install -r requirements.txt
)

if errorlevel 1 (
    echo.
    echo ❌ 依赖安装失败，请检查错误信息
    pause
    exit /b 1
)

echo.
echo ✅ 依赖安装完成！
echo.
echo 🎉 现在可以启动项目了：
echo    python EarlyBird/server.py
echo    或
echo    python EarlyBird/web_server.py
echo.
pause
