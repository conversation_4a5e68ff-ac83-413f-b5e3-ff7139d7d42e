<template>
  <section>
    <div class="paragraphs" v-for="(para, index) in paragraphs" :key="para.id">
      <div
        :class="'paragraph_one '"
        :data-paraId="para.id"
        @click="handlerClickPara(para)"
      >
        <div :class="'paragraph_one_title title-level' + level">
          [{{ chapterTitlePrefix + (index + 1) }}] {{ para.title }}
          <small>共 {{ para.length || 0 }} 字</small>
        </div>
        <div class="paragraph_one_text" v-if="!onlyShowTitle">
          <div class="result-text-inner">
            <div v-for="(p, pIndex) in replaceNewLine(para.text)" :key="pIndex" v-html="processTableContent(p, para.id, pIndex)"></div>
          </div>
        </div>
        <div class="paragraph_one_edit">
          <button @click="handlerReGen(para)">AI生成本段</button>

          <!-- <span @click="handlerReGen(para.id)">AI生成本段</span> -->
          <button @click="handlerEdit(para)">手动编辑</button>
          <button @click="handlerTableTools(para)" style="background-color: #007bff; color: white;">📊 表格工具</button>
          <button @click="handlerAddPara(para, 'next')">段后增加段落</button>
          <button @click="handlerAddPara(para, 'child')">给本段增加子段</button>
          <button @click="handlerMove(para, 'up')">上移</button>
          <button @click="handlerMove(para, 'down')">下移</button>
        </div>
      </div>
      <ParagraphBox
        :thesisId="thesisId"
        :onlyShowTitle="onlyShowTitle"
        :paragraphs="para.subtitle"
        :chapterTitlePrefix="chapterTitlePrefix + (index + 1) + '.'"
        :level="level + 1"
        :lang="lang"
        @onReGen="handlerReGen"
        @onUpdate="handlerUpdate"
        @onEdit="handlerEdit"
        @onDelete="handlerDelete"
        @onTableTools="handlerTableTools"
      >
      </ParagraphBox>

      <!---以下是增加段落的diallog-->
      <div class="dialog-box_bg" v-if="newParaFormVisible"></div>
      <div class="dialog-box" v-if="newParaFormVisible">
        <div class="dialog-box_header">
          <div class="dialog-box_header_left">
            {{ newParaForm.addType == "child" ? "本段中插入子段落" : "本段后新增一段" }}
          </div>
          <div class="dialog-box_header_right">
            <i class="el-icon-circle-close" @click="dialogClose"></i>
          </div>
        </div>
        <div class="dialog-box_body">
          <el-form
            :model="newParaForm"
            ref="newParaFormRef"
            label-width="150px"
            label-position="top"
          >
            <el-form-item label="">
              在段落 【{{ newParaForm["basePara"]["title"] }}】

              {{
                newParaForm.addType == "child" ? "章中，插入一个子段落" : "后,新增一段"
              }}
            </el-form-item>
            <el-form-item
              :label="$t('paragraphBox.newParaDialog.formTitle')"
              prop="newTitle"
              required
            >
              <el-input placeholder="请输入内容" v-model="newParaForm.newTitle" clearable>
              </el-input>
            </el-form-item>

            <el-form-item
              :label="$t('paragraphBox.newParaDialog.formContent')"
              prop="newContent"
            >
              <el-input
                type="textarea"
                placeholder="请输入内容，可留白"
                v-model="newParaForm.newContent"
                maxlength="500"
                show-word-limit
              >
              </el-input>
            </el-form-item>
          </el-form>
        </div>
        <div class="dialog-box_footer">
          <el-popconfirm
            :confirmButtonText="$t('paragraphBox.newParaDialog.btnContinue')"
            @cancel="dialogClose"
            :cancelButtonText="$t('paragraphBox.newParaDialog.btnCancel')"
            icon="el-icon-info"
            iconColor="red"
            :title="$t('paragraphBox.newParaDialog.tipCancel')"
          >
            <el-button type="primary" size="medium" plain slot="reference">{{
              $t("paragraphBox.newParaDialog.btnCancel")
            }}</el-button>
          </el-popconfirm>

          <el-button type="primary" size="medium" @click="dialogSubmit">{{
            $t("paragraphBox.newParaDialog.btnSubmit")
          }}</el-button>
        </div>
      </div>
    </div>
  </section>
</template>
<script>
import { saveNewParagraph, paragraphMove, paragraphDelete } from "@/api/thesis";
import { mapState } from "vuex";

export default {
  name: "ParagraphBox",
  props: {
    paragraphs: { type: Array, default: () => [] },
    chapterTitlePrefix: { type: String, default: "" },
    thesisId: { type: Number, default: -1 },
    level: { type: Number, default: 1 },
    lang: { type: String, default: "中文" },
    onlyShowTitle: { type: Boolean, default: false },
  },

  computed: {
    ...mapState({
      selectPara: (state) => state.thesis.outlineTreeSelectPara,
    }),
    selectedPid() {
      return this.selectPara["id"];
    },
  },
  watch: {
    selectedPid(n, o) {
      const d = document.querySelector('.paragraph_one[data-paraId="' + n + '"]');
      d.classList.add("s");
    },
  },

  data() {
    return {
      paragraphRegenFormVisible: false,
      paragraphRegenForm: {
        length: 500,
        instruction: "",
      },

      newParaFormVisible: false,
      newParaForm: {
        basePara: {},
        thesisId: this.thesisId,
        baseParaId: "",
        addType: "",
        newTitle: "",
        newContent: "",
      },
    };
  },

  methods: {
    handlerClickPara(p) {
      this.$store.commit("thesis/SET_SELECTED_PARAGRAPH", p);
    },
    handlerAddPara(para, addType = "next") {
      this.newParaFormVisible = true;
      this.newParaForm["addType"] = addType;
      this.newParaForm["thesisId"] = this.thesisId;
      this.newParaForm["basePara"] = para;
      this.newParaForm["baseParaId"] = para.id;
    },

    handlerMove(para, moveType) {
      paragraphMove({ thesisId: this.thesisId, paraId: para.id, moveType })
        .then((res) => {
          this.$message({ type: "success", message: "操作完成" });
          this.$emit("onUpdate");
        })
        .catch((e) => this.$message({ type: "error", message: "操作完成" + e }));
    },

    // dialog
    dialogClose() {
      this.newParaForm["thesisId"] = this.thesisId;
      this.newParaForm["basePara"] = {};
      this.newParaForm["baseParaId"] = "";
      this.newParaForm["newTitle"] = "";
      this.newParaForm["newContent"] = "";
      this.newParaFormVisible = false;
    },
    dialogSubmit() {
      if (this.newParaForm.newTitle == "") {
        this.$message({ type: "error", message: "请填写标题" });
        return;
      }
      saveNewParagraph(this.newParaForm)
        .then((res) => {
          this.$message({ type: "success", message: "保存完成" });
          this.dialogClose();
          this.$emit("onUpdate");
        })
        .catch((e) => {
          this.$message({ type: "error", message: "遇到错误 " + e });
        });
    },
    replaceNewLine(t) {
      if (t == undefined || t == "") return ["尚未生成..."];
      
      // 检查是否包含HTML标签
      if (t.includes('<table') || t.includes('<div') || t.includes('<p>')) {
        // 如果包含HTML，直接返回整个内容作为一个元素
        return [t];
      }
      
      // 原来的逻辑处理纯文本
      const tArr = t.split("\n");
      const result = [];
      for (let i in tArr) {
        if (tArr[i] == "" || tArr[i] == "```") {
          continue;
        }
        result.push(tArr[i]);
      }
      return result;
    },
    getParagraphTitle(prefix, index) {
      if (this.lang == "中文") {
        return "[" + prefix + (index + 1) + "]";
      }
      return prefix + (index + 1);
    },
    handlerUpdate() {
      this.$emit("onUpdate");
    },
    handlerEdit(para) {
      this.$emit("onEdit", para);
    },
    handlerDelete(para) {
      this.$confirm("删除后无法恢复,确认删除本段？", "提示", {
        confirmButtonText: "确定删除",
        cancelButtonText: "取消",
        type: "warning",
      }).then((res) => {
        paragraphDelete({ thesisId: this.thesisId, paraId: para.id })
          .then((res) => {
            this.$message({ type: "success", message: "删除完毕" });
            this.$emit("onUpdate");
          })
          .catch((e) => this.$message({ type: "error", message: e }));
      });
    },
    handlerReGen(para) {
      this.$emit("onReGen", para);
    },
    handlerTableTools(para) {
      this.$emit("onTableTools", para);
    },
    processTableContent(content, paraId, pIndex) {
      // 检查是否包含表格
      if (content.includes('<table')) {
        // 直接返回原始表格内容，不添加删除按钮
        return content;
      }
      return content;
    },
    saveParagraphContent(paraId, content) {
      // 调用API保存段落内容
      const paragraph = this.paragraphs.find(p => p.id === paraId);
      if (paragraph) {
        paragraph.text = content;
        
        // 调用后端API保存内容
        const updateData = {
          userId: this.$store.state.user.userInfo.id,
          thesisId: this.thesisId,
          paragraphId: paraId,
          title: paragraph.title,
          text: content
        };
        
        // 导入saveSingleParagraph API
        import('@/api/thesis').then(({ saveSingleParagraph }) => {
          saveSingleParagraph(updateData)
            .then((res) => {
              console.log('段落内容已保存到后端:', paraId);
            })
            .catch((e) => {
              console.error('保存段落内容失败:', e);
              this.$message.error('保存失败: ' + e);
            });
        }).catch((e) => {
          console.error('导入API失败:', e);
        });
      } else {
        console.error('未找到段落，无法保存:', paraId);
      }
    },
  },
  mounted() {
  },
};
</script>
<style lang="scss" scoped>
.paragraphs {
  margin-bottom: 10px;

  .paragraph_one {
    background: #f6f6f6;
    border: 1px solid #fff;
    margin-bottom: 10px;
    border-radius: 4px;

    .paragraph_one_title {
      padding: 20px;
      padding-bottom: 0;
      text-indent: 0;
      font-size: 14px;
      margin-bottom: 5px;
      display: flex;
      align-items: center;
      small {
        text-align: right;
        flex: 1;
        margin-left: 10px;
        font-size: 16px;
        font-style: normal;
        color: #444;
        font-weight: bold;
      }
    }

    .paragraph_one_text,
    .result-text-absence {
      color: #ccc;
      padding: 5px 20px;

      .result-text-inner {
        p {
          line-height: 30px;
          color: #444;
          text-indent: 2em;
          margin: 0;
          padding: 5px 0;
          white-space: pre-wrap;
        }
        
        // 添加对HTML内容的样式支持
        div {
          line-height: 30px;
          color: #444;
          margin: 0;
          padding: 5px 0;
          
          // 表格样式
          table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
            font-size: 14px;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            
            th, td {
              border: 1px solid #e0e0e0;
              padding: 12px 16px;
              text-align: left;
              vertical-align: top;
              word-wrap: break-word;
              max-width: 300px;
            }
            
            th {
              background: #f8f9fa;
              font-weight: 600;
              color: #333;
              border-bottom: 2px solid #dee2e6;
            }
            
            td {
              background: white;
              color: #444;
              line-height: 1.5;
            }
            
            tr:hover td {
              background: #f8f9fa;
            }
            
            // 响应式表格
            @media (max-width: 768px) {
              th, td {
                padding: 8px 12px;
                font-size: 13px;
              }
            }
          }
        }
      }
    }

    .title-level1 {
      font-size: 28px;
      font-weight: bold;
    }

    .title-level2 {
      font-size: 24px;
    }

    .title-level3 {
      font-size: 20px;
    }

    .title-level4,
    .title-level5,
    .title-level6,
    .title-level7 {
      font-size: 16px;
    }
  }

  .paragraph_one_edit {
    // visibility: hidden;
    color: #409eff;
    padding: 20px;

    button {
      border-radius: 6px;
      cursor: pointer;
      margin-right: 10px;
      background: #efefef;
      border: 1px solid #ccccccaa;
      font-style: normal;
      padding: 8px 16px;
      font-size: 12px;
      font-weight: normal;
    }

    button:hover {
      border: 1px solid #cccccc;
      background-color: #fff;
    }
  }

  .paragraph_one.s {
    border: 1px solid #0083b0 !important;
  }

  .paragraph_one:hover {
    border: 1px solid #0083b033;
    cursor: pointer;

    .paragraph_one_edit {
      visibility: visible;
    }
  }

  .result-text-absence {
    font-style: italic;
  }
}

.result-text-absence:hover,
.result-text:hover {
  border: 1px solid #409eff;

  .edit-box {
    visibility: visible;

    i:hover {
      color: red;
      margin-top: -1px;
    }
  }
}

.dialog-box_bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: #00000033;
  z-index: 10;
}

.dialog-box {
  position: fixed;
  top: 50px;
  left: 50%;
  width: 500px;
  margin-left: -250px;
  background: #fff;
  border-radius: 4px;
  z-index: 11;
  display: flex;
  flex-direction: column;
}

.dialog-box_header {
  display: flex;
  border-bottom: 1px solid #f1f1f1;
  font-size: 20px;
  font-weight: bold;

  i {
    font-size: 24px;
    font-weight: bold;
  }
}

.dialog-box_header_left {
  flex: 1;
  padding: 10px 20px;
}

.dialog-box_header_right {
  padding: 10px 20px;
}

.dialog-box_header_right {
  i {
    cursor: pointer;
  }
}

::v-deep .el-rate__icon {
  font-size: 30px !important;
}

::v-deep .el-checkbox__label {
  white-space: pre-wrap;
}

::v-deep .el-textarea__inner {
  height: 100px !important;
}

.dialog-box_body {
  padding: 20px 40px;
  flex: 1;
  font-size: 14px;
}

.dialog-box_footer {
  display: flex;
  justify-content: center;
  padding: 20px;
  border-top: 1px solid #f1f1f1;

  button {
    padding: 10px 40px;
    font-size: 16px;
    margin: 0 20px;
  }
}
.paragraph_regen_form {
  margin: 12px;
  .el-form-item {
    margin-bottom: 10px;
    ::v-deep .el-textarea__inner {
      font-size: 14px;
    }
  }
}
</style>
