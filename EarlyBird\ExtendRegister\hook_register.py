import logging
from flask import request, session, g, jsonify
from EarlyBird.common import ApiResponse
from EarlyBird.model.user import User
from EarlyBird.model.admin import Admin
from EarlyBird.api.admin.auth import verify_token
from datetime import datetime

logger = logging.getLogger(__name__)

# 定义不需要登录的白名单路由
WHITE_LIST_ROUTES = [
    '/api/user/login',
    '/api/user/register',
    '/api/user/logout',
    '/api/home/<USER>',  # 激活接口
    '/api/home/<USER>',  # 获取设置接口
    '/api/agreement/',  # 协议相关接口
    '/api/admin/auth/login',  # 管理员登录接口
    '/api/admin/auth/logout',  # 管理员登出接口
    '/api/home/',  # 首页相关接口
    '/api/help/',  # 帮助相关接口
    '/api/guide/',  # 指南相关接口
    '/api/static/',  # 静态资源
    '/static/',  # 静态资源
    '/favicon.ico',  # 网站图标
    '/index.html',  # 首页
    '/api/generate/tableContent',  # 表格生成API - 临时添加到白名单
    '/api/pay/',  # 支付相关接口
]

# 定义管理员API的白名单路由（不需要Authorization头）
ADMIN_WHITE_LIST_ROUTES = [
    '/api/admin/auth/login',
    '/api/admin/auth/logout',
]

def register_hook(app):

    @app.before_request
    def before_every_request():
        # 特殊处理根路径
        if request.path == '/':
            return None
            
        # 特殊处理静态文件请求
        if request.path.startswith('/static/') or request.path.startswith('/favicon.ico'):
            return None
            
        # 特殊处理前端路由（非API请求）
        if not request.path.startswith('/api/'):
            return None
            
        # 检查是否是白名单路由
        for route in WHITE_LIST_ROUTES:
            if request.path.startswith(route):
                return None
        
        # 检查是否是管理员API
        if request.path.startswith('/api/admin/'):
            # 检查是否是管理员API的白名单路由
            for route in ADMIN_WHITE_LIST_ROUTES:
                if request.path.startswith(route):
                    return None
            
            # 管理员API使用JWT token验证
            auth_header = request.headers.get('Authorization')
            if not auth_header:
                # 只在非白名单路由时记录警告
                logger.warning(f"管理员API未提供Authorization头: {request.path}")
                return jsonify({
                    "success": False,
                    "code": 401,
                    "message": "未提供认证信息，请登录",
                    "data": None
                }), 401
            
            # 移除Bearer前缀
            if auth_header.startswith('Bearer '):
                token = auth_header[7:]
            else:
                token = auth_header
                
            logger.debug(f"管理员API Token: {token[:10]}...")
            
            # 验证token
            payload = verify_token(token)
            if not payload:
                logger.warning(f"管理员API Token无效: {request.path}")
                return jsonify({
                    "success": False,
                    "code": 401,
                    "message": "认证信息无效或已过期，请重新登录",
                    "data": None
                }), 401
            
            # 获取管理员信息
            admin_id = payload.get('admin_id')
            admin = Admin.query.get(admin_id)
            if not admin:
                logger.warning(f"管理员不存在: {admin_id}")
                return jsonify({
                    "success": False,
                    "code": 401,
                    "message": "管理员账号不存在",
                    "data": None
                }), 401
                
            if not admin.is_active:
                logger.warning(f"管理员已被禁用: {admin_id}")
                return jsonify({
                    "success": False,
                    "code": 401,
                    "message": "管理员账号已被禁用",
                    "data": None
                }), 401
            
            # 设置全局管理员信息
            request.admin = admin
            g.admin = admin
            g.admin_id = admin.id
            logger.debug(f"管理员 {admin.username} 访问: {request.path}")
            return None
                
        # 检查用户登录状态（普通用户API）
        user_id = session.get('user_id')
        
        # 添加详细的调试日志
        logger.info(f"🔍 钩子函数调试 - 路径: {request.path}")
        logger.info(f"🔍 Session内容: {dict(session)}")
        logger.info(f"🔍 提取的user_id: {user_id}")
        logger.info(f"🔍 Session ID: {session.get('session_id', 'N/A')}")
        
        if not user_id:
            logger.warning(f"未登录用户尝试访问: {request.path}")
            return ApiResponse().needLogin().json()
            
        # 从数据库获取用户信息
        try:
            user = User.query.get(user_id)
            if not user:
                logger.warning(f"无效的用户ID {user_id} 尝试访问: {request.path}")
                session.clear()
                return ApiResponse().needLogin().json()
                
            # 设置全局用户信息
            g.userid = user.id
            g.user = user
            g.isVip = user.isVip()
            
            # 如果session中有激活码，确保用户是VIP
            activation_key = session.get('activation_key')
            if activation_key and not g.isVip:
                logger.info(f"用户 {user.id} 有激活码但不是VIP，强制设置为VIP")
                g.isVip = True
                
            logger.debug(f"用户 {user.id} 访问: {request.path}, VIP状态: {g.isVip}")
            return None
            
        except Exception as e:
            logger.error(f"验证用户时发生错误: {str(e)}")
            session.clear()
            return ApiResponse().needLogin().json()

    @app.after_request
    def after_request(response):
        # 确保session持久化
        if hasattr(g, 'userid') and g.userid:
            session.permanent = True
            session.modified = True
        return response
