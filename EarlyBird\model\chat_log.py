from EarlyBird.common.libs.BaseModel import BaseModel
from sqlalchemy import Column, Integer, String
from . import TABLE_PREFIX
from datetime import datetime


def _format_date(d, f: str = "%Y-%m-%d"):
    if d is None or type(d) is not datetime:
        return ""
    return datetime.strftime(d, f)


class ChatLog(BaseModel):
    SEND = "send"
    RECEIVE = "recv"

    __tablename__ = f"{TABLE_PREFIX}chat_log"
    __table_args__ = {"comment": "对话记录"}

    modelName = Column(String(50), comment="模型名称")
    type = Column(String(1024), default="", comment="标题")
    msg = Column(String(1024), default="", comment="标题")
    uid = Column(Integer, default=0, index=True, comment="用户ID")

    def to_json(self):
        return {
            "id": self.id,
            "modelName": self.modelName,
            "type": self.type,
            "msg": self.msg,
            "uid": self.uid,
            "createAt": _format_date(self.create_time,"%Y-%m-%d %H:%M:%S"),
        } 