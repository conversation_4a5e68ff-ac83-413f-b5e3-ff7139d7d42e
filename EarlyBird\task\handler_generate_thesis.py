import logging
import json
import time
from threading import Thread, Event
import time
from typing import List
from sqlalchemy import or_
from EarlyBird.common import Result, TaskStatus
from EarlyBird.ExtendRegister.model_register import Paragraph, Thesis
from EarlyBird.ExtendRegister.db_register import db
from EarlyBird.api.talk2ai.service import Service
from EarlyBird.api.thesis.service import ThesisServie
from EarlyBird.api.user.service import UserService
from EarlyBird.api.talk2ai.dantic import ParamThesisId, ParamGenerateSingleParagraph
from EarlyBird.api.talk2ai.utils import travelOutlineDict

LOGGING = logging.getLogger(__name__)


class HandlerGenerateThesis:
    def __init__(self, flaskApp, thesisId: int, stopEvent: Event, globalSettings: dict = None) -> None:
        self.flaskApp = flaskApp
        self.thesisId = thesisId
        self.userInfo = None
        self.thesis = None
        self.stopEvent: Event = stopEvent
        self.globalSettings = globalSettings  # 添加全局设置参数

    def dispatch(self):
        LOGGING.info(f"HandlerGenerateThesis 开始运行 {self.thesisId}")
        with self.flaskApp.app_context():
            try:
                self.getThesis()

                self._updateStatus(TaskStatus.RUNING)
                self.getUser()
                self.genDigest()
                self.genOutline()
                self._updateStatus(TaskStatus.SUCCESS)
            except Exception as e:
                LOGGING.exception(e)
                self._updateStatus(TaskStatus.ERROR)

    def getUser(self):
        ## 用户信息
        userId = self.thesis["uid"]
        res: Result = UserService().get_user_info(userId)
        if not res.isSucc():
            self._updateStatus(TaskStatus.ERROR)
            raise Exception("UID 不能找到用户")

        self.userInfo = res.data
        LOGGING.info(f"userInfo {self.userInfo}")

    def getThesis(self):

        ## 论文信息
        thesis: Thesis = Thesis.query.filter(Thesis.id == self.thesisId).first()
        if thesis is None:
            self._updateStatus(TaskStatus.ERROR)
            raise Exception("thesisId 不能找到")
        self.thesis = thesis

    def genDigest(self):
        body: ParamThesisId = ParamThesisId(
            thesisId=self.thesisId,
            userId=self.userInfo["id"],
            isVipUser=self.userInfo["isVip"],
        )
        res: Result = Service().generateDigest(body)
        if res.is_success():
            LOGGING.info(f"中文摘要  生成成功 thesisiId: {self.thesisId}")
        else:
            LOGGING.error(f"中文摘要 生成失败 thesisiId: {self.thesisId}")

        res: Result = Service().generateDigestEnglish(body)
        if res.is_success():
            LOGGING.info(f"英文摘要 生成成功 thesisiId: {self.thesisId}")
        else:
            LOGGING.error(f"英文摘要 生成失败 thesisiId: {self.thesisId}")

    def genOutline(self):
        ## 从outline中收集段落
        # 摘要

        paraList: List[Paragraph] = (
            Paragraph.query.filter(Paragraph.thesisId == self.thesisId)
            .filter(
                or_(
                    Paragraph.status == TaskStatus.RUNING,
                    Paragraph.status == TaskStatus.INIT,
                )
            )
            .order_by(Paragraph.paraId)  # 按段落ID排序，确保按顺序生成
            .all()
        )
        
        LOGGING.info(f"开始批量生成 {len(paraList)} 个段落")
        
        for i, p in enumerate(paraList):
            LOGGING.info(f"开始生成段落 {i+1}/{len(paraList)}: {p.title} {p.id}")
            # Remove speed limit
            # LOGGING.info("由于kimi免费用户 每分钟3次请求，这里限速一下 ")
            # time.sleep(40)

            if self.stopEvent.is_set():
                LOGGING.error("收到了 shutdonw 消息，终止任务，退出线程")
                return

            self._updateParagraphStatue(p.id, TaskStatus.RUNING)

            # 构建生成参数
            generate_params = ParamGenerateSingleParagraph(
                id=p.id,
                thesisId=self.thesisId,
                paragraphId=p.paraId,
                title=p.title,
                userId=self.userInfo["id"],
                isVipUser=self.userInfo["isVip"],
            )
            
            # 如果有全局设置，应用到每个段落
            if hasattr(self, 'globalSettings') and self.globalSettings:
                settings = self.globalSettings
                generate_params.length = settings.get('paragraphLength', 500)
                generate_params.lengthMode = settings.get('lengthControlMode', 'fixed')
                generate_params.minLength = settings.get('minParagraphLength', 100)
                generate_params.maxLength = settings.get('maxParagraphLength', 3000)
                generate_params.instruction = settings.get('globalInstruction', '')
                
                # 根据段落类型智能调整字数
                if generate_params.lengthMode == 'auto':
                    generate_params.length = self._calculateSmartLength(p.title)

            res: Result = Service().generateSingleParagraph(generate_params)

            if res.is_success():
                self._updateParagraphStatue(p.id, TaskStatus.SUCCESS)
                LOGGING.info(f"段落 {p.title} 生成成功")
            else:
                self._updateParagraphStatue(p.id, TaskStatus.ERROR)
                LOGGING.error(f"段落 {p.title} 生成失败: {res.message}")
        
        LOGGING.info(f"批量生成完成，共处理 {len(paraList)} 个段落")
    
    def _calculateSmartLength(self, title):
        """根据段落标题智能计算字数"""
        title_lower = title.lower()
        if '引言' in title_lower or '结论' in title_lower:
            return 800
        elif '摘要' in title_lower:
            return 300
        elif '方法' in title_lower or '实验' in title_lower:
            return 600
        elif '讨论' in title_lower or '分析' in title_lower:
            return 700
        else:
            return 500  # 默认500字

    def _updateParagraphStatue(self, id, s):
        try:
            p: Paragraph = Paragraph.query.filter(Paragraph.id == id).first()
            p.status = s
            p.save()
            LOGGING.info(f"更新段落 {id} 状态为 {s}")
        except Exception as e:
            LOGGING.exception(str(e))

    def _updateStatus(self, s):
        try:
            t: Thesis = Thesis().query.get(self.thesisId)
            if t is not None:
                t.status = s
                t.save()
        except Exception as e:
            LOGGING.exception(e)


class ParasList:
    def __init__(self) -> None:
        self.paras = []

    def getList(self):
        return self.paras

    def add(self, id, title):
        self.paras.append(
            {
                "title": title,
                "paraId": id,
                "state": "w",
            }
        )

    def getJson(self):
        return json.dumps(self.paras, ensure_ascii=False)

    def setStateRuning(self, id):
        for p in self.paras:
            if p["paraId"] == id:
                p["state"] = "r"
                LOGGING.info(f"更新 {id} 为 生成中")

    def setStateSuccess(self, id):
        for p in self.paras:
            if p["paraId"] == id:
                p["state"] = "s"
                LOGGING.info(f"更新 {id} 为 已完成")

        print(self.paras)

    def setStateError(self, id):
        for p in self.paras:
            if p["paraId"] == id:
                p["state"] = "e"
                LOGGING.info(f"更新 {id} 为 生成错误")
