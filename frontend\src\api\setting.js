//定义了和服务器端交互的接口
//具体的提交格式在utils/request中进行了定义
//request.js里面实际上没有request的定义字样，实际上import的是export defaut的部分，也就是 export default service
import request from '@/utils/request'


export function saveSetting(params) {
    return request({
        url: '/api/home/<USER>',
        method: 'post',
        data: params,
        timeout: 30000,
    })
}

export function getSetting(params) {
    return request({
        url: '/api/home/<USER>',
        method: 'post',
        data: params,
        timeout: 30000,
    })
}