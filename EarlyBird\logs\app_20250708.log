2025-07-08 14:47:17  init_flask.py 27: INFO  {'CpuCount': 6, 'Mem': '32 GB'} [MainThread]
2025-07-08 14:47:17  web_server.py 32: INFO  Static directory: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\ui [MainThread]
2025-07-08 14:47:17  web_server.py 33: INFO  Static directory exists: True [MainThread]
2025-07-08 14:47:17  web_server.py 37: INFO  设置工作目录: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird [MainThread]
2025-07-08 14:47:17  EarlyBird.config.config 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-08 14:47:17  config.py 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-08 14:47:17  config.py 94: INFO  Database URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-08 14:47:20  EarlyBird.config.wechat_pay_config 49: WARNING  无法从数据库获取微信支付配置: No application found. Either work inside a view function or push an application context. See http://flask-sqlalchemy.pocoo.org/contexts/. [MainThread]
2025-07-08 14:47:20  wechat_pay_config.py 49: WARNING  无法从数据库获取微信支付配置: No application found. Either work inside a view function or push an application context. See http://flask-sqlalchemy.pocoo.org/contexts/. [MainThread]
2025-07-08 14:47:20  EarlyBird.config.wechat_pay_config 52: INFO  使用环境变量中的微信支付配置 [MainThread]
2025-07-08 14:47:20  wechat_pay_config.py 52: INFO  使用环境变量中的微信支付配置 [MainThread]
2025-07-08 14:47:20  EarlyBird.common.wechat_pay_v3 54: ERROR  加载商户私钥失败: [Errno 2] No such file or directory: 'certs/apiclient_key.pem' [MainThread]
2025-07-08 14:47:20  wechat_pay_v3.py 54: ERROR  加载商户私钥失败: [Errno 2] No such file or directory: 'certs/apiclient_key.pem' [MainThread]
2025-07-08 14:47:20  EarlyBird.api.pay.apis 31: ERROR  微信支付V3初始化失败: [Errno 2] No such file or directory: 'certs/apiclient_key.pem' [MainThread]
2025-07-08 14:47:20  apis.py 31: ERROR  微信支付V3初始化失败: [Errno 2] No such file or directory: 'certs/apiclient_key.pem' [MainThread]
2025-07-08 14:47:20  EarlyBird.api.admin.app 39: INFO  ✅ 后台管理系统API已注册 [MainThread]
2025-07-08 14:47:20  app.py 39: INFO  ✅ 后台管理系统API已注册 [MainThread]
2025-07-08 14:47:21  EarlyBird 40: INFO  确保数据目录存在: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\data [MainThread]
2025-07-08 14:47:21  server.py 40: INFO  确保数据目录存在: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\data [MainThread]
2025-07-08 14:47:21  EarlyBird 44: INFO  开始执行数据库迁移... [MainThread]
2025-07-08 14:47:21  server.py 44: INFO  开始执行数据库迁移... [MainThread]
2025-07-08 14:47:21  config.py 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-08 14:47:21  config.py 94: INFO  Database URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-08 14:47:21  EarlyBird 53: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-08 14:47:21  server.py 53: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-08 14:47:21  EarlyBird 97: WARNING  ChatLog表不存在，将在应用启动时自动创建 [MainThread]
2025-07-08 14:47:21  server.py 97: WARNING  ChatLog表不存在，将在应用启动时自动创建 [MainThread]
2025-07-08 14:47:21  add_uid_to_chatlog.py 20: INFO  开始执行ChatLog表迁移... [MainThread]
2025-07-08 14:47:21  add_uid_to_chatlog.py 24: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-08 14:47:21  add_uid_to_chatlog.py 38: WARNING  ChatLog表不存在，可能需要先创建表 [MainThread]
2025-07-08 14:47:21  task_api_server.py 31: INFO  TaskApiServer start [MainThread]
2025-07-08 14:47:21  task_paper_generator.py 38: INFO  论文生成任务处理线程初始化 [MainThread]
2025-07-08 14:47:21  task_paper_generator.py 72: INFO  论文生成任务处理线程启动 [Thread-2]
2025-07-08 14:47:21  task_api_server.py 47: INFO  TaskApiServer listen on 127.0.0.1:3302 [Thread-1]
2025-07-08 14:47:21  task_api_server.py 17: INFO  _ApiServerThread start [Thread-3]
2025-07-08 14:48:07  web_server.py 78: INFO  Serving request for path: admin/settings/model [waitress-0]
2025-07-08 14:48:07  web_server.py 84: INFO  Serving index.html for path: admin/settings/model [waitress-0]
2025-07-08 14:48:07  web_server.py 78: INFO  Serving request for path: favicon.ico [waitress-1]
2025-07-08 14:48:07  web_server.py 84: INFO  Serving index.html for path: favicon.ico [waitress-1]
2025-07-08 14:48:08  web_server.py 78: INFO  Serving request for path: admin/settings/model [waitress-2]
2025-07-08 14:48:08  web_server.py 84: INFO  Serving index.html for path: admin/settings/model [waitress-2]
2025-07-08 14:48:09  web_server.py 78: INFO  Serving request for path: admin/settings/model [waitress-3]
2025-07-08 14:48:09  web_server.py 84: INFO  Serving index.html for path: admin/settings/model [waitress-3]
2025-07-08 14:48:09  web_server.py 78: INFO  Serving request for path: admin/settings/model [waitress-0]
2025-07-08 14:48:09  web_server.py 84: INFO  Serving index.html for path: admin/settings/model [waitress-0]
2025-07-08 14:48:36  web_server.py 78: INFO  Serving request for path: admin/settings/model [waitress-1]
2025-07-08 14:48:36  web_server.py 84: INFO  Serving index.html for path: admin/settings/model [waitress-1]
2025-07-08 14:48:36  web_server.py 78: INFO  Serving request for path: js/chunk-vendors.7f82116b.js [waitress-2]
2025-07-08 14:48:36  web_server.py 78: INFO  Serving request for path: js/app.7235a76e.js [waitress-3]
2025-07-08 14:48:36  web_server.py 81: INFO  Serving static file: js/chunk-vendors.7f82116b.js [waitress-2]
2025-07-08 14:48:36  web_server.py 81: INFO  Serving static file: js/app.7235a76e.js [waitress-3]
2025-07-08 14:48:36  web_server.py 78: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-0]
2025-07-08 14:48:36  web_server.py 78: INFO  Serving request for path: css/app.2ea35194.css [waitress-1]
2025-07-08 14:48:36  web_server.py 81: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-0]
2025-07-08 14:48:36  web_server.py 81: INFO  Serving static file: css/app.2ea35194.css [waitress-1]
2025-07-08 14:48:37  web_server.py 78: INFO  Serving request for path: css/8.38fdf013.css [waitress-3]
2025-07-08 14:48:37  web_server.py 78: INFO  Serving request for path: js/8.a13b62ce.js [waitress-0]
2025-07-08 14:48:37  web_server.py 81: INFO  Serving static file: css/8.38fdf013.css [waitress-3]
2025-07-08 14:48:37  web_server.py 81: INFO  Serving static file: js/8.a13b62ce.js [waitress-0]
2025-07-08 14:48:37  web_server.py 78: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-2]
2025-07-08 14:48:37  web_server.py 81: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-2]
2025-07-08 14:48:37  web_server.py 78: INFO  Serving request for path: fonts/element-icons.ff18efd1.woff [waitress-1]
2025-07-08 14:48:37  web_server.py 81: INFO  Serving static file: fonts/element-icons.ff18efd1.woff [waitress-1]
2025-07-08 14:48:37  web_server.py 78: INFO  Serving request for path: logo.png [waitress-2]
2025-07-08 14:48:37  web_server.py 81: INFO  Serving static file: logo.png [waitress-2]
2025-07-08 14:48:39  web_server.py 78: INFO  Serving request for path: js/388.6189b2b6.js [waitress-1]
2025-07-08 14:48:39  web_server.py 81: INFO  Serving static file: js/388.6189b2b6.js [waitress-1]
2025-07-08 14:48:40  web_server.py 78: INFO  Serving request for path: css/315.27b4d9c8.css [waitress-2]
2025-07-08 14:48:40  web_server.py 78: INFO  Serving request for path: js/315.f13759ae.js [waitress-3]
2025-07-08 14:48:40  web_server.py 81: INFO  Serving static file: css/315.27b4d9c8.css [waitress-2]
2025-07-08 14:48:40  web_server.py 81: INFO  Serving static file: js/315.f13759ae.js [waitress-3]
2025-07-08 16:38:48  web_server.py 78: INFO  Serving request for path: admin/settings/payment [waitress-0]
2025-07-08 16:38:48  web_server.py 84: INFO  Serving index.html for path: admin/settings/payment [waitress-0]
2025-07-08 16:38:48  web_server.py 78: INFO  Serving request for path: js/chunk-vendors.7f82116b.js [waitress-1]
2025-07-08 16:38:48  web_server.py 78: INFO  Serving request for path: js/app.8406b136.js [waitress-2]
2025-07-08 16:38:48  web_server.py 81: INFO  Serving static file: js/chunk-vendors.7f82116b.js [waitress-1]
2025-07-08 16:38:48  web_server.py 81: INFO  Serving static file: js/app.8406b136.js [waitress-2]
2025-07-08 16:38:48  web_server.py 78: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-3]
2025-07-08 16:38:48  web_server.py 78: INFO  Serving request for path: css/app.7ae45238.css [waitress-0]
2025-07-08 16:38:48  web_server.py 81: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-3]
2025-07-08 16:38:48  web_server.py 81: INFO  Serving static file: css/app.7ae45238.css [waitress-0]
2025-07-08 16:38:48  web_server.py 78: INFO  Serving request for path: js/388.6189b2b6.js [waitress-2]
2025-07-08 16:38:48  web_server.py 81: INFO  Serving static file: js/388.6189b2b6.js [waitress-2]
2025-07-08 16:38:48  web_server.py 78: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-3]
2025-07-08 16:38:48  web_server.py 81: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-3]
2025-07-08 16:38:48  EarlyBird.api.admin.auth 37: WARNING  Token已过期 [waitress-1]
2025-07-08 16:38:48  web_server.py 78: INFO  Serving request for path: fonts/element-icons.ff18efd1.woff [waitress-0]
2025-07-08 16:38:48  auth.py 37: WARNING  Token已过期 [waitress-1]
2025-07-08 16:38:48  EarlyBird.ExtendRegister.hook_register 92: WARNING  管理员API Token无效: /api/admin/auth/profile [waitress-1]
2025-07-08 16:38:48  web_server.py 81: INFO  Serving static file: fonts/element-icons.ff18efd1.woff [waitress-0]
2025-07-08 16:38:48  hook_register.py 92: WARNING  管理员API Token无效: /api/admin/auth/profile [waitress-1]
2025-07-08 16:38:48  web_server.py 78: INFO  Serving request for path: css/318.61ca778d.css [waitress-2]
2025-07-08 16:38:48  web_server.py 78: INFO  Serving request for path: js/318.f0e0c059.js [waitress-3]
2025-07-08 16:38:48  web_server.py 81: INFO  Serving static file: js/318.f0e0c059.js [waitress-3]
2025-07-08 16:38:48  web_server.py 81: INFO  Serving static file: css/318.61ca778d.css [waitress-2]
2025-07-08 16:38:48  EarlyBird.api.admin.auth 37: WARNING  Token已过期 [waitress-1]
2025-07-08 16:38:48  auth.py 37: WARNING  Token已过期 [waitress-1]
2025-07-08 16:38:48  EarlyBird.api.admin.auth 77: WARNING  管理员API Token无效: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:48  auth.py 77: WARNING  管理员API Token无效: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:48  EarlyBird.api.admin.auth 37: WARNING  Token已过期 [waitress-0]
2025-07-08 16:38:48  auth.py 37: WARNING  Token已过期 [waitress-0]
2025-07-08 16:38:48  EarlyBird.ExtendRegister.hook_register 92: WARNING  管理员API Token无效: /api/admin/auth/profile [waitress-0]
2025-07-08 16:38:48  hook_register.py 92: WARNING  管理员API Token无效: /api/admin/auth/profile [waitress-0]
2025-07-08 16:38:48  EarlyBird.api.admin.auth 37: WARNING  Token已过期 [waitress-3]
2025-07-08 16:38:48  auth.py 37: WARNING  Token已过期 [waitress-3]
2025-07-08 16:38:48  EarlyBird.api.admin.auth 77: WARNING  管理员API Token无效: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:48  auth.py 77: WARNING  管理员API Token无效: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:48  EarlyBird.api.admin.auth 37: WARNING  Token已过期 [waitress-2]
2025-07-08 16:38:48  auth.py 37: WARNING  Token已过期 [waitress-2]
2025-07-08 16:38:48  EarlyBird.ExtendRegister.hook_register 92: WARNING  管理员API Token无效: /api/admin/auth/profile [waitress-2]
2025-07-08 16:38:48  hook_register.py 92: WARNING  管理员API Token无效: /api/admin/auth/profile [waitress-2]
2025-07-08 16:38:48  web_server.py 78: INFO  Serving request for path: logo.png [waitress-1]
2025-07-08 16:38:48  web_server.py 81: INFO  Serving static file: logo.png [waitress-1]
2025-07-08 16:38:48  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:48  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:48  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:48  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:48  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:48  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:48  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:48  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:48  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:48  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:48  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:48  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:48  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:48  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:48  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:48  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:48  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:48  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:48  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:48  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:48  EarlyBird.ExtendRegister.hook_register 73: WARNING  管理员API未提供Authorization头: /api/admin/stats/overview [waitress-2]
2025-07-08 16:38:48  hook_register.py 73: WARNING  管理员API未提供Authorization头: /api/admin/stats/overview [waitress-2]
2025-07-08 16:38:48  EarlyBird.ExtendRegister.hook_register 73: WARNING  管理员API未提供Authorization头: /api/admin/stats/users [waitress-1]
2025-07-08 16:38:48  hook_register.py 73: WARNING  管理员API未提供Authorization头: /api/admin/stats/users [waitress-1]
2025-07-08 16:38:48  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:48  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:48  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:48  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:48  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:48  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:48  EarlyBird.ExtendRegister.hook_register 73: WARNING  管理员API未提供Authorization头: /api/admin/stats/overview [waitress-1]
2025-07-08 16:38:48  EarlyBird.ExtendRegister.hook_register 73: WARNING  管理员API未提供Authorization头: /api/admin/stats/users [waitress-0]
2025-07-08 16:38:48  hook_register.py 73: WARNING  管理员API未提供Authorization头: /api/admin/stats/overview [waitress-1]
2025-07-08 16:38:48  hook_register.py 73: WARNING  管理员API未提供Authorization头: /api/admin/stats/users [waitress-0]
2025-07-08 16:38:48  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:48  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:48  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:48  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:48  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:48  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:48  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:48  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:48  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:48  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:48  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:48  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:48  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:48  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:48  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:48  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:48  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:48  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:48  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:48  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:48  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:48  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:48  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:48  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:48  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:48  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:48  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:48  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:48  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:48  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:48  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:48  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:48  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:48  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:48  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:48  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:48  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:48  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:48  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:48  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:48  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:48  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:48  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:48  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:48  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:48  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:49  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:49  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:50  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:50  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:50  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:50  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-08 16:38:50  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:50  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:50  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:50  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:50  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:50  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:50  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:50  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:50  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:50  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:50  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:50  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:50  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:50  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:50  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:50  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:50  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:50  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:50  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:50  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:50  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:50  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:50  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:50  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:50  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:50  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:50  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:50  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:50  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:50  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:50  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:50  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:50  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:50  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:50  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:50  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:50  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:50  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:50  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:50  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:50  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:50  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:50  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:50  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:50  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:50  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:50  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:50  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:50  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:50  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:50  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:50  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:50  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:50  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:50  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:50  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:50  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:50  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:50  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:50  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:50  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:50  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-08 16:38:50  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:50  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-08 16:38:50  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:50  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-08 16:38:54  web_server.py 78: INFO  Serving request for path: css/315.27b4d9c8.css [waitress-1]
2025-07-08 16:38:54  web_server.py 78: INFO  Serving request for path: js/315.f13759ae.js [waitress-3]
2025-07-08 16:38:54  web_server.py 81: INFO  Serving static file: css/315.27b4d9c8.css [waitress-1]
2025-07-08 16:38:54  web_server.py 81: INFO  Serving static file: js/315.f13759ae.js [waitress-3]
2025-07-08 16:39:00  web_server.py 78: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-2]
2025-07-08 16:39:00  web_server.py 78: INFO  Serving request for path: css/app.7ae45238.css [waitress-1]
2025-07-08 16:39:00  web_server.py 78: INFO  Serving request for path: css/315.27b4d9c8.css [waitress-0]
2025-07-08 16:39:00  web_server.py 78: INFO  Serving request for path: css/318.61ca778d.css [waitress-3]
2025-07-08 16:39:00  web_server.py 81: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-2]
2025-07-08 16:39:00  task.py 113: WARNING  Task queue depth is 1 [MainThread]
2025-07-08 16:39:00  web_server.py 81: INFO  Serving static file: css/app.7ae45238.css [waitress-1]
2025-07-08 16:39:00  web_server.py 81: INFO  Serving static file: css/315.27b4d9c8.css [waitress-0]
2025-07-08 16:39:00  web_server.py 81: INFO  Serving static file: css/318.61ca778d.css [waitress-3]
2025-07-08 16:39:00  web_server.py 78: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-2]
2025-07-08 16:39:00  web_server.py 84: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-2]
2025-07-08 16:39:05  web_server.py 78: INFO  Serving request for path: css/8.38fdf013.css [waitress-1]
2025-07-08 16:39:05  web_server.py 78: INFO  Serving request for path: js/8.a13b62ce.js [waitress-0]
2025-07-08 16:39:05  web_server.py 81: INFO  Serving static file: css/8.38fdf013.css [waitress-1]
2025-07-08 16:39:05  web_server.py 81: INFO  Serving static file: js/8.a13b62ce.js [waitress-0]
2025-07-08 16:42:23  web_server.py 78: INFO  Serving request for path: admin/settings/payment [waitress-2]
2025-07-08 16:42:23  web_server.py 84: INFO  Serving index.html for path: admin/settings/payment [waitress-2]
2025-07-08 16:42:23  web_server.py 78: INFO  Serving request for path: js/chunk-vendors.7f82116b.js [waitress-1]
2025-07-08 16:42:23  web_server.py 81: INFO  Serving static file: js/chunk-vendors.7f82116b.js [waitress-1]
2025-07-08 16:42:23  web_server.py 78: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-0]
2025-07-08 16:42:23  web_server.py 78: INFO  Serving request for path: js/app.8406b136.js [waitress-3]
2025-07-08 16:42:23  web_server.py 84: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-0]
2025-07-08 16:42:23  task.py 113: WARNING  Task queue depth is 1 [MainThread]
2025-07-08 16:42:23  web_server.py 81: INFO  Serving static file: js/app.8406b136.js [waitress-3]
2025-07-08 16:42:23  web_server.py 78: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-2]
2025-07-08 16:42:23  web_server.py 81: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-2]
2025-07-08 16:42:23  web_server.py 78: INFO  Serving request for path: css/app.7ae45238.css [waitress-0]
2025-07-08 16:42:23  web_server.py 81: INFO  Serving static file: css/app.7ae45238.css [waitress-0]
2025-07-08 16:42:24  web_server.py 78: INFO  Serving request for path: js/388.6189b2b6.js [waitress-3]
2025-07-08 16:42:24  web_server.py 81: INFO  Serving static file: js/388.6189b2b6.js [waitress-3]
2025-07-08 16:42:24  web_server.py 78: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-2]
2025-07-08 16:42:24  web_server.py 81: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-2]
2025-07-08 16:42:24  web_server.py 78: INFO  Serving request for path: fonts/element-icons.ff18efd1.woff [waitress-1]
2025-07-08 16:42:24  web_server.py 81: INFO  Serving static file: fonts/element-icons.ff18efd1.woff [waitress-1]
2025-07-08 16:42:24  web_server.py 78: INFO  Serving request for path: logo.png [waitress-3]
2025-07-08 16:42:24  web_server.py 81: INFO  Serving static file: logo.png [waitress-3]
2025-07-08 16:42:26  web_server.py 78: INFO  Serving request for path: css/315.27b4d9c8.css [waitress-0]
2025-07-08 16:42:26  web_server.py 78: INFO  Serving request for path: js/315.f13759ae.js [waitress-2]
2025-07-08 16:42:26  web_server.py 81: INFO  Serving static file: css/315.27b4d9c8.css [waitress-0]
2025-07-08 16:42:26  web_server.py 81: INFO  Serving static file: js/315.f13759ae.js [waitress-2]
2025-07-08 16:42:27  web_server.py 78: INFO  Serving request for path: css/8.38fdf013.css [waitress-1]
2025-07-08 16:42:27  web_server.py 78: INFO  Serving request for path: js/8.a13b62ce.js [waitress-3]
2025-07-08 16:42:27  web_server.py 81: INFO  Serving static file: js/8.a13b62ce.js [waitress-3]
2025-07-08 16:42:27  web_server.py 81: INFO  Serving static file: css/8.38fdf013.css [waitress-1]
2025-07-08 16:46:34  init_flask.py 27: INFO  {'CpuCount': 6, 'Mem': '32 GB'} [MainThread]
2025-07-08 16:46:34  web_server.py 32: INFO  Static directory: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\ui [MainThread]
2025-07-08 16:46:34  web_server.py 33: INFO  Static directory exists: True [MainThread]
2025-07-08 16:46:34  web_server.py 37: INFO  设置工作目录: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird [MainThread]
2025-07-08 16:46:34  EarlyBird.config.config 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-08 16:46:34  config.py 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-08 16:46:34  config.py 94: INFO  Database URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-08 16:46:36  EarlyBird.config.wechat_pay_config 49: WARNING  无法从数据库获取微信支付配置: No application found. Either work inside a view function or push an application context. See http://flask-sqlalchemy.pocoo.org/contexts/. [MainThread]
2025-07-08 16:46:36  wechat_pay_config.py 49: WARNING  无法从数据库获取微信支付配置: No application found. Either work inside a view function or push an application context. See http://flask-sqlalchemy.pocoo.org/contexts/. [MainThread]
2025-07-08 16:46:36  EarlyBird.config.wechat_pay_config 52: INFO  使用环境变量中的微信支付配置 [MainThread]
2025-07-08 16:46:36  wechat_pay_config.py 52: INFO  使用环境变量中的微信支付配置 [MainThread]
2025-07-08 16:46:36  EarlyBird.common.wechat_pay_v3 54: ERROR  加载商户私钥失败: [Errno 2] No such file or directory: 'certs/apiclient_key.pem' [MainThread]
2025-07-08 16:46:36  wechat_pay_v3.py 54: ERROR  加载商户私钥失败: [Errno 2] No such file or directory: 'certs/apiclient_key.pem' [MainThread]
2025-07-08 16:46:36  EarlyBird.api.pay.apis 31: ERROR  微信支付V3初始化失败: [Errno 2] No such file or directory: 'certs/apiclient_key.pem' [MainThread]
2025-07-08 16:46:36  apis.py 31: ERROR  微信支付V3初始化失败: [Errno 2] No such file or directory: 'certs/apiclient_key.pem' [MainThread]
2025-07-08 16:46:37  EarlyBird.api.admin.app 39: INFO  ✅ 后台管理系统API已注册 [MainThread]
2025-07-08 16:46:37  app.py 39: INFO  ✅ 后台管理系统API已注册 [MainThread]
2025-07-08 16:46:37  web_server.py 97: ERROR  创建应用失败 [MainThread]
Traceback (most recent call last):
  File "d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\web_server.py", line 72, in create_custom_app
    register_blueprints(app)
  File "d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\ExtendRegister\bp_register.py", line 15, in register_bp
    app.register_blueprint(admin_bp)  # 注册admin_bp到Flask应用
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask\scaffold.py", line 56, in wrapper_func
    return f(self, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask\app.py", line 1030, in register_blueprint
    blueprint.register(self, options)
  File "C:\Python311\Lib\site-packages\flask\blueprints.py", line 304, in register
    raise ValueError(
ValueError: The name 'admin' is already registered for a different blueprint. Use 'name=' to provide a unique name.
2025-07-08 16:46:37  EarlyBird 33: ERROR  Failed to create Flask app [MainThread]
2025-07-08 16:46:37  server.py 33: ERROR  Failed to create Flask app [MainThread]
2025-07-08 16:47:46  init_flask.py 27: INFO  {'CpuCount': 6, 'Mem': '32 GB'} [MainThread]
2025-07-08 16:47:46  web_server.py 32: INFO  Static directory: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\ui [MainThread]
2025-07-08 16:47:46  web_server.py 33: INFO  Static directory exists: True [MainThread]
2025-07-08 16:47:46  web_server.py 37: INFO  设置工作目录: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird [MainThread]
2025-07-08 16:47:46  EarlyBird.config.config 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-08 16:47:46  config.py 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-08 16:47:46  config.py 94: INFO  Database URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-08 16:47:48  EarlyBird.config.wechat_pay_config 49: WARNING  无法从数据库获取微信支付配置: No application found. Either work inside a view function or push an application context. See http://flask-sqlalchemy.pocoo.org/contexts/. [MainThread]
2025-07-08 16:47:48  wechat_pay_config.py 49: WARNING  无法从数据库获取微信支付配置: No application found. Either work inside a view function or push an application context. See http://flask-sqlalchemy.pocoo.org/contexts/. [MainThread]
2025-07-08 16:47:48  EarlyBird.config.wechat_pay_config 52: INFO  使用环境变量中的微信支付配置 [MainThread]
2025-07-08 16:47:48  wechat_pay_config.py 52: INFO  使用环境变量中的微信支付配置 [MainThread]
2025-07-08 16:47:48  EarlyBird.common.wechat_pay_v3 54: ERROR  加载商户私钥失败: [Errno 2] No such file or directory: 'certs/apiclient_key.pem' [MainThread]
2025-07-08 16:47:48  wechat_pay_v3.py 54: ERROR  加载商户私钥失败: [Errno 2] No such file or directory: 'certs/apiclient_key.pem' [MainThread]
2025-07-08 16:47:48  EarlyBird.api.pay.apis 31: ERROR  微信支付V3初始化失败: [Errno 2] No such file or directory: 'certs/apiclient_key.pem' [MainThread]
2025-07-08 16:47:48  apis.py 31: ERROR  微信支付V3初始化失败: [Errno 2] No such file or directory: 'certs/apiclient_key.pem' [MainThread]
2025-07-08 16:47:48  EarlyBird.api.admin.app 39: INFO  ✅ 后台管理系统API已注册 [MainThread]
2025-07-08 16:47:48  app.py 39: INFO  ✅ 后台管理系统API已注册 [MainThread]
2025-07-08 16:47:48  EarlyBird 40: INFO  确保数据目录存在: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\data [MainThread]
2025-07-08 16:47:48  server.py 40: INFO  确保数据目录存在: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\data [MainThread]
2025-07-08 16:47:48  EarlyBird 44: INFO  开始执行数据库迁移... [MainThread]
2025-07-08 16:47:48  server.py 44: INFO  开始执行数据库迁移... [MainThread]
2025-07-08 16:47:48  config.py 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-08 16:47:48  config.py 94: INFO  Database URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-08 16:47:48  EarlyBird 53: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-08 16:47:48  server.py 53: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-08 16:47:48  EarlyBird 97: WARNING  ChatLog表不存在，将在应用启动时自动创建 [MainThread]
2025-07-08 16:47:48  server.py 97: WARNING  ChatLog表不存在，将在应用启动时自动创建 [MainThread]
2025-07-08 16:47:48  add_uid_to_chatlog.py 20: INFO  开始执行ChatLog表迁移... [MainThread]
2025-07-08 16:47:48  add_uid_to_chatlog.py 24: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-08 16:47:48  add_uid_to_chatlog.py 38: WARNING  ChatLog表不存在，可能需要先创建表 [MainThread]
2025-07-08 16:47:48  task_api_server.py 31: INFO  TaskApiServer start [MainThread]
2025-07-08 16:47:48  task_paper_generator.py 38: INFO  论文生成任务处理线程初始化 [MainThread]
2025-07-08 16:47:48  task_paper_generator.py 72: INFO  论文生成任务处理线程启动 [Thread-2]
2025-07-08 16:47:48  task_api_server.py 47: INFO  TaskApiServer listen on 127.0.0.1:3302 [Thread-1]
2025-07-08 16:47:48  task_api_server.py 17: INFO  _ApiServerThread start [Thread-3]
2025-07-08 16:47:56  web_server.py 78: INFO  Serving request for path: admin/settings/payment [waitress-0]
2025-07-08 16:47:56  web_server.py 84: INFO  Serving index.html for path: admin/settings/payment [waitress-0]
2025-07-08 16:47:57  web_server.py 78: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-1]
2025-07-08 16:47:57  web_server.py 78: INFO  Serving request for path: js/chunk-vendors.7f82116b.js [waitress-2]
2025-07-08 16:47:57  web_server.py 84: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-1]
2025-07-08 16:47:57  web_server.py 81: INFO  Serving static file: js/chunk-vendors.7f82116b.js [waitress-2]
2025-07-08 16:47:57  web_server.py 78: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-3]
2025-07-08 16:47:57  web_server.py 78: INFO  Serving request for path: js/app.8406b136.js [waitress-0]
2025-07-08 16:47:57  task.py 113: WARNING  Task queue depth is 1 [MainThread]
2025-07-08 16:47:57  web_server.py 81: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-3]
2025-07-08 16:47:57  web_server.py 81: INFO  Serving static file: js/app.8406b136.js [waitress-0]
2025-07-08 16:47:57  web_server.py 78: INFO  Serving request for path: css/app.7ae45238.css [waitress-1]
2025-07-08 16:47:57  web_server.py 81: INFO  Serving static file: css/app.7ae45238.css [waitress-1]
2025-07-08 16:47:57  web_server.py 78: INFO  Serving request for path: js/388.6189b2b6.js [waitress-2]
2025-07-08 16:47:57  web_server.py 81: INFO  Serving static file: js/388.6189b2b6.js [waitress-2]
2025-07-08 16:47:57  web_server.py 78: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-0]
2025-07-08 16:47:57  web_server.py 81: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-0]
2025-07-08 16:47:57  web_server.py 78: INFO  Serving request for path: fonts/element-icons.ff18efd1.woff [waitress-1]
2025-07-08 16:47:57  web_server.py 81: INFO  Serving static file: fonts/element-icons.ff18efd1.woff [waitress-1]
2025-07-08 16:47:57  web_server.py 78: INFO  Serving request for path: logo.png [waitress-2]
2025-07-08 16:47:57  web_server.py 81: INFO  Serving static file: logo.png [waitress-2]
2025-07-08 16:47:58  web_server.py 78: INFO  Serving request for path: admin/settings/payment [waitress-0]
2025-07-08 16:47:58  web_server.py 84: INFO  Serving index.html for path: admin/settings/payment [waitress-0]
2025-07-08 16:47:58  web_server.py 78: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-1]
2025-07-08 16:47:58  web_server.py 84: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-1]
2025-07-08 16:47:58  web_server.py 78: INFO  Serving request for path: js/chunk-vendors.7f82116b.js [waitress-3]
2025-07-08 16:47:58  web_server.py 81: INFO  Serving static file: js/chunk-vendors.7f82116b.js [waitress-3]
2025-07-08 16:47:58  web_server.py 78: INFO  Serving request for path: js/app.8406b136.js [waitress-2]
2025-07-08 16:47:58  web_server.py 81: INFO  Serving static file: js/app.8406b136.js [waitress-2]
2025-07-08 16:47:58  web_server.py 78: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-0]
2025-07-08 16:47:58  web_server.py 81: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-0]
2025-07-08 16:47:58  web_server.py 78: INFO  Serving request for path: css/app.7ae45238.css [waitress-1]
2025-07-08 16:47:58  web_server.py 81: INFO  Serving static file: css/app.7ae45238.css [waitress-1]
2025-07-08 16:47:58  web_server.py 78: INFO  Serving request for path: js/388.6189b2b6.js [waitress-3]
2025-07-08 16:47:58  web_server.py 81: INFO  Serving static file: js/388.6189b2b6.js [waitress-3]
2025-07-08 16:47:58  web_server.py 78: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-2]
2025-07-08 16:47:58  web_server.py 81: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-2]
2025-07-08 16:47:58  web_server.py 78: INFO  Serving request for path: logo.png [waitress-1]
2025-07-08 16:47:58  web_server.py 81: INFO  Serving static file: logo.png [waitress-1]
2025-07-08 16:48:08  web_server.py 78: INFO  Serving request for path: css/315.27b4d9c8.css [waitress-3]
2025-07-08 16:48:08  web_server.py 78: INFO  Serving request for path: js/315.f13759ae.js [waitress-2]
2025-07-08 16:48:08  web_server.py 81: INFO  Serving static file: css/315.27b4d9c8.css [waitress-3]
2025-07-08 16:48:08  web_server.py 81: INFO  Serving static file: js/315.f13759ae.js [waitress-2]
2025-07-08 16:51:21  init_flask.py 27: INFO  {'CpuCount': 6, 'Mem': '32 GB'} [MainThread]
2025-07-08 16:51:21  web_server.py 32: INFO  Static directory: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\ui [MainThread]
2025-07-08 16:51:21  web_server.py 33: INFO  Static directory exists: True [MainThread]
2025-07-08 16:51:21  web_server.py 37: INFO  设置工作目录: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird [MainThread]
2025-07-08 16:51:21  EarlyBird.config.config 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-08 16:51:21  config.py 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-08 16:51:21  config.py 94: INFO  Database URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-08 16:51:23  EarlyBird.config.wechat_pay_config 49: WARNING  无法从数据库获取微信支付配置: No application found. Either work inside a view function or push an application context. See http://flask-sqlalchemy.pocoo.org/contexts/. [MainThread]
2025-07-08 16:51:23  wechat_pay_config.py 49: WARNING  无法从数据库获取微信支付配置: No application found. Either work inside a view function or push an application context. See http://flask-sqlalchemy.pocoo.org/contexts/. [MainThread]
2025-07-08 16:51:23  EarlyBird.config.wechat_pay_config 52: INFO  使用环境变量中的微信支付配置 [MainThread]
2025-07-08 16:51:23  wechat_pay_config.py 52: INFO  使用环境变量中的微信支付配置 [MainThread]
2025-07-08 16:51:23  EarlyBird.common.wechat_pay_v3 54: ERROR  加载商户私钥失败: [Errno 2] No such file or directory: 'certs/apiclient_key.pem' [MainThread]
2025-07-08 16:51:23  wechat_pay_v3.py 54: ERROR  加载商户私钥失败: [Errno 2] No such file or directory: 'certs/apiclient_key.pem' [MainThread]
2025-07-08 16:51:23  EarlyBird.api.pay.apis 31: ERROR  微信支付V3初始化失败: [Errno 2] No such file or directory: 'certs/apiclient_key.pem' [MainThread]
2025-07-08 16:51:23  apis.py 31: ERROR  微信支付V3初始化失败: [Errno 2] No such file or directory: 'certs/apiclient_key.pem' [MainThread]
2025-07-08 16:51:23  EarlyBird.api.admin.app 39: INFO  ✅ 后台管理系统API已注册 [MainThread]
2025-07-08 16:51:23  app.py 39: INFO  ✅ 后台管理系统API已注册 [MainThread]
2025-07-08 16:51:23  EarlyBird 40: INFO  确保数据目录存在: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\data [MainThread]
2025-07-08 16:51:23  server.py 40: INFO  确保数据目录存在: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\data [MainThread]
2025-07-08 16:51:23  EarlyBird 44: INFO  开始执行数据库迁移... [MainThread]
2025-07-08 16:51:23  server.py 44: INFO  开始执行数据库迁移... [MainThread]
2025-07-08 16:51:23  config.py 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-08 16:51:23  config.py 94: INFO  Database URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-08 16:51:23  EarlyBird 53: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-08 16:51:23  server.py 53: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-08 16:51:23  EarlyBird 97: WARNING  ChatLog表不存在，将在应用启动时自动创建 [MainThread]
2025-07-08 16:51:23  server.py 97: WARNING  ChatLog表不存在，将在应用启动时自动创建 [MainThread]
2025-07-08 16:51:23  add_uid_to_chatlog.py 20: INFO  开始执行ChatLog表迁移... [MainThread]
2025-07-08 16:51:23  add_uid_to_chatlog.py 24: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-08 16:51:23  add_uid_to_chatlog.py 38: WARNING  ChatLog表不存在，可能需要先创建表 [MainThread]
2025-07-08 16:51:23  task_api_server.py 31: INFO  TaskApiServer start [MainThread]
2025-07-08 16:51:23  task_paper_generator.py 38: INFO  论文生成任务处理线程初始化 [MainThread]
2025-07-08 16:51:23  task_paper_generator.py 72: INFO  论文生成任务处理线程启动 [Thread-2]
2025-07-08 16:51:23  task_api_server.py 47: INFO  TaskApiServer listen on 127.0.0.1:3302 [Thread-1]
2025-07-08 16:51:23  task_api_server.py 17: INFO  _ApiServerThread start [Thread-3]
2025-07-08 16:51:26  web_server.py 78: INFO  Serving request for path: admin/settings/payment [waitress-0]
2025-07-08 16:51:26  web_server.py 84: INFO  Serving index.html for path: admin/settings/payment [waitress-0]
2025-07-08 16:51:26  web_server.py 78: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-1]
2025-07-08 16:51:26  web_server.py 84: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-1]
2025-07-08 16:51:26  web_server.py 78: INFO  Serving request for path: js/chunk-vendors.7f82116b.js [waitress-2]
2025-07-08 16:51:26  web_server.py 78: INFO  Serving request for path: js/app.a0267689.js [waitress-3]
2025-07-08 16:51:26  web_server.py 81: INFO  Serving static file: js/chunk-vendors.7f82116b.js [waitress-2]
2025-07-08 16:51:26  web_server.py 81: INFO  Serving static file: js/app.a0267689.js [waitress-3]
2025-07-08 16:51:26  web_server.py 78: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-0]
2025-07-08 16:51:26  web_server.py 78: INFO  Serving request for path: css/app.7ae45238.css [waitress-1]
2025-07-08 16:51:26  web_server.py 81: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-0]
2025-07-08 16:51:26  web_server.py 81: INFO  Serving static file: css/app.7ae45238.css [waitress-1]
2025-07-08 16:51:26  web_server.py 78: INFO  Serving request for path: js/388.6189b2b6.js [waitress-3]
2025-07-08 16:51:26  web_server.py 81: INFO  Serving static file: js/388.6189b2b6.js [waitress-3]
2025-07-08 16:51:26  web_server.py 78: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-0]
2025-07-08 16:51:26  web_server.py 81: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-0]
2025-07-08 16:51:26  web_server.py 78: INFO  Serving request for path: fonts/element-icons.ff18efd1.woff [waitress-2]
2025-07-08 16:51:26  web_server.py 81: INFO  Serving static file: fonts/element-icons.ff18efd1.woff [waitress-2]
2025-07-08 16:51:26  web_server.py 78: INFO  Serving request for path: logo.png [waitress-3]
2025-07-08 16:51:26  web_server.py 81: INFO  Serving static file: logo.png [waitress-3]
2025-07-08 16:51:27  web_server.py 78: INFO  Serving request for path: admin/settings/payment [waitress-0]
2025-07-08 16:51:27  web_server.py 84: INFO  Serving index.html for path: admin/settings/payment [waitress-0]
2025-07-08 16:51:27  web_server.py 78: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-2]
2025-07-08 16:51:27  web_server.py 84: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-2]
2025-07-08 16:51:27  web_server.py 78: INFO  Serving request for path: js/chunk-vendors.7f82116b.js [waitress-1]
2025-07-08 16:51:27  web_server.py 78: INFO  Serving request for path: js/app.a0267689.js [waitress-3]
2025-07-08 16:51:27  web_server.py 78: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-0]
2025-07-08 16:51:27  web_server.py 81: INFO  Serving static file: js/app.a0267689.js [waitress-3]
2025-07-08 16:51:27  web_server.py 81: INFO  Serving static file: js/chunk-vendors.7f82116b.js [waitress-1]
2025-07-08 16:51:27  web_server.py 81: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-0]
2025-07-08 16:51:27  web_server.py 78: INFO  Serving request for path: css/app.7ae45238.css [waitress-2]
2025-07-08 16:51:27  web_server.py 81: INFO  Serving static file: css/app.7ae45238.css [waitress-2]
2025-07-08 16:51:27  web_server.py 78: INFO  Serving request for path: js/388.6189b2b6.js [waitress-3]
2025-07-08 16:51:27  web_server.py 81: INFO  Serving static file: js/388.6189b2b6.js [waitress-3]
2025-07-08 16:51:27  web_server.py 78: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-1]
2025-07-08 16:51:27  web_server.py 81: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-1]
2025-07-08 16:51:27  web_server.py 78: INFO  Serving request for path: logo.png [waitress-2]
2025-07-08 16:51:27  web_server.py 81: INFO  Serving static file: logo.png [waitress-2]
2025-07-08 16:51:28  web_server.py 78: INFO  Serving request for path: admin/settings/payment [waitress-3]
2025-07-08 16:51:28  web_server.py 84: INFO  Serving index.html for path: admin/settings/payment [waitress-3]
2025-07-08 16:51:28  web_server.py 78: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-1]
2025-07-08 16:51:28  web_server.py 84: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-1]
2025-07-08 16:51:28  web_server.py 78: INFO  Serving request for path: js/chunk-vendors.7f82116b.js [waitress-0]
2025-07-08 16:51:28  web_server.py 78: INFO  Serving request for path: js/app.a0267689.js [waitress-2]
2025-07-08 16:51:28  web_server.py 81: INFO  Serving static file: js/chunk-vendors.7f82116b.js [waitress-0]
2025-07-08 16:51:28  web_server.py 81: INFO  Serving static file: js/app.a0267689.js [waitress-2]
2025-07-08 16:51:28  web_server.py 78: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-3]
2025-07-08 16:51:28  web_server.py 78: INFO  Serving request for path: css/app.7ae45238.css [waitress-1]
2025-07-08 16:51:28  web_server.py 81: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-3]
2025-07-08 16:51:28  web_server.py 81: INFO  Serving static file: css/app.7ae45238.css [waitress-1]
2025-07-08 16:51:28  web_server.py 78: INFO  Serving request for path: js/388.6189b2b6.js [waitress-0]
2025-07-08 16:51:28  web_server.py 81: INFO  Serving static file: js/388.6189b2b6.js [waitress-0]
2025-07-08 16:51:28  web_server.py 78: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-2]
2025-07-08 16:51:28  web_server.py 81: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-2]
2025-07-08 16:51:28  web_server.py 78: INFO  Serving request for path: logo.png [waitress-1]
2025-07-08 16:51:28  web_server.py 81: INFO  Serving static file: logo.png [waitress-1]
2025-07-08 16:55:00  init_flask.py 27: INFO  {'CpuCount': 6, 'Mem': '32 GB'} [MainThread]
2025-07-08 16:55:00  web_server.py 32: INFO  Static directory: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\ui [MainThread]
2025-07-08 16:55:00  web_server.py 33: INFO  Static directory exists: True [MainThread]
2025-07-08 16:55:00  web_server.py 37: INFO  设置工作目录: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird [MainThread]
2025-07-08 16:55:00  EarlyBird.config.config 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-08 16:55:00  config.py 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-08 16:55:00  config.py 94: INFO  Database URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-08 16:55:02  EarlyBird.config.wechat_pay_config 49: WARNING  无法从数据库获取微信支付配置: No application found. Either work inside a view function or push an application context. See http://flask-sqlalchemy.pocoo.org/contexts/. [MainThread]
2025-07-08 16:55:02  wechat_pay_config.py 49: WARNING  无法从数据库获取微信支付配置: No application found. Either work inside a view function or push an application context. See http://flask-sqlalchemy.pocoo.org/contexts/. [MainThread]
2025-07-08 16:55:02  EarlyBird.config.wechat_pay_config 52: INFO  使用环境变量中的微信支付配置 [MainThread]
2025-07-08 16:55:02  wechat_pay_config.py 52: INFO  使用环境变量中的微信支付配置 [MainThread]
2025-07-08 16:55:02  EarlyBird.common.wechat_pay_v3 54: ERROR  加载商户私钥失败: [Errno 2] No such file or directory: 'certs/apiclient_key.pem' [MainThread]
2025-07-08 16:55:02  wechat_pay_v3.py 54: ERROR  加载商户私钥失败: [Errno 2] No such file or directory: 'certs/apiclient_key.pem' [MainThread]
2025-07-08 16:55:02  EarlyBird.api.pay.apis 31: ERROR  微信支付V3初始化失败: [Errno 2] No such file or directory: 'certs/apiclient_key.pem' [MainThread]
2025-07-08 16:55:02  apis.py 31: ERROR  微信支付V3初始化失败: [Errno 2] No such file or directory: 'certs/apiclient_key.pem' [MainThread]
2025-07-08 16:55:02  EarlyBird.api.admin.app 39: INFO  ✅ 后台管理系统API已注册 [MainThread]
2025-07-08 16:55:02  app.py 39: INFO  ✅ 后台管理系统API已注册 [MainThread]
2025-07-08 16:55:02  EarlyBird 40: INFO  确保数据目录存在: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\data [MainThread]
2025-07-08 16:55:02  server.py 40: INFO  确保数据目录存在: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\data [MainThread]
2025-07-08 16:55:02  EarlyBird 44: INFO  开始执行数据库迁移... [MainThread]
2025-07-08 16:55:02  server.py 44: INFO  开始执行数据库迁移... [MainThread]
2025-07-08 16:55:02  config.py 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-08 16:55:02  config.py 94: INFO  Database URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-08 16:55:02  EarlyBird 53: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-08 16:55:02  server.py 53: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-08 16:55:02  EarlyBird 97: WARNING  ChatLog表不存在，将在应用启动时自动创建 [MainThread]
2025-07-08 16:55:02  server.py 97: WARNING  ChatLog表不存在，将在应用启动时自动创建 [MainThread]
2025-07-08 16:55:02  add_uid_to_chatlog.py 20: INFO  开始执行ChatLog表迁移... [MainThread]
2025-07-08 16:55:02  add_uid_to_chatlog.py 24: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-08 16:55:02  add_uid_to_chatlog.py 38: WARNING  ChatLog表不存在，可能需要先创建表 [MainThread]
2025-07-08 16:55:02  task_api_server.py 31: INFO  TaskApiServer start [MainThread]
2025-07-08 16:55:02  task_paper_generator.py 38: INFO  论文生成任务处理线程初始化 [MainThread]
2025-07-08 16:55:02  task_paper_generator.py 72: INFO  论文生成任务处理线程启动 [Thread-2]
2025-07-08 16:55:02  task_api_server.py 47: INFO  TaskApiServer listen on 127.0.0.1:3302 [Thread-1]
2025-07-08 16:55:02  task_api_server.py 17: INFO  _ApiServerThread start [Thread-3]
2025-07-08 16:55:15  web_server.py 78: INFO  Serving request for path: admin/settings/payment [waitress-0]
2025-07-08 16:55:15  web_server.py 84: INFO  Serving index.html for path: admin/settings/payment [waitress-0]
2025-07-08 16:55:15  web_server.py 78: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-1]
2025-07-08 16:55:15  web_server.py 84: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-1]
2025-07-08 16:55:15  web_server.py 78: INFO  Serving request for path: js/chunk-vendors.7f82116b.js [waitress-2]
2025-07-08 16:55:15  web_server.py 78: INFO  Serving request for path: js/app.a0267689.js [waitress-3]
2025-07-08 16:55:15  web_server.py 81: INFO  Serving static file: js/chunk-vendors.7f82116b.js [waitress-2]
2025-07-08 16:55:15  web_server.py 81: INFO  Serving static file: js/app.a0267689.js [waitress-3]
2025-07-08 16:55:15  web_server.py 78: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-0]
2025-07-08 16:55:15  web_server.py 78: INFO  Serving request for path: css/app.7ae45238.css [waitress-1]
2025-07-08 16:55:15  web_server.py 81: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-0]
2025-07-08 16:55:15  web_server.py 81: INFO  Serving static file: css/app.7ae45238.css [waitress-1]
2025-07-08 16:55:15  web_server.py 78: INFO  Serving request for path: js/388.6189b2b6.js [waitress-3]
2025-07-08 16:55:15  web_server.py 81: INFO  Serving static file: js/388.6189b2b6.js [waitress-3]
2025-07-08 16:55:15  web_server.py 78: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-0]
2025-07-08 16:55:15  web_server.py 81: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-0]
2025-07-08 16:55:15  web_server.py 78: INFO  Serving request for path: fonts/element-icons.ff18efd1.woff [waitress-1]
2025-07-08 16:55:15  web_server.py 81: INFO  Serving static file: fonts/element-icons.ff18efd1.woff [waitress-1]
2025-07-08 16:55:15  web_server.py 78: INFO  Serving request for path: logo.png [waitress-3]
2025-07-08 16:55:15  web_server.py 81: INFO  Serving static file: logo.png [waitress-3]
2025-07-08 16:55:17  web_server.py 78: INFO  Serving request for path: css/315.27b4d9c8.css [waitress-0]
2025-07-08 16:55:17  web_server.py 78: INFO  Serving request for path: js/315.f13759ae.js [waitress-1]
2025-07-08 16:55:17  web_server.py 81: INFO  Serving static file: css/315.27b4d9c8.css [waitress-0]
2025-07-08 16:55:17  web_server.py 81: INFO  Serving static file: js/315.f13759ae.js [waitress-1]
2025-07-08 16:55:18  web_server.py 78: INFO  Serving request for path: css/8.38fdf013.css [waitress-2]
2025-07-08 16:55:18  web_server.py 78: INFO  Serving request for path: js/8.a13b62ce.js [waitress-3]
2025-07-08 16:55:18  web_server.py 81: INFO  Serving static file: css/8.38fdf013.css [waitress-2]
2025-07-08 16:55:18  web_server.py 81: INFO  Serving static file: js/8.a13b62ce.js [waitress-3]
