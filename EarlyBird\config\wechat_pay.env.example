# 微信支付V3环境变量配置示例
# 请复制此文件为 .env 并填入实际的配置值

# 微信支付应用ID（必填）
WECHAT_APPID=your_appid_here

# 微信支付商户号（必填）
WECHAT_MCHID=your_mchid_here

# 商户APIv3密钥（必填）
WECHAT_API_V3_KEY=your_api_v3_key_here

# 商户证书序列号（必填）
WECHAT_SERIAL_NO=your_serial_no_here

# 商户私钥文件路径（必填）
WECHAT_PRIVATE_KEY_PATH=EarlyBird/certs/apiclient_key.pem

# 微信支付平台证书文件路径（用于验签）
WECHAT_PLATFORM_CERT_PATH=EarlyBird/certs/platform_cert.pem

# 回调通知地址（必填）
WECHAT_NOTIFY_URL=https://your-domain.com/api/pay/wechat_notify

# 是否启用沙箱环境（开发测试时使用）
WECHAT_SANDBOX=false

# 沙箱环境配置（仅在WECHAT_SANDBOX=true时使用）
WECHAT_SANDBOX_APPID=your_sandbox_appid_here
WECHAT_SANDBOX_MCHID=your_sandbox_mchid_here
WECHAT_SANDBOX_API_V3_KEY=your_sandbox_api_v3_key_here
WECHAT_SANDBOX_SERIAL_NO=your_sandbox_serial_no_here
WECHAT_SANDBOX_PRIVATE_KEY_PATH=EarlyBird/certs/sandbox_apiclient_key.pem 