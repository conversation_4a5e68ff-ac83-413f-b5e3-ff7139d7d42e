<template>
  <div class="user-info">
    <el-dropdown v-if="isLoggedIn" @command="handleCommand" trigger="click">
      <div class="user-avatar">
        <el-avatar :size="36" :src="avatarSrc">
          <img src="@/assets/images/logo.png" alt="默认头像" />
        </el-avatar>
        <div class="user-details">
          <div class="username-row">
            <span class="username" :title="displayName">{{ displayName }}</span>
            <!-- VIP标识 -->
            <div v-if="isVip" class="vip-badge">
              <i class="el-icon-star-on"></i>
              <span>VIP</span>
            </div>
            <div v-else class="free-badge">
              <i class="el-icon-star-off"></i>
              <span>免费</span>
            </div>
          </div>
          <!-- VIP状态提示 - 简化显示 -->
          <div v-if="isVip" class="vip-status">
            <span v-if="userInfo.vip_balance_days" class="vip-expire">
              剩余{{ userInfo.vip_balance_days }}天
            </span>
          </div>
          <div v-else class="free-status">
            <span class="upgrade-hint" @click="showUpgradeInfo">升级VIP</span>
          </div>
        </div>
        <i class="el-icon-arrow-down el-icon--right"></i>
      </div>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item command="profile">
          <i class="el-icon-user"></i>
          个人资料
        </el-dropdown-item>
        <!-- VIP用户专属菜单 -->
        <el-dropdown-item v-if="isVip" command="vip-info">
          <i class="el-icon-star-on"></i>
          VIP权益
        </el-dropdown-item>
        <el-dropdown-item v-else command="upgrade">
          <i class="el-icon-star-on"></i>
          升级VIP
        </el-dropdown-item>
        <el-dropdown-item command="logout" divided>
          <i class="el-icon-switch-button"></i>
          退出登录
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
    
    <div v-else class="login-actions">
      <el-button type="text" class="login-btn" @click="goToLogin">登录</el-button>
      <el-button type="primary" class="register-btn" size="small" @click="goToRegister">注册</el-button>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'

export default {
  name: 'UserInfo',
  computed: {
    ...mapGetters('user', ['userInfo', 'isLoggedIn', 'username', 'nickname', 'isVip']),
    displayName() {
      // 优先显示昵称，没有昵称显示用户名
      const name = this.nickname || this.username || '用户';
      // 超过10字符省略
      return name.length > 10 ? name.slice(0, 10) + '…' : name;
    },
    avatarSrc() {
      // 如果有用户头像则使用，否则返回null让el-avatar显示默认内容
      return this.userInfo.headimg || null;
    }
  },
  methods: {
    ...mapActions('user', ['logout']),
    async handleCommand(command) {
      switch (command) {
        case 'profile':
          this.goToProfile()
          break
        case 'vip-info':
          this.showVipInfo()
          break
        case 'upgrade':
          this.showUpgradeInfo()
          break
        case 'logout':
          await this.handleLogout()
          break
      }
    },
    goToProfile() {
      this.$router.push('/user/profile')
    },
    showVipInfo() {
      this.$alert(`
        <div style="text-align: left;">
          <h3 style="color: #E6A23C; margin-bottom: 15px;">🎉 VIP会员权益</h3>
          <div style="margin-bottom: 10px;">
            <strong>📝 论文生成：</strong>最多可生成5篇论文（免费用户仅1篇）
          </div>
          <div style="margin-bottom: 10px;">
            <strong>🚀 生成速度：</strong>优先处理，更快的生成速度
          </div>
          <div style="margin-bottom: 10px;">
            <strong>💎 高级功能：</strong>解锁所有高级AI功能
          </div>
          <div style="margin-bottom: 10px;">
            <strong>🎯 专属服务：</strong>优先客服支持
          </div>
          <div style="color: #67C23A; font-weight: bold;">
            剩余时间：${this.userInfo.vip_balance_days || '未知'}
          </div>
        </div>
      `, 'VIP会员权益', {
        dangerouslyUseHTMLString: true,
        confirmButtonText: '知道了',
        type: 'success'
      })
    },
    showUpgradeInfo() {
      this.$alert(`
        <div style="text-align: left;">
          <h3 style="color: #E6A23C; margin-bottom: 15px;">🌟 升级VIP会员</h3>
          <div style="margin-bottom: 10px;">
            <strong>📝 论文数量：</strong>从1篇提升至5篇
          </div>
          <div style="margin-bottom: 10px;">
            <strong>🚀 生成速度：</strong>享受优先处理
          </div>
          <div style="margin-bottom: 10px;">
            <strong>💎 高级功能：</strong>解锁所有AI功能
          </div>
          <div style="margin-bottom: 10px;">
            <strong>🎯 专属服务：</strong>优先客服支持
          </div>
          <div style="color: #F56C6C; font-weight: bold;">
            立即升级，享受更多权益！
          </div>
        </div>
      `, '升级VIP', {
        dangerouslyUseHTMLString: true,
        confirmButtonText: '立即升级',
        cancelButtonText: '稍后再说',
        type: 'warning'
      }).then(() => {
        // TODO: 跳转到升级页面
        this.$message.info('升级功能开发中...')
      }).catch(() => {
        // 用户取消
      })
    },
    async handleLogout() {
      try {
        await this.$confirm('确定要退出登录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        await this.logout()
        this.$message.success('已退出登录')
        this.$router.push('/user/login')
      } catch (error) {
        if (error !== 'cancel') {
          console.error('退出登录失败:', error)
          this.$message.error('退出登录失败，请稍后重试')
        }
      }
    },
    goToLogin() {
      this.$router.push('/user/login')
    },
    goToRegister() {
      this.$router.push('/user/register')
    }
  }
}
</script>

<style scoped>
.user-info {
  display: flex;
  align-items: center;
  height: 52px;
  padding: 0 4px 0 2px;
  width: 100%;
  overflow: hidden;
}

.user-avatar {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 4px 6px 4px 2px;
  border-radius: 6px;
  transition: all 0.3s ease;
  min-width: 0;
  max-width: 100%;
  width: 100%;
  margin-left: -2px;
}

.user-avatar:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.user-details {
  display: flex;
  flex-direction: column;
  margin: 0 4px;
  min-width: 0;
  flex: 1;
  overflow: hidden;
}

.username-row {
  display: flex;
  align-items: center;
  gap: 3px;
  margin-bottom: 2px;
  min-width: 0;
  flex-wrap: wrap;
  width: 100%;
}

.username {
  font-size: 12px;
  color: #333;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: middle;
  font-weight: 600;
  line-height: 1.2;
  flex-shrink: 0;
  display: block;
  width: 100%;
}

.vip-badge {
  display: flex;
  align-items: center;
  gap: 1px;
  background: linear-gradient(135deg, #FFD700, #FFA500);
  color: #fff;
  padding: 1px 4px;
  border-radius: 6px;
  font-size: 9px;
  font-weight: bold;
  box-shadow: 0 1px 2px rgba(255, 215, 0, 0.3);
  animation: vip-glow 2s ease-in-out infinite alternate;
  flex-shrink: 0;
  margin-top: 1px;
}

.free-badge {
  display: flex;
  align-items: center;
  gap: 1px;
  background: #f5f5f5;
  color: #666;
  padding: 1px 4px;
  border-radius: 6px;
  font-size: 9px;
  font-weight: bold;
  border: 1px solid #e0e0e0;
  flex-shrink: 0;
  margin-top: 1px;
}

.vip-status {
  display: flex;
  align-items: center;
  gap: 3px;
  margin-top: 1px;
  min-width: 0;
  flex-wrap: wrap;
  width: 100%;
}

.vip-expire {
  font-size: 9px;
  color: #67C23A;
  background: rgba(103, 194, 58, 0.1);
  padding: 1px 3px;
  border-radius: 3px;
  border: 1px solid rgba(103, 194, 58, 0.2);
  flex-shrink: 0;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.free-status {
  display: flex;
  align-items: center;
  gap: 3px;
  margin-top: 1px;
  min-width: 0;
  flex-wrap: wrap;
  width: 100%;
}

.upgrade-hint {
  font-size: 9px;
  color: #409EFF;
  background: rgba(64, 158, 255, 0.1);
  padding: 1px 3px;
  border-radius: 3px;
  cursor: pointer;
  transition: all 0.3s;
  border: 1px solid rgba(64, 158, 255, 0.2);
  font-weight: 500;
  flex-shrink: 0;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.upgrade-hint:hover {
  background: rgba(64, 158, 255, 0.2);
  color: #fff;
}

@keyframes vip-glow {
  from {
    box-shadow: 0 1px 3px rgba(255, 215, 0, 0.3);
  }
  to {
    box-shadow: 0 1px 6px rgba(255, 215, 0, 0.6);
  }
}

.login-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.login-btn {
  font-size: 13px;
  color: #409EFF;
  padding: 0 6px;
  font-weight: 500;
}

.register-btn {
  font-size: 12px;
  border-radius: 14px;
  padding: 0 12px;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .user-info {
    height: 52px;
    padding: 0 3px 0 1px;
  }
  
  .user-avatar {
    padding: 3px 4px 3px 1px;
    margin-left: -1px;
  }
  
  .user-details {
    margin: 0 3px;
  }
  
  .username {
    font-size: 11px;
  }
  
  .vip-badge,
  .free-badge {
    padding: 1px 3px;
    font-size: 8px;
  }
  
  .upgrade-hint,
  .vip-expire {
    font-size: 8px;
    padding: 1px 2px;
  }
}

@media (max-width: 480px) {
  .user-info {
    height: 50px;
    padding: 0 2px 0 1px;
  }
  
  .user-avatar {
    padding: 2px 3px 2px 1px;
    margin-left: 0;
  }
  
  .username {
    font-size: 10px;
  }
  
  .user-details {
    margin: 0 2px;
  }
  
  .username-row {
    gap: 2px;
  }
  
  .vip-status,
  .free-status {
    gap: 2px;
  }
  
  .vip-badge,
  .free-badge {
    padding: 1px 2px;
    font-size: 7px;
  }
  
  .upgrade-hint,
  .vip-expire {
    font-size: 7px;
    padding: 1px 1px;
  }
}
</style> 