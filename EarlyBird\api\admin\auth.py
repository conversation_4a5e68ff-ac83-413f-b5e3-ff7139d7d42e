from flask import Blueprint, request, jsonify, session, g
from EarlyBird.common.libs.api_result import ApiResult
from EarlyBird.model.admin import Admin, AdminLog
from EarlyBird.common.libs.BaseModel import db
import jwt
import datetime
import logging
import functools

logger = logging.getLogger(__name__)

# 创建蓝图
admin_auth_bp = Blueprint('admin_auth', __name__)

# JWT密钥（生产环境应该从配置文件读取）
JWT_SECRET = "zaoniao_admin_secret_key_2024"
JWT_EXPIRE_HOURS = 24


def generate_token(admin_id, username):
    """生成JWT Token"""
    payload = {
        'admin_id': admin_id,
        'username': username,
        'exp': datetime.datetime.utcnow() + datetime.timedelta(hours=JWT_EXPIRE_HOURS),
        'iat': datetime.datetime.utcnow()
    }
    return jwt.encode(payload, JWT_SECRET, algorithm='HS256')


def verify_token(token):
    """验证JWT Token"""
    try:
        payload = jwt.decode(token, JWT_SECRET, algorithms=['HS256'])
        return payload
    except jwt.ExpiredSignatureError:
        logger.warning("Token已过期")
        return None
    except jwt.InvalidTokenError:
        logger.warning("无效的Token")
        return None
    except Exception as e:
        logger.error(f"验证Token时发生错误: {str(e)}")
        return None


def admin_auth_required(f):
    """管理员认证装饰器"""
    @functools.wraps(f)
    def decorated_function(*args, **kwargs):
        # 检查全局管理员信息（由钩子函数设置）
        if hasattr(request, 'admin') and request.admin:
            return f(*args, **kwargs)
            
        # 如果没有在request中找到，尝试从g中获取
        if hasattr(g, 'admin') and g.admin:
            request.admin = g.admin
            return f(*args, **kwargs)
            
        # 尝试从请求头获取token
        auth_header = request.headers.get('Authorization')
        if not auth_header:
            logger.warning(f"管理员API未提供Authorization头: {request.path}")
            return ApiResult.error("请先登录", code=401)
            
        # 移除Bearer前缀
        if auth_header.startswith('Bearer '):
            token = auth_header[7:]
        else:
            token = auth_header
            
        logger.debug(f"管理员API Token: {token[:10]}...")
        
        # 验证token
        payload = verify_token(token)
        if not payload:
            logger.warning(f"管理员API Token无效: {request.path}")
            return ApiResult.error("认证信息无效或已过期，请重新登录", code=401)
        
        # 获取管理员信息
        admin_id = payload.get('admin_id')
        admin = Admin.query.get(admin_id)
        
        if not admin:
            logger.warning(f"管理员不存在: {admin_id}")
            return ApiResult.error("管理员账号不存在", code=401)
            
        if not admin.is_active:
            logger.warning(f"管理员已被禁用: {admin_id}")
            return ApiResult.error("管理员账号已被禁用", code=401)
        
        # 设置全局管理员信息
        request.admin = admin
        g.admin = admin
        
        return f(*args, **kwargs)
    
    return decorated_function


@admin_auth_bp.route('/login', methods=['POST'])
def admin_login():
    """管理员登录"""
    try:
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')
        
        if not username or not password:
            return ApiResult.error("用户名和密码不能为空")
        
        # 查找管理员
        admin = Admin.query.filter_by(username=username).first()
        if not admin:
            return ApiResult.error("用户名或密码错误")
        
        # 验证密码
        if not admin.check_password(password):
            return ApiResult.error("用户名或密码错误")
        
        # 检查是否激活
        if not admin.is_active:
            return ApiResult.error("账号已被禁用")
        
        # 生成Token
        token = generate_token(admin.id, admin.username)
        
        # 更新登录信息
        ip_address = request.remote_addr
        admin.update_login_info(ip_address)
        
        # 记录登录日志
        AdminLog.create_log(
            admin_id=admin.id,
            admin_username=admin.username,
            action="login",
            resource="auth",
            description="管理员登录",
            ip_address=ip_address,
            user_agent=request.headers.get('User-Agent', '')
        )
        
        return ApiResult.success({
            "token": token,
            "admin_info": admin.get_info()
        }, "登录成功")
        
    except Exception as e:
        logger.error(f"管理员登录失败: {str(e)}")
        return ApiResult.error("登录失败，请稍后重试")


@admin_auth_bp.route('/logout', methods=['POST'])
def admin_logout():
    """管理员登出"""
    try:
        # 尝试获取管理员信息，但不强制要求
        admin = None

        # 尝试从请求头获取token并验证
        auth_header = request.headers.get('Authorization')
        if auth_header:
            try:
                # 移除Bearer前缀
                if auth_header.startswith('Bearer '):
                    token = auth_header[7:]
                else:
                    token = auth_header

                # 验证token
                payload = verify_token(token)
                if payload:
                    admin_id = payload.get('admin_id')
                    admin = Admin.query.get(admin_id)
            except Exception as e:
                logger.warning(f"登出时验证token失败: {str(e)}")

        # 如果能获取到管理员信息，记录登出日志
        if admin:
            try:
                AdminLog.create_log(
                    admin_id=admin.id,
                    admin_username=admin.username,
                    action="logout",
                    resource="auth",
                    description="管理员登出",
                    ip_address=request.remote_addr,
                    user_agent=request.headers.get('User-Agent', '')
                )
                logger.info(f"管理员 {admin.username} 登出成功")
            except Exception as e:
                logger.warning(f"记录登出日志失败: {str(e)}")
        else:
            logger.info("匿名用户尝试登出（token无效或已过期）")

        # 无论是否有有效token，都返回成功，确保前端能正常登出
        return ApiResult.success(None, "登出成功")

    except Exception as e:
        logger.error(f"管理员登出失败: {str(e)}")
        # 即使出错也返回成功，确保前端能正常登出
        return ApiResult.success(None, "登出成功")


@admin_auth_bp.route('/profile', methods=['GET'])
@admin_auth_required
def admin_profile():
    """获取管理员信息"""
    try:
        admin = request.admin
        return ApiResult.success(admin.get_info())
        
    except Exception as e:
        logger.error(f"获取管理员信息失败: {str(e)}")
        return ApiResult.error("获取信息失败")


@admin_auth_bp.route('/change-password', methods=['POST'])
@admin_auth_required
def change_password():
    """修改密码"""
    try:
        data = request.get_json()
        old_password = data.get('old_password')
        new_password = data.get('new_password')
        
        if not old_password or not new_password:
            return ApiResult.error("旧密码和新密码不能为空")
        
        admin = request.admin
        
        # 验证旧密码
        if not admin.check_password(old_password):
            return ApiResult.error("旧密码错误")
        
        # 设置新密码
        admin.password = new_password
        admin.save()
        
        # 记录操作日志
        AdminLog.create_log(
            admin_id=admin.id,
            admin_username=admin.username,
            action="change_password",
            resource="auth",
            description="修改密码",
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', '')
        )
        
        return ApiResult.success(None, "密码修改成功")
        
    except Exception as e:
        logger.error(f"修改密码失败: {str(e)}")
        return ApiResult.error("修改密码失败") 