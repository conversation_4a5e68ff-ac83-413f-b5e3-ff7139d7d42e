#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import logging
import json
from flask import Flask
from flask_migrate import Migrate
import traceback
from sqlalchemy import create_engine, text, inspect
from config.config import AppConfig
from common.libs.BaseModel import BaseModel
from model.payment import PaymentConfig

# 设置环境变量
os.environ["FLASK_ENV"] = "development"

# 添加项目根目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)
sys.path.insert(0, current_dir)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s  %(filename)s %(lineno)s: %(levelname)s  %(message)s [%(threadName)s]",
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger(__name__)

# 创建Flask应用
app = Flask(__name__)

# 注册配置
from EarlyBird.ExtendRegister.conf_register import register_config
register_config(app)

# 初始化数据库
from EarlyBird.common.libs.BaseModel import db
db.init_app(app)

# 导入Admin模型
from EarlyBird.model.admin import Admin

# 创建迁移对象
migrate = Migrate(app, db)

def migrate_chatlog_table():
    """为ChatLog表添加uid字段，实现用户隔离"""
    try:
        # 获取数据库连接
        db_uri = AppConfig.SQLALCHEMY_DATABASE_URI
        logger.info(f"数据库连接URI: {db_uri}")
        
        engine = create_engine(db_uri)
        conn = engine.connect()
        
        # 检查表是否存在 (SQLite兼容方式)
        check_table_sql = """
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name='earlybird_chat_log';
        """
        try:
            result = conn.execute(text(check_table_sql)).fetchone()
            if not result:
                logger.warning("ChatLog表不存在，可能需要先创建表")
                conn.close()
                return False
            else:
                logger.info("ChatLog表存在，继续执行迁移")
        except Exception as e:
            logger.error(f"检查表是否存在时出错: {str(e)}")
            logger.error(traceback.format_exc())
            conn.close()
            return False
            
        # 检查uid字段是否已存在 (SQLite兼容方式)
        check_sql = """
        PRAGMA table_info(earlybird_chat_log);
        """
        try:
            columns = conn.execute(text(check_sql)).fetchall()
            
            # 检查是否存在uid列
            uid_exists = False
            for col in columns:
                if col[1] == 'uid':  # 列名是结果中的第二个元素
                    uid_exists = True
                    break
            
            # 如果uid字段不存在，则添加
            if not uid_exists:
                logger.info("开始为ChatLog表添加uid字段...")
                
                # 添加uid字段
                add_column_sql = """
                ALTER TABLE earlybird_chat_log 
                ADD COLUMN uid INTEGER DEFAULT 0;
                """
                conn.execute(text(add_column_sql))
                
                # 添加索引
                add_index_sql = """
                CREATE INDEX idx_uid ON earlybird_chat_log (uid);
                """
                conn.execute(text(add_index_sql))
                
                # 提交事务
                conn.commit()
                
                logger.info("成功为ChatLog表添加uid字段")
                
                # 更新现有记录，将session中的用户ID关联到聊天记录
                update_existing_records(conn)
            else:
                logger.info("ChatLog表已存在uid字段，无需迁移")
                
            conn.close()
            return True
        except Exception as e:
            logger.error(f"执行SQL时出错: {str(e)}")
            logger.error(traceback.format_exc())
            conn.close()
            return False
    except Exception as e:
        logger.error(f"数据库迁移失败: {str(e)}")
        logger.error(traceback.format_exc())
        return False

def update_existing_records(conn):
    """更新现有记录，尝试关联用户ID"""
    try:
        # 获取所有用户
        users_sql = "SELECT id FROM earlybird_user"
        users = conn.execute(text(users_sql)).fetchall()
        
        if not users:
            logger.warning("没有找到用户记录，无法更新现有聊天记录")
            return
            
        # 随机分配用户ID给现有聊天记录
        for user in users:
            user_id = user[0]
            # 更新一部分无用户ID的记录 (SQLite兼容语法)
            update_sql = f"""
            UPDATE earlybird_chat_log 
            SET uid = {user_id}
            WHERE uid = 0
            LIMIT 100
            """
            try:
                # SQLite不支持LIMIT子句在UPDATE语句中，所以我们需要先查询ID，然后更新
                select_ids_sql = """
                SELECT id FROM earlybird_chat_log
                WHERE uid = 0
                LIMIT 100
                """
                rows = conn.execute(text(select_ids_sql)).fetchall()
                if not rows:
                    continue
                    
                ids = [str(row[0]) for row in rows]
                ids_str = ','.join(ids)
                
                # 使用IN子句更新
                update_sql = f"""
                UPDATE earlybird_chat_log 
                SET uid = {user_id}
                WHERE id IN ({ids_str})
                """
                result = conn.execute(text(update_sql))
                conn.commit()
                logger.info(f"为用户 {user_id} 更新了 {len(ids)} 条聊天记录")
            except Exception as e:
                logger.error(f"更新记录时出错: {str(e)}")
                conn.rollback()
            
        logger.info("完成现有聊天记录的用户ID关联")
    except Exception as e:
        logger.error(f"更新现有记录时出错: {str(e)}")
        logger.error(traceback.format_exc())
        conn.rollback()

def migrate_report_history_table():
    """创建报告历史表"""
    try:
        # 导入报告历史表迁移模块
        from migrations.add_report_history_table import migrate
        
        # 执行迁移
        success = migrate()
        
        if success:
            logger.info("报告历史表迁移成功")
        else:
            logger.error("报告历史表迁移失败")
            
        return success
    except Exception as e:
        logger.error(f"报告历史表迁移出错: {str(e)}")
        logger.error(traceback.format_exc())
        return False

def create_superadmin():
    """创建超级管理员账户"""
    with app.app_context():
        # 检查是否已存在超级管理员
        admin = Admin.query.filter_by(username='admin').first()
        
        if admin:
            print("超级管理员账户已存在，更新为超级管理员权限")
            admin.is_superadmin = True
            admin.role = "superadmin"
            admin.save()
        else:
            print("创建新的超级管理员账户")
            admin = Admin(
                username='admin',
                password='admin123',  # 初始密码
                realname='超级管理员',
                email='<EMAIL>',
                role='superadmin',
                is_superadmin=True,
                is_active=True
            )
            admin.save()
        
        print(f"超级管理员账户信息: {admin.username}, ID: {admin.id}")

def run_migration():
    """执行数据库迁移"""
    logger.info("开始执行数据库迁移...")
    
    # 获取数据库连接URI
    db_uri = AppConfig.SQLALCHEMY_DATABASE_URI
    logger.info(f"数据库连接URI: {db_uri}")
    
    # 创建数据库引擎
    engine = create_engine(db_uri)
    inspector = inspect(engine)
    
    # 检查支付配置表是否存在
    table_name = PaymentConfig.__tablename__
    if not inspector.has_table(table_name):
        logger.warning(f"表 {table_name} 不存在，将创建该表")
        # 创建表
        BaseModel.metadata.create_all(engine, tables=[PaymentConfig.__table__])
        logger.info(f"成功创建表 {table_name}")
        
        # 添加初始配置
        with engine.connect() as conn:
            conn.execute(text(f"""
            INSERT INTO {table_name} (name, config_key, config_value, description, create_time, update_time, is_deleted, status) 
            VALUES 
            ('商户ID', 'pid', '', '支付平台分配的商户ID', NOW(), NOW(), 0, 1),
            ('异步通知地址', 'notify_url', '', '支付结果异步通知地址', NOW(), NOW(), 0, 1),
            ('同步跳转地址', 'return_url', '', '支付完成后跳转地址', NOW(), NOW(), 0, 1),
            ('商户私钥', 'merchant_private_key', '', 'RSA私钥，用于签名', NOW(), NOW(), 0, 1),
            ('平台公钥', 'platform_public_key', '', 'RSA公钥，用于验签', NOW(), NOW(), 0, 1)
            """))
            conn.commit()
            logger.info(f"成功添加初始配置数据")
    else:
        logger.info(f"表 {table_name} 已存在，无需创建")
    
    logger.info("数据库迁移执行完成")

if __name__ == "__main__":
    logger.info("开始执行数据库迁移...")
    
    # 执行聊天记录表迁移
    chatlog_success = migrate_chatlog_table()
    if chatlog_success:
        logger.info("聊天记录表迁移成功完成")
    else:
        logger.error("聊天记录表迁移失败")
    
    # 执行报告历史表迁移
    report_success = migrate_report_history_table()
    if report_success:
        logger.info("报告历史表迁移成功完成")
    else:
        logger.error("报告历史表迁移失败")
    
    # 如果所有迁移都成功，则退出码为0，否则为1
    if chatlog_success and report_success:
        logger.info("所有数据库迁移成功完成")
    else:
        logger.error("部分或全部数据库迁移失败")
        sys.exit(1)
        
    logger.info("迁移脚本执行完毕")

    if len(sys.argv) > 1 and sys.argv[1] == 'create-superadmin':
        create_superadmin()
    else:
        print("使用方法: python run_migration.py create-superadmin")

    run_migration() 