<template>
  <div class="profile-container">
    <el-tabs v-model="activeTab" type="card">
      <el-tab-pane label="个人资料" name="profile">
        <el-card class="profile-card">
          <div slot="header" class="card-header">
            <span>个人资料</span>
          </div>
          
          <el-form
            ref="profileForm"
            :model="profileForm"
            :rules="profileRules"
            label-width="100px"
            class="profile-form"
          >
            <el-form-item label="用户名" prop="username">
              <el-input v-model="profileForm.username" disabled />
            </el-form-item>
            
            <el-form-item label="昵称" prop="nickname">
              <el-input v-model="profileForm.nickname" placeholder="请输入昵称" />
            </el-form-item>
            
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="profileForm.email" placeholder="请输入邮箱" />
            </el-form-item>
            
            <el-form-item label="用户类型">
              <div class="vip-status-display">
                <el-tag :type="userInfo.isVip ? 'success' : 'info'" size="medium">
                  <i :class="userInfo.isVip ? 'el-icon-star-on' : 'el-icon-star-off'"></i>
                  {{ userInfo.isVip ? 'VIP用户' : '普通用户' }}
                </el-tag>
                <div v-if="userInfo.isVip" class="vip-details">
                  <div class="vip-level">
                    <span class="level-label">VIP等级：</span>
                    <span class="level-value">{{ userInfo.vip_level || 1 }}级</span>
                  </div>
                  <div v-if="userInfo.vip_balance_days" class="vip-expire">
                    <span class="expire-label">剩余时间：</span>
                    <span class="expire-value">{{ userInfo.vip_balance_days }}</span>
                  </div>
                  <div v-if="userInfo.vip_expire_at" class="vip-date">
                    <span class="date-label">到期时间：</span>
                    <span class="date-value">{{ userInfo.vip_expire_at }}</span>
                  </div>
                </div>
                <div v-else class="free-details">
                  <div class="upgrade-prompt">
                    <span class="prompt-text">升级VIP享受更多权益</span>
                    <el-button type="warning" size="mini" @click="activeTab = 'vip'">
                      立即升级
                    </el-button>
                  </div>
                </div>
              </div>
            </el-form-item>
            
            <el-form-item label="注册时间">
              <span>{{ formatDate(userInfo.create_time) }}</span>
            </el-form-item>
            
            <el-form-item label="最后登录">
              <span>{{ formatDate(userInfo.last_login_time) }}</span>
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="handleUpdateProfile" :loading="loading">
                更新资料
              </el-button>
              <el-button @click="goBack">返回</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-tab-pane>
      
      <el-tab-pane label="会员购买" name="vip">
        <!-- 会员状态卡片 -->
        <el-card class="vip-status-card">
          <div slot="header" class="card-header">
            <span>会员状态</span>
          </div>
          
          <div class="vip-status-content">
            <div class="vip-status-info">
              <div class="vip-icon">
                <i :class="userInfo.isVip ? 'el-icon-star-on' : 'el-icon-star-off'" 
                   :style="{ color: userInfo.isVip ? '#E6A23C' : '#909399', fontSize: '48px' }"></i>
              </div>
              <div class="vip-text">
                <h3>{{ userInfo.isVip ? 'VIP会员' : '普通用户' }}</h3>
                <p v-if="userInfo.isVip">
                  VIP等级: {{ userInfo.vip_level || 1 }}级 | 
                  到期时间: {{ userInfo.vip_expire_at || '未知' }} | 
                  剩余: {{ userInfo.vip_balance_days || '未知' }}
                </p>
                <p v-else>升级VIP会员，享受更多权益</p>
              </div>
            </div>
          </div>
        </el-card>
        
        <!-- 会员套餐卡片 -->
        <el-card class="vip-packages-card">
          <div slot="header" class="card-header">
            <span>会员套餐</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="refreshProducts">
              刷新
            </el-button>
          </div>
          
          <div class="packages-container" v-loading="productsLoading">
            <el-row :gutter="20">
              <el-col :span="8" v-for="product in products" :key="product.id">
                <div class="package-item" :class="{'package-active': userInfo.isVip && userInfo.vip_level >= product.vip_level}">
                  <div class="package-header">
                    <h3>{{ product.name }}</h3>
                    <div class="package-price">
                      <span class="price">¥{{ product.discount_price || product.price }}</span>
                      <span class="original-price" v-if="product.discount_price">¥{{ product.price }}</span>
                    </div>
                  </div>
                  <div class="package-body">
                    <p>{{ product.description || `${product.vip_days}天VIP会员` }}</p>
                    <p>VIP等级: {{ product.vip_level || 1 }}</p>
                    <p>会员天数: {{ product.vip_days }}天</p>
                  </div>
                  <div class="package-footer">
                    <el-button 
                      type="primary" 
                      @click="buyProduct(product)" 
                      :disabled="userInfo.isVip && userInfo.vip_level >= product.vip_level"
                    >
                      {{ userInfo.isVip && userInfo.vip_level >= product.vip_level ? '已开通' : '立即购买' }}
                    </el-button>
                  </div>
                </div>
              </el-col>
            </el-row>
            
            <div class="empty-products" v-if="products.length === 0">
              <el-empty description="暂无可购买的会员套餐"></el-empty>
            </div>
          </div>
        </el-card>
        
        <!-- VIP权益对比卡片 -->
        <el-card class="vip-comparison-card">
          <div slot="header" class="comparison-header">
            <span>VIP权益对比</span>
          </div>
          
          <div class="comparison-table">
            <div class="comparison-row header">
              <div class="feature">功能特性</div>
              <div class="free-user">免费用户</div>
              <div class="vip-user">VIP用户</div>
            </div>
            
            <div class="comparison-row">
              <div class="feature">论文生成数量</div>
              <div class="free-user">
                <i class="el-icon-close"></i>
                <span>1篇</span>
              </div>
              <div class="vip-user">
                <i class="el-icon-check"></i>
                <span>5篇</span>
              </div>
            </div>
            
            <div class="comparison-row">
              <div class="feature">生成速度</div>
              <div class="free-user">
                <i class="el-icon-close"></i>
                <span>普通</span>
              </div>
              <div class="vip-user">
                <i class="el-icon-check"></i>
                <span>优先</span>
              </div>
            </div>
            
            <div class="comparison-row">
              <div class="feature">高级AI功能</div>
              <div class="free-user">
                <i class="el-icon-close"></i>
                <span>部分</span>
              </div>
              <div class="vip-user">
                <i class="el-icon-check"></i>
                <span>全部</span>
              </div>
            </div>
            
            <div class="comparison-row">
              <div class="feature">客服支持</div>
              <div class="free-user">
                <i class="el-icon-close"></i>
                <span>普通</span>
              </div>
              <div class="vip-user">
                <i class="el-icon-check"></i>
                <span>优先</span>
              </div>
            </div>
            
            <div class="comparison-row">
              <div class="feature">数据导出</div>
              <div class="free-user">
                <i class="el-icon-close"></i>
                <span>基础</span>
              </div>
              <div class="vip-user">
                <i class="el-icon-check"></i>
                <span>完整</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-tab-pane>
      
      <el-tab-pane label="订单记录" name="orders">
        <el-card class="orders-card">
          <div slot="header" class="card-header">
            <span>订单记录</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="loadOrders">
              刷新
            </el-button>
          </div>
          
          <div class="orders-content" v-loading="ordersLoading">
            <el-table :data="orders" border style="width: 100%">
              <el-table-column prop="out_trade_no" label="订单号" width="180"></el-table-column>
              <el-table-column prop="product.name" label="商品名称" width="150"></el-table-column>
              <el-table-column prop="amount" label="金额" width="100">
                <template slot-scope="scope">
                  ¥{{ scope.row.amount }}
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="100">
                <template slot-scope="scope">
                  <el-tag :type="scope.row.status === 1 ? 'success' : 'info'">
                    {{ scope.row.status === 1 ? '已支付' : '未支付' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="create_time" label="创建时间" width="180"></el-table-column>
              <el-table-column prop="pay_time" label="支付时间" width="180"></el-table-column>
              <el-table-column label="操作">
                <template slot-scope="scope">
                  <el-button size="mini" @click="checkOrder(scope.row)" v-if="scope.row.status === 0">
                    查询
                  </el-button>
                  <el-button size="mini" type="primary" @click="continuePayment(scope.row)" v-if="scope.row.status === 0">
                    继续支付
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            
            <div class="empty-orders" v-if="orders.length === 0">
              <el-empty description="暂无订单记录"></el-empty>
            </div>
          </div>
        </el-card>
      </el-tab-pane>
    </el-tabs>
    
    <!-- 支付对话框 -->
    <el-dialog title="订单支付" :visible.sync="paymentDialogVisible" width="500px">
      <div class="payment-dialog" v-loading="paymentLoading">
        <div class="payment-info">
          <p><strong>商品名称：</strong>{{ currentOrder.product ? currentOrder.product.name : '' }}</p>
          <p><strong>订单金额：</strong>¥{{ currentOrder.amount }}</p>
          <p><strong>订单编号：</strong>{{ currentOrder.out_trade_no }}</p>
        </div>
        
        <div class="payment-qrcode" v-if="currentOrder.pay_url">
          <p>请使用支付宝扫码支付</p>
          <div class="qrcode-container">
            <img :src="`https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(currentOrder.pay_url)}`" alt="支付二维码">
          </div>
        </div>
        
        <div class="payment-actions">
          <el-button @click="paymentDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="openPayUrl">打开支付页面</el-button>
          <el-button @click="checkPaymentStatus">检查支付状态</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getPaymentProducts, createPayment, queryPayment } from '@/api/payment'

export default {
  name: 'UserProfile',
  data() {
    return {
      activeTab: 'profile',
      loading: false,
      profileForm: {
        username: '',
        nickname: '',
        email: ''
      },
      profileRules: {
        nickname: [
          { max: 20, message: '昵称长度不能超过 20 个字符', trigger: 'blur' }
        ],
        email: [
          { 
            pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/, 
            message: '请输入正确的邮箱格式', 
            trigger: 'blur' 
          }
        ]
      },
      // 会员商品
      products: [],
      productsLoading: false,
      
      // 订单记录
      orders: [],
      ordersLoading: false,
      
      // 支付相关
      paymentDialogVisible: false,
      paymentLoading: false,
      currentOrder: {}
    }
  },
  computed: {
    ...mapGetters('user', ['userInfo'])
  },
  created() {
    this.initProfileForm()
    this.loadProducts()
    this.loadOrders()
  },
  methods: {
    initProfileForm() {
      if (this.userInfo) {
        this.profileForm.username = this.userInfo.username || ''
        this.profileForm.nickname = this.userInfo.nickname || ''
        this.profileForm.email = this.userInfo.email || ''
      }
    },
    
    formatDate(dateStr) {
      if (!dateStr) return '未知'
      try {
        const date = new Date(dateStr)
        return date.toLocaleString('zh-CN')
      } catch (error) {
        return dateStr
      }
    },
    
    async handleUpdateProfile() {
      try {
        const valid = await this.$refs.profileForm.validate()
        if (!valid) return
        
        this.loading = true
        
        // TODO: 实现更新个人资料的API调用
        this.$message.success('个人资料更新功能开发中...')
        
      } catch (error) {
        console.error('更新个人资料失败:', error)
        this.$message.error('更新失败，请稍后重试')
      } finally {
        this.loading = false
      }
    },
    
    goBack() {
      this.$router.go(-1)
    },
    
    // 加载会员商品
    async loadProducts() {
      this.productsLoading = true
      try {
        const res = await getPaymentProducts()
        if (res.code === 0) {
          this.products = res.data
        } else {
          this.$message.error(res.msg || '获取会员套餐失败')
        }
      } catch (error) {
        console.error('获取会员套餐失败', error)
        this.$message.error('获取会员套餐失败')
      } finally {
        this.productsLoading = false
      }
    },
    
    // 刷新商品列表
    refreshProducts() {
      this.loadProducts()
    },
    
    // 购买商品
    async buyProduct(product) {
      try {
        const res = await createPayment({
          user_id: this.userInfo.id,
          product_id: product.product_id,
          pay_type: 'alipay'
        })
        
        if (res.code === 0) {
          this.currentOrder = res.data
          this.paymentDialogVisible = true
          this.loadOrders()
        } else {
          this.$message.error(res.msg || '创建订单失败')
        }
      } catch (error) {
        console.error('创建订单失败', error)
        this.$message.error('创建订单失败')
      }
    },
    
    // 加载订单记录
    async loadOrders() {
      this.ordersLoading = true
      try {
        // 这里需要后端提供获取用户订单列表的API
        // 暂时使用模拟数据
        this.orders = []
        
        // 模拟API调用延迟
        setTimeout(() => {
          this.ordersLoading = false
        }, 500)
      } catch (error) {
        console.error('获取订单记录失败', error)
        this.$message.error('获取订单记录失败')
        this.ordersLoading = false
      }
    },
    
    // 查询订单状态
    async checkOrder(order) {
      try {
        const res = await queryPayment({
          out_trade_no: order.out_trade_no
        })
        
        if (res.code === 0) {
          // 更新订单状态
          const index = this.orders.findIndex(item => item.out_trade_no === order.out_trade_no)
          if (index !== -1) {
            this.orders[index] = res.data
          }
          
          if (res.data.status === 1) {
            this.$message.success('订单已支付')
            // 刷新用户信息
            this.$store.dispatch('user/getUserInfo')
          } else {
            this.$message.info('订单未支付')
          }
        } else {
          this.$message.error(res.msg || '查询订单失败')
        }
      } catch (error) {
        console.error('查询订单失败', error)
        this.$message.error('查询订单失败')
      }
    },
    
    // 继续支付
    continuePayment(order) {
      this.currentOrder = order
      this.paymentDialogVisible = true
    },
    
    // 打开支付链接
    openPayUrl() {
      if (this.currentOrder.pay_url) {
        window.open(this.currentOrder.pay_url, '_blank')
      }
    },
    
    // 检查支付状态
    async checkPaymentStatus() {
      this.paymentLoading = true
      try {
        const res = await queryPayment({
          out_trade_no: this.currentOrder.out_trade_no
        })
        
        if (res.code === 0) {
          if (res.data.status === 1) {
            this.$message.success('订单已支付')
            this.paymentDialogVisible = false
            // 刷新用户信息
            this.$store.dispatch('user/getUserInfo')
            this.loadOrders()
          } else {
            this.$message.info('订单未支付')
          }
        } else {
          this.$message.error(res.msg || '查询支付状态失败')
        }
      } catch (error) {
        console.error('查询支付状态失败', error)
        this.$message.error('查询支付状态失败')
      } finally {
        this.paymentLoading = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.profile-container {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.profile-card, .vip-status-card, .vip-packages-card, .vip-comparison-card, .orders-card {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.profile-form {
  margin-top: 20px;
}

.profile-form .el-form-item {
  margin-bottom: 20px;
}

.profile-form .el-input {
  max-width: 300px;
}

.vip-status-display {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.vip-details {
  margin-top: 10px;
  padding: 15px;
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.1));
  border-radius: 8px;
  border-left: 4px solid #E6A23C;
}

.vip-level, .vip-expire, .vip-date {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.level-label, .expire-label, .date-label {
  font-weight: 500;
  color: #666;
  min-width: 80px;
}

.level-value {
  color: #E6A23C;
  font-weight: bold;
}

.expire-value {
  color: #67C23A;
  font-weight: bold;
}

.date-value {
  color: #409EFF;
  font-weight: bold;
}

.free-details {
  margin-top: 10px;
  padding: 15px;
  background: rgba(64, 158, 255, 0.1);
  border-radius: 8px;
  border-left: 4px solid #409EFF;
}

.upgrade-prompt {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.prompt-text {
  color: #409EFF;
  font-weight: 500;
}

/* 会员状态卡片 */
.vip-status-content {
  padding: 20px 0;
}

.vip-status-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.vip-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 80px;
  height: 80px;
  background: #f8f9fa;
  border-radius: 50%;
}

.vip-text h3 {
  margin: 0 0 10px 0;
  color: #333;
}

.vip-text p {
  margin: 0;
  color: #666;
}

/* 会员套餐卡片 */
.packages-container {
  padding: 20px 0;
}

.package-item {
  border: 1px solid #EBEEF5;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  height: 100%;
  transition: all 0.3s;
  
  &:hover {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    transform: translateY(-5px);
  }
  
  &.package-active {
    border-color: #67C23A;
    background: rgba(103, 194, 58, 0.05);
  }
}

.package-header {
  border-bottom: 1px solid #EBEEF5;
  padding-bottom: 15px;
  margin-bottom: 15px;
  
  h3 {
    margin: 0 0 10px 0;
    color: #333;
  }
}

.package-price {
  .price {
    font-size: 24px;
    color: #F56C6C;
    font-weight: bold;
  }
  
  .original-price {
    margin-left: 8px;
    color: #909399;
    text-decoration: line-through;
  }
}

.package-body {
  min-height: 100px;
  
  p {
    margin: 8px 0;
    color: #666;
  }
}

.package-footer {
  text-align: center;
  margin-top: 15px;
}

/* 订单记录卡片 */
.orders-content {
  padding: 20px 0;
}

.empty-orders, .empty-products {
  padding: 30px 0;
}

/* 支付对话框 */
.payment-dialog {
  .payment-info {
    margin-bottom: 20px;
    
    p {
      margin: 10px 0;
    }
  }
  
  .payment-qrcode {
    text-align: center;
    margin: 30px 0;
    
    p {
      margin-bottom: 15px;
    }
    
    .qrcode-container {
      display: inline-block;
      padding: 15px;
      background: #fff;
      border: 1px solid #EBEEF5;
      border-radius: 4px;
    }
  }
  
  .payment-actions {
    text-align: center;
    margin-top: 20px;
  }
}

/* VIP权益对比卡片 */
.comparison-table {
  margin-top: 15px;
}

.comparison-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  gap: 15px;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  align-items: center;
}

.comparison-row.header {
  font-weight: bold;
  color: #333;
  background: #f8f9fa;
  border-radius: 6px;
  padding: 15px 0;
  margin-bottom: 10px;
}

.comparison-row:last-child {
  border-bottom: none;
}

.feature {
  font-weight: 500;
  color: #333;
}

.free-user, .vip-user {
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: center;
}

.free-user i {
  color: #F56C6C;
  font-size: 16px;
}

.vip-user i {
  color: #67C23A;
  font-size: 16px;
}

.free-user span {
  color: #666;
}

.vip-user span {
  color: #67C23A;
  font-weight: 500;
}
</style> 