from datetime import datetime
from EarlyBird.common.libs.BaseModel import BaseModel
from sqlalchemy import (
    BigInteger,
    Integer,
    String,
    Boolean,
    Column,
    Enum,
    SmallInteger,
    DateTime,
    Table,
    BIGINT,
    ForeignKey,
    MetaData,
    Text,
    Numeric,
    Float,
)
import json
from . import TABLE_PREFIX
from sqlalchemy.orm import relationship


class Payment(BaseModel):
    """支付记录模型"""
    
    __tablename__ = f"{TABLE_PREFIX}payment"
    __table_args__ = {"comment": "支付记录表"}

    user_id = Column(BigInteger, nullable=False, comment='用户ID')
    product_id = Column(String(32), nullable=False, comment='商品ID')
    amount = Column(Numeric(10, 2), nullable=False, comment='支付金额')
    out_trade_no = Column(String(64), unique=True, nullable=False, comment='商户订单号')
    trade_no = Column(String(64), comment='平台订单号')
    status = Column(Integer, default=0, comment='支付状态：0-未支付，1-已支付，2-已退款')
    pay_type = Column(String(16), comment='支付方式')
    pay_time = Column(String(32), comment='支付时间')
    notify_data = Column(Text, comment='回调通知数据')
    
    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'product_id': self.product_id,
            'amount': float(self.amount) if self.amount else 0,
            'out_trade_no': self.out_trade_no,
            'trade_no': self.trade_no,
            'status': self.status,
            'pay_type': self.pay_type,
            'create_time': self.create_time.strftime('%Y-%m-%d %H:%M:%S') if self.create_time else '',
            'pay_time': self.pay_time
        }
    
    def __str__(self):
        return json.dumps(self.to_dict())


class PaymentProduct(BaseModel):
    """支付商品模型"""
    
    __tablename__ = f"{TABLE_PREFIX}payment_product"
    __table_args__ = {"comment": "支付商品表"}
    
    name = Column(String(100), nullable=False, comment='商品名称')
    product_id = Column(String(32), unique=True, nullable=False, comment='商品ID')
    price = Column(Numeric(10, 2), nullable=False, comment='商品价格')
    discount_price = Column(Numeric(10, 2), comment='折扣价格')
    description = Column(String(255), comment='商品描述')
    vip_level = Column(Integer, comment='对应会员等级')
    vip_days = Column(Integer, comment='会员天数')
    is_active = Column(Boolean, default=True, comment='是否启用')
    sort_order = Column(Integer, default=0, comment='排序')
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'product_id': self.product_id,
            'price': float(self.price) if self.price else 0,
            'discount_price': float(self.discount_price) if self.discount_price else None,
            'description': self.description,
            'vip_level': self.vip_level,
            'vip_days': self.vip_days,
            'is_active': self.is_active,
            'sort_order': self.sort_order,
            'create_time': self.create_time.strftime('%Y-%m-%d %H:%M:%S') if self.create_time else ''
        }
    
    def __str__(self):
        return json.dumps(self.to_dict())


class PaymentConfig(BaseModel):
    """支付配置模型"""
    
    __tablename__ = f"{TABLE_PREFIX}payment_config"
    __table_args__ = {"comment": "支付配置表"}
    
    name = Column(String(100), nullable=False, comment='配置名称')
    config_key = Column(String(100), nullable=False, comment='配置键')
    config_value = Column(Text, nullable=True, comment='配置值')
    description = Column(String(500), nullable=True, comment='配置描述')
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'config_key': self.config_key,
            'config_value': self.config_value,
            'description': self.description,
            'create_time': self.create_time.strftime('%Y-%m-%d %H:%M:%S') if self.create_time else ''
        }
    
    def __str__(self):
        return json.dumps(self.to_dict())


class ThesisDownloadRecord(BaseModel):
    """论文下载记录"""
    __tablename__ = f"{TABLE_PREFIX}thesis_download_record"
    __table_args__ = {"comment": "论文下载记录表"}
    
    uid = Column(Integer, nullable=False, comment='用户ID')
    thesis_id = Column(Integer, nullable=False, comment='论文ID')
    is_paid = Column(Boolean, nullable=False, default=False, comment='是否已支付')
    price = Column(Float, nullable=False, default=0.0, comment='支付金额')
    payment_method = Column(String(50), nullable=True, comment='支付方式')
    payment_time = Column(DateTime, nullable=True, comment='支付时间')
    payment_order_id = Column(String(100), nullable=True, comment='支付订单号')
    
    def to_dict(self):
        return {
            'id': self.id,
            'uid': self.uid,
            'thesis_id': self.thesis_id,
            'is_paid': self.is_paid,
            'price': self.price,
            'payment_method': self.payment_method,
            'payment_time': self.payment_time.strftime('%Y-%m-%d %H:%M:%S') if self.payment_time else '',
            'payment_order_id': self.payment_order_id,
            'create_time': self.create_time.strftime('%Y-%m-%d %H:%M:%S') if self.create_time else ''
        }
    
    def __str__(self):
        return json.dumps(self.to_dict())


class V990rPaymentConfig(BaseModel):
    """v990r支付配置"""
    __tablename__ = 'earlybird_paper_v990r_payment_config'

    merchant_id = Column(String(50), nullable=False, comment='商户ID')
    merchant_key = Column(String(100), nullable=False, comment='商户密钥')
    notify_url = Column(String(255), nullable=True, comment='异步通知地址')
    return_url = Column(String(255), nullable=True, comment='同步跳转地址')
    gateway_url = Column(String(255), nullable=False, default='https://v.990r.com/api/pay/submit', comment='网关地址')
    sign_type = Column(String(20), nullable=False, default='MD5', comment='签名类型')
    is_active = Column(Boolean, nullable=False, default=True, comment='是否启用')
    payment_methods = Column(Text, nullable=True, comment='支付方式配置，JSON格式')
    version = Column(String(10), nullable=True, default='1.0', comment='接口版本')
    timeout = Column(Integer, nullable=True, default=30, comment='超时时间(秒)')
    remark = Column(String(500), nullable=True, comment='备注') 