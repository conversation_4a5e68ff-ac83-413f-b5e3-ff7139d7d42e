export function getModelList() {
    return [
        // doubao endopoint的问题未测试，先注释了吧
        // {
        //     'id': '<PERSON><PERSON><PERSON>',
        //     'name': '豆包',
        //     'remark': '豆包'
        // },
        {
            'id': '<PERSON><PERSON>',
            'name': '<PERSON><PERSON>',
            'remark': '<PERSON><PERSON>'
        },
        {
            'id': '<PERSON><PERSON><PERSON>',
            'name': '千问Max',
            'remark': '千问Max'
        },
        {
            'id': 'DeepSeekR1',
            'name': 'DeepSeekR1',
            'remark': 'DeepSeekR1'
        },

        // {
        //     'id': 'Gpt',
        //     'name': 'Gpt3.5',
        //     'remark': 'Gpt3.5'
        // },
        // {
        //     'id': 'Gemini',
        //     'name': 'Gemini1.5',
        //     'remark': 'gemini'
        // },

    ]
}