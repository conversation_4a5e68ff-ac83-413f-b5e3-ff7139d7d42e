<template>
  <router-view />
</template>

<script>
export default {
  name: "App",
};
</script>

<style>
/* Loading percentage styles */
.loading-with-percentage .el-loading-text {
  text-align: center;
  font-size: 16px;
  color: #333;
  margin-top: 15px;
  display: block;
  word-break: break-all;
  line-height: 1.5;
}

.loading-percentage {
  font-weight: bold;
  color: #409eff;
  font-size: 18px;
  margin-top: 8px;
  display: block;
  text-align: center;
  background: rgba(64, 158, 255, 0.1);
  padding: 4px 12px;
  border-radius: 4px;
  width: fit-content;
  margin: 8px auto 0;
}

/* 让Element UI的loading组件支持HTML内容 */
.el-loading-mask .el-loading-spinner .el-loading-text {
  white-space: normal;
  line-height: 1.5;
  padding: 12px 20px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(4px);
  max-width: 80%;
  margin: 0 auto;
}

.el-loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.el-loading-spinner .circular {
  width: 42px;
  height: 42px;
  animation: loading-rotate 2s linear infinite;
}

.el-loading-spinner .path {
  stroke: #409EFF;
  stroke-width: 2;
  stroke-linecap: round;
  animation: loading-dash 1.5s ease-in-out infinite;
}
</style>