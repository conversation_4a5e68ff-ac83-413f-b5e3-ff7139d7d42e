# Flask Web框架相关
Flask==2.3.3
Flask-SQLAlchemy==3.0.5
Flask-Migrate==4.0.5
Flask-CORS==4.0.0
Flask-Pydantic==0.12.0

# 数据库相关
SQLAlchemy==2.0.21
PyMySQL==1.1.0
alembic==1.12.0

# 数据验证和序列化
pydantic==2.4.2

# HTTP请求库
requests==2.31.0

# 加密和安全相关
cryptography==41.0.7
PyJWT==2.8.0
pycryptodome==3.19.0
arc4==0.3.0
Werkzeug==2.3.7

# 文档处理
python-docx==0.8.11
beautifulsoup4==4.12.2
lxml==4.9.3
markdown==3.5.1

# 系统信息和工具
psutil==5.9.6
prettytable==3.9.0
shortuuid==1.0.11

# Windows系统相关（如果在Windows上运行）
pywin32==306
WMI==1.5.1

# 生产服务器
waitress==2.1.2

# 日志和调试
colorama==0.4.6

# 时间处理
python-dateutil==2.8.2

# 配置文件处理
configparser==6.0.0

# 其他工具库
typing-extensions==4.8.0
click==8.1.7
itsdangerous==2.1.2
Jinja2==3.1.2
MarkupSafe==2.1.3
greenlet==3.0.0

# 可选依赖（根据需要安装）
# 如果使用其他数据库
# psycopg2-binary==2.9.7  # PostgreSQL
# cx-Oracle==8.3.0        # Oracle

# 开发和测试工具（可选）
# pytest==7.4.3
# pytest-flask==1.3.0
# coverage==7.3.2
