import logging
import os
import sys
from flask import send_from_directory, redirect, Flask
from datetime import timedelta

# 添加项目根目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)
sys.path.insert(0, current_dir)

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s  %(filename)s %(lineno)s: %(levelname)s  %(message)s [%(threadName)s]",
    datefmt="%Y-%m-%d %H:%M:%S",
)

logger = logging.getLogger(__name__)

# 使用新的环境变量配置
os.environ["FLASK_DEBUG"] = "0"  # 生产环境设为0，开发环境设为1
os.environ["FLASK_ENV"] = "production"  # 添加这个，配置文件需要

def create_custom_app():
    """创建自定义Flask应用，确保静态文件路由正确注册"""
    try:
        # 静态文件路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        static_dir = os.path.join(current_dir, "resources", "ui")
        
        logger.info(f"Static directory: {static_dir}")
        logger.info(f"Static directory exists: {os.path.exists(static_dir)}")
        
        # 确保工作目录正确
        os.chdir(current_dir)
        logger.info(f"设置工作目录: {os.getcwd()}")
        
        # 创建Flask应用
        app = Flask(__name__, static_folder=static_dir, static_url_path='/static')
        
        # 使用统一的初始化函数进行配置
        from EarlyBird.common.init_flask import init_app
        init_app(app, None)
        
        # 其他配置（SECRET_KEY已在init_app中设置，这里不再重复设置）
        app.config["SEND_FILE_MAX_AGE_DEFAULT"] = 1
        
        # 设置CORS - 确保支持credentials和cookies
        from flask_cors import CORS
        CORS(app, 
             supports_credentials=True,
             origins=["http://127.0.0.1:3301", "http://localhost:3301", "http://localhost:3000", "http://127.0.0.1:3000"],
             allow_headers=["Content-Type", "Authorization", "X-Requested-With"],
             expose_headers=["Content-Type", "Set-Cookie"],
             methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"]
        )

        # 注册配置和数据库（但不注册钩子函数）
        from EarlyBird.ExtendRegister.conf_register import register_config
        register_config(app)

        from EarlyBird.ExtendRegister.db_register import register_db, db
        register_db(app)
        
        from flask_migrate import Migrate
        migrate = Migrate(app, db)

        # 先注册API蓝图（确保API路由优先）
        from EarlyBird.ExtendRegister.bp_register import register_bp as register_blueprints
        register_blueprints(app)

        # 然后添加下载文件路由（在通配符路由之前）
        @app.route('/resources/download/<filename>')
        def serve_download_file(filename):
            logger.info(f"Serving download file: {filename}")

            # 构建下载文件的完整路径
            download_dir = os.path.join(current_dir, "resources", "download")
            file_path = os.path.join(download_dir, filename)

            # 检查文件是否存在
            if os.path.exists(file_path):
                logger.info(f"Download file found: {file_path}")
                # 设置正确的Content-Type和下载头
                from flask import Response
                response = send_from_directory(download_dir, filename, as_attachment=True)
                response.headers['Content-Type'] = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
                return response
            else:
                logger.error(f"Download file not found: {file_path}")
                from flask import abort
                abort(404)

        # 最后注册静态文件路由（通配符路由放在最后）
        @app.route('/', defaults={'path': ''})
        @app.route('/<path:path>')
        def serve_spa(path):
            logger.info(f"Serving request for path: {path}")

            # 如果是API路径，跳过静态文件处理
            if path.startswith('api/'):
                logger.info(f"Skipping static file handling for API path: {path}")
                from flask import abort
                abort(404)  # 让API路由处理

            # 如果是下载路径，跳过静态文件处理（让下载路由处理）
            if path.startswith('resources/download/'):
                logger.info(f"Skipping static file handling for download path: {path}")
                from flask import abort
                abort(404)  # 让下载路由处理

            if path != "" and os.path.exists(os.path.join(static_dir, path)):
                logger.info(f"Serving static file: {path}")
                return send_from_directory(static_dir, path)
            else:
                logger.info(f"Serving index.html for path: {path}")
                return send_from_directory(static_dir, 'index.html')

        # 最后注册钩子函数（这样不会影响静态文件路由）
        from EarlyBird.ExtendRegister.hook_register import register_hook
        register_hook(app)

        # 注册异常处理
        from EarlyBird.ExtendRegister.excep_register import register_excep
        register_excep(app)

        return app
    except Exception as e:
        logger.exception("创建应用失败")
        return None

def run_web_server():
    try:
        from common.init_flask import printEnvInfo
        printEnvInfo()
        
        app = create_custom_app()
        
        if app is None:
            logger.error("Failed to create Flask app")
            return

        from task.task_api_server import TaskApiServer
        from task.task_paper_generator import TaskPaperGenerator
        from threading import Event

        stop_event = Event()
        TaskApiServer(stop_event, app).start()
        TaskPaperGenerator(stop_event, app).start()

        # 直接运行 Flask 服务器
        print("\n=== 服务器已启动 ===")
        print("请在浏览器中访问: http://127.0.0.1:3301")
        print("=================\n")
        
        # 禁用自动重载，使用 waitress 作为生产服务器
        from waitress import serve
        serve(app, host="127.0.0.1", port=3301)

    except Exception as e:
        logger.exception(e)
        logger.error(f"catch exception when running web server: {str(e)}")

if __name__ == "__main__":
    run_web_server() 