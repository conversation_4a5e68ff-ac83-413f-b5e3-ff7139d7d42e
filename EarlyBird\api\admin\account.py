from flask import Blueprint, request, jsonify, g
from EarlyBird.common.libs.api_result import ApiResult
from EarlyBird.model.admin import Admin, AdminLog
from EarlyBird.api.admin.auth import admin_auth_required
from EarlyBird.common.libs.BaseModel import db
import logging
import traceback
import time
import sqlalchemy.exc

logger = logging.getLogger(__name__)

# 创建蓝图
admin_account_bp = Blueprint('admin_account', __name__)


@admin_account_bp.route('/list', methods=['GET'])
@admin_auth_required
def get_admin_list():
    """获取管理员列表"""
    start_time = time.time()
    try:
        # 验证是否为超级管理员
        admin = request.admin
        if not admin.is_superadmin:
            logger.warning(f"非超级管理员 {admin.username}(ID:{admin.id}) 尝试访问管理员列表")
            return ApiResult.error("权限不足，只有超级管理员可以管理账号")
        
        # 获取查询参数
        page = int(request.args.get('page', 1))
        size = int(request.args.get('size', 20))
        keyword = request.args.get('keyword', '')
        
        logger.info(f"获取管理员列表: page={page}, size={size}, keyword={keyword}")
        
        try:
            # 优化查询：只查询需要的字段，避免大数据量查询
            query = db.session.query(
                Admin.id, 
                Admin.username, 
                Admin.realname, 
                Admin.email, 
                Admin.phone, 
                Admin.is_active, 
                Admin.is_superadmin, 
                Admin.role, 
                Admin.create_time, 
                Admin.last_login_time
            ).filter(Admin.is_deleted == False)
            
            # 关键词搜索
            if keyword:
                query = query.filter(Admin.username.like(f'%{keyword}%') | 
                                    Admin.realname.like(f'%{keyword}%') | 
                                    Admin.email.like(f'%{keyword}%'))
            
            # 计算总数 - 使用子查询优化计数操作
            from sqlalchemy import func
            count_query = db.session.query(func.count(Admin.id)).filter(Admin.is_deleted == False)
            if keyword:
                count_query = count_query.filter(Admin.username.like(f'%{keyword}%') | 
                                               Admin.realname.like(f'%{keyword}%') | 
                                               Admin.email.like(f'%{keyword}%'))
            total = count_query.scalar()
            
            # 分页 - 使用高效的分页查询
            admins = query.order_by(Admin.id.desc()).offset((page - 1) * size).limit(size).all()
            
            # 构建响应数据
            admin_list = []
            for admin_item in admins:
                admin_data = {
                    'id': admin_item.id,
                    'username': admin_item.username,
                    'realname': admin_item.realname,
                    'email': admin_item.email,
                    'phone': admin_item.phone,
                    'is_active': admin_item.is_active,
                    'is_superadmin': admin_item.is_superadmin,
                    'role': admin_item.role,
                    'created_at': admin_item.create_time.strftime('%Y-%m-%d %H:%M:%S') if admin_item.create_time else None,
                    'last_login_at': admin_item.last_login_time.strftime('%Y-%m-%d %H:%M:%S') if admin_item.last_login_time else None
                }
                admin_list.append(admin_data)
            
        except sqlalchemy.exc.OperationalError as e:
            logger.error(f"数据库连接错误: {str(e)}")
            db.session.rollback()
            return ApiResult.error(f"数据库连接错误，请稍后重试: {str(e)}")
        except Exception as e:
            logger.error(f"查询管理员列表失败: {str(e)}")
            logger.error(traceback.format_exc())
            return ApiResult.error(f"查询管理员列表失败: {str(e)}")
        
        elapsed_time = time.time() - start_time
        logger.info(f"成功获取管理员列表: 共{total}条记录，当前返回{len(admin_list)}条，耗时{elapsed_time:.2f}秒")
        
        return ApiResult.success({
            'list': admin_list,
            'total': total,
            'page': page,
            'size': size,
            'elapsed_time': f"{elapsed_time:.2f}秒"
        })
    
    except Exception as e:
        elapsed_time = time.time() - start_time
        logger.error(f"获取管理员列表失败: {str(e)}, 耗时{elapsed_time:.2f}秒")
        logger.error(traceback.format_exc())
        # 确保会话状态正常
        try:
            db.session.rollback()
        except:
            pass
        
        return ApiResult.error(f"获取管理员列表失败: {str(e)}")


@admin_account_bp.route('/<int:admin_id>', methods=['GET'])
@admin_auth_required
def get_admin_detail(admin_id):
    """获取管理员详情"""
    try:
        # 验证是否为超级管理员
        admin = request.admin
        if not admin.is_superadmin:
            return ApiResult.error("权限不足，只有超级管理员可以管理账号")
        
        # 查询管理员
        target_admin = Admin.query.get(admin_id)
        if not target_admin:
            return ApiResult.error("管理员不存在")
        
        # 获取管理员信息
        admin_data = target_admin.get_info()
        # 移除敏感信息
        admin_data.pop('password_hash', None)
        
        logger.info(f"成功获取管理员详情: ID={admin_id}, username={target_admin.username}")
        return ApiResult.success(admin_data)
    
    except Exception as e:
        logger.error(f"获取管理员详情失败: {str(e)}")
        logger.error(traceback.format_exc())
        return ApiResult.error(f"获取管理员详情失败: {str(e)}")


@admin_account_bp.route('', methods=['POST'])
@admin_auth_required
def create_admin():
    """创建管理员"""
    try:
        # 验证是否为超级管理员
        admin = request.admin
        if not admin.is_superadmin:
            return ApiResult.error("权限不足，只有超级管理员可以创建管理员")
        
        # 获取请求数据
        data = request.get_json()
        if not data:
            return ApiResult.error("请求数据不能为空")
            
        username = data.get('username')
        password = data.get('password')
        realname = data.get('realname')
        email = data.get('email')
        phone = data.get('phone')
        is_active = data.get('is_active', True)
        
        # 验证必填字段
        if not username or not password:
            return ApiResult.error("用户名和密码不能为空")
        
        # 检查用户名是否已存在
        if Admin.query.filter_by(username=username).first():
            return ApiResult.error("用户名已存在")
        
        # 创建新管理员
        new_admin = Admin(
            username=username,
            password=password,  # 会自动哈希
            realname=realname,
            email=email,
            phone=phone,
            is_active=is_active,
            is_superadmin=False,  # 默认为普通管理员
            role='admin'
        )
        new_admin.save()
        
        # 记录操作日志
        AdminLog.create_log(
            admin_id=admin.id,
            admin_username=admin.username,
            action="create",
            resource="admin",
            resource_id=new_admin.id,
            description=f"创建管理员: {username}",
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', '')
        )
        
        logger.info(f"成功创建管理员: ID={new_admin.id}, username={username}")
        return ApiResult.success(new_admin.get_info(), "管理员创建成功")
    
    except Exception as e:
        logger.error(f"创建管理员失败: {str(e)}")
        logger.error(traceback.format_exc())
        db.session.rollback()
        return ApiResult.error(f"创建管理员失败: {str(e)}")


@admin_account_bp.route('/<int:admin_id>', methods=['PUT'])
@admin_auth_required
def update_admin(admin_id):
    """更新管理员信息"""
    try:
        # 验证是否为超级管理员
        admin = request.admin
        if not admin.is_superadmin:
            return ApiResult.error("权限不足，只有超级管理员可以更新管理员信息")
        
        # 查询管理员
        target_admin = Admin.query.get(admin_id)
        if not target_admin:
            return ApiResult.error("管理员不存在")
        
        # 不允许修改超级管理员
        if target_admin.is_superadmin and target_admin.id != admin.id:
            return ApiResult.error("不能修改其他超级管理员的信息")
        
        # 获取请求数据
        data = request.get_json()
        if not data:
            return ApiResult.error("请求数据不能为空")
            
        realname = data.get('realname')
        email = data.get('email')
        phone = data.get('phone')
        is_active = data.get('is_active')
        
        # 更新信息
        if realname is not None:
            target_admin.realname = realname
        if email is not None:
            target_admin.email = email
        if phone is not None:
            target_admin.phone = phone
        if is_active is not None:
            target_admin.is_active = is_active
        
        target_admin.save()
        
        # 记录操作日志
        AdminLog.create_log(
            admin_id=admin.id,
            admin_username=admin.username,
            action="update",
            resource="admin",
            resource_id=target_admin.id,
            description=f"更新管理员信息: {target_admin.username}",
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', '')
        )
        
        logger.info(f"成功更新管理员信息: ID={admin_id}, username={target_admin.username}")
        return ApiResult.success(None, "管理员信息更新成功")
    
    except Exception as e:
        logger.error(f"更新管理员信息失败: {str(e)}")
        logger.error(traceback.format_exc())
        db.session.rollback()
        return ApiResult.error(f"更新管理员信息失败: {str(e)}")


@admin_account_bp.route('/<int:admin_id>/reset-password', methods=['POST'])
@admin_auth_required
def reset_admin_password(admin_id):
    """重置管理员密码"""
    try:
        # 验证是否为超级管理员
        admin = request.admin
        if not admin.is_superadmin:
            return ApiResult.error("权限不足，只有超级管理员可以重置密码")
        
        # 查询管理员
        target_admin = Admin.query.get(admin_id)
        if not target_admin:
            return ApiResult.error("管理员不存在")
        
        # 不允许修改超级管理员密码（除非是自己）
        if target_admin.is_superadmin and target_admin.id != admin.id:
            return ApiResult.error("不能重置其他超级管理员的密码")
        
        # 获取新密码
        data = request.get_json()
        if not data:
            return ApiResult.error("请求数据不能为空")
            
        new_password = data.get('new_password')
        
        if not new_password:
            return ApiResult.error("新密码不能为空")
        
        # 重置密码
        target_admin.password = new_password
        target_admin.save()
        
        # 记录操作日志
        AdminLog.create_log(
            admin_id=admin.id,
            admin_username=admin.username,
            action="reset_password",
            resource="admin",
            resource_id=target_admin.id,
            description=f"重置管理员密码: {target_admin.username}",
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', '')
        )
        
        logger.info(f"成功重置管理员密码: ID={admin_id}, username={target_admin.username}")
        return ApiResult.success(None, "密码重置成功")
    
    except Exception as e:
        logger.error(f"重置管理员密码失败: {str(e)}")
        logger.error(traceback.format_exc())
        db.session.rollback()
        return ApiResult.error(f"重置管理员密码失败: {str(e)}")


@admin_account_bp.route('/<int:admin_id>', methods=['DELETE'])
@admin_auth_required
def delete_admin(admin_id):
    """删除管理员"""
    try:
        # 验证是否为超级管理员
        admin = request.admin
        if not admin.is_superadmin:
            return ApiResult.error("权限不足，只有超级管理员可以删除管理员")
        
        # 查询管理员
        target_admin = Admin.query.get(admin_id)
        if not target_admin:
            return ApiResult.error("管理员不存在")
        
        # 不允许删除超级管理员
        if target_admin.is_superadmin:
            return ApiResult.error("不能删除超级管理员")
        
        # 不允许删除自己
        if target_admin.id == admin.id:
            return ApiResult.error("不能删除自己的账号")
        
        # 软删除
        target_admin.is_deleted = True
        target_admin.save()
        
        # 记录操作日志
        AdminLog.create_log(
            admin_id=admin.id,
            admin_username=admin.username,
            action="delete",
            resource="admin",
            resource_id=target_admin.id,
            description=f"删除管理员: {target_admin.username}",
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent', '')
        )
        
        logger.info(f"成功删除管理员: ID={admin_id}, username={target_admin.username}")
        return ApiResult.success(None, "管理员删除成功")
    
    except Exception as e:
        logger.error(f"删除管理员失败: {str(e)}")
        logger.error(traceback.format_exc())
        db.session.rollback()
        return ApiResult.error(f"删除管理员失败: {str(e)}") 