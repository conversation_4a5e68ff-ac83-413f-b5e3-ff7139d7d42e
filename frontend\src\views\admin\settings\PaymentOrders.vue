<!--
作者名：EarlyBird
作者邮箱：<EMAIL>
官方网址：https://blog.zaoniao.vip

订单管理页面
-->
<template>
  <div class="payment-orders">
    <!-- 添加页面标题 -->
    <div class="page-header">
      <h2>订单管理</h2>
      <div class="header-actions">
        <el-button type="primary" size="small" @click="loadOrders" icon="el-icon-refresh">刷新</el-button>
      </div>
    </div>
    
    <!-- 搜索条件 -->
    <el-card class="filter-container">
      <el-form :inline="true" :model="queryParams" size="small">
        <el-form-item label="订单状态">
          <el-select v-model="queryParams.status" placeholder="全部状态" clearable>
            <el-option label="未支付" :value="0"></el-option>
            <el-option label="已支付" :value="1"></el-option>
            <el-option label="已退款" :value="2"></el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="用户ID">
          <el-input v-model="queryParams.user_id" placeholder="用户ID" clearable></el-input>
        </el-form-item>
        
        <el-form-item label="论文ID">
          <el-input v-model="queryParams.product_id" placeholder="论文ID" clearable></el-input>
        </el-form-item>
        
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            align="right">
          </el-date-picker>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch" icon="el-icon-search">搜索</el-button>
          <el-button @click="resetQuery" icon="el-icon-refresh-left">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 订单列表 -->
    <el-card class="table-container" v-loading="loading">
      <el-table :data="orderList" style="width: 100%" border>
        <el-table-column prop="id" label="ID" width="80" align="center"></el-table-column>
        <el-table-column prop="user_id" label="用户ID" width="100" align="center"></el-table-column>
        <el-table-column label="论文信息" min-width="180">
          <template slot-scope="scope">
            <div v-if="scope.row.thesisInfo">
              <div class="thesis-info">
                <span class="thesis-id">ID: {{ scope.row.thesisInfo.id }}</span>
                <el-link type="primary" @click="viewThesisDetail(scope.row.thesisInfo.id)" class="thesis-title">
                  {{ scope.row.thesisInfo.title || '未知论文' }}
                </el-link>
              </div>
            </div>
            <div v-else-if="scope.row.product_id && scope.row.product_id.includes('thesis_download')">
              <div class="thesis-info pending">
                <span class="thesis-id">ID: {{ extractThesisId(scope.row.product_id) }}</span>
                <el-tooltip content="论文可能已被删除或无法访问" placement="top">
                  <span class="thesis-title-pending">论文信息未找到</span>
                </el-tooltip>
              </div>
            </div>
            <div v-else>
              <span class="non-thesis-product">{{ scope.row.product_id || '未知商品' }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="amount" label="金额" width="100" align="right">
          <template slot-scope="scope">
            {{ scope.row.amount.toFixed(2) }} 元
          </template>
        </el-table-column>
        <el-table-column prop="out_trade_no" label="订单号" min-width="180"></el-table-column>
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="pay_type" label="支付方式" width="100" align="center"></el-table-column>
        <el-table-column prop="create_time" label="创建时间" width="160" align="center"></el-table-column>
        <el-table-column prop="pay_time" label="支付时间" width="160" align="center"></el-table-column>
        <el-table-column label="操作" width="120" align="center">
          <template slot-scope="scope">
            <el-button 
              type="text" 
              size="small" 
              @click="viewOrderDetail(scope.row)"
              icon="el-icon-view">
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="queryParams.page"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="queryParams.size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </div>
    </el-card>
  </div>
</template>

<script>
import { adminPayment } from '@/api/admin'
import { adminThesis } from '@/api/admin'

export default {
  name: 'PaymentOrders',
  data() {
    return {
      // 查询参数
      queryParams: {
        page: 1,
        size: 20,
        status: '',
        user_id: '',
        product_id: '',
        start_date: '',
        end_date: ''
      },
      
      // 日期范围
      dateRange: [],
      
      // 订单列表
      orderList: [],
      
      // 论文信息缓存
      thesisCache: {},
      
      // 总数
      total: 0,
      
      // 加载状态
      loading: false
    }
  },
  
  created() {
    this.loadOrders()
  },
  
  methods: {
    /**
     * 加载订单列表
     */
    async loadOrders() {
      try {
        this.loading = true
        
        // 处理日期范围
        if (this.dateRange && this.dateRange.length === 2) {
          this.queryParams.start_date = this.dateRange[0]
          this.queryParams.end_date = this.dateRange[1]
        }
        
        const response = await adminPayment.getOrders(this.queryParams)
        
        if (response.success && response.data) {
          this.orderList = response.data.list || []
          this.total = response.data.total || 0
          
          // 加载论文信息
          this.loadThesisInfo()
        } else {
          this.$message.error(response.message || '加载订单列表失败')
        }
      } catch (error) {
        console.error('加载订单列表失败:', error)
        this.$message.error('加载订单列表失败: ' + (error.message || '未知错误'))
      } finally {
        this.loading = false
      }
    },
    
    /**
     * 加载论文信息
     */
    async loadThesisInfo() {
      try {
        console.log('开始加载论文信息...');
        
        // 提取所有订单中的论文ID
        const thesisIds = this.orderList
          .filter(order => order.product_id && order.product_id.includes('thesis_download'))
          .map(order => {
            // 从product_id中提取论文ID
            const match = order.product_id.match(/thesis_download_?(\d+)?/)
            const thesisId = match ? match[1] || null : null;
            console.log(`从订单 ${order.id} 的商品ID ${order.product_id} 中提取论文ID: ${thesisId}`);
            return thesisId;
          })
          .filter(id => id !== null)
        
        // 去重
        const uniqueIds = [...new Set(thesisIds)]
        console.log(`找到 ${uniqueIds.length} 个唯一论文ID需要加载: ${uniqueIds.join(', ')}`);
        
        // 批量获取论文信息
        for (const id of uniqueIds) {
          if (!this.thesisCache[id]) {
            try {
              console.log(`获取论文ID ${id} 的标题信息...`);
              // 使用简化的标题API，只获取论文标题，避免401错误
              const thesisResponse = await this.$axios.get(`/api/admin/thesis/title/${id}`)

              if (thesisResponse.data && thesisResponse.data.success && thesisResponse.data.data) {
                this.thesisCache[id] = thesisResponse.data.data
                console.log(`成功加载论文ID ${id} 的标题: ${thesisResponse.data.data.title}`);
              } else {
                console.warn(`获取论文ID ${id} 标题失败: ${thesisResponse.data ? thesisResponse.data.message : '未知错误'}`);
                // 如果获取失败，设置默认标题
                this.thesisCache[id] = {
                  id: id,
                  title: `论文${id}`
                }
                console.log(`使用默认标题: 论文${id}`);
              }
            } catch (error) {
              console.error(`获取论文ID ${id} 标题失败:`, error)
              // 如果请求失败，设置默认标题
              this.thesisCache[id] = {
                id: id,
                title: `论文${id}`
              }
              console.log(`请求失败，使用默认标题: 论文${id}`);
            }
          } else {
            console.log(`使用缓存中的论文ID ${id} 信息: ${this.thesisCache[id].title}`);
          }
        }
        
        // 将论文信息关联到订单
        this.orderList = this.orderList.map(order => {
          if (order.product_id && order.product_id.includes('thesis_download')) {
            const match = order.product_id.match(/thesis_download_?(\d+)?/)
            const thesisId = match ? match[1] || null : null
            
            if (thesisId && this.thesisCache[thesisId]) {
              console.log(`关联论文ID ${thesisId} 到订单 ${order.id}`);
              return {
                ...order,
                thesisInfo: this.thesisCache[thesisId]
              }
            } else {
              console.log(`订单 ${order.id} 的论文ID ${thesisId} 未找到对应信息`);
            }
          }
          return order
        })
      } catch (error) {
        console.error('加载论文信息失败:', error)
      }
    },
    
    /**
     * 查看论文详情
     */
    viewThesisDetail(thesisId) {
      if (!thesisId) return
      
      this.$router.push({ 
        name: 'AdminThesisList',
        query: { id: thesisId }
      })
    },
    
    /**
     * 提取论文ID
     */
    extractThesisId(productId) {
      const match = productId.match(/thesis_download_?(\d+)?/)
      return match ? match[1] || '未知' : '未知'
    },
    
    /**
     * 获取状态类型
     */
    getStatusType(status) {
      switch (status) {
        case 0:
          return 'warning'
        case 1:
          return 'success'
        case 2:
          return 'info'
        default:
          return 'info'
      }
    },
    
    /**
     * 获取状态文本
     */
    getStatusText(status) {
      switch (status) {
        case 0:
          return '未支付'
        case 1:
          return '已支付'
        case 2:
          return '已退款'
        default:
          return '未知'
      }
    },
    
    /**
     * 查看订单详情
     */
    viewOrderDetail(order) {
      this.$router.push({ 
        name: 'AdminPaymentOrderDetail', 
        params: { id: order.id }
      })
    },
    
    /**
     * 搜索
     */
    handleSearch() {
      this.queryParams.page = 1
      this.loadOrders()
    },
    
    /**
     * 重置查询
     */
    resetQuery() {
      this.queryParams = {
        page: 1,
        size: 20,
        status: '',
        user_id: '',
        product_id: '',
        start_date: '',
        end_date: ''
      }
      this.dateRange = []
      this.loadOrders()
    },
    
    /**
     * 处理每页数量变化
     */
    handleSizeChange(size) {
      this.queryParams.size = size
      this.loadOrders()
    },
    
    /**
     * 处理页码变化
     */
    handleCurrentChange(page) {
      this.queryParams.page = page
      this.loadOrders()
    },
    
    /**
     * 返回上一页
     */
    goBack() {
      this.$router.push({ name: 'AdminThesis' })
    },
    
    /**
     * 跳转到统计分析页面
     */
    goToStats() {
      this.$message.info('统计分析功能即将上线')
    }
  }
}
</script>

<style scoped>
.payment-orders {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.page-header h2 {
  margin: 0;
  font-size: 20px;
  color: #333;
}

.header-actions {
  display: flex;
  align-items: center;
}

.filter-container {
  margin-bottom: 20px;
}

.table-container {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.thesis-info {
  display: flex;
  align-items: center;
  gap: 5px;
}

.thesis-id {
  font-weight: bold;
  color: #409EFF;
}

.thesis-title {
  font-size: 14px;
  color: #409EFF;
}

.thesis-info.pending {
  color: #E6A23C;
  font-style: italic;
}

.thesis-title-pending {
  font-size: 14px;
  color: #E6A23C;
}

.non-thesis-product {
  font-style: italic;
  color: #909399;
}
</style> 