import { adminAuth, adminUser, adminThesis, adminStats, adminSettings, adminAccount } from '@/api/admin'
import Cookies from 'js-cookie'

const state = {
  // 管理员信息
  adminInfo: JSON.parse(localStorage.getItem('admin_info')) || null,
  // 管理员Token
  adminToken: Cookies.get('admin_token') || localStorage.getItem('admin_token') || '',
  // 用户列表
  userList: [],
  userTotal: 0,
  userLoading: false,
  // 论文列表
  thesisList: [],
  thesisTotal: 0,
  thesisLoading: false,
  // 统计数据
  overviewData: null,
  userStats: null,
  thesisStats: null,
  chatStats: null,
  // 系统设置
  systemSettings: null,
  settingsLoading: false,
  // 管理员账号管理
  accountList: [],
  accountTotal: 0,
  accountLoading: false
}

const mutations = {
  // 设置管理员信息
  SET_ADMIN_INFO(state, info) {
    state.adminInfo = info
    if (info) {
      localStorage.setItem('admin_info', JSON.stringify(info))
    } else {
      localStorage.removeItem('admin_info')
    }
  },
  
  // 设置管理员Token
  SET_ADMIN_TOKEN(state, token) {
    state.adminToken = token
    if (token) {
      Cookies.set('admin_token', token, { expires: 7 })
      localStorage.setItem('admin_token', token)
    } else {
      Cookies.remove('admin_token')
      localStorage.removeItem('admin_token')
    }
  },
  
  // 设置用户列表
  SET_USER_LIST(state, { list, total }) {
    state.userList = list
    state.userTotal = total
  },
  
  // 设置用户加载状态
  SET_USER_LOADING(state, loading) {
    state.userLoading = loading
  },
  
  // 设置论文列表
  SET_THESIS_LIST(state, { list, total }) {
    state.thesisList = list
    state.thesisTotal = total
  },
  
  // 设置论文加载状态
  SET_THESIS_LOADING(state, loading) {
    state.thesisLoading = loading
  },
  
  // 设置概览数据
  SET_OVERVIEW_DATA(state, data) {
    state.overviewData = data
  },
  
  // 设置用户统计数据
  SET_USER_STATS(state, data) {
    state.userStats = data
  },
  
  // 设置论文统计数据
  SET_THESIS_STATS(state, data) {
    state.thesisStats = data
  },
  
  // 设置聊天统计数据
  SET_CHAT_STATS(state, data) {
    state.chatStats = data
  },
  
  // 设置系统设置
  SET_SYSTEM_SETTINGS(state, settings) {
    state.systemSettings = settings
  },
  
  // 设置设置加载状态
  SET_SETTINGS_LOADING(state, loading) {
    state.settingsLoading = loading
  },
  
  // 管理员账号管理
  SET_ACCOUNT_LIST(state, { list, total }) {
    state.accountList = list
    state.accountTotal = total
  },
  
  SET_ACCOUNT_LOADING(state, loading) {
    state.accountLoading = loading
  }
}

const actions = {
  // 管理员登录
  async adminLogin({ commit }, loginData) {
    try {
      const response = await adminAuth.login(loginData)
      if (response.success) {
        const { token, admin_info } = response.data
        
        // 存储token到多个位置，确保可用性
        commit('SET_ADMIN_TOKEN', token)
        commit('SET_ADMIN_INFO', admin_info)
        
        // 设置axios默认header
        import('@/utils/request').then(module => {
          const request = module.default
          if (request && request.defaults) {
            request.defaults.headers.common['Authorization'] = `Bearer ${token}`
          }
        })
        
        return { success: true, data: response.data }
      } else {
        return { success: false, message: response.message }
      }
    } catch (error) {
      return { success: false, message: error.message || '登录失败' }
    }
  },
  
  // 管理员登出
  async adminLogout({ commit }) {
    try {
      console.log('开始管理员登出流程')

      // 先清除本地状态，避免无限循环
      commit('SET_ADMIN_TOKEN', '')
      commit('SET_ADMIN_INFO', null)

      // 清除所有相关cookie和localStorage
      Cookies.remove('admin_token')
      Cookies.remove('admin_token', { path: '/' })
      Cookies.remove('admin_token', { path: '/admin' })
      localStorage.removeItem('admin_token')

      // 清除axios默认header
      try {
        const request = await import('@/utils/request')
        if (request.default && request.default.defaults) {
          delete request.default.defaults.headers.common['Authorization']
        }
      } catch (error) {
        console.warn('清除axios header失败:', error)
      }

      // 尝试调用登出接口（可选，失败不影响登出流程）
      try {
        await adminAuth.logout()
        console.log('登出API调用成功')
      } catch (error) {
        console.warn('登出API调用失败，但本地状态已清理:', error.message)
      }

      console.log('管理员登出流程完成')
      return { success: true, message: '登出成功' }
    } catch (error) {
      console.error('管理员登出过程中发生错误:', error)
      // 即使出错也返回成功，确保用户可以退出
      return { success: true, message: '已退出登录' }
    }
  },
  
  // 获取管理员信息
  async getAdminProfile({ commit, state }) {
    try {
      // 如果没有token，直接返回失败
      if (!state.adminToken) {
        return { success: false, message: '未登录' }
      }
      
      const response = await adminAuth.getProfile()
      if (response.success) {
        commit('SET_ADMIN_INFO', response.data)
        return { success: true, data: response.data }
      } else {
        return { success: false, message: response.message }
      }
    } catch (error) {
      console.error('获取管理员信息失败:', error)
      
      // 如果是401错误，清除登录状态
      if (error.response && error.response.status === 401) {
        commit('SET_ADMIN_TOKEN', '')
        commit('SET_ADMIN_INFO', null)
      }
      
      return { success: false, message: error.message || '获取信息失败' }
    }
  },
  
  // 修改密码
  async changePassword({ commit }, passwordData) {
    try {
      const response = await adminAuth.changePassword(passwordData)
      if (response.success) {
        return { success: true, message: response.message }
      } else {
        return { success: false, message: response.message }
      }
    } catch (error) {
      return { success: false, message: error.message || '修改密码失败' }
    }
  },
  
  // 获取用户列表
  async getUserList({ commit }, params) {
    commit('SET_USER_LOADING', true)
    try {
      const response = await adminUser.getList(params)
      if (response.success) {
        const { list, total } = response.data
        commit('SET_USER_LIST', { list, total })
        return { success: true, data: response.data }
      } else {
        return { success: false, message: response.message }
      }
    } catch (error) {
      return { success: false, message: error.message || '获取用户列表失败' }
    } finally {
      commit('SET_USER_LOADING', false)
    }
  },
  
  // 获取用户详情
  async getUserDetail({ commit }, userId) {
    try {
      const response = await adminUser.getDetail(userId)
      if (response.success) {
        return { success: true, data: response.data }
      } else {
        return { success: false, message: response.message }
      }
    } catch (error) {
      return { success: false, message: error.message || '获取用户详情失败' }
    }
  },
  
  // 创建用户
  async createUser({ commit }, data) {
    try {
      const response = await adminUser.create(data)
      if (response.success) {
        return { success: true, message: response.message, data: response.data }
      } else {
        return { success: false, message: response.message }
      }
    } catch (error) {
      return { success: false, message: error.message || '创建用户失败' }
    }
  },
  
  // 更新用户信息
  async updateUser({ commit }, { userId, data }) {
    try {
      const response = await adminUser.update(userId, data)
      if (response.success) {
        return { success: true, message: response.message }
      } else {
        return { success: false, message: response.message }
      }
    } catch (error) {
      return { success: false, message: error.message || '更新用户信息失败' }
    }
  },
  
  // 删除用户
  async deleteUser({ commit }, userId) {
    try {
      const response = await adminUser.delete(userId)
      if (response.success) {
        return { success: true, message: response.message }
      } else {
        return { success: false, message: response.message }
      }
    } catch (error) {
      return { success: false, message: error.message || '删除用户失败' }
    }
  },
  
  // 锁定/解锁用户
  async toggleUserLock({ commit }, { userId, isLock }) {
    try {
      const response = await adminUser.toggleLock(userId, { is_lock: isLock })
      if (response.success) {
        return { success: true, message: response.message }
      } else {
        return { success: false, message: response.message }
      }
    } catch (error) {
      return { success: false, message: error.message || '锁定/解锁用户失败' }
    }
  },
  
  // VIP管理
  async vipManage({ commit }, { userId, data }) {
    try {
      const response = await adminUser.vipManage(userId, data)
      if (response.success) {
        return { success: true, message: response.message }
      } else {
        return { success: false, message: response.message }
      }
    } catch (error) {
      return { success: false, message: error.message || 'VIP管理失败' }
    }
  },
  
  // 批量删除用户
  async batchDeleteUsers({ commit }, userIds) {
    try {
      const response = await adminUser.batchDelete({ user_ids: userIds })
      if (response.success) {
        return { success: true, message: response.message, data: response.data }
      } else {
        return { success: false, message: response.message }
      }
    } catch (error) {
      return { success: false, message: error.message || '批量删除用户失败' }
    }
  },
  
  // 批量锁定/解锁用户
  async batchLockUsers({ commit }, { userIds, isLock }) {
    try {
      const response = await adminUser.batchLock({ user_ids: userIds, is_lock: isLock })
      if (response.success) {
        return { success: true, message: response.message, data: response.data }
      } else {
        return { success: false, message: response.message }
      }
    } catch (error) {
      return { success: false, message: error.message || '批量锁定/解锁用户失败' }
    }
  },
  
  // 批量VIP管理
  async batchVipUsers({ commit }, { userIds, vipLevel, days, action }) {
    try {
      const response = await adminUser.batchVip({ 
        user_ids: userIds, 
        vip_level: vipLevel, 
        days: days, 
        action: action 
      })
      if (response.success) {
        return { success: true, message: response.message, data: response.data }
      } else {
        return { success: false, message: response.message }
      }
    } catch (error) {
      return { success: false, message: error.message || '批量VIP管理失败' }
    }
  },
  
  // 获取论文列表
  async getThesisList({ commit }, params) {
    commit('SET_THESIS_LOADING', true)
    try {
      const response = await adminThesis.getList(params)
      if (response.success) {
        const { list, total } = response.data
        commit('SET_THESIS_LIST', { list, total })
        return { success: true, data: response.data }
      } else {
        return { success: false, message: response.message }
      }
    } catch (error) {
      return { success: false, message: error.message || '获取论文列表失败' }
    } finally {
      commit('SET_THESIS_LOADING', false)
    }
  },
  
  // 获取论文详情
  async getThesisDetail({ commit }, thesisId) {
    try {
      const response = await adminThesis.getDetail(thesisId)
      if (response.success) {
        return { success: true, data: response.data }
      } else {
        return { success: false, message: response.message }
      }
    } catch (error) {
      return { success: false, message: error.message || '获取论文详情失败' }
    }
  },
  
  // 删除论文
  async deleteThesis({ commit }, thesisId) {
    try {
      const response = await adminThesis.delete(thesisId)
      if (response.success) {
        return { success: true, message: response.message }
      } else {
        return { success: false, message: response.message }
      }
    } catch (error) {
      return { success: false, message: error.message || '删除论文失败' }
    }
  },
  
  // 获取数据概览
  async getOverview({ commit }) {
    try {
      const response = await adminStats.getOverview()
      if (response.success) {
        commit('SET_OVERVIEW_DATA', response.data)
        return { success: true, data: response.data }
      } else {
        return { success: false, message: response.message }
      }
    } catch (error) {
      return { success: false, message: error.message || '获取数据概览失败' }
    }
  },
  
  // 获取用户统计
  async getUserStats({ commit }, params) {
    try {
      const response = await adminStats.getUserStats(params)
      if (response.success) {
        commit('SET_USER_STATS', response.data)
        return { success: true, data: response.data }
      } else {
        return { success: false, message: response.message }
      }
    } catch (error) {
      return { success: false, message: error.message || '获取用户统计失败' }
    }
  },
  
  // 获取论文统计
  async getThesisStats({ commit }, params) {
    try {
      const response = await adminStats.getThesisStats(params)
      if (response.success) {
        commit('SET_THESIS_STATS', response.data)
        return { success: true, data: response.data }
      } else {
        return { success: false, message: response.message }
      }
    } catch (error) {
      return { success: false, message: error.message || '获取论文统计失败' }
    }
  },
  
  // 获取聊天统计
  async getChatStats({ commit }, params) {
    try {
      const response = await adminStats.getChatStats(params)
      if (response.success) {
        commit('SET_CHAT_STATS', response.data)
        return { success: true, data: response.data }
      } else {
        return { success: false, message: response.message }
      }
    } catch (error) {
      return { success: false, message: error.message || '获取聊天统计失败' }
    }
  },
  
  // 重置用户密码
  async resetUserPassword({ commit }, { userId, newPassword }) {
    try {
      const response = await adminUser.resetPassword(userId, { new_password: newPassword })
      if (response.success) {
        return { success: true, message: response.message }
      } else {
        return { success: false, message: response.message }
      }
    } catch (error) {
      return { success: false, message: error.message || '重置密码失败' }
    }
  },
  
  // 获取系统设置
  async getSystemSettings({ commit }) {
    commit('SET_SETTINGS_LOADING', true)
    try {
      const response = await adminSettings.getSettings()
      if (response.success) {
        commit('SET_SYSTEM_SETTINGS', response.data)
        return { success: true, data: response.data }
      } else {
        return { success: false, message: response.message }
      }
    } catch (error) {
      // 忽略钱包相关错误
      if (error && error.code === 4001 && error.message && error.message.includes('wallet')) {
        console.warn('钱包错误被忽略:', error.message)
        // 返回一个空的成功结果，避免UI错误
        return { success: true, data: {} }
      }
      return { success: false, message: error.message || '获取系统设置失败' }
    } finally {
      commit('SET_SETTINGS_LOADING', false)
    }
  },
  
  // 保存系统设置
  async saveSystemSettings({ commit }, settings) {
    try {
      const response = await adminSettings.saveSettings(settings)
      if (response.success) {
        // 更新本地设置
        commit('SET_SYSTEM_SETTINGS', settings)
        return { success: true, message: response.message }
      } else {
        return { success: false, message: response.message }
      }
    } catch (error) {
      return { success: false, message: error.message || '保存系统设置失败' }
    }
  },
  
  // 测试API连接
  async testApiConnection({ commit }, data) {
    try {
      const response = await adminSettings.testApiConnection(data)
      if (response.success) {
        return { success: true, message: response.message }
      } else {
        return { success: false, message: response.message }
      }
    } catch (error) {
      return { success: false, message: error.message || 'API连接测试失败' }
    }
  },
  
  // 重置系统设置
  async resetSystemSettings({ commit }) {
    try {
      const response = await adminSettings.resetSettings()
      if (response.success) {
        commit('SET_SYSTEM_SETTINGS', null)
        return { success: true, message: response.message }
      } else {
        return { success: false, message: response.message }
      }
    } catch (error) {
      return { success: false, message: error.message || '重置系统设置失败' }
    }
  },
  
  // 获取管理员账号列表
  async getAccountList({ commit }, params) {
    commit('SET_ACCOUNT_LOADING', true)
    try {
      const response = await adminAccount.getList(params)
      if (response.success) {
        const { list, total } = response.data
        commit('SET_ACCOUNT_LIST', { list, total })
        return { success: true, data: response.data }
      } else {
        return { success: false, message: response.message }
      }
    } catch (error) {
      return { success: false, message: error.message || '获取管理员列表失败' }
    } finally {
      commit('SET_ACCOUNT_LOADING', false)
    }
  },
  
  // 获取管理员详情
  async getAccountDetail({ commit }, adminId) {
    try {
      const response = await adminAccount.getDetail(adminId)
      if (response.success) {
        return { success: true, data: response.data }
      } else {
        return { success: false, message: response.message }
      }
    } catch (error) {
      return { success: false, message: error.message || '获取管理员详情失败' }
    }
  },
  
  // 创建管理员
  async createAccount({ commit }, data) {
    try {
      const response = await adminAccount.create(data)
      if (response.success) {
        return { success: true, message: response.message, data: response.data }
      } else {
        return { success: false, message: response.message }
      }
    } catch (error) {
      return { success: false, message: error.message || '创建管理员失败' }
    }
  },
  
  // 更新管理员
  async updateAccount({ commit }, { adminId, data }) {
    try {
      const response = await adminAccount.update(adminId, data)
      if (response.success) {
        return { success: true, message: response.message }
      } else {
        return { success: false, message: response.message }
      }
    } catch (error) {
      return { success: false, message: error.message || '更新管理员失败' }
    }
  },
  
  // 删除管理员
  async deleteAccount({ commit }, adminId) {
    try {
      const response = await adminAccount.delete(adminId)
      if (response.success) {
        return { success: true, message: response.message }
      } else {
        return { success: false, message: response.message }
      }
    } catch (error) {
      return { success: false, message: error.message || '删除管理员失败' }
    }
  },
  
  // 重置管理员密码
  async resetAccountPassword({ commit }, { adminId, newPassword }) {
    try {
      const response = await adminAccount.resetPassword(adminId, { new_password: newPassword })
      if (response.success) {
        return { success: true, message: response.message }
      } else {
        return { success: false, message: response.message }
      }
    } catch (error) {
      return { success: false, message: error.message || '重置密码失败' }
    }
  }
}

const getters = {
  // 是否已登录
  isAdminLoggedIn: state => !!state.adminToken,
  
  // 管理员Token
  adminToken: state => state.adminToken,
  
  // 管理员信息
  adminInfo: state => state.adminInfo,
  
  // 用户列表
  userList: state => state.userList,
  userTotal: state => state.userTotal,
  userLoading: state => state.userLoading,
  
  // 论文列表
  thesisList: state => state.thesisList,
  thesisTotal: state => state.thesisTotal,
  thesisLoading: state => state.thesisLoading,
  
  // 统计数据
  overviewData: state => state.overviewData,
  userStats: state => state.userStats,
  thesisStats: state => state.thesisStats,
  chatStats: state => state.chatStats,
  
  // 用户统计计算属性
  vipUserCount: state => {
    if (!state.userList || state.userList.length === 0) return 0
    const now = new Date()
    return state.userList.filter(user => 
      user.vip_expire_at && new Date(user.vip_expire_at) > now
    ).length
  },
  
  activeUserCount: state => {
    if (!state.userList || state.userList.length === 0) return 0
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
    return state.userList.filter(user => 
      user.last_login_time && new Date(user.last_login_time) > thirtyDaysAgo
    ).length
  },
  
  lockedUserCount: state => {
    if (!state.userList || state.userList.length === 0) return 0
    return state.userList.filter(user => user.is_lock).length
  },
  
  // 系统设置
  systemSettings: state => state.systemSettings,
  settingsLoading: state => state.settingsLoading,
  
  // 管理员账号管理
  accountList: state => state.accountList,
  accountTotal: state => state.accountTotal,
  accountLoading: state => state.accountLoading
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
} 