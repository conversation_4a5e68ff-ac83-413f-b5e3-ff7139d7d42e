from werkzeug.security import generate_password_hash, check_password_hash

from EarlyBird.common.libs.BaseModel import BaseModel
from sqlalchemy import (
    Integer,
    String,
    Boolean,
    Column,
    JSON,
    Enum,
    SmallInteger,
    DateTime,
    Table,
    BIGINT,
    ForeignKey,
    MetaData,
    TEXT,
)
from sqlalchemy.orm import relationship
import json
from . import TABLE_PREFIX


class TitleHistory(BaseModel):

    __tablename__ = f"{TABLE_PREFIX}title_history"
    __table_args__ = {"comment": "用户选题历史记录"}

    uid = Column(Integer, nullable=False, comment="用户id")
    domain = Column(String(200), comment="研究专业")
    topic = Column(String(500), comment="研究课题")
    level = Column(String(100), comment="学历水平")
    lang = Column(String(100), comment="语言")
    keyword = Column(String(500), comment="关键字")
    titles = Column(JSON, comment="生成的标题列表")
    form_data = Column(JSON, comment="表单数据")

    def __repr__(self):
        return json.dumps(
            {
                "uid": self.uid,
                "domain": self.domain,
                "topic": self.topic,
                "level": self.level,
                "lang": self.lang,
                "keyword": self.keyword,
                "id": self.id,
            },
            ensure_ascii=False
        ) 