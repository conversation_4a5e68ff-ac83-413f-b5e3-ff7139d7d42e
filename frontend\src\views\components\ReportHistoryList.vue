<template>
  <div class="report-history-list">
    <div class="history-header">
      <h3>历史报告</h3>
      <div class="header-actions">
        <!-- 移除ID过滤输入框 -->
        <el-button size="small" type="text" @click="refreshList">
          <i class="el-icon-refresh"></i> 刷新
        </el-button>
      </div>
    </div>
    
    <div class="history-content">
      <div v-if="loading" class="loading-container">
        <i class="el-icon-loading"></i>
        <p>加载中...</p>
      </div>
      
      <div v-else-if="!reportList || reportList.length === 0" class="empty-container">
        <i class="el-icon-document"></i>
        <p>暂无历史报告</p>
      </div>
      
      <div 
        v-else
        v-for="(report, index) in reportList" 
        :key="report.id"
        :class="['report-item', selectedReportId === report.id ? 'active' : '']"
        @click="selectReport(report, index)"
      >
        <div class="report-item-header">
          <div class="report-title-container">
            <span class="report-id">#{{ report.id }}</span>
            <span class="report-title">{{ report.title || '未命名报告' }}</span>
          </div>
          <el-button 
            type="danger" 
            size="mini"
            class="delete-btn" 
            @click.stop="confirmDelete(report)"
            icon="el-icon-delete">
            删除
          </el-button>
        </div>
        <div class="report-info">
          <span class="report-type">{{ getReportTypeName(report.report_type) }}</span>
          <span class="report-date">{{ report.create_time }}</span>
        </div>
      </div>
      
      <!-- 分页控件 -->
      <div class="pagination-container" v-if="total > pageSize">
        <el-pagination
          background
          layout="prev, pager, next"
          :page-size="pageSize"
          :total="total"
          :current-page.sync="currentPage"
          @current-change="handlePageChange">
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import { getReportHistory, deleteReportHistory } from '@/api/generate'

export default {
  name: 'ReportHistoryList',
  props: {
    selectedReportId: {
      type: [Number, String],
      default: -1
    },
    reportType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      reportList: [],
      currentPage: 1,
      pageSize: 20,
      total: 0
    }
  },
  mounted() {
    this.loadReportList()
  },
  watch: {
    reportType() {
      this.currentPage = 1
      this.loadReportList()
    }
  },
  methods: {
    loadReportList() {
      this.loading = true;
      
      const params = {
        page: this.currentPage,
        pageSize: this.pageSize
      };
      
      // 如果指定了报告类型，则按类型过滤
      if (this.reportType) {
        params.reportType = this.reportType;
      }
      
      console.log('加载历史报告，参数:', params);
      
      // 调用API获取历史报告
      getReportHistory(params)
        .then(res => {
          console.log('历史报告结果:', res);
          if (res.is_success) {
            this.reportList = res.data.list || [];
            this.total = res.data.total || 0;
            console.log(`成功加载${this.reportList.length}条历史报告，总计${this.total}条`);
            
            // 调试输出
            if (this.reportList.length > 0) {
              console.log('第一条记录:', JSON.stringify(this.reportList[0]));
            } else {
              console.log('没有历史报告记录');
            }
          } else {
            console.error('加载历史报告失败:', res.message);
            this.$message.error(res.message || '加载历史报告失败');
            this.reportList = [];
            this.total = 0;
          }
          this.loading = false;
        })
        .catch(error => {
          console.error('加载历史报告失败:', error);
          this.$message.error('加载历史报告失败');
          this.reportList = [];
          this.total = 0;
          this.loading = false;
        });
    },
    
    refreshList() {
      this.loadReportList();
    },
    
    selectReport(report, index) {
      this.$emit('select', report, index)
    },
    
    handlePageChange(page) {
      this.currentPage = page
      this.loadReportList()
    },
    
    confirmDelete(report) {
      this.$confirm('确定要删除这份报告吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteReport(report.id)
      }).catch(() => {
        // 取消删除，不做任何操作
      })
    },
    
    deleteReport(id) {
      deleteReportHistory({ id })
        .then(res => {
          if (res.is_success) {
            this.$message.success('删除成功')
            // 如果删除的是当前选中的报告，则清除选中状态
            if (id === this.selectedReportId) {
              this.$emit('select', null, -1)
            }
            // 重新加载列表
            this.loadReportList()
          } else {
            this.$message.error(res.message || '删除失败')
          }
        })
        .catch(error => {
          console.error('删除报告失败:', error)
          this.$message.error('删除报告失败')
        })
    },
    
    getReportTypeName(type) {
      const typeMap = {
        'daily': '日报',
        'weekly': '周报',
        'monthly': '月报',
        'summary': '总结'
      }
      
      return typeMap[type] || '未知类型'
    }
  }
}
</script>

<style lang="scss" scoped>
.report-history-list {
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .history-header {
    padding: 10px 15px;
    border-bottom: 1px solid #ebeef5;
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    h3 {
      margin: 0;
      font-size: 16px;
      color: #303133;
    }
    
    .header-actions {
      display: flex;
      align-items: center;
      
      // 移除ID过滤框样式
    }
  }
  
  .history-content {
    flex: 1;
    overflow-y: auto;
    padding: 10px 0;
    display: flex;
    flex-direction: column;
    
    .loading-container,
    .empty-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100px;
      color: #909399;
      
      i {
        font-size: 24px;
        margin-bottom: 10px;
      }
      
      p {
        margin: 0;
      }
    }
    
    .report-item {
      padding: 12px 15px;
      border-bottom: 1px solid #ebeef5;
      cursor: pointer;
      transition: all 0.3s;
      
      &:hover {
        background-color: #f5f7fa;
        
        .delete-btn {
          opacity: 1;
        }
      }
      
      &.active {
        background-color: #ecf5ff;
        color: #409EFF;
      }
      
      .report-item-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 5px;
        
        .report-title-container {
          display: flex;
          align-items: center;
          
          .report-id {
            margin-right: 5px;
            font-weight: 500;
            color: #909399;
            font-size: 12px;
          }
          
          .report-title {
            font-weight: 500;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 70%;
          }
        }
        
        .delete-btn {
          opacity: 0.8;
          transition: opacity 0.3s;
          padding: 2px 8px;
          
          &:hover {
            opacity: 1;
          }
        }
      }
      
      .report-info {
        display: flex;
        justify-content: space-between;
        font-size: 12px;
        color: #909399;
        
        .report-type {
          background-color: #f0f9eb;
          color: #67c23a;
          padding: 2px 6px;
          border-radius: 2px;
        }
      }
    }
  }
  
  .pagination-container {
    margin-top: 15px;
    display: flex;
    justify-content: center;
    padding: 10px 0;
  }
}
</style> 