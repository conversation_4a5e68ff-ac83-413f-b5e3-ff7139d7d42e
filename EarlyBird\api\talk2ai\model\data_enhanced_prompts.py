#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据增强的提示词模板
用于生成更丰富、更有数据支撑的论文内容
"""

import random

class DataEnhancedPrompts:
    """数据增强提示词类"""
    
    @staticmethod
    def get_data_examples():
        """获取数据示例模板"""
        return {
            "percentage": [
                "增长25%", "下降30%", "占比60%", "提高40%", "降低15%",
                "上升35%", "减少20%", "达到80%", "超过90%", "接近70%"
            ],
            "numbers": [
                "销售额100万", "用户数5000", "成本降低20万", "利润30万",
                "市场份额15%", "投资额500万", "员工数200人", "客户数1000",
                "收入增长50万", "支出减少10万"
            ],
            "time_periods": [
                "2023年", "近5年", "第一季度", "上半年", "去年同期",
                "过去三年", "本年度", "未来五年", "近期", "长期"
            ],
            "comparisons": [
                "相比去年同期", "与行业平均水平相比", "对比其他企业",
                "相对于竞争对手", "与历史数据相比", "相比预期目标"
            ],
            "rankings": [
                "位居第一", "排名第三", "行业领先", "市场份额第一",
                "增长率最高", "效益最好", "规模最大", "影响力最强"
            ]
        }
    
    @staticmethod
    def get_research_data_sources():
        """获取研究数据来源模板"""
        return [
            "根据XX研究报告显示",
            "XX机构发布的数据指出",
            "XX统计局的统计数据显示",
            "XX行业协会的调查表明",
            "XX学术期刊的研究发现",
            "XX咨询公司的分析报告",
            "XX政府部门发布的数据",
            "XX国际组织的统计报告",
            "XX研究机构的调查结果",
            "XX权威媒体的报道显示"
        ]
    
    @staticmethod
    def get_case_study_templates():
        """获取案例研究模板"""
        return [
            "以XX公司为例，该公司在2023年实现了销售额增长30%，主要得益于...",
            "XX项目的成功实施，在6个月内提升了效率25%，具体表现为...",
            "XX地区的实践案例显示，通过XX措施，在一年内实现了...",
            "XX企业的转型经验表明，通过XX策略，在三年内实现了...",
            "XX行业的典型案例分析显示，采用XX方法后，效果显著提升..."
        ]
    
    @staticmethod
    def get_analysis_methods():
        """获取分析方法模板"""
        return [
            "通过定量分析发现",
            "基于统计数据显示",
            "根据实证研究结果",
            "通过对比分析得出",
            "基于问卷调查数据",
            "通过回归分析发现",
            "基于时间序列分析",
            "通过相关性分析得出",
            "基于因子分析结果",
            "通过聚类分析发现"
        ]
    
    @staticmethod
    def enhance_paragraph_prompt(base_prompt, paragraph_type):
        """根据段落类型增强提示词"""
        enhanced_prompt = base_prompt
        
        if "绪论" in paragraph_type or "引言" in paragraph_type:
            enhanced_prompt += "\n\n【绪论段落数据要求】\n"
            enhanced_prompt += "1. 研究背景：提供行业规模、增长率、市场现状等具体数据\n"
            enhanced_prompt += "2. 问题现状：用数据说明当前存在的问题和挑战\n"
            enhanced_prompt += "3. 研究意义：量化研究的重要性和价值\n"
            enhanced_prompt += "4. 文献综述：引用相关研究的具体数据和结论\n"
        
        elif "分析" in paragraph_type or "理论" in paragraph_type:
            enhanced_prompt += "\n\n【分析段落数据要求】\n"
            enhanced_prompt += "1. 理论模型：提供模型参数、假设条件等具体数据\n"
            enhanced_prompt += "2. 分析框架：用数据支撑分析方法的有效性\n"
            enhanced_prompt += "3. 变量定义：提供变量的具体数值范围和分布\n"
            enhanced_prompt += "4. 假设检验：用数据验证理论假设的合理性\n"
        
        elif "实证" in paragraph_type or "研究" in paragraph_type:
            enhanced_prompt += "\n\n【实证研究数据要求】\n"
            enhanced_prompt += "1. 数据来源：详细说明数据收集方法和样本信息\n"
            enhanced_prompt += "2. 描述性统计：提供均值、标准差、分布等统计量\n"
            enhanced_prompt += "3. 回归结果：提供回归系数、显著性水平、R方等\n"
            enhanced_prompt += "4. 假设检验：提供t值、p值、置信区间等\n"
        
        elif "案例" in paragraph_type or "应用" in paragraph_type:
            enhanced_prompt += "\n\n【案例分析数据要求】\n"
            enhanced_prompt += "1. 案例背景：提供案例企业的具体数据和背景信息\n"
            enhanced_prompt += "2. 实施过程：详细描述实施步骤和关键数据\n"
            enhanced_prompt += "3. 效果评估：提供量化的效果指标和对比数据\n"
            enhanced_prompt += "4. 经验总结：基于数据总结成功经验和教训\n"
        
        elif "结论" in paragraph_type or "总结" in paragraph_type:
            enhanced_prompt += "\n\n【结论段落数据要求】\n"
            enhanced_prompt += "1. 主要发现：用数据总结最重要的研究发现\n"
            enhanced_prompt += "2. 理论贡献：量化对理论发展的贡献\n"
            enhanced_prompt += "3. 实践意义：提供具体的应用价值和效果预测\n"
            enhanced_prompt += "4. 政策建议：基于数据提出具体的政策建议\n"
        
        return enhanced_prompt
    
    @staticmethod
    def get_random_data_example():
        """获取随机数据示例"""
        examples = DataEnhancedPrompts.get_data_examples()
        data_type = random.choice(list(examples.keys()))
        return random.choice(examples[data_type])
    
    @staticmethod
    def get_random_data_source():
        """获取随机数据来源"""
        sources = DataEnhancedPrompts.get_research_data_sources()
        return random.choice(sources)
    
    @staticmethod
    def get_random_case_template():
        """获取随机案例模板"""
        templates = DataEnhancedPrompts.get_case_study_templates()
        return random.choice(templates)
    
    @staticmethod
    def get_random_analysis_method():
        """获取随机分析方法"""
        methods = DataEnhancedPrompts.get_analysis_methods()
        return random.choice(methods) 