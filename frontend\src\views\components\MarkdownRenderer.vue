<template>
  <div class="markdown-content" v-html="renderedContent"></div>
</template>

<script>
import 'github-markdown-css'
import { marked } from 'marked'

export default {
  name: 'MarkdownRenderer',
  props: {
    content: {
      type: String,
      default: ''
    }
  },
  computed: {
    renderedContent() {
      if (!this.content) return ''
      try {
        // 预处理内容，移除代码块标记
        let processedContent = this.content;
        
        // 处理 ```markdown 开头的代码块
        processedContent = processedContent.replace(/```markdown\s*\n/g, '');
        
        // 处理结尾的 ``` 标记
        processedContent = processedContent.replace(/\n\s*```\s*$/g, '');
        
        // marked v15+ 使用不同的配置方式
        const renderer = new marked.Renderer();
        
        // 设置 marked 选项
        const options = {
          renderer: renderer,
          gfm: true,
          breaks: true,
          headerIds: true,
          mangle: false
        };
        
        // 使用新的 marked 方法
        const html = marked.parse(processedContent, options);
        // 添加markdown-body类到最外层元素
        return `<div class="markdown-body">${html}</div>`;
      } catch (error) {
        console.error('Markdown渲染错误:', error)
        return `<div class="error">Markdown渲染错误: ${error.message}</div>`
      }
    }
  },
  mounted() {
    console.log('MarkdownRenderer mounted, content:', this.content ? this.content.substring(0, 50) + '...' : 'empty');
  }
}
</script>

<style>
/* 导入github-markdown-css样式 */
@import 'github-markdown-css/github-markdown.css';

.markdown-content {
  padding: 15px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  overflow: auto;
}

/* 确保markdown-body样式正确应用 */
.markdown-body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif;
  font-size: 16px;
  line-height: 1.6;
  word-wrap: break-word;
  color: #24292e;
  background-color: #fff;
}

.markdown-body h1,
.markdown-body h2,
.markdown-body h3,
.markdown-body h4,
.markdown-body h5,
.markdown-body h6 {
  margin-top: 24px;
  margin-bottom: 16px;
  font-weight: 600;
  line-height: 1.25;
  color: #24292e;
}

.markdown-body h1 {
  font-size: 2em;
  border-bottom: 1px solid #eaecef;
  padding-bottom: 0.3em;
}

.markdown-body h2 {
  font-size: 1.5em;
  border-bottom: 1px solid #eaecef;
  padding-bottom: 0.3em;
}

.markdown-body h3 {
  font-size: 1.25em;
}

.markdown-body p {
  margin-top: 0;
  margin-bottom: 16px;
}

.markdown-body ul,
.markdown-body ol {
  padding-left: 2em;
  margin-top: 0;
  margin-bottom: 16px;
}

.markdown-body code {
  padding: 0.2em 0.4em;
  margin: 0;
  font-size: 85%;
  background-color: rgba(27, 31, 35, 0.05);
  border-radius: 3px;
  color: #24292e;
}

.markdown-body pre {
  padding: 16px;
  overflow: auto;
  font-size: 85%;
  line-height: 1.45;
  background-color: #f6f8fa;
  border-radius: 3px;
}

.markdown-body pre code {
  padding: 0;
  margin: 0;
  background-color: transparent;
}

.markdown-body blockquote {
  padding: 0 1em;
  color: #6a737d;
  border-left: 0.25em solid #dfe2e5;
  margin: 0 0 16px 0;
}

.markdown-body table {
  display: block;
  width: 100%;
  overflow: auto;
  border-spacing: 0;
  border-collapse: collapse;
  margin-bottom: 16px;
}

.markdown-body table th,
.markdown-body table td {
  padding: 6px 13px;
  border: 1px solid #dfe2e5;
}

.markdown-body table th {
  font-weight: 600;
}

.markdown-body table tr {
  background-color: #fff;
  border-top: 1px solid #c6cbd1;
}

.markdown-body table tr:nth-child(2n) {
  background-color: #f6f8fa;
}
</style> 