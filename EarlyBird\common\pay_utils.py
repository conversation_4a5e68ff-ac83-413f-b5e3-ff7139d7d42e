import time
import base64
import hashlib
import urllib.parse
import requests
import json
import uuid
from datetime import datetime
from Crypto.PublicKey import RSA
from Crypto.Signature import PKCS1_v1_5
from Crypto.Hash import SHA256
from flask import current_app
import logging

logger = logging.getLogger(__name__)

class PaymentUtil:
    """支付工具类，处理支付相关的签名、验签等功能"""
    
    @staticmethod
    def get_payment_config():
        """获取支付配置"""
        from EarlyBird.model.payment import PaymentConfig
        
        # 获取所有支付配置
        configs = PaymentConfig.query.all()
        config_dict = {}
        for config in configs:
            config_dict[config.config_key] = config.config_value
        
        # 确保必要的配置项存在
        required_keys = ['pid', 'merchant_private_key', 'platform_public_key', 'notify_url', 'return_url']
        for key in required_keys:
            if key not in config_dict:
                logger.warning(f"支付配置缺少必要项: {key}")
                config_dict[key] = ''
        
        return config_dict
    
    @staticmethod
    def sign(params, private_key_str):
        """使用商户私钥对参数进行签名
        
        Args:
            params: 要签名的参数字典
            private_key_str: 商户私钥字符串
            
        Returns:
            签名字符串
        """
        # 1. 过滤空值和sign、sign_type参数
        filtered_params = {k: v for k, v in params.items() if v and k != 'sign' and k != 'sign_type'}
        
        # 2. 按照参数名ASCII码从小到大排序
        sorted_params = sorted(filtered_params.items(), key=lambda x: x[0])
        
        # 3. 组合成"参数=参数值"格式，用&连接
        sign_str = '&'.join([f"{k}={v}" for k, v in sorted_params])
        
        logger.debug(f"待签名字符串: {sign_str}")
        
        try:
            # 4. RSA签名
            private_key = RSA.importKey(private_key_str)
            signer = PKCS1_v1_5.new(private_key)
            hash_obj = SHA256.new(sign_str.encode('utf-8'))
            signature = base64.b64encode(signer.sign(hash_obj))
            
            return signature.decode('utf-8')
        except Exception as e:
            logger.error(f"签名失败: {str(e)}")
            raise Exception(f"签名失败: {str(e)}")
    
    @staticmethod
    def verify_sign(params, sign, public_key_str):
        """使用平台公钥验证签名
        
        Args:
            params: 参数字典
            sign: 签名字符串
            public_key_str: 平台公钥字符串
            
        Returns:
            验证结果，True表示验证通过，False表示验证失败
        """
        # 1. 过滤空值和sign、sign_type参数
        filtered_params = {k: v for k, v in params.items() if v and k != 'sign' and k != 'sign_type'}
        
        # 2. 按照参数名ASCII码从小到大排序
        sorted_params = sorted(filtered_params.items(), key=lambda x: x[0])
        
        # 3. 组合成"参数=参数值"格式，用&连接
        sign_str = '&'.join([f"{k}={v}" for k, v in sorted_params])
        
        logger.debug(f"验签字符串: {sign_str}")
        
        try:
            # 4. RSA验签
            public_key = RSA.importKey(public_key_str)
            verifier = PKCS1_v1_5.new(public_key)
            hash_obj = SHA256.new(sign_str.encode('utf-8'))
            
            return verifier.verify(hash_obj, base64.b64decode(sign))
        except Exception as e:
            logger.error(f"验签失败: {str(e)}")
            return False
    
    @staticmethod
    def create_order_params(out_trade_no, name, money, pay_type='alipay'):
        """创建支付订单参数
        
        Args:
            out_trade_no: 商户订单号
            name: 商品名称
            money: 金额
            pay_type: 支付方式，默认为支付宝
            
        Returns:
            支付参数字典
        """
        config = PaymentUtil.get_payment_config()
        
        params = {
            'pid': config['pid'],
            'type': pay_type,
            'out_trade_no': out_trade_no,
            'notify_url': config['notify_url'],
            'return_url': config['return_url'],
            'name': name,
            'money': money,
            'timestamp': str(int(time.time())),
            'sign_type': 'RSA'
        }
        
        # 生成签名
        params['sign'] = PaymentUtil.sign(params, config['merchant_private_key'])
        
        return params
    
    @staticmethod
    def generate_order_no():
        """生成订单号
        
        Returns:
            订单号字符串，格式为：ZN + 年月日时分秒 + 6位随机字符
        """
        return f"ZN{time.strftime('%Y%m%d%H%M%S')}{uuid.uuid4().hex[:6]}"
    
    @staticmethod
    def query_order(out_trade_no):
        """查询订单状态
        
        Args:
            out_trade_no: 商户订单号
            
        Returns:
            查询结果字典
        """
        config = PaymentUtil.get_payment_config()
        
        # 构造查询参数
        query_params = {
            'pid': config['pid'],
            'out_trade_no': out_trade_no,
            'timestamp': str(int(time.time())),
            'sign_type': 'RSA'
        }
        
        # 生成签名
        query_params['sign'] = PaymentUtil.sign(query_params, config['merchant_private_key'])
        
        # 发送查询请求
        try:
            response = requests.post(
                'https://v.990r.com/api/pay/query',
                data=query_params,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                
                # 验证签名
                if 'sign' in result:
                    sign = result.pop('sign')
                    if PaymentUtil.verify_sign(result, sign, config['platform_public_key']):
                        return result
                    else:
                        logger.warning("订单查询返回数据验签失败")
                        return {'success': False, 'message': '验签失败'}
                else:
                    return result
            else:
                logger.error(f"订单查询请求失败: HTTP {response.status_code}")
                return {'success': False, 'message': f'HTTP错误: {response.status_code}'}
        except Exception as e:
            logger.error(f"订单查询异常: {str(e)}")
            return {'success': False, 'message': f'查询异常: {str(e)}'} 