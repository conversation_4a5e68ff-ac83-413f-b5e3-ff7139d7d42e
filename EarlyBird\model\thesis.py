from werkzeug.security import generate_password_hash, check_password_hash

from EarlyBird.common.libs.BaseModel import BaseModel
from sqlalchemy import (
    Integer,
    String,
    Boolean,
    Column,
    JSON,
    Enum,
    SmallInteger,
    DateTime,
    Table,
    BIGINT,
    ForeignKey,
    MetaData,
    TEXT,
)
from sqlalchemy.orm import relationship
import json
from . import TABLE_PREFIX


class Thesis(BaseModel):

    __tablename__ = f"{TABLE_PREFIX}thesis"
    __table_args__ = {"comment": "论文内容表"}

    uid = Column(Integer, nullable=False, comment="用户id")
    lang = Column(String(100), comment="语言")
    level = Column(String(100), comment="水平")
    length = Column(String(100), comment="长度")
    title = Column(String(300), comment="标题")
    author = Column(String(300), comment="作者")
    outline = Column(JSON, comment="提纲和内容")
    digest = Column(TEXT(65536), comment="摘要")
    digestEn = Column(TEXT(65536), comment="摘要英文")
    keywords = Column(TEXT(65536), comment="关键字，逗号分隔")
    keywordsEn = Column(TEXT(65536), comment="关键字en,逗号分隔")

    references = Column(TEXT(65536), comment="参考文献")
    thanks = Column(TEXT(65536), comment="致谢")

    def __repr__(self):
        return json.dumps(
            {
                "uid": self.uid,
                "lang": self.lang,
                "level": self.level,
                "length": self.length,
                "title": self.title,
                "author": self.author,
                "digest": self.digest,
                "digestEn": self.digestEn,
                "keywords": self.keywords,
                "keywordsEn": self.keywordsEn,
            }
        )
