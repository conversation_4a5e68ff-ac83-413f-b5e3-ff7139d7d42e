import os
import configparser
from datetime import timedelta
import sys
import logging


def get_config():
    conf = configparser.ConfigParser()
    flask_env = os.environ.get("FLASK_ENV")
    if flask_env is None:
        raise Exception("need define evnValue : FLASK_ENV")

    base_path = os.path.dirname(os.path.abspath(__file__)) + "/"
    config_file = f"{base_path}{flask_env}.ini"

    if not os.path.exists(config_file):
        raise Exception(f"config file not exist {config_file}")

    conf.read(config_file, encoding='utf-8')
    logging.getLogger(__name__).info(f"load configFile at {config_file}")
    return conf


def get_api_keys():
    conf = configparser.ConfigParser()
    base_path = os.path.dirname(os.path.abspath(__file__)) + "/"
    api_keys_file = f"{base_path}api_keys.ini"

    if not os.path.exists(api_keys_file):
        raise Exception(f"API keys config file not exist: {api_keys_file}")

    conf.read(api_keys_file, encoding='utf-8')
    return {
        'kimi_api_key': conf.get('api_keys', 'kimi_api_key'),
        'qianwen_api_key': conf.get('api_keys', 'qianwen_api_key'),
        'deepseek_api_key': conf.get('api_keys', 'deepseek_api_key'),
        'default_language': conf.get('settings', 'default_language'),
        'kimi_api_url': conf.get('api_endpoints', 'kimi_api_url'),
        'qianwen_api_url': conf.get('api_endpoints', 'qianwen_api_url'),
        'deepseek_api_url': conf.get('api_endpoints', 'deepseek_api_url'),
        'doubao_api_url': conf.get('api_endpoints', 'doubao_api_url')
    }


class BaseConfig:

    SECRET_KEY = "ShaHeTop-Almighty-ares"  # session加密
    PERMANENT_SESSION_LIFETIME = timedelta(days=30)  # 设置session过期时间
    DEBUG = True
    RUN_HOST = "0.0.0.0"
    RUN_PORT = 9999

    @staticmethod
    def init_app(app):
        pass


class AppConfig(BaseConfig):
    """区分配置文件"""

    conf = get_config()  # 根据环境变量获取对应的配置文件
    api_keys = get_api_keys()  # 获取 API Keys 配置

    FLASK_PYDANTIC_VALIDATION_ERROR_RAISE = True
    # base
    SECRET_KEY = conf.get("base", "SECRET_KEY")  # session加密
    PERMANENT_SESSION_LIFETIME = timedelta(days=30)  # 设置session过期时间
    DEBUG = conf.getboolean("base", "DEBUG")
    RUN_HOST = conf.get("base", "RUN_HOST")
    RUN_PORT = conf.getint("base", "RUN_PORT")

    UPLOAD_FOLDER = conf.get("base", "UPLOAD_FOLDER")
    ALLOWED_EXTENSIONS = set(["txt", "pdf", "png", "jpg", "jpeg", "gif"])

    # 卡密激活功能开关 - 优先使用环境变量，其次使用配置文件
    ENABLE_ACTIVATION_KEY = os.environ.get('ENABLE_ACTIVATION_KEY', 
                                          conf.get("base", "ENABLE_ACTIVATION_KEY", fallback="false")).lower() == 'true'

    # API Keys
    KIMI_API_KEY = api_keys['kimi_api_key']
    QIANWEN_API_KEY = api_keys['qianwen_api_key']
    DEEPSEEK_API_KEY = api_keys['deepseek_api_key']
    DEFAULT_LANGUAGE = api_keys['default_language']

    # API URLs
    KIMI_API_URL = api_keys['kimi_api_url']
    QIANWEN_API_URL = api_keys['qianwen_api_url']
    DEEPSEEK_API_URL = api_keys['deepseek_api_url']
    DOUBAO_API_URL = api_keys['doubao_api_url']

    # MySQL
    DB_URI = "mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4"
    logging.info(f"Database URI: {DB_URI}")
    SQLALCHEMY_DATABASE_URI = DB_URI
    SQLALCHEMY_TRACK_MODIFICATIONS = True
    SQLALCHEMY_ECHO = False

    WIN_TITLE = "OPEN-ESSSAY"
    WIN_HOME_PAGE = f"http://{RUN_HOST}:{RUN_PORT}/index.html"
