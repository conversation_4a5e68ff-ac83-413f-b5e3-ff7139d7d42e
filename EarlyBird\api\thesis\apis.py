from EarlyBird.api.thesis import thesis_blueprint
import logging
import os
from flask import jsonify, g, request, send_from_directory
from flask_pydantic import validate
from EarlyBird.common.libs.api_result import ApiResponse
from EarlyBird.common import ApiResponse, Result
from EarlyBird.common.docx import THESIS_EXPORT_BASE
from EarlyBird.api.thesis.schema import (
    ParamThesisId,
    ParamSaveSingleParagraph,
    ParamSaveDigest,
    SaveNewPara,
    MovePara,
    DeleteParagraph,
    SaveThesisProperty,
    ParamPayDownload,
    ParamPaymentStatus,
    ParamConfirmPayment
)
from EarlyBird.api.thesis.service import ThesisServie


LOGGER = logging.getLogger(__name__)


@thesis_blueprint.route("/saveNewParagraph", methods=["POST"])
@validate()
def saveNewParagraph(body: SaveNewPara):
    try:
        body.userId = g.userid
        res: Result = ThesisServie().saveNewPara(body)
        if res.isSucc():
            return ApiResponse().json()
        return ApiResponse(data=[]).error(res.message).json()
    except Exception as e:
        LOGGER.exception(e)
        return ApiResponse().error(str(e)).json()


@thesis_blueprint.route("/paragraphMove", methods=["POST"])
@validate()
def paragraphMove(body: MovePara):
    try:
        body.userId = g.userid
        res: Result = ThesisServie().movelPara(body)
        if res.isSucc():
            return ApiResponse(data=res.data).json()
        return ApiResponse(data=[]).error(res.message).json()
    except Exception as e:
        LOGGER.exception(e)
        return ApiResponse().error(str(e)).json()
    

@thesis_blueprint.route("/paragraphDelete", methods=["POST"])
@validate()
def paragraphDelete(body: DeleteParagraph):
    try:
        body.userId = g.userid
        res: Result = ThesisServie().deletePara(body)
        if res.isSucc():
            return ApiResponse(data=res.data).json()
        return ApiResponse(data=[]).error(res.message).json()
    except Exception as e:
        LOGGER.exception(e)
        return ApiResponse().error(str(e)).json()


@thesis_blueprint.route("/getProgress", methods=["POST"])
@validate()
def getProgress(body: ParamThesisId):
    try:
        body.userId = g.userid
        res: Result = ThesisServie().getProgress(body)
        if res.isSucc():
            return ApiResponse(data=res.data).json()
        return ApiResponse(data=[]).error(res.message).json()
    except Exception as e:
        LOGGER.exception(e)
        return ApiResponse().error(str(e)).json()


@thesis_blueprint.route("/getList", methods=["POST"])
@validate()
def getList():
    try:
        # 检查用户是否已登录
        if not hasattr(g, 'userid') or not g.userid:
            LOGGER.warning("用户未登录或session失效，无法获取论文列表")
            return ApiResponse().needLogin().json()
            
        LOGGER.info(f"用户 {g.userid} 请求获取论文列表")
        res: Result = ThesisServie().getListByUid(g.userid)
        if res.isSucc():
            LOGGER.info(f"用户 {g.userid} 获取论文列表成功，共 {len(res.data)} 篇")
            return ApiResponse(data=res.data).json()
        return ApiResponse().error(res.message).json()

    except Exception as e:
        LOGGER.exception(e)
        return ApiResponse().error(str(e)).json()


@thesis_blueprint.route("/getDetail", methods=["POST"])
@validate()
def detail(body: ParamThesisId):

    body.userId = g.userid
    res: Result = ThesisServie().getDetailById(body)
    if not res.is_success():
        return ApiResponse().error(res.message).json()

    thesis = res.data

    return ApiResponse().set_data(thesis.to_json()).json()


@thesis_blueprint.route("/getOutline", methods=["POST"])
@validate()
def getOutline(body: ParamThesisId):

    body.userId = g.userid
    res: Result = ThesisServie().getOutlineProgressById(body)
    if not res.is_success():
        return ApiResponse().error(res.message).json()

    return ApiResponse().set_data(res.data).json()


@thesis_blueprint.route("/deleteThesis", methods=["POST"])
@validate()
def deleteThesis(body: ParamThesisId):
    body.userId = g.userid
    res: Result = ThesisServie().deleteThesis(body)
    if not res.is_success():
        return ApiResponse().error(res.message).json()

    return ApiResponse().json()


@thesis_blueprint.route("/downloadThesis", methods=["POST"])
@validate()
def downloadThesis(body: ParamThesisId):
    body.userId = g.userid
    LOGGER.info(f"用户 {body.userId} 请求下载论文 {body.thesisId}")
    res: Result = ThesisServie().exportThesis(body)
    
    # 检查是否需要支付
    if not res.is_success() and res.data and isinstance(res.data, dict) and res.data.get("need_payment"):
        # 需要支付，返回成功状态但包含支付信息，让前端根据need_payment字段判断
        LOGGER.info(f"用户 {body.userId} 需要支付才能下载论文 {body.thesisId}，价格: {res.data.get('price', 10.0)}")
        # ApiResponse默认就是成功状态（CODE_OK=0），直接设置数据即可
        return ApiResponse().set_data(res.data).json()
    elif not res.is_success():
        # 其他错误
        LOGGER.error(f"用户 {body.userId} 下载论文 {body.thesisId} 失败: {res.message}")
        return ApiResponse().error(res.message).json()

    # 下载成功
    LOGGER.info(f"用户 {body.userId} 下载论文 {body.thesisId} 成功")
    # ApiResponse默认就是成功状态，直接设置数据即可
    return ApiResponse().set_data(res.data).json()


@thesis_blueprint.route("/payDownload", methods=["POST"])
@validate()
def payDownload(body: ParamPayDownload):
    body.userId = g.userid

    # API级别的请求锁定机制
    import time
    lock_key = f"payDownload_{body.userId}_{body.thesisId}"

    # 简单的内存锁（生产环境建议使用Redis分布式锁）
    if not hasattr(payDownload, '_locks'):
        payDownload._locks = {}
        payDownload._lock_times = {}

    current_time = time.time()

    # 清理过期的锁（超过30秒的锁自动清除）
    expired_keys = [k for k, t in payDownload._lock_times.items() if current_time - t > 30]
    for key in expired_keys:
        payDownload._locks.pop(key, None)
        payDownload._lock_times.pop(key, None)

    # 检查是否有正在处理的请求
    if lock_key in payDownload._locks:
        last_time = payDownload._lock_times[lock_key]
        if current_time - last_time < 3:  # 3秒内不允许重复请求
            LOGGER.warning(f"API级别检测到重复支付请求 - 用户: {body.userId}, 论文: {body.thesisId}, 距离上次: {current_time - last_time:.2f}秒")
            return ApiResponse().error("请求过于频繁，请稍后再试").json()

    # 设置锁
    payDownload._locks[lock_key] = True
    payDownload._lock_times[lock_key] = current_time

    try:
        LOGGER.info(f"开始处理支付请求 - 用户: {body.userId}, 论文: {body.thesisId}")
        res: Result = ThesisServie().payDownload(body)
        if not res.is_success():
            return ApiResponse().error(res.message).json()

        return ApiResponse().set_data(res.data).json()
    finally:
        # 请求完成后，延迟清除锁（防止立即重复请求）
        def clear_lock():
            time.sleep(2)  # 延迟2秒清除锁
            payDownload._locks.pop(lock_key, None)
            payDownload._lock_times.pop(lock_key, None)
            LOGGER.info(f"清除支付请求锁 - 用户: {body.userId}, 论文: {body.thesisId}")

        import threading
        threading.Thread(target=clear_lock, daemon=True).start()


@thesis_blueprint.route("/confirmPayment", methods=["POST"])
@validate()
def confirmPayment(body: ParamConfirmPayment):
    """确认支付并下载论文"""
    body.userId = g.userid
    LOGGER.info(f"用户 {body.userId} 确认支付订单 {body.orderId} 并下载论文 {body.thesisId}")
    
    res: Result = ThesisServie().confirmPaymentAndDownload(body)
    if not res.is_success():
        LOGGER.warning(f"用户 {body.userId} 确认支付失败: {res.message}")
        return ApiResponse().error(res.message).json()

    LOGGER.info(f"用户 {body.userId} 确认支付成功，开始下载论文 {body.thesisId}")
    return ApiResponse().set_data(res.data).json()


@thesis_blueprint.route("/paymentStatus", methods=["POST"])
@validate()
def paymentStatus(body: ParamPaymentStatus):
    body.userId = g.userid
    res: Result = ThesisServie().getPaymentStatus(body)
    if not res.is_success():
        return ApiResponse().error(res.message).json()

    return ApiResponse().set_data(res.data).json()


@thesis_blueprint.route("/download", methods=["GET"])
def download():
    fileName = request.values.get("fileName")
    if not fileName:
        return ApiResponse().error("filename is required").json()

    LOGGER.info(f"下载请求 - 原始fileName: {fileName}")

    # 处理文件名，移除可能的路径前缀
    if fileName.startswith('/resources/download/'):
        fileName = fileName[len('/resources/download/'):]
    elif fileName.startswith('resources/download/'):
        fileName = fileName[len('resources/download/'):]
    elif fileName.startswith('/'):
        fileName = fileName[1:]

    LOGGER.info(f"下载请求 - 处理后fileName: {fileName}")

    # Get base directory and construct download path
    base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    download_dir = os.path.join(base_dir, "resources", "download")
    file_path = os.path.join(download_dir, fileName)

    LOGGER.info(f"下载请求 - 完整文件路径: {file_path}")
    LOGGER.info(f"下载请求 - 文件是否存在: {os.path.exists(file_path)}")

    if not os.path.exists(file_path):
        LOGGER.error(f"下载失败 - 文件不存在: {file_path}")
        return ApiResponse().error("file not found").json()

    LOGGER.info(f"下载成功 - 文件: {fileName}")
    return send_from_directory(download_dir, fileName, as_attachment=True)


@thesis_blueprint.route("/simulatePayment", methods=["POST"])
def simulatePayment():
    """模拟支付成功（仅用于测试）"""
    try:
        data = request.get_json()
        order_id = data.get('order_id')

        if not order_id:
            return ApiResponse().error("缺少订单号").json()

        res: Result = ThesisServie().simulatePaymentSuccess(order_id)
        if not res.is_success():
            return ApiResponse().error(res.message).json()

        return ApiResponse().set_data(res.data).json()

    except Exception as e:
        LOGGER.exception(e)
        return ApiResponse().error(f"模拟支付失败: {str(e)}").json()


@thesis_blueprint.route("/saveSingleParagraph", methods=["POST"])
@validate()
def saveSingleParagraph(body: ParamSaveSingleParagraph):
    body.userId = g.userid
    res: Result = ThesisServie().saveSingleParagraph(body)
    if not res.is_success():
        return ApiResponse().error(res.message).json()

    return ApiResponse().json()


@thesis_blueprint.route("/saveDigest", methods=["POST"])
@validate()
def saveDigest(body: ParamSaveDigest):
    body.userId = g.userid
    res: Result = ThesisServie().saveDigest(body)
    if not res.is_success():
        return ApiResponse().error(res.message).json()

    return ApiResponse().json()


@thesis_blueprint.route("/saveReference", methods=["POST"])
@validate()
def saveReference(body: SaveThesisProperty):
    body.propName = 'references'
    body.userId = g.userid
    res: Result = ThesisServie().saveThesisProperty(body)
    if not res.is_success():
        return ApiResponse().error(res.message).json()

    return ApiResponse().json()


@thesis_blueprint.route("/saveThanks", methods=["POST"])
@validate()
def saveThanks(body: SaveThesisProperty):
    body.propName = 'thanks'
    body.userId = g.userid
    res: Result = ThesisServie().saveThesisProperty(body)
    if not res.is_success():
        return ApiResponse().error(res.message).json()

    return ApiResponse().json()


@thesis_blueprint.route("/getUserDownloadRights", methods=["POST"])
@validate()
def getUserDownloadRights(body: ParamThesisId):
    """获取用户下载权限信息"""
    body.userId = g.userid
    res: Result = ThesisServie().getUserDownloadRights(body)
    if not res.is_success():
        return ApiResponse().error(res.message).json()

    return ApiResponse().set_data(res.data).json()
