<!--
作者名：EarlyBird
作者邮箱：<EMAIL>
官方网址：https://blog.zaoniao.vip

微信支付配置管理页面
-->
<template>
  <div class="wechat-pay-config">
    <div class="page-header">
      <h2>微信支付配置管理</h2>
      <el-button type="primary" @click="showCreateDialog" icon="el-icon-plus">
        新增配置
      </el-button>
    </div>

    <!-- 配置列表 -->
    <el-card class="config-list">
      <div slot="header">
        <span>配置列表</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="refreshList">
          刷新
        </el-button>
      </div>

      <el-table :data="configList" v-loading="loading" style="width: 100%">
        <el-table-column prop="id" label="ID" width="80"></el-table-column>
        <el-table-column prop="appid" label="应用ID" width="180"></el-table-column>
        <el-table-column prop="mchid" label="商户号" width="120"></el-table-column>
        <el-table-column prop="notify_url" label="回调地址" min-width="200"></el-table-column>
        <el-table-column label="环境" width="100">
          <template slot-scope="scope">
            <el-tag :type="scope.row.is_sandbox ? 'warning' : 'success'">
              {{ scope.row.is_sandbox ? '沙箱' : '正式' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template slot-scope="scope">
            <el-tag :type="scope.row.is_enabled ? 'success' : 'info'">
              {{ scope.row.is_enabled ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="测试模式" width="100">
          <template slot-scope="scope">
            <el-tag :type="scope.row.is_test_mode ? 'warning' : 'info'">
              {{ scope.row.is_test_mode ? '是' : '否' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="updated_at" label="更新时间" width="160"></el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template slot-scope="scope">
            <el-button size="mini" @click="showEditDialog(scope.row)">编辑</el-button>
            <el-button 
              size="mini" 
              type="success" 
              v-if="!scope.row.is_enabled"
              @click="enableConfig(scope.row.id)"
            >
              启用
            </el-button>
            <el-button 
              size="mini" 
              type="warning" 
              v-if="scope.row.is_enabled"
              @click="disableConfig(scope.row.id)"
            >
              禁用
            </el-button>
            <el-button 
              size="mini" 
              type="danger" 
              v-if="!scope.row.is_enabled"
              @click="deleteConfig(scope.row.id)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.page"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total">
        </el-pagination>
      </div>
    </el-card>

    <!-- 新增/编辑配置对话框 -->
    <el-dialog 
      :title="dialogTitle" 
      :visible.sync="dialogVisible" 
      width="800px"
      @close="resetForm"
    >
      <el-form :model="form" :rules="rules" ref="form" label-width="140px">
        <!-- 基础配置 -->
        <el-divider content-position="left">基础配置</el-divider>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="应用ID" prop="appid">
              <el-input v-model="form.appid" placeholder="请输入微信支付应用ID"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="商户号" prop="mchid">
              <el-input v-model="form.mchid" placeholder="请输入微信支付商户号"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="APIv3密钥" prop="api_v3_key">
              <el-input v-model="form.api_v3_key" placeholder="请输入商户APIv3密钥" show-password></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="证书序列号" prop="serial_no">
              <el-input v-model="form.serial_no" placeholder="请输入商户证书序列号"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="私钥文件路径" prop="private_key_path">
              <el-input v-model="form.private_key_path" placeholder="请输入商户私钥文件路径"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="平台证书路径">
              <el-input v-model="form.platform_cert_path" placeholder="请输入微信支付平台证书路径（可选）"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="回调通知地址" prop="notify_url">
          <el-input v-model="form.notify_url" placeholder="请输入回调通知地址"></el-input>
        </el-form-item>

        <!-- 环境配置 -->
        <el-divider content-position="left">环境配置</el-divider>
        
        <el-form-item label="启用沙箱环境">
          <el-switch v-model="form.is_sandbox" @change="handleSandboxChange"></el-switch>
        </el-form-item>

        <!-- 沙箱环境配置 -->
        <div v-if="form.is_sandbox">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="沙箱应用ID" prop="sandbox_appid">
                <el-input v-model="form.sandbox_appid" placeholder="请输入沙箱环境应用ID"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="沙箱商户号" prop="sandbox_mchid">
                <el-input v-model="form.sandbox_mchid" placeholder="请输入沙箱环境商户号"></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="沙箱APIv3密钥" prop="sandbox_api_v3_key">
                <el-input v-model="form.sandbox_api_v3_key" placeholder="请输入沙箱环境APIv3密钥" show-password></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="沙箱证书序列号" prop="sandbox_serial_no">
                <el-input v-model="form.sandbox_serial_no" placeholder="请输入沙箱环境证书序列号"></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="沙箱私钥文件路径" prop="sandbox_private_key_path">
            <el-input v-model="form.sandbox_private_key_path" placeholder="请输入沙箱环境私钥文件路径"></el-input>
          </el-form-item>
        </div>

        <!-- 状态配置 -->
        <el-divider content-position="left">状态配置</el-divider>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="启用配置">
              <el-switch v-model="form.is_enabled"></el-switch>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="测试模式">
              <el-switch v-model="form.is_test_mode"></el-switch>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="备注">
          <el-input 
            v-model="form.remark" 
            type="textarea" 
            :rows="3"
            placeholder="请输入备注信息"
          ></el-input>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="testConfig" :loading="testing">测试配置</el-button>
        <el-button type="success" @click="submitForm" :loading="submitting">保存</el-button>
      </div>
    </el-dialog>

    <!-- 测试结果对话框 -->
    <el-dialog title="配置测试结果" :visible.sync="testResultVisible" width="500px">
      <div v-if="testResult.success">
        <el-alert
          title="测试成功"
          type="success"
          :closable="false"
          show-icon>
          <div slot="description">
            <p>配置测试成功！</p>
            <p v-if="testResult.data && testResult.data.code_url">
              <strong>测试二维码：</strong>
              <img :src="testResult.data.code_url" style="max-width: 200px; margin-top: 10px;" />
            </p>
          </div>
        </el-alert>
      </div>
      <div v-else>
        <el-alert
          title="测试失败"
          type="error"
          :closable="false"
          show-icon>
          <div slot="description">
            <p>{{ testResult.message }}</p>
          </div>
        </el-alert>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getWeChatPayConfigList, createWeChatPayConfig, updateWeChatPayConfig, deleteWeChatPayConfig, enableWeChatPayConfig, disableWeChatPayConfig, testWeChatPayConfig } from '@/api/admin'

export default {
  name: 'WeChatPayConfig',
  data() {
    return {
      loading: false,
      submitting: false,
      testing: false,
      dialogVisible: false,
      testResultVisible: false,
      isEdit: false,
      configList: [],
      pagination: {
        page: 1,
        size: 10,
        total: 0
      },
      form: {
        appid: '',
        mchid: '',
        api_v3_key: '',
        serial_no: '',
        private_key_path: '',
        platform_cert_path: '',
        notify_url: '',
        is_sandbox: false,
        sandbox_appid: '',
        sandbox_mchid: '',
        sandbox_api_v3_key: '',
        sandbox_serial_no: '',
        sandbox_private_key_path: '',
        is_enabled: true,
        is_test_mode: false,
        remark: ''
      },
      rules: {
        appid: [
          { required: true, message: '请输入应用ID', trigger: 'blur' }
        ],
        mchid: [
          { required: true, message: '请输入商户号', trigger: 'blur' }
        ],
        api_v3_key: [
          { required: true, message: '请输入APIv3密钥', trigger: 'blur' }
        ],
        serial_no: [
          { required: true, message: '请输入证书序列号', trigger: 'blur' }
        ],
        private_key_path: [
          { required: true, message: '请输入私钥文件路径', trigger: 'blur' }
        ],
        notify_url: [
          { required: true, message: '请输入回调通知地址', trigger: 'blur' }
        ],
        sandbox_appid: [
          { required: true, message: '请输入沙箱应用ID', trigger: 'blur' }
        ],
        sandbox_mchid: [
          { required: true, message: '请输入沙箱商户号', trigger: 'blur' }
        ],
        sandbox_api_v3_key: [
          { required: true, message: '请输入沙箱APIv3密钥', trigger: 'blur' }
        ],
        sandbox_serial_no: [
          { required: true, message: '请输入沙箱证书序列号', trigger: 'blur' }
        ],
        sandbox_private_key_path: [
          { required: true, message: '请输入沙箱私钥文件路径', trigger: 'blur' }
        ]
      },
      testResult: {
        success: false,
        message: '',
        data: null
      }
    }
  },
  computed: {
    dialogTitle() {
      return this.isEdit ? '编辑微信支付配置' : '新增微信支付配置'
    }
  },
  created() {
    this.loadConfigList()
  },
  methods: {
    // 加载配置列表
    async loadConfigList() {
      this.loading = true
      try {
        const response = await getWeChatPayConfigList({
          page: this.pagination.page,
          size: this.pagination.size
        })
        
        if (response.code === 200) {
          this.configList = response.data.list
          this.pagination.total = response.data.total
        } else {
          this.$message.error(response.message || '加载配置列表失败')
        }
      } catch (error) {
        console.error('加载配置列表失败:', error)
        this.$message.error('加载配置列表失败')
      } finally {
        this.loading = false
      }
    },

    // 刷新列表
    refreshList() {
      this.loadConfigList()
    },

    // 显示新增对话框
    showCreateDialog() {
      this.isEdit = false
      this.dialogVisible = true
      this.resetForm()
    },

    // 显示编辑对话框
    showEditDialog(config) {
      this.isEdit = true
      this.dialogVisible = true
      this.form = { ...config }
    },

    // 重置表单
    resetForm() {
      this.form = {
        appid: '',
        mchid: '',
        api_v3_key: '',
        serial_no: '',
        private_key_path: '',
        platform_cert_path: '',
        notify_url: '',
        is_sandbox: false,
        sandbox_appid: '',
        sandbox_mchid: '',
        sandbox_api_v3_key: '',
        sandbox_serial_no: '',
        sandbox_private_key_path: '',
        is_enabled: true,
        is_test_mode: false,
        remark: ''
      }
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.clearValidate()
      })
    },

    // 处理沙箱环境切换
    handleSandboxChange(value) {
      if (value) {
        // 启用沙箱环境时，复制正式环境配置到沙箱配置
        this.form.sandbox_appid = this.form.appid
        this.form.sandbox_mchid = this.form.mchid
        this.form.sandbox_api_v3_key = this.form.api_v3_key
        this.form.sandbox_serial_no = this.form.serial_no
        this.form.sandbox_private_key_path = this.form.private_key_path
      }
    },

    // 提交表单
    async submitForm() {
      this.$refs.form.validate(async (valid) => {
        if (!valid) {
          return
        }

        // 验证沙箱环境配置
        if (this.form.is_sandbox) {
          const sandboxFields = ['sandbox_appid', 'sandbox_mchid', 'sandbox_api_v3_key', 'sandbox_serial_no', 'sandbox_private_key_path']
          for (const field of sandboxFields) {
            if (!this.form[field]) {
              this.$message.error(`沙箱环境配置不完整，请填写${field}`)
              return
            }
          }
        }

        this.submitting = true
        try {
          let response
          if (this.isEdit) {
            response = await updateWeChatPayConfig(this.form.id, this.form)
          } else {
            response = await createWeChatPayConfig(this.form)
          }

          if (response.code === 200) {
            this.$message.success(response.message || '保存成功')
            this.dialogVisible = false
            this.loadConfigList()
          } else {
            this.$message.error(response.message || '保存失败')
          }
        } catch (error) {
          console.error('保存配置失败:', error)
          this.$message.error('保存配置失败')
        } finally {
          this.submitting = false
        }
      })
    },

    // 测试配置
    async testConfig() {
      this.$refs.form.validate(async (valid) => {
        if (!valid) {
          return
        }

        this.testing = true
        try {
          const response = await testWeChatPayConfig(this.form)
          
          this.testResult = {
            success: response.code === 200,
            message: response.message,
            data: response.data
          }
          
          this.testResultVisible = true
        } catch (error) {
          console.error('测试配置失败:', error)
          this.testResult = {
            success: false,
            message: '测试配置失败',
            data: null
          }
          this.testResultVisible = true
        } finally {
          this.testing = false
        }
      })
    },

    // 启用配置
    async enableConfig(configId) {
      try {
        const response = await enableWeChatPayConfig(configId)
        if (response.code === 200) {
          this.$message.success('启用配置成功')
          this.loadConfigList()
        } else {
          this.$message.error(response.message || '启用配置失败')
        }
      } catch (error) {
        console.error('启用配置失败:', error)
        this.$message.error('启用配置失败')
      }
    },

    // 禁用配置
    async disableConfig(configId) {
      try {
        const response = await disableWeChatPayConfig(configId)
        if (response.code === 200) {
          this.$message.success('禁用配置成功')
          this.loadConfigList()
        } else {
          this.$message.error(response.message || '禁用配置失败')
        }
      } catch (error) {
        console.error('禁用配置失败:', error)
        this.$message.error('禁用配置失败')
      }
    },

    // 删除配置
    async deleteConfig(configId) {
      this.$confirm('确定要删除这个配置吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const response = await deleteWeChatPayConfig(configId)
          if (response.code === 200) {
            this.$message.success('删除配置成功')
            this.loadConfigList()
          } else {
            this.$message.error(response.message || '删除配置失败')
          }
        } catch (error) {
          console.error('删除配置失败:', error)
          this.$message.error('删除配置失败')
        }
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },

    // 分页处理
    handleSizeChange(size) {
      this.pagination.size = size
      this.loadConfigList()
    },

    handleCurrentChange(page) {
      this.pagination.page = page
      this.loadConfigList()
    }
  }
}
</script>

<style lang="scss" scoped>
.wechat-pay-config {
  padding: 20px;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h2 {
      margin: 0;
      color: #303133;
    }
  }

  .config-list {
    margin-bottom: 20px;
  }

  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }

  .el-divider {
    margin: 20px 0;
  }
}
</style> 