"use strict";(self["webpackChunkopen_essay_gui"]=self["webpackChunkopen_essay_gui"]||[]).push([[345],{345:(t,a,s)=>{s.r(a),s.d(a,{default:()=>d});var e=function(){var t=this,a=t._self._c;return a("div",{staticClass:"admin-stats-thesis"},[a("el-card",[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("论文统计")]),a("div",{staticClass:"header-right"},[a("el-radio-group",{attrs:{size:"small"},on:{change:t.fetchThesisStats},model:{value:t.period,callback:function(a){t.period=a},expression:"period"}},[a("el-radio-button",{attrs:{label:"7d"}},[t._v("最近7天")]),a("el-radio-button",{attrs:{label:"30d"}},[t._v("最近30天")]),a("el-radio-button",{attrs:{label:"90d"}},[t._v("最近90天")])],1),a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{size:"small",type:"primary",icon:"el-icon-refresh"},on:{click:t.fetchThesisStats}},[t._v(" 刷新 ")])],1)]),a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}]},[a("div",{staticClass:"stat-cards"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-card",{staticClass:"stat-card",attrs:{shadow:"hover"}},[a("div",{staticClass:"stat-card-title"},[t._v("总论文数")]),a("div",{staticClass:"stat-card-value"},[t._v(t._s(t.statsData.total_thesis||0))])])],1),a("el-col",{attrs:{span:12}},[a("el-card",{staticClass:"stat-card",attrs:{shadow:"hover"}},[a("div",{staticClass:"stat-card-title"},[t._v("新增论文数")]),a("div",{staticClass:"stat-card-value"},[t._v(t._s(t.statsData.new_thesis||0))])])],1)],1)],1),a("div",{staticClass:"chart-container"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:24}},[a("el-card",{staticClass:"chart-card",attrs:{shadow:"hover"}},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("每日新增论文趋势")])]),a("div",{staticClass:"chart",staticStyle:{height:"300px"},attrs:{id:"daily-thesis-chart"}})])],1)],1),a("el-row",{staticStyle:{"margin-top":"20px"},attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-card",{staticClass:"chart-card",attrs:{shadow:"hover"}},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("论文语言分布")])]),a("div",{staticClass:"chart",staticStyle:{height:"300px"},attrs:{id:"lang-chart"}})])],1),a("el-col",{attrs:{span:8}},[a("el-card",{staticClass:"chart-card",attrs:{shadow:"hover"}},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("论文级别分布")])]),a("div",{staticClass:"chart",staticStyle:{height:"300px"},attrs:{id:"level-chart"}})])],1),a("el-col",{attrs:{span:8}},[a("el-card",{staticClass:"chart-card",attrs:{shadow:"hover"}},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("论文长度分布")])]),a("div",{staticClass:"chart",staticStyle:{height:"300px"},attrs:{id:"length-chart"}})])],1)],1),a("el-row",{staticStyle:{"margin-top":"20px"},attrs:{gutter:20}},[a("el-col",{attrs:{span:24}},[a("el-card",{attrs:{shadow:"hover"}},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("论文数据表格")])]),a("el-table",{staticStyle:{width:"100%"},attrs:{data:t.tableData}},[a("el-table-column",{attrs:{prop:"date",label:"日期",width:"180"}}),a("el-table-column",{attrs:{prop:"count",label:"新增论文数"}})],1)],1)],1)],1)],1)])])],1)},i=[],r=s(9192),l=s(9393);const h={name:"AdminStatsThesis",data(){return{loading:!1,period:"7d",statsData:{},tableData:[],charts:{dailyThesisChart:null,langChart:null,levelChart:null,lengthChart:null}}},mounted(){this.fetchThesisStats(),window.addEventListener("resize",this.resizeCharts)},beforeDestroy(){window.removeEventListener("resize",this.resizeCharts),Object.keys(this.charts).forEach((t=>{this.charts[t]&&this.charts[t].dispose()}))},methods:{async fetchThesisStats(){this.loading=!0;try{const t=await r.bk.getThesisStats({period:this.period});t.success?(this.statsData=t.data,this.tableData=this.statsData.daily_stats||[],this.$nextTick((()=>{this.initCharts()}))):this.$message.error(t.message||"获取论文统计数据失败")}catch(t){this.$message.error("获取论文统计数据失败："+t.message)}finally{this.loading=!1}},initCharts(){this.initDailyThesisChart(),this.initLangChart(),this.initLevelChart(),this.initLengthChart()},initDailyThesisChart(){const t=document.getElementById("daily-thesis-chart");if(!t)return;this.charts.dailyThesisChart&&this.charts.dailyThesisChart.dispose(),this.charts.dailyThesisChart=l.init(t);const a=this.statsData.daily_stats||[],s=a.map((t=>t.date)),e=a.map((t=>t.count)),i={tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:[{type:"category",data:s,axisLabel:{rotate:45}}],yAxis:[{type:"value"}],series:[{name:"新增论文",data:e,type:"bar",itemStyle:{color:"#67C23A"}}]};this.charts.dailyThesisChart.setOption(i)},initLangChart(){const t=document.getElementById("lang-chart");if(!t)return;this.charts.langChart&&this.charts.langChart.dispose(),this.charts.langChart=l.init(t);const a=this.statsData.lang_stats||[],s=a.map((t=>({name:t.lang||"未知",value:t.count}))),e={tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",right:10,top:"center",data:s.map((t=>t.name))},series:[{name:"语言分布",type:"pie",radius:["50%","70%"],avoidLabelOverlap:!1,label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:"16",fontWeight:"bold"}},labelLine:{show:!1},data:s}]};this.charts.langChart.setOption(e)},initLevelChart(){const t=document.getElementById("level-chart");if(!t)return;this.charts.levelChart&&this.charts.levelChart.dispose(),this.charts.levelChart=l.init(t);const a=this.statsData.level_stats||[],s=a.map((t=>({name:t.level||"未知",value:t.count}))),e={tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",right:10,top:"center",data:s.map((t=>t.name))},series:[{name:"级别分布",type:"pie",radius:["50%","70%"],avoidLabelOverlap:!1,label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:"16",fontWeight:"bold"}},labelLine:{show:!1},data:s}]};this.charts.levelChart.setOption(e)},initLengthChart(){const t=document.getElementById("length-chart");if(!t)return;this.charts.lengthChart&&this.charts.lengthChart.dispose(),this.charts.lengthChart=l.init(t);const a=this.statsData.length_stats||[],s=a.map((t=>({name:t.length?`${t.length}字`:"未知",value:t.count}))),e={tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",right:10,top:"center",data:s.map((t=>t.name))},series:[{name:"长度分布",type:"pie",radius:["50%","70%"],avoidLabelOverlap:!1,label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:"16",fontWeight:"bold"}},labelLine:{show:!1},data:s}]};this.charts.lengthChart.setOption(e)},resizeCharts(){Object.keys(this.charts).forEach((t=>{this.charts[t]&&this.charts[t].resize()}))}}},n=h;var o=s(1656),c=(0,o.A)(n,e,i,!1,null,"a119913e",null);const d=c.exports}}]);