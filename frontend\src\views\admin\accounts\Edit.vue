<template>
  <div class="admin-account-edit-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>{{ isEdit ? '编辑管理员' : '创建管理员' }}</span>
      </div>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" class="form">
        <el-form-item label="用户名" prop="username" :disabled="isEdit">
          <el-input v-model="form.username" :disabled="isEdit" placeholder="请输入用户名"></el-input>
        </el-form-item>
        
        <el-form-item label="密码" prop="password" v-if="!isEdit">
          <el-input v-model="form.password" type="password" show-password placeholder="请输入密码"></el-input>
        </el-form-item>
        
        <el-form-item label="确认密码" prop="confirm_password" v-if="!isEdit">
          <el-input v-model="form.confirm_password" type="password" show-password placeholder="请确认密码"></el-input>
        </el-form-item>
        
        <el-form-item label="姓名" prop="realname">
          <el-input v-model="form.realname" placeholder="请输入姓名"></el-input>
        </el-form-item>
        
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="form.email" placeholder="请输入邮箱"></el-input>
        </el-form-item>
        
        <el-form-item label="电话" prop="phone">
          <el-input v-model="form.phone" placeholder="请输入电话"></el-input>
        </el-form-item>
        
        <el-form-item label="状态" prop="is_active">
          <el-switch
            v-model="form.is_active"
            active-text="启用"
            inactive-text="禁用"
          ></el-switch>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="submitForm">保存</el-button>
          <el-button @click="cancel">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { adminAccount } from '@/api/admin'

export default {
  name: 'AdminAccountEdit',
  data() {
    const validateConfirmPassword = (rule, value, callback) => {
      if (value !== this.form.password) {
        callback(new Error('两次输入密码不一致'))
      } else {
        callback()
      }
    }
    
    return {
      isEdit: false,
      adminId: null,
      form: {
        username: '',
        password: '',
        confirm_password: '',
        realname: '',
        email: '',
        phone: '',
        is_active: true
      },
      rules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, message: '密码长度不能少于6个字符', trigger: 'blur' }
        ],
        confirm_password: [
          { required: true, message: '请确认密码', trigger: 'blur' },
          { validator: validateConfirmPassword, trigger: 'blur' }
        ],
        email: [
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ],
        phone: [
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    // 判断是编辑还是创建
    if (this.$route.params.id) {
      this.isEdit = true
      this.adminId = this.$route.params.id
      this.getAdminDetail()
    }
  },
  methods: {
    getAdminDetail() {
      adminAccount.getDetail(this.adminId).then(response => {
        if (response.success) {
          const adminData = response.data
          this.form = {
            username: adminData.username,
            realname: adminData.realname || '',
            email: adminData.email || '',
            phone: adminData.phone || '',
            is_active: adminData.is_active
          }
        } else {
          this.$message.error(response.message || '获取管理员信息失败')
        }
      })
    },
    submitForm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          if (this.isEdit) {
            // 编辑管理员
            const updateData = {
              realname: this.form.realname,
              email: this.form.email,
              phone: this.form.phone,
              is_active: this.form.is_active
            }
            
            adminAccount.update(this.adminId, updateData).then(response => {
              if (response.success) {
                this.$message.success('管理员信息更新成功')
                this.$router.push({ name: 'AdminAccountsList' })
              } else {
                this.$message.error(response.message || '管理员信息更新失败')
              }
            })
          } else {
            // 创建管理员
            const createData = {
              username: this.form.username,
              password: this.form.password,
              realname: this.form.realname,
              email: this.form.email,
              phone: this.form.phone,
              is_active: this.form.is_active
            }
            
            adminAccount.create(createData).then(response => {
              if (response.success) {
                this.$message.success('管理员创建成功')
                this.$router.push({ name: 'AdminAccountsList' })
              } else {
                this.$message.error(response.message || '管理员创建失败')
              }
            })
          }
        }
      })
    },
    cancel() {
      this.$router.push({ name: 'AdminAccountsList' })
    }
  }
}
</script>

<style scoped>
.admin-account-edit-container {
  padding: 20px;
}
.form {
  width: 600px;
  margin: 0 auto;
}
</style> 