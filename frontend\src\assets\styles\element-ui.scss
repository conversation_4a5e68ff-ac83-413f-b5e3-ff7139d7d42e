// cover some element-ui styles

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

.cell {
  .el-tag {
    margin-right: 0;
  }
}

.small-padding {
  .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.fixed-width {
  .el-button--mini {
    padding: 7px 10px;
    width: 60px;
  }
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;

    .el-tag {
      margin-right: 0;
    }
  }
}

// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block;
  }
}

// fix date-picker ui bug in filter-item
.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

.el-dialog {
  border-radius: 10px !important;
}
// thesis 项目 自定义
.el-dialog {
  border-radius: 10px;
  overflow: hidden;
}
.el-dialog__header {
  color: #fff;
  background: rgb(83, 168, 255);
}
.el-dialog__title {
  color: #fff;
}
.el-dialog__close {
  color: #fff !important;
}
.el-textarea {
  textarea {
    height: 200px !important;
    font-size: 16px;
    line-height: 30px;
  }
}

.xiaolong-btn {
  border: #00b09b;

  background: #00b4db; /* fallback for old browsers */
  background: -webkit-linear-gradient(to right, #0083b0, #00b4db); /* Chrome 10-25, Safari 5.1-6 */
  background: linear-gradient(
    to right,
    #0083b0,
    #00b4db
  ); /* W3C, IE 10+/ Edge, Firefox 16+, Chrome 26+, Opera 12+, Safari 7+ */

  font-weight: bold;
  color: #f1f1f1;
}

.xiaolong-btn:hover {
  color: #fff;
}

.el-loading-mask {
  background-color: rgba(255, 255, 255, 0.6) !important;
  backdrop-filter: blur(2px);
  
  .el-loading-spinner {
    .circular {
      width: 42px;
      height: 42px;
      animation: loading-rotate 2s linear infinite;
    }
    
    .path {
      stroke: #409EFF;
      stroke-width: 2;
      stroke-linecap: round;
      animation: loading-dash 1.5s ease-in-out infinite;
    }
    
    .el-loading-text {
      margin-top: 10px;
      font-size: 14px;
      color: #2c3e50;
      font-weight: 500;
      text-align: center;
      padding: 8px 16px;
      background: rgba(255, 255, 255, 0.9);
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      backdrop-filter: blur(4px);
    }
  }
}

@keyframes loading-rotate {
  100% {
    transform: rotate(360deg);
  }
}

@keyframes loading-dash {
  0% {
    stroke-dasharray: 1, 200;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -40px;
  }
  100% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -120px;
  }
}

// 菜单样式覆盖 - 确保AdminLayout的样式生效
.el-menu {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
}

.el-menu-item,
.el-submenu__title {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  color: #cbd5e1 !important;
}

.el-menu-item:hover,
.el-submenu__title:hover {
  background: rgba(255, 255, 255, 0.08) !important;
  color: #f1f5f9 !important;
  border: none !important;
  box-shadow: none !important;
}

.el-menu-item.is-active,
.el-submenu__title.is-active {
  background: rgba(255, 255, 255, 0.12) !important;
  color: #f1f5f9 !important;
  border: none !important;
  box-shadow: none !important;
}

// 覆盖所有可能的Element UI菜单样式
.el-menu--vertical,
.el-menu--horizontal,
.el-menu--popup {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
}

.el-menu--vertical .el-menu-item,
.el-menu--horizontal .el-menu-item,
.el-menu--popup .el-menu-item {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  color: #cbd5e1 !important;
}

.el-menu--vertical .el-menu-item:hover,
.el-menu--horizontal .el-menu-item:hover,
.el-menu--popup .el-menu-item:hover {
  background: rgba(255, 255, 255, 0.08) !important;
  color: #f1f5f9 !important;
  border: none !important;
  box-shadow: none !important;
}

.el-menu--vertical .el-menu-item.is-active,
.el-menu--horizontal .el-menu-item.is-active,
.el-menu--popup .el-menu-item.is-active {
  background: rgba(255, 255, 255, 0.12) !important;
  color: #f1f5f9 !important;
  border: none !important;
  box-shadow: none !important;
}

// 强制覆盖Element UI的默认活跃状态样式
.el-menu-item.is-active,
.el-submenu__title.is-active {
  background: rgba(255, 255, 255, 0.12) !important;
  color: #f1f5f9 !important;
  box-shadow: none !important;
  border: none !important;
}
