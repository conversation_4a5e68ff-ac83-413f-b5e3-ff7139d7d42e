2025-07-07 10:44:52  init_flask.py 27: INFO  {'CpuCount': 6, 'Mem': '32 GB'} [MainThread]
2025-07-07 10:44:52  web_server.py 32: INFO  Static directory: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\ui [MainThread]
2025-07-07 10:44:52  web_server.py 33: INFO  Static directory exists: True [MainThread]
2025-07-07 10:44:52  web_server.py 37: INFO  设置工作目录: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird [MainThread]
2025-07-07 10:44:52  EarlyBird.config.config 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-07 10:44:52  config.py 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-07 10:44:52  config.py 94: INFO  Database URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-07 10:44:54  EarlyBird.config.wechat_pay_config 49: WARNING  无法从数据库获取微信支付配置: No application found. Either work inside a view function or push an application context. See http://flask-sqlalchemy.pocoo.org/contexts/. [MainThread]
2025-07-07 10:44:54  wechat_pay_config.py 49: WARNING  无法从数据库获取微信支付配置: No application found. Either work inside a view function or push an application context. See http://flask-sqlalchemy.pocoo.org/contexts/. [MainThread]
2025-07-07 10:44:54  EarlyBird.config.wechat_pay_config 52: INFO  使用环境变量中的微信支付配置 [MainThread]
2025-07-07 10:44:54  wechat_pay_config.py 52: INFO  使用环境变量中的微信支付配置 [MainThread]
2025-07-07 10:44:54  EarlyBird.common.wechat_pay_v3 54: ERROR  加载商户私钥失败: [Errno 2] No such file or directory: 'certs/apiclient_key.pem' [MainThread]
2025-07-07 10:44:54  wechat_pay_v3.py 54: ERROR  加载商户私钥失败: [Errno 2] No such file or directory: 'certs/apiclient_key.pem' [MainThread]
2025-07-07 10:44:54  EarlyBird.api.pay.apis 31: ERROR  微信支付V3初始化失败: [Errno 2] No such file or directory: 'certs/apiclient_key.pem' [MainThread]
2025-07-07 10:44:54  apis.py 31: ERROR  微信支付V3初始化失败: [Errno 2] No such file or directory: 'certs/apiclient_key.pem' [MainThread]
2025-07-07 10:44:54  EarlyBird.api.admin.app 39: INFO  ✅ 后台管理系统API已注册 [MainThread]
2025-07-07 10:44:54  app.py 39: INFO  ✅ 后台管理系统API已注册 [MainThread]
2025-07-07 10:44:54  EarlyBird 40: INFO  确保数据目录存在: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\data [MainThread]
2025-07-07 10:44:54  server.py 40: INFO  确保数据目录存在: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\data [MainThread]
2025-07-07 10:44:54  EarlyBird 44: INFO  开始执行数据库迁移... [MainThread]
2025-07-07 10:44:54  server.py 44: INFO  开始执行数据库迁移... [MainThread]
2025-07-07 10:44:54  config.py 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-07 10:44:54  config.py 94: INFO  Database URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-07 10:44:54  EarlyBird 53: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-07 10:44:54  server.py 53: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-07 10:44:54  EarlyBird 97: WARNING  ChatLog表不存在，将在应用启动时自动创建 [MainThread]
2025-07-07 10:44:54  server.py 97: WARNING  ChatLog表不存在，将在应用启动时自动创建 [MainThread]
2025-07-07 10:44:54  add_uid_to_chatlog.py 20: INFO  开始执行ChatLog表迁移... [MainThread]
2025-07-07 10:44:54  add_uid_to_chatlog.py 24: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-07 10:44:54  add_uid_to_chatlog.py 38: WARNING  ChatLog表不存在，可能需要先创建表 [MainThread]
2025-07-07 10:44:54  task_api_server.py 31: INFO  TaskApiServer start [MainThread]
2025-07-07 10:44:54  task_paper_generator.py 38: INFO  论文生成任务处理线程初始化 [MainThread]
2025-07-07 10:44:54  task_paper_generator.py 72: INFO  论文生成任务处理线程启动 [Thread-2]
2025-07-07 10:44:54  task_api_server.py 47: INFO  TaskApiServer listen on 127.0.0.1:3302 [Thread-1]
2025-07-07 10:44:54  task_api_server.py 17: INFO  _ApiServerThread start [Thread-3]
2025-07-07 10:44:58  web_server.py 78: INFO  Serving request for path:  [waitress-0]
2025-07-07 10:44:58  web_server.py 84: INFO  Serving index.html for path:  [waitress-0]
2025-07-07 10:44:58  web_server.py 78: INFO  Serving request for path: js/chunk-vendors.7f82116b.js [waitress-1]
2025-07-07 10:44:58  web_server.py 81: INFO  Serving static file: js/chunk-vendors.7f82116b.js [waitress-1]
2025-07-07 10:44:58  web_server.py 78: INFO  Serving request for path: js/app.bd2b20c8.js [waitress-2]
2025-07-07 10:44:58  web_server.py 81: INFO  Serving static file: js/app.bd2b20c8.js [waitress-2]
2025-07-07 10:44:58  web_server.py 78: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-3]
2025-07-07 10:44:58  web_server.py 81: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-3]
2025-07-07 10:44:58  web_server.py 78: INFO  Serving request for path: css/app.2999c538.css [waitress-0]
2025-07-07 10:44:58  web_server.py 81: INFO  Serving static file: css/app.2999c538.css [waitress-0]
2025-07-07 10:44:58  web_server.py 78: INFO  Serving request for path: js/54.e30b26e3.js [waitress-2]
2025-07-07 10:44:58  web_server.py 78: INFO  Serving request for path: js/22.683bc166.js [waitress-3]
2025-07-07 10:44:58  web_server.py 81: INFO  Serving static file: js/54.e30b26e3.js [waitress-2]
2025-07-07 10:44:58  web_server.py 78: INFO  Serving request for path: css/552.bcca5555.css [waitress-0]
2025-07-07 10:44:58  web_server.py 78: INFO  Serving request for path: js/552.cc8c7fa5.js [waitress-1]
2025-07-07 10:44:58  web_server.py 81: INFO  Serving static file: js/22.683bc166.js [waitress-3]
2025-07-07 10:44:58  web_server.py 81: INFO  Serving static file: css/552.bcca5555.css [waitress-0]
2025-07-07 10:44:58  web_server.py 81: INFO  Serving static file: js/552.cc8c7fa5.js [waitress-1]
2025-07-07 10:44:58  web_server.py 78: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-2]
2025-07-07 10:44:58  web_server.py 81: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-2]
2025-07-07 10:44:59  web_server.py 78: INFO  Serving request for path: fonts/element-icons.ff18efd1.woff [waitress-3]
2025-07-07 10:44:59  web_server.py 81: INFO  Serving static file: fonts/element-icons.ff18efd1.woff [waitress-3]
2025-07-07 10:45:04  web_server.py 78: INFO  Serving request for path: admin/login [waitress-1]
2025-07-07 10:45:04  web_server.py 84: INFO  Serving index.html for path: admin/login [waitress-1]
2025-07-07 10:45:04  web_server.py 78: INFO  Serving request for path: js/chunk-vendors.7f82116b.js [waitress-2]
2025-07-07 10:45:04  web_server.py 81: INFO  Serving static file: js/chunk-vendors.7f82116b.js [waitress-2]
2025-07-07 10:45:04  web_server.py 78: INFO  Serving request for path: js/app.bd2b20c8.js [waitress-0]
2025-07-07 10:45:04  web_server.py 78: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-3]
2025-07-07 10:45:04  web_server.py 78: INFO  Serving request for path: css/app.2999c538.css [waitress-1]
2025-07-07 10:45:04  web_server.py 81: INFO  Serving static file: js/app.bd2b20c8.js [waitress-0]
2025-07-07 10:45:04  web_server.py 81: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-3]
2025-07-07 10:45:04  web_server.py 81: INFO  Serving static file: css/app.2999c538.css [waitress-1]
2025-07-07 10:45:05  web_server.py 78: INFO  Serving request for path: css/318.61ca778d.css [waitress-2]
2025-07-07 10:45:05  web_server.py 81: INFO  Serving static file: css/318.61ca778d.css [waitress-2]
2025-07-07 10:45:05  web_server.py 78: INFO  Serving request for path: js/318.f0e0c059.js [waitress-0]
2025-07-07 10:45:05  web_server.py 78: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-1]
2025-07-07 10:45:05  web_server.py 81: INFO  Serving static file: js/318.f0e0c059.js [waitress-0]
2025-07-07 10:45:05  web_server.py 81: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-1]
2025-07-07 10:45:05  web_server.py 78: INFO  Serving request for path: fonts/element-icons.ff18efd1.woff [waitress-3]
2025-07-07 10:45:05  web_server.py 81: INFO  Serving static file: fonts/element-icons.ff18efd1.woff [waitress-3]
2025-07-07 10:45:06  web_server.py 78: INFO  Serving request for path: css/30.60faab94.css [waitress-3]
2025-07-07 10:45:06  web_server.py 78: INFO  Serving request for path: js/54.e30b26e3.js [waitress-2]
2025-07-07 10:45:06  web_server.py 81: INFO  Serving static file: css/30.60faab94.css [waitress-3]
2025-07-07 10:45:06  web_server.py 81: INFO  Serving static file: js/54.e30b26e3.js [waitress-2]
2025-07-07 10:45:07  EarlyBird.api.admin.settings 69: INFO  成功获取系统设置，返回17个设置项 [waitress-1]
2025-07-07 10:45:07  settings.py 69: INFO  成功获取系统设置，返回17个设置项 [waitress-1]
2025-07-07 10:45:07  EarlyBird.api.admin.settings 276: INFO  开始获取支付配置... [waitress-0]
2025-07-07 10:45:07  settings.py 276: INFO  开始获取支付配置... [waitress-0]
2025-07-07 10:45:07  EarlyBird.api.admin.settings 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-0]
2025-07-07 10:45:07  settings.py 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-0]
2025-07-07 10:45:07  EarlyBird.api.admin.settings 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-0]
2025-07-07 10:45:07  settings.py 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-0]
2025-07-07 10:45:11  web_server.py 78: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-2]
2025-07-07 10:45:11  web_server.py 81: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-2]
2025-07-07 10:45:11  web_server.py 78: INFO  Serving request for path: css/app.2999c538.css [waitress-3]
2025-07-07 10:45:11  web_server.py 78: INFO  Serving request for path: css/318.61ca778d.css [waitress-2]
2025-07-07 10:45:11  web_server.py 81: INFO  Serving static file: css/app.2999c538.css [waitress-3]
2025-07-07 10:45:11  web_server.py 81: INFO  Serving static file: css/318.61ca778d.css [waitress-2]
2025-07-07 10:45:11  web_server.py 78: INFO  Serving request for path: css/30.60faab94.css [waitress-0]
2025-07-07 10:45:11  web_server.py 81: INFO  Serving static file: css/30.60faab94.css [waitress-0]
2025-07-07 10:45:11  web_server.py 78: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-1]
2025-07-07 10:45:11  web_server.py 84: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-1]
2025-07-07 10:48:38  web_server.py 78: INFO  Serving request for path: admin/settings [waitress-3]
2025-07-07 10:48:38  web_server.py 84: INFO  Serving index.html for path: admin/settings [waitress-3]
2025-07-07 10:48:38  web_server.py 78: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-2]
2025-07-07 10:48:38  web_server.py 84: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-2]
2025-07-07 10:48:38  web_server.py 78: INFO  Serving request for path: js/chunk-vendors.7f82116b.js [waitress-0]
2025-07-07 10:48:38  web_server.py 78: INFO  Serving request for path: js/app.bb90f92e.js [waitress-1]
2025-07-07 10:48:38  web_server.py 81: INFO  Serving static file: js/chunk-vendors.7f82116b.js [waitress-0]
2025-07-07 10:48:38  web_server.py 81: INFO  Serving static file: js/app.bb90f92e.js [waitress-1]
2025-07-07 10:48:38  web_server.py 78: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-3]
2025-07-07 10:48:38  web_server.py 81: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-3]
2025-07-07 10:48:38  web_server.py 78: INFO  Serving request for path: css/app.2999c538.css [waitress-2]
2025-07-07 10:48:38  web_server.py 81: INFO  Serving static file: css/app.2999c538.css [waitress-2]
2025-07-07 10:48:39  web_server.py 78: INFO  Serving request for path: js/724.c1bed2fc.js [waitress-0]
2025-07-07 10:48:39  web_server.py 78: INFO  Serving request for path: css/672.38681c2c.css [waitress-1]
2025-07-07 10:48:39  web_server.py 81: INFO  Serving static file: js/724.c1bed2fc.js [waitress-0]
2025-07-07 10:48:39  web_server.py 81: INFO  Serving static file: css/672.38681c2c.css [waitress-1]
2025-07-07 10:48:39  web_server.py 78: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-3]
2025-07-07 10:48:39  web_server.py 81: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-3]
2025-07-07 10:48:39  web_server.py 78: INFO  Serving request for path: fonts/element-icons.ff18efd1.woff [waitress-1]
2025-07-07 10:48:39  web_server.py 81: INFO  Serving static file: fonts/element-icons.ff18efd1.woff [waitress-1]
2025-07-07 10:48:39  web_server.py 78: INFO  Serving request for path: logo.png [waitress-3]
2025-07-07 10:48:39  web_server.py 81: INFO  Serving static file: logo.png [waitress-3]
2025-07-07 10:48:39  EarlyBird.api.admin.settings 69: INFO  成功获取系统设置，返回17个设置项 [waitress-2]
2025-07-07 10:48:39  settings.py 69: INFO  成功获取系统设置，返回17个设置项 [waitress-2]
2025-07-07 10:48:40  EarlyBird.api.admin.settings 276: INFO  开始获取支付配置... [waitress-0]
2025-07-07 10:48:40  settings.py 276: INFO  开始获取支付配置... [waitress-0]
2025-07-07 10:48:40  EarlyBird.api.admin.settings 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-0]
2025-07-07 10:48:40  settings.py 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-0]
2025-07-07 10:48:40  EarlyBird.api.admin.settings 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-0]
2025-07-07 10:48:40  settings.py 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-0]
2025-07-07 10:55:41  web_server.py 78: INFO  Serving request for path: admin/settings [waitress-1]
2025-07-07 10:55:41  web_server.py 84: INFO  Serving index.html for path: admin/settings [waitress-1]
2025-07-07 10:55:41  web_server.py 78: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-3]
2025-07-07 10:55:41  web_server.py 84: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-3]
2025-07-07 10:55:41  web_server.py 78: INFO  Serving request for path: js/chunk-vendors.7f82116b.js [waitress-2]
2025-07-07 10:55:41  web_server.py 81: INFO  Serving static file: js/chunk-vendors.7f82116b.js [waitress-2]
2025-07-07 10:55:41  web_server.py 78: INFO  Serving request for path: js/app.1ed9d2e6.js [waitress-0]
2025-07-07 10:55:41  web_server.py 81: INFO  Serving static file: js/app.1ed9d2e6.js [waitress-0]
2025-07-07 10:55:41  web_server.py 78: INFO  Serving request for path: css/app.2999c538.css [waitress-1]
2025-07-07 10:55:41  web_server.py 78: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-3]
2025-07-07 10:55:41  web_server.py 81: INFO  Serving static file: css/app.2999c538.css [waitress-1]
2025-07-07 10:55:41  web_server.py 81: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-3]
2025-07-07 10:55:41  web_server.py 78: INFO  Serving request for path: js/767.c755b153.js [waitress-2]
2025-07-07 10:55:41  web_server.py 78: INFO  Serving request for path: css/916.caa4b776.css [waitress-0]
2025-07-07 10:55:41  web_server.py 81: INFO  Serving static file: js/767.c755b153.js [waitress-2]
2025-07-07 10:55:41  web_server.py 81: INFO  Serving static file: css/916.caa4b776.css [waitress-0]
2025-07-07 10:55:41  web_server.py 78: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-3]
2025-07-07 10:55:41  web_server.py 81: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-3]
2025-07-07 10:55:41  web_server.py 78: INFO  Serving request for path: fonts/element-icons.ff18efd1.woff [waitress-0]
2025-07-07 10:55:41  web_server.py 81: INFO  Serving static file: fonts/element-icons.ff18efd1.woff [waitress-0]
2025-07-07 10:55:41  web_server.py 78: INFO  Serving request for path: logo.png [waitress-3]
2025-07-07 10:55:41  web_server.py 81: INFO  Serving static file: logo.png [waitress-3]
2025-07-07 10:55:41  EarlyBird.api.admin.settings 69: INFO  成功获取系统设置，返回17个设置项 [waitress-1]
2025-07-07 10:55:41  settings.py 69: INFO  成功获取系统设置，返回17个设置项 [waitress-1]
2025-07-07 10:55:43  EarlyBird.api.admin.settings 276: INFO  开始获取支付配置... [waitress-2]
2025-07-07 10:55:43  settings.py 276: INFO  开始获取支付配置... [waitress-2]
2025-07-07 10:55:43  EarlyBird.api.admin.settings 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-2]
2025-07-07 10:55:43  settings.py 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-2]
2025-07-07 10:55:43  EarlyBird.api.admin.settings 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-2]
2025-07-07 10:55:43  settings.py 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-2]
2025-07-07 10:59:13  web_server.py 78: INFO  Serving request for path: admin/settings [waitress-0]
2025-07-07 10:59:13  web_server.py 84: INFO  Serving index.html for path: admin/settings [waitress-0]
2025-07-07 10:59:13  web_server.py 78: INFO  Serving request for path: js/chunk-vendors.7f82116b.js [waitress-3]
2025-07-07 10:59:13  web_server.py 81: INFO  Serving static file: js/chunk-vendors.7f82116b.js [waitress-3]
2025-07-07 10:59:13  web_server.py 78: INFO  Serving request for path: js/app.0de44b8c.js [waitress-1]
2025-07-07 10:59:13  web_server.py 81: INFO  Serving static file: js/app.0de44b8c.js [waitress-1]
2025-07-07 10:59:13  web_server.py 78: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-2]
2025-07-07 10:59:13  web_server.py 81: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-2]
2025-07-07 10:59:13  web_server.py 78: INFO  Serving request for path: css/app.2999c538.css [waitress-0]
2025-07-07 10:59:13  web_server.py 81: INFO  Serving static file: css/app.2999c538.css [waitress-0]
2025-07-07 10:59:13  web_server.py 78: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-0]
2025-07-07 10:59:13  web_server.py 84: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-0]
2025-07-07 10:59:13  web_server.py 78: INFO  Serving request for path: js/719.a9f3098e.js [waitress-1]
2025-07-07 10:59:13  web_server.py 78: INFO  Serving request for path: css/729.c19b62b7.css [waitress-2]
2025-07-07 10:59:13  web_server.py 81: INFO  Serving static file: css/729.c19b62b7.css [waitress-2]
2025-07-07 10:59:13  web_server.py 81: INFO  Serving static file: js/719.a9f3098e.js [waitress-1]
2025-07-07 10:59:13  web_server.py 78: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-3]
2025-07-07 10:59:13  web_server.py 81: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-3]
2025-07-07 10:59:13  web_server.py 78: INFO  Serving request for path: fonts/element-icons.ff18efd1.woff [waitress-1]
2025-07-07 10:59:13  web_server.py 81: INFO  Serving static file: fonts/element-icons.ff18efd1.woff [waitress-1]
2025-07-07 10:59:13  web_server.py 78: INFO  Serving request for path: logo.png [waitress-3]
2025-07-07 10:59:13  web_server.py 81: INFO  Serving static file: logo.png [waitress-3]
2025-07-07 10:59:13  EarlyBird.api.admin.settings 69: INFO  成功获取系统设置，返回17个设置项 [waitress-0]
2025-07-07 10:59:13  settings.py 69: INFO  成功获取系统设置，返回17个设置项 [waitress-0]
2025-07-07 10:59:16  EarlyBird.api.admin.settings 276: INFO  开始获取支付配置... [waitress-2]
2025-07-07 10:59:16  settings.py 276: INFO  开始获取支付配置... [waitress-2]
2025-07-07 10:59:16  EarlyBird.api.admin.settings 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-2]
2025-07-07 10:59:16  settings.py 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-2]
2025-07-07 10:59:16  EarlyBird.api.admin.settings 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-2]
2025-07-07 10:59:16  settings.py 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-2]
2025-07-07 11:01:48  web_server.py 78: INFO  Serving request for path: admin/settings [waitress-1]
2025-07-07 11:01:48  web_server.py 84: INFO  Serving index.html for path: admin/settings [waitress-1]
2025-07-07 11:01:48  web_server.py 78: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-3]
2025-07-07 11:01:48  web_server.py 84: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-3]
2025-07-07 11:01:48  web_server.py 78: INFO  Serving request for path: js/chunk-vendors.7f82116b.js [waitress-0]
2025-07-07 11:01:48  web_server.py 78: INFO  Serving request for path: js/app.4731ef69.js [waitress-2]
2025-07-07 11:01:48  web_server.py 81: INFO  Serving static file: js/chunk-vendors.7f82116b.js [waitress-0]
2025-07-07 11:01:48  web_server.py 81: INFO  Serving static file: js/app.4731ef69.js [waitress-2]
2025-07-07 11:01:48  web_server.py 78: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-1]
2025-07-07 11:01:48  web_server.py 81: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-1]
2025-07-07 11:01:48  web_server.py 78: INFO  Serving request for path: css/app.2999c538.css [waitress-3]
2025-07-07 11:01:48  web_server.py 81: INFO  Serving static file: css/app.2999c538.css [waitress-3]
2025-07-07 11:01:49  web_server.py 78: INFO  Serving request for path: js/710.d8a2d4b2.js [waitress-2]
2025-07-07 11:01:49  web_server.py 78: INFO  Serving request for path: css/968.83b50e89.css [waitress-0]
2025-07-07 11:01:49  web_server.py 81: INFO  Serving static file: js/710.d8a2d4b2.js [waitress-2]
2025-07-07 11:01:49  web_server.py 81: INFO  Serving static file: css/968.83b50e89.css [waitress-0]
2025-07-07 11:01:49  web_server.py 78: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-1]
2025-07-07 11:01:49  web_server.py 81: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-1]
2025-07-07 11:01:49  web_server.py 78: INFO  Serving request for path: fonts/element-icons.ff18efd1.woff [waitress-2]
2025-07-07 11:01:49  web_server.py 81: INFO  Serving static file: fonts/element-icons.ff18efd1.woff [waitress-2]
2025-07-07 11:01:49  web_server.py 78: INFO  Serving request for path: logo.png [waitress-3]
2025-07-07 11:01:49  web_server.py 81: INFO  Serving static file: logo.png [waitress-3]
2025-07-07 11:01:49  EarlyBird.api.admin.settings 69: INFO  成功获取系统设置，返回17个设置项 [waitress-1]
2025-07-07 11:01:49  settings.py 69: INFO  成功获取系统设置，返回17个设置项 [waitress-1]
2025-07-07 11:01:52  EarlyBird.api.admin.settings 276: INFO  开始获取支付配置... [waitress-0]
2025-07-07 11:01:52  settings.py 276: INFO  开始获取支付配置... [waitress-0]
2025-07-07 11:01:52  EarlyBird.api.admin.settings 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-0]
2025-07-07 11:01:52  settings.py 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-0]
2025-07-07 11:01:52  EarlyBird.api.admin.settings 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-0]
2025-07-07 11:01:52  settings.py 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-0]
2025-07-07 11:05:51  web_server.py 78: INFO  Serving request for path: admin/settings [waitress-2]
2025-07-07 11:05:51  web_server.py 84: INFO  Serving index.html for path: admin/settings [waitress-2]
2025-07-07 11:05:51  web_server.py 78: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-3]
2025-07-07 11:05:51  web_server.py 84: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-3]
2025-07-07 11:05:51  web_server.py 78: INFO  Serving request for path: js/chunk-vendors.7f82116b.js [waitress-1]
2025-07-07 11:05:51  web_server.py 78: INFO  Serving request for path: js/app.ccd39244.js [waitress-0]
2025-07-07 11:05:51  web_server.py 81: INFO  Serving static file: js/chunk-vendors.7f82116b.js [waitress-1]
2025-07-07 11:05:51  web_server.py 81: INFO  Serving static file: js/app.ccd39244.js [waitress-0]
2025-07-07 11:05:51  web_server.py 78: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-2]
2025-07-07 11:05:51  web_server.py 78: INFO  Serving request for path: css/app.2999c538.css [waitress-3]
2025-07-07 11:05:51  web_server.py 81: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-2]
2025-07-07 11:05:51  web_server.py 81: INFO  Serving static file: css/app.2999c538.css [waitress-3]
2025-07-07 11:05:52  web_server.py 78: INFO  Serving request for path: js/551.bf8887dd.js [waitress-2]
2025-07-07 11:05:52  web_server.py 81: INFO  Serving static file: js/551.bf8887dd.js [waitress-2]
2025-07-07 11:05:52  web_server.py 78: INFO  Serving request for path: css/182.bd1f74b0.css [waitress-0]
2025-07-07 11:05:52  web_server.py 81: INFO  Serving static file: css/182.bd1f74b0.css [waitress-0]
2025-07-07 11:05:52  web_server.py 78: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-1]
2025-07-07 11:05:52  web_server.py 81: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-1]
2025-07-07 11:05:52  web_server.py 78: INFO  Serving request for path: fonts/element-icons.ff18efd1.woff [waitress-0]
2025-07-07 11:05:52  web_server.py 81: INFO  Serving static file: fonts/element-icons.ff18efd1.woff [waitress-0]
2025-07-07 11:05:52  web_server.py 78: INFO  Serving request for path: logo.png [waitress-1]
2025-07-07 11:05:52  web_server.py 81: INFO  Serving static file: logo.png [waitress-1]
2025-07-07 11:05:52  EarlyBird.api.admin.settings 69: INFO  成功获取系统设置，返回17个设置项 [waitress-3]
2025-07-07 11:05:52  settings.py 69: INFO  成功获取系统设置，返回17个设置项 [waitress-3]
2025-07-07 11:05:54  EarlyBird.api.admin.settings 276: INFO  开始获取支付配置... [waitress-2]
2025-07-07 11:05:54  settings.py 276: INFO  开始获取支付配置... [waitress-2]
2025-07-07 11:05:54  EarlyBird.api.admin.settings 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-2]
2025-07-07 11:05:54  settings.py 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-2]
2025-07-07 11:05:54  EarlyBird.api.admin.settings 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-2]
2025-07-07 11:05:54  settings.py 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-2]
2025-07-07 11:08:23  web_server.py 78: INFO  Serving request for path: admin/settings [waitress-0]
2025-07-07 11:08:23  web_server.py 84: INFO  Serving index.html for path: admin/settings [waitress-0]
2025-07-07 11:08:23  web_server.py 78: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-1]
2025-07-07 11:08:23  web_server.py 84: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-1]
2025-07-07 11:08:23  web_server.py 78: INFO  Serving request for path: js/app.b4d34f16.js [waitress-3]
2025-07-07 11:08:23  web_server.py 78: INFO  Serving request for path: js/chunk-vendors.7f82116b.js [waitress-2]
2025-07-07 11:08:23  task.py 113: WARNING  Task queue depth is 1 [MainThread]
2025-07-07 11:08:23  web_server.py 81: INFO  Serving static file: js/app.b4d34f16.js [waitress-3]
2025-07-07 11:08:23  web_server.py 81: INFO  Serving static file: js/chunk-vendors.7f82116b.js [waitress-2]
2025-07-07 11:08:23  web_server.py 78: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-0]
2025-07-07 11:08:23  web_server.py 81: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-0]
2025-07-07 11:08:23  web_server.py 78: INFO  Serving request for path: css/app.2999c538.css [waitress-1]
2025-07-07 11:08:23  web_server.py 81: INFO  Serving static file: css/app.2999c538.css [waitress-1]
2025-07-07 11:08:23  web_server.py 78: INFO  Serving request for path: js/982.cc0a0f63.js [waitress-3]
2025-07-07 11:08:23  web_server.py 78: INFO  Serving request for path: css/952.0e04fbe3.css [waitress-0]
2025-07-07 11:08:23  web_server.py 81: INFO  Serving static file: js/982.cc0a0f63.js [waitress-3]
2025-07-07 11:08:23  web_server.py 81: INFO  Serving static file: css/952.0e04fbe3.css [waitress-0]
2025-07-07 11:08:23  web_server.py 78: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-1]
2025-07-07 11:08:23  web_server.py 81: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-1]
2025-07-07 11:08:23  web_server.py 78: INFO  Serving request for path: fonts/element-icons.ff18efd1.woff [waitress-0]
2025-07-07 11:08:23  web_server.py 81: INFO  Serving static file: fonts/element-icons.ff18efd1.woff [waitress-0]
2025-07-07 11:08:23  web_server.py 78: INFO  Serving request for path: logo.png [waitress-1]
2025-07-07 11:08:23  web_server.py 81: INFO  Serving static file: logo.png [waitress-1]
2025-07-07 11:08:23  EarlyBird.api.admin.settings 69: INFO  成功获取系统设置，返回17个设置项 [waitress-2]
2025-07-07 11:08:23  settings.py 69: INFO  成功获取系统设置，返回17个设置项 [waitress-2]
2025-07-07 11:08:25  EarlyBird.api.admin.settings 276: INFO  开始获取支付配置... [waitress-3]
2025-07-07 11:08:25  settings.py 276: INFO  开始获取支付配置... [waitress-3]
2025-07-07 11:08:25  EarlyBird.api.admin.settings 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-3]
2025-07-07 11:08:25  settings.py 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-3]
2025-07-07 11:08:25  EarlyBird.api.admin.settings 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-3]
2025-07-07 11:08:25  settings.py 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-3]
2025-07-07 11:12:29  web_server.py 78: INFO  Serving request for path: admin/settings [waitress-0]
2025-07-07 11:12:29  web_server.py 84: INFO  Serving index.html for path: admin/settings [waitress-0]
2025-07-07 11:12:29  web_server.py 78: INFO  Serving request for path: js/chunk-vendors.7f82116b.js [waitress-1]
2025-07-07 11:12:29  web_server.py 78: INFO  Serving request for path: js/app.e42d2b89.js [waitress-2]
2025-07-07 11:12:29  web_server.py 81: INFO  Serving static file: js/chunk-vendors.7f82116b.js [waitress-1]
2025-07-07 11:12:29  web_server.py 81: INFO  Serving static file: js/app.e42d2b89.js [waitress-2]
2025-07-07 11:12:29  web_server.py 78: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-3]
2025-07-07 11:12:29  web_server.py 84: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-3]
2025-07-07 11:12:29  web_server.py 78: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-0]
2025-07-07 11:12:29  web_server.py 81: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-0]
2025-07-07 11:12:29  web_server.py 78: INFO  Serving request for path: css/app.2999c538.css [waitress-3]
2025-07-07 11:12:29  web_server.py 81: INFO  Serving static file: css/app.2999c538.css [waitress-3]
2025-07-07 11:12:30  web_server.py 78: INFO  Serving request for path: js/791.9b7cf460.js [waitress-2]
2025-07-07 11:12:30  web_server.py 78: INFO  Serving request for path: css/92.e37e1fc5.css [waitress-1]
2025-07-07 11:12:30  web_server.py 81: INFO  Serving static file: css/92.e37e1fc5.css [waitress-1]
2025-07-07 11:12:30  web_server.py 81: INFO  Serving static file: js/791.9b7cf460.js [waitress-2]
2025-07-07 11:12:30  web_server.py 78: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-0]
2025-07-07 11:12:30  web_server.py 81: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-0]
2025-07-07 11:12:30  web_server.py 78: INFO  Serving request for path: fonts/element-icons.ff18efd1.woff [waitress-1]
2025-07-07 11:12:30  web_server.py 81: INFO  Serving static file: fonts/element-icons.ff18efd1.woff [waitress-1]
2025-07-07 11:12:30  web_server.py 78: INFO  Serving request for path: logo.png [waitress-0]
2025-07-07 11:12:30  web_server.py 81: INFO  Serving static file: logo.png [waitress-0]
2025-07-07 11:12:30  EarlyBird.api.admin.settings 69: INFO  成功获取系统设置，返回17个设置项 [waitress-3]
2025-07-07 11:12:30  settings.py 69: INFO  成功获取系统设置，返回17个设置项 [waitress-3]
2025-07-07 11:12:31  EarlyBird.api.admin.settings 276: INFO  开始获取支付配置... [waitress-2]
2025-07-07 11:12:31  settings.py 276: INFO  开始获取支付配置... [waitress-2]
2025-07-07 11:12:31  EarlyBird.api.admin.settings 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-2]
2025-07-07 11:12:31  settings.py 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-2]
2025-07-07 11:12:31  EarlyBird.api.admin.settings 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-2]
2025-07-07 11:12:31  settings.py 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-2]
2025-07-07 11:18:00  web_server.py 78: INFO  Serving request for path: admin/settings [waitress-1]
2025-07-07 11:18:00  web_server.py 84: INFO  Serving index.html for path: admin/settings [waitress-1]
2025-07-07 11:18:00  web_server.py 78: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-0]
2025-07-07 11:18:00  web_server.py 84: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-0]
2025-07-07 11:18:00  web_server.py 78: INFO  Serving request for path: js/chunk-vendors.7f82116b.js [waitress-3]
2025-07-07 11:18:00  web_server.py 78: INFO  Serving request for path: js/app.4fedc202.js [waitress-2]
2025-07-07 11:18:00  web_server.py 81: INFO  Serving static file: js/chunk-vendors.7f82116b.js [waitress-3]
2025-07-07 11:18:00  web_server.py 81: INFO  Serving static file: js/app.4fedc202.js [waitress-2]
2025-07-07 11:18:00  web_server.py 78: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-1]
2025-07-07 11:18:00  web_server.py 81: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-1]
2025-07-07 11:18:00  web_server.py 78: INFO  Serving request for path: css/app.2999c538.css [waitress-0]
2025-07-07 11:18:00  web_server.py 81: INFO  Serving static file: css/app.2999c538.css [waitress-0]
2025-07-07 11:18:00  web_server.py 78: INFO  Serving request for path: js/320.47678a4d.js [waitress-2]
2025-07-07 11:18:00  web_server.py 78: INFO  Serving request for path: css/188.6da0e773.css [waitress-3]
2025-07-07 11:18:00  web_server.py 81: INFO  Serving static file: js/320.47678a4d.js [waitress-2]
2025-07-07 11:18:00  web_server.py 81: INFO  Serving static file: css/188.6da0e773.css [waitress-3]
2025-07-07 11:18:00  web_server.py 78: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-1]
2025-07-07 11:18:00  web_server.py 81: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-1]
2025-07-07 11:18:00  web_server.py 78: INFO  Serving request for path: fonts/element-icons.ff18efd1.woff [waitress-2]
2025-07-07 11:18:00  web_server.py 81: INFO  Serving static file: fonts/element-icons.ff18efd1.woff [waitress-2]
2025-07-07 11:18:00  web_server.py 78: INFO  Serving request for path: logo.png [waitress-1]
2025-07-07 11:18:00  web_server.py 81: INFO  Serving static file: logo.png [waitress-1]
2025-07-07 11:18:00  EarlyBird.api.admin.settings 69: INFO  成功获取系统设置，返回17个设置项 [waitress-0]
2025-07-07 11:18:00  settings.py 69: INFO  成功获取系统设置，返回17个设置项 [waitress-0]
2025-07-07 11:18:02  EarlyBird.api.admin.settings 276: INFO  开始获取支付配置... [waitress-3]
2025-07-07 11:18:02  settings.py 276: INFO  开始获取支付配置... [waitress-3]
2025-07-07 11:18:02  EarlyBird.api.admin.settings 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-3]
2025-07-07 11:18:02  settings.py 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-3]
2025-07-07 11:18:02  EarlyBird.api.admin.settings 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-3]
2025-07-07 11:18:02  settings.py 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-3]
2025-07-07 11:20:43  web_server.py 78: INFO  Serving request for path: admin/settings [waitress-2]
2025-07-07 11:20:43  web_server.py 84: INFO  Serving index.html for path: admin/settings [waitress-2]
2025-07-07 11:20:43  web_server.py 78: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-1]
2025-07-07 11:20:43  web_server.py 84: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-1]
2025-07-07 11:20:43  web_server.py 78: INFO  Serving request for path: js/chunk-vendors.7f82116b.js [waitress-0]
2025-07-07 11:20:43  web_server.py 78: INFO  Serving request for path: js/app.7c8f8e38.js [waitress-3]
2025-07-07 11:20:43  web_server.py 81: INFO  Serving static file: js/chunk-vendors.7f82116b.js [waitress-0]
2025-07-07 11:20:43  web_server.py 81: INFO  Serving static file: js/app.7c8f8e38.js [waitress-3]
2025-07-07 11:20:43  web_server.py 78: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-2]
2025-07-07 11:20:43  web_server.py 78: INFO  Serving request for path: css/app.2999c538.css [waitress-1]
2025-07-07 11:20:43  web_server.py 81: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-2]
2025-07-07 11:20:43  web_server.py 81: INFO  Serving static file: css/app.2999c538.css [waitress-1]
2025-07-07 11:20:44  web_server.py 78: INFO  Serving request for path: js/394.d2247a08.js [waitress-3]
2025-07-07 11:20:44  web_server.py 78: INFO  Serving request for path: css/347.0e32366c.css [waitress-2]
2025-07-07 11:20:44  web_server.py 81: INFO  Serving static file: js/394.d2247a08.js [waitress-3]
2025-07-07 11:20:44  web_server.py 81: INFO  Serving static file: css/347.0e32366c.css [waitress-2]
2025-07-07 11:20:44  web_server.py 78: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-0]
2025-07-07 11:20:44  web_server.py 81: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-0]
2025-07-07 11:20:44  web_server.py 78: INFO  Serving request for path: fonts/element-icons.ff18efd1.woff [waitress-2]
2025-07-07 11:20:44  web_server.py 81: INFO  Serving static file: fonts/element-icons.ff18efd1.woff [waitress-2]
2025-07-07 11:20:44  web_server.py 78: INFO  Serving request for path: logo.png [waitress-0]
2025-07-07 11:20:44  web_server.py 81: INFO  Serving static file: logo.png [waitress-0]
2025-07-07 11:20:44  EarlyBird.api.admin.settings 69: INFO  成功获取系统设置，返回17个设置项 [waitress-1]
2025-07-07 11:20:44  settings.py 69: INFO  成功获取系统设置，返回17个设置项 [waitress-1]
2025-07-07 11:20:46  EarlyBird.api.admin.settings 276: INFO  开始获取支付配置... [waitress-3]
2025-07-07 11:20:46  settings.py 276: INFO  开始获取支付配置... [waitress-3]
2025-07-07 11:20:46  EarlyBird.api.admin.settings 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-3]
2025-07-07 11:20:46  settings.py 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-3]
2025-07-07 11:20:46  EarlyBird.api.admin.settings 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-3]
2025-07-07 11:20:46  settings.py 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-3]
2025-07-07 11:24:32  web_server.py 78: INFO  Serving request for path: admin/settings [waitress-2]
2025-07-07 11:24:32  web_server.py 84: INFO  Serving index.html for path: admin/settings [waitress-2]
2025-07-07 11:24:32  web_server.py 78: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-0]
2025-07-07 11:24:32  web_server.py 84: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-0]
2025-07-07 11:24:32  web_server.py 78: INFO  Serving request for path: js/chunk-vendors.7f82116b.js [waitress-1]
2025-07-07 11:24:32  web_server.py 78: INFO  Serving request for path: js/app.cc21b611.js [waitress-3]
2025-07-07 11:24:32  web_server.py 78: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-2]
2025-07-07 11:24:32  web_server.py 81: INFO  Serving static file: js/chunk-vendors.7f82116b.js [waitress-1]
2025-07-07 11:24:32  web_server.py 81: INFO  Serving static file: js/app.cc21b611.js [waitress-3]
2025-07-07 11:24:32  web_server.py 81: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-2]
2025-07-07 11:24:32  web_server.py 78: INFO  Serving request for path: css/app.2999c538.css [waitress-0]
2025-07-07 11:24:32  web_server.py 81: INFO  Serving static file: css/app.2999c538.css [waitress-0]
2025-07-07 11:24:32  web_server.py 78: INFO  Serving request for path: js/997.a7a0e862.js [waitress-2]
2025-07-07 11:24:32  web_server.py 78: INFO  Serving request for path: css/816.2929f14c.css [waitress-3]
2025-07-07 11:24:32  web_server.py 81: INFO  Serving static file: js/997.a7a0e862.js [waitress-2]
2025-07-07 11:24:32  web_server.py 81: INFO  Serving static file: css/816.2929f14c.css [waitress-3]
2025-07-07 11:24:32  web_server.py 78: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-1]
2025-07-07 11:24:32  web_server.py 81: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-1]
2025-07-07 11:24:32  web_server.py 78: INFO  Serving request for path: fonts/element-icons.ff18efd1.woff [waitress-2]
2025-07-07 11:24:32  web_server.py 81: INFO  Serving static file: fonts/element-icons.ff18efd1.woff [waitress-2]
2025-07-07 11:24:32  web_server.py 78: INFO  Serving request for path: logo.png [waitress-1]
2025-07-07 11:24:32  web_server.py 81: INFO  Serving static file: logo.png [waitress-1]
2025-07-07 11:24:32  EarlyBird.api.admin.settings 69: INFO  成功获取系统设置，返回17个设置项 [waitress-0]
2025-07-07 11:24:32  settings.py 69: INFO  成功获取系统设置，返回17个设置项 [waitress-0]
2025-07-07 11:24:35  EarlyBird.api.admin.settings 276: INFO  开始获取支付配置... [waitress-3]
2025-07-07 11:24:35  settings.py 276: INFO  开始获取支付配置... [waitress-3]
2025-07-07 11:24:35  EarlyBird.api.admin.settings 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-3]
2025-07-07 11:24:35  settings.py 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-3]
2025-07-07 11:24:35  EarlyBird.api.admin.settings 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-3]
2025-07-07 11:24:35  settings.py 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-3]
2025-07-07 11:44:43  web_server.py 78: INFO  Serving request for path: admin/settings [waitress-2]
2025-07-07 11:44:43  web_server.py 84: INFO  Serving index.html for path: admin/settings [waitress-2]
2025-07-07 11:44:43  web_server.py 78: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-1]
2025-07-07 11:44:43  web_server.py 84: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-1]
2025-07-07 11:44:43  web_server.py 78: INFO  Serving request for path: js/chunk-vendors.7f82116b.js [waitress-0]
2025-07-07 11:44:43  web_server.py 78: INFO  Serving request for path: js/app.463c7e78.js [waitress-3]
2025-07-07 11:44:43  web_server.py 81: INFO  Serving static file: js/chunk-vendors.7f82116b.js [waitress-0]
2025-07-07 11:44:43  web_server.py 81: INFO  Serving static file: js/app.463c7e78.js [waitress-3]
2025-07-07 11:44:43  web_server.py 78: INFO  Serving request for path: css/app.2999c538.css [waitress-1]
2025-07-07 11:44:43  web_server.py 78: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-2]
2025-07-07 11:44:43  web_server.py 81: INFO  Serving static file: css/app.2999c538.css [waitress-1]
2025-07-07 11:44:43  web_server.py 81: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-2]
2025-07-07 11:44:43  web_server.py 78: INFO  Serving request for path: css/752.9ff8b85c.css [waitress-3]
2025-07-07 11:44:43  web_server.py 78: INFO  Serving request for path: js/752.868b78a6.js [waitress-0]
2025-07-07 11:44:43  web_server.py 81: INFO  Serving static file: css/752.9ff8b85c.css [waitress-3]
2025-07-07 11:44:43  web_server.py 81: INFO  Serving static file: js/752.868b78a6.js [waitress-0]
2025-07-07 11:44:43  web_server.py 78: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-2]
2025-07-07 11:44:43  web_server.py 81: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-2]
2025-07-07 11:44:43  web_server.py 78: INFO  Serving request for path: fonts/element-icons.ff18efd1.woff [waitress-3]
2025-07-07 11:44:43  web_server.py 81: INFO  Serving static file: fonts/element-icons.ff18efd1.woff [waitress-3]
2025-07-07 11:44:43  web_server.py 78: INFO  Serving request for path: logo.png [waitress-0]
2025-07-07 11:44:43  web_server.py 81: INFO  Serving static file: logo.png [waitress-0]
2025-07-07 11:44:47  web_server.py 78: INFO  Serving request for path: css/917.197b99a7.css [waitress-2]
2025-07-07 11:44:47  web_server.py 78: INFO  Serving request for path: js/917.b30e576f.js [waitress-1]
2025-07-07 11:44:47  web_server.py 81: INFO  Serving static file: css/917.197b99a7.css [waitress-2]
2025-07-07 11:44:47  web_server.py 81: INFO  Serving static file: js/917.b30e576f.js [waitress-1]
2025-07-07 11:51:03  web_server.py 78: INFO  Serving request for path: admin/settings [waitress-0]
2025-07-07 11:51:03  web_server.py 84: INFO  Serving index.html for path: admin/settings [waitress-0]
2025-07-07 11:51:03  web_server.py 78: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-2]
2025-07-07 11:51:03  web_server.py 84: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-2]
2025-07-07 11:51:03  web_server.py 78: INFO  Serving request for path: js/chunk-vendors.7f82116b.js [waitress-1]
2025-07-07 11:51:03  web_server.py 78: INFO  Serving request for path: js/app.a093cceb.js [waitress-3]
2025-07-07 11:51:03  web_server.py 81: INFO  Serving static file: js/chunk-vendors.7f82116b.js [waitress-1]
2025-07-07 11:51:03  web_server.py 81: INFO  Serving static file: js/app.a093cceb.js [waitress-3]
2025-07-07 11:51:03  web_server.py 78: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-0]
2025-07-07 11:51:03  web_server.py 81: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-0]
2025-07-07 11:51:03  web_server.py 78: INFO  Serving request for path: css/app.2999c538.css [waitress-2]
2025-07-07 11:51:03  web_server.py 81: INFO  Serving static file: css/app.2999c538.css [waitress-2]
2025-07-07 11:51:03  web_server.py 78: INFO  Serving request for path: css/81.645eac93.css [waitress-3]
2025-07-07 11:51:03  web_server.py 78: INFO  Serving request for path: js/81.dfa7dae9.js [waitress-0]
2025-07-07 11:51:03  web_server.py 81: INFO  Serving static file: css/81.645eac93.css [waitress-3]
2025-07-07 11:51:03  web_server.py 81: INFO  Serving static file: js/81.dfa7dae9.js [waitress-0]
2025-07-07 11:51:03  web_server.py 78: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-1]
2025-07-07 11:51:03  web_server.py 81: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-1]
2025-07-07 11:51:03  EarlyBird.api.admin.settings 276: INFO  开始获取支付配置... [waitress-2]
2025-07-07 11:51:03  web_server.py 78: INFO  Serving request for path: fonts/element-icons.ff18efd1.woff [waitress-0]
2025-07-07 11:51:03  settings.py 276: INFO  开始获取支付配置... [waitress-2]
2025-07-07 11:51:03  EarlyBird.api.admin.settings 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-2]
2025-07-07 11:51:03  web_server.py 81: INFO  Serving static file: fonts/element-icons.ff18efd1.woff [waitress-0]
2025-07-07 11:51:03  settings.py 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-2]
2025-07-07 11:51:03  EarlyBird.api.admin.settings 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-2]
2025-07-07 11:51:03  settings.py 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-2]
2025-07-07 11:51:03  web_server.py 78: INFO  Serving request for path: logo.png [waitress-1]
2025-07-07 11:51:03  web_server.py 81: INFO  Serving static file: logo.png [waitress-1]
2025-07-07 11:53:37  web_server.py 78: INFO  Serving request for path: admin/settings [waitress-3]
2025-07-07 11:53:37  web_server.py 84: INFO  Serving index.html for path: admin/settings [waitress-3]
2025-07-07 11:53:37  web_server.py 78: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-0]
2025-07-07 11:53:37  web_server.py 84: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-0]
2025-07-07 11:53:37  web_server.py 78: INFO  Serving request for path: js/chunk-vendors.7f82116b.js [waitress-2]
2025-07-07 11:53:37  web_server.py 81: INFO  Serving static file: js/chunk-vendors.7f82116b.js [waitress-2]
2025-07-07 11:53:37  web_server.py 78: INFO  Serving request for path: js/app.524f12c8.js [waitress-1]
2025-07-07 11:53:37  web_server.py 81: INFO  Serving static file: js/app.524f12c8.js [waitress-1]
2025-07-07 11:53:37  web_server.py 78: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-3]
2025-07-07 11:53:37  web_server.py 81: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-3]
2025-07-07 11:53:37  web_server.py 78: INFO  Serving request for path: css/app.2999c538.css [waitress-2]
2025-07-07 11:53:37  web_server.py 81: INFO  Serving static file: css/app.2999c538.css [waitress-2]
2025-07-07 11:53:38  web_server.py 78: INFO  Serving request for path: css/764.d3c65090.css [waitress-0]
2025-07-07 11:53:38  web_server.py 78: INFO  Serving request for path: js/764.932c02bf.js [waitress-1]
2025-07-07 11:53:38  web_server.py 81: INFO  Serving static file: js/764.932c02bf.js [waitress-1]
2025-07-07 11:53:38  web_server.py 81: INFO  Serving static file: css/764.d3c65090.css [waitress-0]
2025-07-07 11:53:38  web_server.py 78: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-3]
2025-07-07 11:53:38  web_server.py 81: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-3]
2025-07-07 11:53:38  EarlyBird.api.admin.settings 276: INFO  开始获取支付配置... [waitress-2]
2025-07-07 11:53:38  settings.py 276: INFO  开始获取支付配置... [waitress-2]
2025-07-07 11:53:38  EarlyBird.api.admin.settings 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-2]
2025-07-07 11:53:38  web_server.py 78: INFO  Serving request for path: fonts/element-icons.ff18efd1.woff [waitress-0]
2025-07-07 11:53:38  settings.py 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-2]
2025-07-07 11:53:38  web_server.py 81: INFO  Serving static file: fonts/element-icons.ff18efd1.woff [waitress-0]
2025-07-07 11:53:38  EarlyBird.api.admin.settings 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-2]
2025-07-07 11:53:38  settings.py 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-2]
2025-07-07 11:53:38  web_server.py 78: INFO  Serving request for path: logo.png [waitress-3]
2025-07-07 11:53:38  web_server.py 81: INFO  Serving static file: logo.png [waitress-3]
2025-07-07 11:53:38  web_server.py 78: INFO  Serving request for path: admin/settings [waitress-1]
2025-07-07 11:53:38  web_server.py 84: INFO  Serving index.html for path: admin/settings [waitress-1]
2025-07-07 11:53:38  web_server.py 78: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-0]
2025-07-07 11:53:39  web_server.py 84: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-0]
2025-07-07 11:53:39  web_server.py 78: INFO  Serving request for path: js/chunk-vendors.7f82116b.js [waitress-2]
2025-07-07 11:53:39  web_server.py 81: INFO  Serving static file: js/chunk-vendors.7f82116b.js [waitress-2]
2025-07-07 11:53:39  web_server.py 78: INFO  Serving request for path: js/app.524f12c8.js [waitress-3]
2025-07-07 11:53:39  web_server.py 78: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-1]
2025-07-07 11:53:39  web_server.py 78: INFO  Serving request for path: css/app.2999c538.css [waitress-0]
2025-07-07 11:53:39  web_server.py 81: INFO  Serving static file: js/app.524f12c8.js [waitress-3]
2025-07-07 11:53:39  web_server.py 81: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-1]
2025-07-07 11:53:39  web_server.py 81: INFO  Serving static file: css/app.2999c538.css [waitress-0]
2025-07-07 11:53:39  web_server.py 78: INFO  Serving request for path: css/764.d3c65090.css [waitress-2]
2025-07-07 11:53:39  web_server.py 81: INFO  Serving static file: css/764.d3c65090.css [waitress-2]
2025-07-07 11:53:39  web_server.py 78: INFO  Serving request for path: js/764.932c02bf.js [waitress-3]
2025-07-07 11:53:39  web_server.py 81: INFO  Serving static file: js/764.932c02bf.js [waitress-3]
2025-07-07 11:53:39  web_server.py 78: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-1]
2025-07-07 11:53:39  web_server.py 81: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-1]
2025-07-07 11:53:39  EarlyBird.api.admin.settings 276: INFO  开始获取支付配置... [waitress-0]
2025-07-07 11:53:39  settings.py 276: INFO  开始获取支付配置... [waitress-0]
2025-07-07 11:53:39  EarlyBird.api.admin.settings 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-0]
2025-07-07 11:53:39  settings.py 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-0]
2025-07-07 11:53:39  web_server.py 78: INFO  Serving request for path: logo.png [waitress-3]
2025-07-07 11:53:39  web_server.py 81: INFO  Serving static file: logo.png [waitress-3]
2025-07-07 11:53:39  EarlyBird.api.admin.settings 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-0]
2025-07-07 11:53:39  settings.py 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-0]
2025-07-07 11:53:39  web_server.py 78: INFO  Serving request for path: admin/settings [waitress-1]
2025-07-07 11:53:39  web_server.py 84: INFO  Serving index.html for path: admin/settings [waitress-1]
2025-07-07 11:53:39  EarlyBird.api.admin.settings 276: INFO  开始获取支付配置... [waitress-2]
2025-07-07 11:53:39  settings.py 276: INFO  开始获取支付配置... [waitress-2]
2025-07-07 11:53:39  EarlyBird.api.admin.settings 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-2]
2025-07-07 11:53:39  settings.py 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-2]
2025-07-07 11:53:39  EarlyBird.api.admin.settings 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-2]
2025-07-07 11:53:39  settings.py 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-2]
2025-07-07 11:56:18  web_server.py 78: INFO  Serving request for path: admin/settings [waitress-0]
2025-07-07 11:56:18  web_server.py 84: INFO  Serving index.html for path: admin/settings [waitress-0]
2025-07-07 11:56:18  web_server.py 78: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-1]
2025-07-07 11:56:18  web_server.py 84: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-1]
2025-07-07 11:56:18  web_server.py 78: INFO  Serving request for path: js/chunk-vendors.7f82116b.js [waitress-3]
2025-07-07 11:56:18  web_server.py 78: INFO  Serving request for path: js/app.524f12c8.js [waitress-2]
2025-07-07 11:56:18  web_server.py 81: INFO  Serving static file: js/chunk-vendors.7f82116b.js [waitress-3]
2025-07-07 11:56:18  web_server.py 81: INFO  Serving static file: js/app.524f12c8.js [waitress-2]
2025-07-07 11:56:18  web_server.py 78: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-0]
2025-07-07 11:56:18  web_server.py 81: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-0]
2025-07-07 11:56:18  web_server.py 78: INFO  Serving request for path: css/app.2999c538.css [waitress-1]
2025-07-07 11:56:18  web_server.py 81: INFO  Serving static file: css/app.2999c538.css [waitress-1]
2025-07-07 11:56:18  web_server.py 78: INFO  Serving request for path: css/764.d3c65090.css [waitress-2]
2025-07-07 11:56:18  web_server.py 78: INFO  Serving request for path: js/764.932c02bf.js [waitress-0]
2025-07-07 11:56:18  web_server.py 81: INFO  Serving static file: css/764.d3c65090.css [waitress-2]
2025-07-07 11:56:18  web_server.py 81: INFO  Serving static file: js/764.932c02bf.js [waitress-0]
2025-07-07 11:56:18  web_server.py 78: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-3]
2025-07-07 11:56:18  web_server.py 81: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-3]
2025-07-07 11:56:18  web_server.py 78: INFO  Serving request for path: fonts/element-icons.ff18efd1.woff [waitress-3]
2025-07-07 11:56:18  web_server.py 81: INFO  Serving static file: fonts/element-icons.ff18efd1.woff [waitress-3]
2025-07-07 11:56:18  EarlyBird.api.admin.settings 276: INFO  开始获取支付配置... [waitress-1]
2025-07-07 11:56:18  settings.py 276: INFO  开始获取支付配置... [waitress-1]
2025-07-07 11:56:18  EarlyBird.api.admin.settings 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-1]
2025-07-07 11:56:18  settings.py 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-1]
2025-07-07 11:56:18  EarlyBird.api.admin.settings 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-1]
2025-07-07 11:56:18  settings.py 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-1]
2025-07-07 11:56:18  web_server.py 78: INFO  Serving request for path: logo.png [waitress-0]
2025-07-07 11:56:18  web_server.py 81: INFO  Serving static file: logo.png [waitress-0]
2025-07-07 11:56:19  web_server.py 78: INFO  Serving request for path: admin/settings [waitress-2]
2025-07-07 11:56:19  web_server.py 84: INFO  Serving index.html for path: admin/settings [waitress-2]
2025-07-07 11:56:19  web_server.py 78: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-3]
2025-07-07 11:56:19  web_server.py 84: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-3]
2025-07-07 11:56:19  web_server.py 78: INFO  Serving request for path: js/chunk-vendors.7f82116b.js [waitress-1]
2025-07-07 11:56:19  web_server.py 81: INFO  Serving static file: js/chunk-vendors.7f82116b.js [waitress-1]
2025-07-07 11:56:19  web_server.py 78: INFO  Serving request for path: js/app.524f12c8.js [waitress-0]
2025-07-07 11:56:19  web_server.py 81: INFO  Serving static file: js/app.524f12c8.js [waitress-0]
2025-07-07 11:56:19  web_server.py 78: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-2]
2025-07-07 11:56:19  web_server.py 78: INFO  Serving request for path: css/app.2999c538.css [waitress-3]
2025-07-07 11:56:19  web_server.py 81: INFO  Serving static file: css/app.2999c538.css [waitress-3]
2025-07-07 11:56:19  web_server.py 81: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-2]
2025-07-07 11:56:19  web_server.py 78: INFO  Serving request for path: css/764.d3c65090.css [waitress-1]
2025-07-07 11:56:19  web_server.py 78: INFO  Serving request for path: js/764.932c02bf.js [waitress-0]
2025-07-07 11:56:19  web_server.py 81: INFO  Serving static file: css/764.d3c65090.css [waitress-1]
2025-07-07 11:56:19  web_server.py 81: INFO  Serving static file: js/764.932c02bf.js [waitress-0]
2025-07-07 11:56:19  web_server.py 78: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-3]
2025-07-07 11:56:19  web_server.py 81: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-3]
2025-07-07 11:56:19  EarlyBird.api.admin.settings 276: INFO  开始获取支付配置... [waitress-2]
2025-07-07 11:56:19  settings.py 276: INFO  开始获取支付配置... [waitress-2]
2025-07-07 11:56:19  EarlyBird.api.admin.settings 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-2]
2025-07-07 11:56:19  settings.py 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-2]
2025-07-07 11:56:19  EarlyBird.api.admin.settings 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-2]
2025-07-07 11:56:19  settings.py 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-2]
2025-07-07 11:56:19  web_server.py 78: INFO  Serving request for path: logo.png [waitress-0]
2025-07-07 11:56:19  web_server.py 81: INFO  Serving static file: logo.png [waitress-0]
2025-07-07 11:56:20  web_server.py 78: INFO  Serving request for path: admin/settings [waitress-3]
2025-07-07 11:56:20  web_server.py 84: INFO  Serving index.html for path: admin/settings [waitress-3]
2025-07-07 11:56:20  web_server.py 78: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-1]
2025-07-07 11:56:20  web_server.py 84: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-1]
2025-07-07 11:56:20  web_server.py 78: INFO  Serving request for path: js/chunk-vendors.7f82116b.js [waitress-2]
2025-07-07 11:56:20  web_server.py 81: INFO  Serving static file: js/chunk-vendors.7f82116b.js [waitress-2]
2025-07-07 11:56:20  web_server.py 78: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-0]
2025-07-07 11:56:20  web_server.py 78: INFO  Serving request for path: css/app.2999c538.css [waitress-3]
2025-07-07 11:56:20  web_server.py 78: INFO  Serving request for path: js/app.524f12c8.js [waitress-1]
2025-07-07 11:56:20  web_server.py 81: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-0]
2025-07-07 11:56:20  web_server.py 81: INFO  Serving static file: css/app.2999c538.css [waitress-3]
2025-07-07 11:56:20  web_server.py 81: INFO  Serving static file: js/app.524f12c8.js [waitress-1]
2025-07-07 11:56:20  web_server.py 78: INFO  Serving request for path: css/764.d3c65090.css [waitress-2]
2025-07-07 11:56:20  web_server.py 81: INFO  Serving static file: css/764.d3c65090.css [waitress-2]
2025-07-07 11:56:20  web_server.py 78: INFO  Serving request for path: js/764.932c02bf.js [waitress-0]
2025-07-07 11:56:20  web_server.py 81: INFO  Serving static file: js/764.932c02bf.js [waitress-0]
2025-07-07 11:56:20  web_server.py 78: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-1]
2025-07-07 11:56:20  web_server.py 81: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-1]
2025-07-07 11:56:20  EarlyBird.api.admin.settings 276: INFO  开始获取支付配置... [waitress-3]
2025-07-07 11:56:20  settings.py 276: INFO  开始获取支付配置... [waitress-3]
2025-07-07 11:56:20  EarlyBird.api.admin.settings 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-3]
2025-07-07 11:56:20  settings.py 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-3]
2025-07-07 11:56:20  web_server.py 78: INFO  Serving request for path: logo.png [waitress-0]
2025-07-07 11:56:20  web_server.py 81: INFO  Serving static file: logo.png [waitress-0]
2025-07-07 11:56:20  EarlyBird.api.admin.settings 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-3]
2025-07-07 11:56:20  settings.py 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-3]
2025-07-07 11:56:20  web_server.py 78: INFO  Serving request for path: admin/settings [waitress-1]
2025-07-07 11:56:20  web_server.py 84: INFO  Serving index.html for path: admin/settings [waitress-1]
2025-07-07 11:56:20  EarlyBird.api.admin.settings 276: INFO  开始获取支付配置... [waitress-2]
2025-07-07 11:56:20  settings.py 276: INFO  开始获取支付配置... [waitress-2]
2025-07-07 11:56:20  EarlyBird.api.admin.settings 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-2]
2025-07-07 11:56:20  settings.py 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-2]
2025-07-07 11:56:20  EarlyBird.api.admin.settings 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-2]
2025-07-07 11:56:20  settings.py 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-2]
2025-07-07 11:56:21  web_server.py 78: INFO  Serving request for path: admin/settings [waitress-3]
2025-07-07 11:56:21  web_server.py 84: INFO  Serving index.html for path: admin/settings [waitress-3]
2025-07-07 11:56:21  web_server.py 78: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-1]
2025-07-07 11:56:21  web_server.py 84: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-1]
2025-07-07 11:56:21  web_server.py 78: INFO  Serving request for path: js/chunk-vendors.7f82116b.js [waitress-0]
2025-07-07 11:56:21  web_server.py 81: INFO  Serving static file: js/chunk-vendors.7f82116b.js [waitress-0]
2025-07-07 11:56:21  web_server.py 78: INFO  Serving request for path: js/app.524f12c8.js [waitress-2]
2025-07-07 11:56:21  web_server.py 81: INFO  Serving static file: js/app.524f12c8.js [waitress-2]
2025-07-07 11:56:21  web_server.py 78: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-2]
2025-07-07 11:56:21  web_server.py 78: INFO  Serving request for path: css/app.2999c538.css [waitress-1]
2025-07-07 11:56:21  web_server.py 81: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-2]
2025-07-07 11:56:21  web_server.py 81: INFO  Serving static file: css/app.2999c538.css [waitress-1]
2025-07-07 11:56:21  web_server.py 78: INFO  Serving request for path: css/764.d3c65090.css [waitress-0]
2025-07-07 11:56:21  web_server.py 81: INFO  Serving static file: css/764.d3c65090.css [waitress-0]
2025-07-07 11:56:21  web_server.py 78: INFO  Serving request for path: js/764.932c02bf.js [waitress-3]
2025-07-07 11:56:21  web_server.py 81: INFO  Serving static file: js/764.932c02bf.js [waitress-3]
2025-07-07 11:56:21  web_server.py 78: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-2]
2025-07-07 11:56:21  web_server.py 81: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-2]
2025-07-07 11:56:21  EarlyBird.api.admin.settings 276: INFO  开始获取支付配置... [waitress-1]
2025-07-07 11:56:21  settings.py 276: INFO  开始获取支付配置... [waitress-1]
2025-07-07 11:56:21  EarlyBird.api.admin.settings 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-1]
2025-07-07 11:56:21  settings.py 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-1]
2025-07-07 11:56:21  web_server.py 78: INFO  Serving request for path: logo.png [waitress-3]
2025-07-07 11:56:21  EarlyBird.api.admin.settings 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-1]
2025-07-07 11:56:21  web_server.py 81: INFO  Serving static file: logo.png [waitress-3]
2025-07-07 11:56:21  settings.py 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-1]
2025-07-07 12:50:09  web_server.py 78: INFO  Serving request for path: admin/settings [waitress-2]
2025-07-07 12:50:09  web_server.py 84: INFO  Serving index.html for path: admin/settings [waitress-2]
2025-07-07 12:50:09  web_server.py 78: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-0]
2025-07-07 12:50:09  web_server.py 84: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-0]
2025-07-07 12:50:09  web_server.py 78: INFO  Serving request for path: js/chunk-vendors.7f82116b.js [waitress-3]
2025-07-07 12:50:09  web_server.py 78: INFO  Serving request for path: js/app.524f12c8.js [waitress-1]
2025-07-07 12:50:09  web_server.py 81: INFO  Serving static file: js/chunk-vendors.7f82116b.js [waitress-3]
2025-07-07 12:50:09  web_server.py 78: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-2]
2025-07-07 12:50:09  web_server.py 81: INFO  Serving static file: js/app.524f12c8.js [waitress-1]
2025-07-07 12:50:09  web_server.py 78: INFO  Serving request for path: css/app.2999c538.css [waitress-0]
2025-07-07 12:50:09  web_server.py 81: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-2]
2025-07-07 12:50:09  web_server.py 81: INFO  Serving static file: css/app.2999c538.css [waitress-0]
2025-07-07 12:50:09  web_server.py 78: INFO  Serving request for path: css/764.d3c65090.css [waitress-1]
2025-07-07 12:50:09  web_server.py 78: INFO  Serving request for path: js/764.932c02bf.js [waitress-2]
2025-07-07 12:50:09  web_server.py 81: INFO  Serving static file: css/764.d3c65090.css [waitress-1]
2025-07-07 12:50:09  web_server.py 81: INFO  Serving static file: js/764.932c02bf.js [waitress-2]
2025-07-07 12:50:09  web_server.py 78: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-3]
2025-07-07 12:50:09  web_server.py 81: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-3]
2025-07-07 12:50:09  EarlyBird.api.admin.settings 276: INFO  开始获取支付配置... [waitress-0]
2025-07-07 12:50:09  settings.py 276: INFO  开始获取支付配置... [waitress-0]
2025-07-07 12:50:09  EarlyBird.api.admin.settings 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-0]
2025-07-07 12:50:09  web_server.py 78: INFO  Serving request for path: fonts/element-icons.ff18efd1.woff [waitress-2]
2025-07-07 12:50:09  settings.py 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-0]
2025-07-07 12:50:09  web_server.py 81: INFO  Serving static file: fonts/element-icons.ff18efd1.woff [waitress-2]
2025-07-07 12:50:09  EarlyBird.api.admin.settings 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-0]
2025-07-07 12:50:09  settings.py 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-0]
2025-07-07 12:50:09  web_server.py 78: INFO  Serving request for path: logo.png [waitress-3]
2025-07-07 12:50:09  web_server.py 81: INFO  Serving static file: logo.png [waitress-3]
2025-07-07 12:50:11  web_server.py 78: INFO  Serving request for path: admin/settings [waitress-1]
2025-07-07 12:50:11  web_server.py 84: INFO  Serving index.html for path: admin/settings [waitress-1]
2025-07-07 12:50:11  web_server.py 78: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-2]
2025-07-07 12:50:11  web_server.py 84: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-2]
2025-07-07 12:50:11  web_server.py 78: INFO  Serving request for path: js/chunk-vendors.7f82116b.js [waitress-0]
2025-07-07 12:50:11  web_server.py 78: INFO  Serving request for path: js/app.524f12c8.js [waitress-3]
2025-07-07 12:50:11  web_server.py 81: INFO  Serving static file: js/chunk-vendors.7f82116b.js [waitress-0]
2025-07-07 12:50:11  web_server.py 78: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-1]
2025-07-07 12:50:11  web_server.py 81: INFO  Serving static file: js/app.524f12c8.js [waitress-3]
2025-07-07 12:50:11  web_server.py 81: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-1]
2025-07-07 12:50:11  web_server.py 78: INFO  Serving request for path: css/app.2999c538.css [waitress-2]
2025-07-07 12:50:11  web_server.py 81: INFO  Serving static file: css/app.2999c538.css [waitress-2]
2025-07-07 12:50:12  web_server.py 78: INFO  Serving request for path: css/764.d3c65090.css [waitress-0]
2025-07-07 12:50:12  web_server.py 78: INFO  Serving request for path: js/764.932c02bf.js [waitress-3]
2025-07-07 12:50:12  web_server.py 81: INFO  Serving static file: css/764.d3c65090.css [waitress-0]
2025-07-07 12:50:12  web_server.py 81: INFO  Serving static file: js/764.932c02bf.js [waitress-3]
2025-07-07 12:50:12  web_server.py 78: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-2]
2025-07-07 12:50:12  web_server.py 81: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-2]
2025-07-07 12:50:12  EarlyBird.api.admin.settings 276: INFO  开始获取支付配置... [waitress-1]
2025-07-07 12:50:12  settings.py 276: INFO  开始获取支付配置... [waitress-1]
2025-07-07 12:50:12  EarlyBird.api.admin.settings 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-1]
2025-07-07 12:50:12  settings.py 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-1]
2025-07-07 12:50:12  EarlyBird.api.admin.settings 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-1]
2025-07-07 12:50:12  settings.py 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-1]
2025-07-07 12:50:12  web_server.py 78: INFO  Serving request for path: logo.png [waitress-3]
2025-07-07 12:50:12  web_server.py 81: INFO  Serving static file: logo.png [waitress-3]
2025-07-07 14:55:18  task_paper_generator.py 51: WARNING  数据库操作失败 (尝试 1/3): (pymysql.err.OperationalError) (2013, 'Lost connection to MySQL server during query ([WinError 10053] 你的主机中的软件中止了一个已建立的连接。)')
[SQL: SELECT earlybird_paper_generate_task.id AS earlybird_paper_generate_task_id, earlybird_paper_generate_task.create_time AS earlybird_paper_generate_task_create_time, earlybird_paper_generate_task.update_time AS earlybird_paper_generate_task_update_time, earlybird_paper_generate_task.is_deleted AS earlybird_paper_generate_task_is_deleted, earlybird_paper_generate_task.uid AS earlybird_paper_generate_task_uid, earlybird_paper_generate_task.`thesisId` AS `earlybird_paper_generate_task_thesisId`, earlybird_paper_generate_task.status AS earlybird_paper_generate_task_status, earlybird_paper_generate_task.msg AS earlybird_paper_generate_task_msg, earlybird_paper_generate_task.progress AS earlybird_paper_generate_task_progress 
FROM earlybird_paper_generate_task 
WHERE earlybird_paper_generate_task.status = %(status_1)s ORDER BY earlybird_paper_generate_task.id ASC 
 LIMIT %(param_1)s]
[parameters: {'status_1': 1, 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/14/e3q8) [Thread-2]
2025-07-07 14:55:18  task_paper_generator.py 56: INFO  数据库会话已回滚 [Thread-2]
2025-07-07 16:13:21  web_server.py 78: INFO  Serving request for path: admin/settings [waitress-2]
2025-07-07 16:13:21  web_server.py 84: INFO  Serving index.html for path: admin/settings [waitress-2]
2025-07-07 16:13:21  web_server.py 78: INFO  Serving request for path: js/chunk-vendors.7f82116b.js [waitress-0]
2025-07-07 16:13:21  web_server.py 78: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-1]
2025-07-07 16:13:21  web_server.py 84: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-1]
2025-07-07 16:13:21  web_server.py 81: INFO  Serving static file: js/chunk-vendors.7f82116b.js [waitress-0]
2025-07-07 16:13:21  web_server.py 78: INFO  Serving request for path: js/app.5dc6c9e8.js [waitress-3]
2025-07-07 16:13:21  web_server.py 81: INFO  Serving static file: js/app.5dc6c9e8.js [waitress-3]
2025-07-07 16:13:21  web_server.py 78: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-2]
2025-07-07 16:13:21  web_server.py 78: INFO  Serving request for path: css/app.2999c538.css [waitress-1]
2025-07-07 16:13:21  web_server.py 81: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-2]
2025-07-07 16:13:21  web_server.py 81: INFO  Serving static file: css/app.2999c538.css [waitress-1]
2025-07-07 16:13:21  web_server.py 78: INFO  Serving request for path: css/164.e09272c3.css [waitress-0]
2025-07-07 16:13:21  web_server.py 78: INFO  Serving request for path: js/164.042bb7a4.js [waitress-3]
2025-07-07 16:13:21  web_server.py 81: INFO  Serving static file: css/164.e09272c3.css [waitress-0]
2025-07-07 16:13:21  web_server.py 81: INFO  Serving static file: js/164.042bb7a4.js [waitress-3]
2025-07-07 16:13:21  web_server.py 78: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-2]
2025-07-07 16:13:21  web_server.py 81: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-2]
2025-07-07 16:13:21  EarlyBird.api.admin.auth 37: WARNING  Token已过期 [waitress-0]
2025-07-07 16:13:21  web_server.py 78: INFO  Serving request for path: fonts/element-icons.ff18efd1.woff [waitress-3]
2025-07-07 16:13:21  EarlyBird.api.admin.auth 37: WARNING  Token已过期 [waitress-2]
2025-07-07 16:13:21  auth.py 37: WARNING  Token已过期 [waitress-0]
2025-07-07 16:13:21  auth.py 37: WARNING  Token已过期 [waitress-2]
2025-07-07 16:13:21  EarlyBird.ExtendRegister.hook_register 92: WARNING  管理员API Token无效: /api/admin/settings/payment/config [waitress-0]
2025-07-07 16:13:21  EarlyBird.ExtendRegister.hook_register 92: WARNING  管理员API Token无效: /api/admin/auth/profile [waitress-2]
2025-07-07 16:13:21  web_server.py 81: INFO  Serving static file: fonts/element-icons.ff18efd1.woff [waitress-3]
2025-07-07 16:13:21  hook_register.py 92: WARNING  管理员API Token无效: /api/admin/settings/payment/config [waitress-0]
2025-07-07 16:13:21  hook_register.py 92: WARNING  管理员API Token无效: /api/admin/auth/profile [waitress-2]
2025-07-07 16:13:21  web_server.py 78: INFO  Serving request for path: js/318.f0e0c059.js [waitress-2]
2025-07-07 16:13:21  web_server.py 78: INFO  Serving request for path: css/318.61ca778d.css [waitress-0]
2025-07-07 16:13:21  web_server.py 81: INFO  Serving static file: js/318.f0e0c059.js [waitress-2]
2025-07-07 16:13:21  web_server.py 81: INFO  Serving static file: css/318.61ca778d.css [waitress-0]
2025-07-07 16:13:21  EarlyBird.api.admin.auth 37: WARNING  Token已过期 [waitress-3]
2025-07-07 16:13:21  auth.py 37: WARNING  Token已过期 [waitress-3]
2025-07-07 16:13:21  EarlyBird.api.admin.auth 77: WARNING  管理员API Token无效: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:21  auth.py 77: WARNING  管理员API Token无效: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:21  EarlyBird.api.admin.auth 37: WARNING  Token已过期 [waitress-1]
2025-07-07 16:13:21  auth.py 37: WARNING  Token已过期 [waitress-1]
2025-07-07 16:13:21  EarlyBird.ExtendRegister.hook_register 92: WARNING  管理员API Token无效: /api/admin/auth/profile [waitress-1]
2025-07-07 16:13:21  hook_register.py 92: WARNING  管理员API Token无效: /api/admin/auth/profile [waitress-1]
2025-07-07 16:13:21  EarlyBird.api.admin.auth 37: WARNING  Token已过期 [waitress-2]
2025-07-07 16:13:21  EarlyBird.api.admin.auth 37: WARNING  Token已过期 [waitress-0]
2025-07-07 16:13:21  auth.py 37: WARNING  Token已过期 [waitress-2]
2025-07-07 16:13:21  auth.py 37: WARNING  Token已过期 [waitress-0]
2025-07-07 16:13:21  EarlyBird.api.admin.auth 77: WARNING  管理员API Token无效: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:21  EarlyBird.ExtendRegister.hook_register 92: WARNING  管理员API Token无效: /api/admin/auth/profile [waitress-0]
2025-07-07 16:13:21  auth.py 77: WARNING  管理员API Token无效: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:21  hook_register.py 92: WARNING  管理员API Token无效: /api/admin/auth/profile [waitress-0]
2025-07-07 16:13:21  web_server.py 78: INFO  Serving request for path: logo.png [waitress-3]
2025-07-07 16:13:21  web_server.py 81: INFO  Serving static file: logo.png [waitress-3]
2025-07-07 16:13:21  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:21  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:21  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:21  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:21  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:21  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:21  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:21  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:21  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:21  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:21  EarlyBird.ExtendRegister.hook_register 73: WARNING  管理员API未提供Authorization头: /api/admin/stats/overview [waitress-2]
2025-07-07 16:13:21  EarlyBird.ExtendRegister.hook_register 73: WARNING  管理员API未提供Authorization头: /api/admin/stats/users [waitress-0]
2025-07-07 16:13:21  hook_register.py 73: WARNING  管理员API未提供Authorization头: /api/admin/stats/overview [waitress-2]
2025-07-07 16:13:21  hook_register.py 73: WARNING  管理员API未提供Authorization头: /api/admin/stats/users [waitress-0]
2025-07-07 16:13:21  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:21  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:21  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:21  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:21  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:21  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:21  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:21  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:21  EarlyBird.ExtendRegister.hook_register 73: WARNING  管理员API未提供Authorization头: /api/admin/stats/overview [waitress-3]
2025-07-07 16:13:21  EarlyBird.ExtendRegister.hook_register 73: WARNING  管理员API未提供Authorization头: /api/admin/stats/users [waitress-1]
2025-07-07 16:13:21  hook_register.py 73: WARNING  管理员API未提供Authorization头: /api/admin/stats/overview [waitress-3]
2025-07-07 16:13:21  hook_register.py 73: WARNING  管理员API未提供Authorization头: /api/admin/stats/users [waitress-1]
2025-07-07 16:13:21  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:21  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:21  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:21  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:21  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:21  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:21  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:21  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:21  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:21  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:21  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:21  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:21  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:21  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:21  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:21  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:21  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:21  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:21  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:21  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:21  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:21  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:21  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:21  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:21  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:21  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:21  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:21  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:21  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:21  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:21  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:21  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:21  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:21  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:21  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:21  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:21  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:21  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:21  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:21  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:21  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:21  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:21  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:21  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:21  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:21  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:21  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:21  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:21  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:21  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:21  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:21  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:22  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:22  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:23  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:23  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:23  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:23  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:23  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:23  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:23  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:23  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:23  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:23  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:23  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:23  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:23  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:23  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:23  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:23  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:23  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:23  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:23  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:23  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-2]
2025-07-07 16:13:23  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:23  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:23  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:23  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:23  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:23  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:23  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:23  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:23  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:23  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:23  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:23  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:23  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:23  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:23  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:23  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:23  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:23  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:23  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:23  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:23  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:23  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:23  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:23  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:23  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:23  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:23  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:23  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:23  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:23  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:23  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:23  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:23  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:23  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:23  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:23  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:23  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:23  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:23  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:23  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:23  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:23  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:23  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:23  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:23  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:23  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:23  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:23  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:23  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:23  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:23  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:23  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:23  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:23  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:23  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:23  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:23  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:23  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-3]
2025-07-07 16:13:23  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:23  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-1]
2025-07-07 16:13:23  EarlyBird.api.admin.auth 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:23  auth.py 63: WARNING  管理员API未提供Authorization头: /api/admin/auth/logout [waitress-0]
2025-07-07 16:13:29  EarlyBird.api.admin.settings 276: INFO  开始获取支付配置... [waitress-2]
2025-07-07 16:13:29  settings.py 276: INFO  开始获取支付配置... [waitress-2]
2025-07-07 16:13:29  EarlyBird.api.admin.settings 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-2]
2025-07-07 16:13:29  settings.py 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-2]
2025-07-07 16:13:29  EarlyBird.api.admin.settings 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-2]
2025-07-07 16:13:29  settings.py 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-2]
2025-07-07 16:13:31  web_server.py 78: INFO  Serving request for path: admin/settings [waitress-0]
2025-07-07 16:13:31  web_server.py 84: INFO  Serving index.html for path: admin/settings [waitress-0]
2025-07-07 16:13:31  web_server.py 78: INFO  Serving request for path: js/chunk-vendors.7f82116b.js [waitress-3]
2025-07-07 16:13:31  web_server.py 81: INFO  Serving static file: js/chunk-vendors.7f82116b.js [waitress-3]
2025-07-07 16:13:31  web_server.py 78: INFO  Serving request for path: js/app.5dc6c9e8.js [waitress-1]
2025-07-07 16:13:31  web_server.py 78: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-2]
2025-07-07 16:13:31  web_server.py 81: INFO  Serving static file: js/app.5dc6c9e8.js [waitress-1]
2025-07-07 16:13:31  web_server.py 81: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-2]
2025-07-07 16:13:31  web_server.py 78: INFO  Serving request for path: css/app.2999c538.css [waitress-0]
2025-07-07 16:13:31  web_server.py 81: INFO  Serving static file: css/app.2999c538.css [waitress-0]
2025-07-07 16:13:31  web_server.py 78: INFO  Serving request for path: css/164.e09272c3.css [waitress-3]
2025-07-07 16:13:31  web_server.py 81: INFO  Serving static file: css/164.e09272c3.css [waitress-3]
2025-07-07 16:13:31  web_server.py 78: INFO  Serving request for path: js/164.042bb7a4.js [waitress-2]
2025-07-07 16:13:31  web_server.py 81: INFO  Serving static file: js/164.042bb7a4.js [waitress-2]
2025-07-07 16:13:31  web_server.py 78: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-1]
2025-07-07 16:13:31  web_server.py 81: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-1]
2025-07-07 16:13:31  EarlyBird.api.admin.settings 276: INFO  开始获取支付配置... [waitress-3]
2025-07-07 16:13:31  settings.py 276: INFO  开始获取支付配置... [waitress-3]
2025-07-07 16:13:31  EarlyBird.api.admin.settings 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-3]
2025-07-07 16:13:31  settings.py 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-3]
2025-07-07 16:13:31  EarlyBird.api.admin.settings 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-3]
2025-07-07 16:13:31  web_server.py 78: INFO  Serving request for path: logo.png [waitress-1]
2025-07-07 16:13:31  settings.py 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-3]
2025-07-07 16:13:31  web_server.py 81: INFO  Serving static file: logo.png [waitress-1]
2025-07-07 16:13:33  web_server.py 78: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-0]
2025-07-07 16:13:33  web_server.py 78: INFO  Serving request for path: css/app.2999c538.css [waitress-2]
2025-07-07 16:13:33  web_server.py 78: INFO  Serving request for path: css/164.e09272c3.css [waitress-3]
2025-07-07 16:13:33  web_server.py 81: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-0]
2025-07-07 16:13:33  web_server.py 81: INFO  Serving static file: css/app.2999c538.css [waitress-2]
2025-07-07 16:13:33  web_server.py 81: INFO  Serving static file: css/164.e09272c3.css [waitress-3]
2025-07-07 16:13:33  web_server.py 78: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-1]
2025-07-07 16:13:33  web_server.py 84: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-1]
2025-07-07 16:18:33  web_server.py 78: INFO  Serving request for path: admin/settings [waitress-0]
2025-07-07 16:18:33  web_server.py 84: INFO  Serving index.html for path: admin/settings [waitress-0]
2025-07-07 16:18:33  web_server.py 78: INFO  Serving request for path: js/chunk-vendors.7f82116b.js [waitress-2]
2025-07-07 16:18:33  web_server.py 78: INFO  Serving request for path: js/app.19dadb7e.js [waitress-3]
2025-07-07 16:18:33  web_server.py 81: INFO  Serving static file: js/chunk-vendors.7f82116b.js [waitress-2]
2025-07-07 16:18:33  web_server.py 78: INFO  Serving request for path: css/app.2999c538.css [waitress-0]
2025-07-07 16:18:33  web_server.py 81: INFO  Serving static file: js/app.19dadb7e.js [waitress-3]
2025-07-07 16:18:33  web_server.py 78: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-1]
2025-07-07 16:18:33  web_server.py 81: INFO  Serving static file: css/app.2999c538.css [waitress-0]
2025-07-07 16:18:33  web_server.py 81: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-1]
2025-07-07 16:18:33  web_server.py 78: INFO  Serving request for path: css/275.08488107.css [waitress-2]
2025-07-07 16:18:33  web_server.py 78: INFO  Serving request for path: js/275.67f4e02e.js [waitress-3]
2025-07-07 16:18:33  web_server.py 81: INFO  Serving static file: css/275.08488107.css [waitress-2]
2025-07-07 16:18:33  web_server.py 81: INFO  Serving static file: js/275.67f4e02e.js [waitress-3]
2025-07-07 16:18:33  web_server.py 78: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-1]
2025-07-07 16:18:33  web_server.py 81: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-1]
2025-07-07 16:18:33  task.py 113: WARNING  Task queue depth is 1 [MainThread]
2025-07-07 16:18:33  web_server.py 78: INFO  Serving request for path: fonts/element-icons.ff18efd1.woff [waitress-1]
2025-07-07 16:18:33  EarlyBird.api.admin.settings 276: INFO  开始获取支付配置... [waitress-2]
2025-07-07 16:18:33  settings.py 276: INFO  开始获取支付配置... [waitress-2]
2025-07-07 16:18:33  web_server.py 81: INFO  Serving static file: fonts/element-icons.ff18efd1.woff [waitress-1]
2025-07-07 16:18:33  EarlyBird.api.admin.settings 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-2]
2025-07-07 16:18:33  settings.py 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-2]
2025-07-07 16:18:33  EarlyBird.api.admin.settings 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-2]
2025-07-07 16:18:33  settings.py 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-2]
2025-07-07 16:18:33  web_server.py 78: INFO  Serving request for path: logo.png [waitress-0]
2025-07-07 16:18:33  web_server.py 81: INFO  Serving static file: logo.png [waitress-0]
2025-07-07 16:19:08  web_server.py 78: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-3]
2025-07-07 16:19:08  web_server.py 78: INFO  Serving request for path: css/app.2999c538.css [waitress-1]
2025-07-07 16:19:08  web_server.py 78: INFO  Serving request for path: css/275.08488107.css [waitress-2]
2025-07-07 16:19:08  web_server.py 81: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-3]
2025-07-07 16:19:08  web_server.py 81: INFO  Serving static file: css/app.2999c538.css [waitress-1]
2025-07-07 16:19:08  web_server.py 81: INFO  Serving static file: css/275.08488107.css [waitress-2]
2025-07-07 16:19:08  web_server.py 78: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-0]
2025-07-07 16:19:08  web_server.py 84: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-0]
2025-07-07 16:20:07  web_server.py 78: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-2]
2025-07-07 16:20:07  web_server.py 84: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-2]
2025-07-07 16:20:07  web_server.py 78: INFO  Serving request for path: favicon.ico [waitress-1]
2025-07-07 16:20:07  web_server.py 84: INFO  Serving index.html for path: favicon.ico [waitress-1]
2025-07-07 16:22:28  web_server.py 78: INFO  Serving request for path: help/guide [waitress-0]
2025-07-07 16:22:28  web_server.py 84: INFO  Serving index.html for path: help/guide [waitress-0]
2025-07-07 16:22:28  web_server.py 78: INFO  Serving request for path: js/app.0d808429.js [waitress-3]
2025-07-07 16:22:28  web_server.py 81: INFO  Serving static file: js/app.0d808429.js [waitress-3]
2025-07-07 16:22:29  web_server.py 78: INFO  Serving request for path: css/689.525e30e9.css [waitress-2]
2025-07-07 16:22:29  web_server.py 78: INFO  Serving request for path: js/689.5a5914d7.js [waitress-1]
2025-07-07 16:22:29  web_server.py 81: INFO  Serving static file: css/689.525e30e9.css [waitress-2]
2025-07-07 16:22:29  web_server.py 81: INFO  Serving static file: js/689.5a5914d7.js [waitress-1]
2025-07-07 16:22:29  web_server.py 78: INFO  Serving request for path: help/guide [waitress-3]
2025-07-07 16:22:29  web_server.py 84: INFO  Serving index.html for path: help/guide [waitress-3]
2025-07-07 16:22:29  web_server.py 78: INFO  Serving request for path: js/chunk-vendors.7f82116b.js [waitress-2]
2025-07-07 16:22:29  web_server.py 81: INFO  Serving static file: js/chunk-vendors.7f82116b.js [waitress-2]
2025-07-07 16:22:29  web_server.py 78: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-1]
2025-07-07 16:22:29  web_server.py 78: INFO  Serving request for path: js/app.0d808429.js [waitress-3]
2025-07-07 16:22:29  web_server.py 78: INFO  Serving request for path: css/app.2999c538.css [waitress-0]
2025-07-07 16:22:29  web_server.py 81: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-1]
2025-07-07 16:22:29  web_server.py 81: INFO  Serving static file: js/app.0d808429.js [waitress-3]
2025-07-07 16:22:29  web_server.py 81: INFO  Serving static file: css/app.2999c538.css [waitress-0]
2025-07-07 16:22:29  web_server.py 78: INFO  Serving request for path: js/22.683bc166.js [waitress-3]
2025-07-07 16:22:29  web_server.py 81: INFO  Serving static file: js/22.683bc166.js [waitress-3]
2025-07-07 16:22:29  web_server.py 78: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-1]
2025-07-07 16:22:29  web_server.py 81: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-1]
2025-07-07 16:22:29  web_server.py 78: INFO  Serving request for path: fonts/element-icons.ff18efd1.woff [waitress-2]
2025-07-07 16:22:29  web_server.py 81: INFO  Serving static file: fonts/element-icons.ff18efd1.woff [waitress-2]
2025-07-07 16:22:29  web_server.py 78: INFO  Serving request for path: logo.png [waitress-3]
2025-07-07 16:22:29  web_server.py 81: INFO  Serving static file: logo.png [waitress-3]
2025-07-07 16:22:31  web_server.py 78: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-1]
2025-07-07 16:22:31  web_server.py 84: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-1]
2025-07-07 16:22:31  EarlyBird.api.admin.settings 276: INFO  开始获取支付配置... [waitress-0]
2025-07-07 16:22:31  settings.py 276: INFO  开始获取支付配置... [waitress-0]
2025-07-07 16:22:31  EarlyBird.api.admin.settings 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-0]
2025-07-07 16:22:31  settings.py 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-0]
2025-07-07 16:22:31  EarlyBird.api.admin.settings 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-0]
2025-07-07 16:22:31  settings.py 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-0]
2025-07-07 16:22:33  web_server.py 78: INFO  Serving request for path: admin/settings [waitress-1]
2025-07-07 16:22:33  web_server.py 84: INFO  Serving index.html for path: admin/settings [waitress-1]
2025-07-07 16:22:33  web_server.py 78: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-2]
2025-07-07 16:22:33  web_server.py 84: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-2]
2025-07-07 16:22:33  web_server.py 78: INFO  Serving request for path: js/chunk-vendors.7f82116b.js [waitress-3]
2025-07-07 16:22:33  web_server.py 81: INFO  Serving static file: js/chunk-vendors.7f82116b.js [waitress-3]
2025-07-07 16:22:33  web_server.py 78: INFO  Serving request for path: js/app.0d808429.js [waitress-0]
2025-07-07 16:22:33  web_server.py 78: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-1]
2025-07-07 16:22:33  web_server.py 81: INFO  Serving static file: js/app.0d808429.js [waitress-0]
2025-07-07 16:22:33  web_server.py 81: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-1]
2025-07-07 16:22:33  web_server.py 78: INFO  Serving request for path: css/app.2999c538.css [waitress-2]
2025-07-07 16:22:33  web_server.py 81: INFO  Serving static file: css/app.2999c538.css [waitress-2]
2025-07-07 16:22:33  web_server.py 78: INFO  Serving request for path: css/936.cbeb820c.css [waitress-3]
2025-07-07 16:22:33  web_server.py 78: INFO  Serving request for path: js/936.99b50d8f.js [waitress-0]
2025-07-07 16:22:33  web_server.py 81: INFO  Serving static file: css/936.cbeb820c.css [waitress-3]
2025-07-07 16:22:33  web_server.py 81: INFO  Serving static file: js/936.99b50d8f.js [waitress-0]
2025-07-07 16:22:33  web_server.py 78: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-1]
2025-07-07 16:22:33  web_server.py 81: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-1]
2025-07-07 16:22:33  EarlyBird.api.admin.settings 276: INFO  开始获取支付配置... [waitress-3]
2025-07-07 16:22:33  settings.py 276: INFO  开始获取支付配置... [waitress-3]
2025-07-07 16:22:33  web_server.py 78: INFO  Serving request for path: logo.png [waitress-1]
2025-07-07 16:22:33  EarlyBird.api.admin.settings 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-3]
2025-07-07 16:22:33  settings.py 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-3]
2025-07-07 16:22:33  web_server.py 81: INFO  Serving static file: logo.png [waitress-1]
2025-07-07 16:22:33  EarlyBird.api.admin.settings 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-3]
2025-07-07 16:22:33  settings.py 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-3]
2025-07-07 16:28:03  web_server.py 78: INFO  Serving request for path: admin/settings [waitress-2]
2025-07-07 16:28:03  web_server.py 84: INFO  Serving index.html for path: admin/settings [waitress-2]
2025-07-07 16:28:03  web_server.py 78: INFO  Serving request for path: js/chunk-vendors.7f82116b.js [waitress-0]
2025-07-07 16:28:03  web_server.py 81: INFO  Serving static file: js/chunk-vendors.7f82116b.js [waitress-0]
2025-07-07 16:28:03  web_server.py 78: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-1]
2025-07-07 16:28:03  web_server.py 78: INFO  Serving request for path: js/app.69d75006.js [waitress-3]
2025-07-07 16:28:03  web_server.py 84: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-1]
2025-07-07 16:28:03  web_server.py 81: INFO  Serving static file: js/app.69d75006.js [waitress-3]
2025-07-07 16:28:03  task.py 113: WARNING  Task queue depth is 1 [MainThread]
2025-07-07 16:28:03  web_server.py 78: INFO  Serving request for path: css/app.2999c538.css [waitress-1]
2025-07-07 16:28:03  web_server.py 78: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-2]
2025-07-07 16:28:03  web_server.py 81: INFO  Serving static file: css/app.2999c538.css [waitress-1]
2025-07-07 16:28:03  web_server.py 81: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-2]
2025-07-07 16:28:03  web_server.py 78: INFO  Serving request for path: css/936.cbeb820c.css [waitress-3]
2025-07-07 16:28:03  web_server.py 78: INFO  Serving request for path: js/936.99b50d8f.js [waitress-0]
2025-07-07 16:28:03  web_server.py 81: INFO  Serving static file: css/936.cbeb820c.css [waitress-3]
2025-07-07 16:28:03  web_server.py 81: INFO  Serving static file: js/936.99b50d8f.js [waitress-0]
2025-07-07 16:28:03  web_server.py 78: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-2]
2025-07-07 16:28:03  web_server.py 81: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-2]
2025-07-07 16:28:03  EarlyBird.api.admin.settings 276: INFO  开始获取支付配置... [waitress-1]
2025-07-07 16:28:03  web_server.py 78: INFO  Serving request for path: fonts/element-icons.ff18efd1.woff [waitress-2]
2025-07-07 16:28:03  settings.py 276: INFO  开始获取支付配置... [waitress-1]
2025-07-07 16:28:03  EarlyBird.api.admin.settings 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-1]
2025-07-07 16:28:03  web_server.py 81: INFO  Serving static file: fonts/element-icons.ff18efd1.woff [waitress-2]
2025-07-07 16:28:03  settings.py 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-1]
2025-07-07 16:28:03  EarlyBird.api.admin.settings 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-1]
2025-07-07 16:28:03  settings.py 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-1]
2025-07-07 16:28:03  web_server.py 78: INFO  Serving request for path: logo.png [waitress-0]
2025-07-07 16:28:03  web_server.py 81: INFO  Serving static file: logo.png [waitress-0]
2025-07-07 16:28:05  web_server.py 78: INFO  Serving request for path: admin/settings [waitress-3]
2025-07-07 16:28:05  web_server.py 84: INFO  Serving index.html for path: admin/settings [waitress-3]
2025-07-07 16:28:05  web_server.py 78: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-2]
2025-07-07 16:28:05  web_server.py 84: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-2]
2025-07-07 16:28:05  web_server.py 78: INFO  Serving request for path: js/chunk-vendors.7f82116b.js [waitress-1]
2025-07-07 16:28:05  web_server.py 78: INFO  Serving request for path: js/app.69d75006.js [waitress-0]
2025-07-07 16:28:05  web_server.py 81: INFO  Serving static file: js/chunk-vendors.7f82116b.js [waitress-1]
2025-07-07 16:28:05  web_server.py 78: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-3]
2025-07-07 16:28:05  web_server.py 81: INFO  Serving static file: js/app.69d75006.js [waitress-0]
2025-07-07 16:28:05  web_server.py 78: INFO  Serving request for path: css/app.2999c538.css [waitress-2]
2025-07-07 16:28:05  web_server.py 81: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-3]
2025-07-07 16:28:05  web_server.py 81: INFO  Serving static file: css/app.2999c538.css [waitress-2]
2025-07-07 16:28:06  web_server.py 78: INFO  Serving request for path: css/936.cbeb820c.css [waitress-1]
2025-07-07 16:28:06  web_server.py 78: INFO  Serving request for path: js/936.99b50d8f.js [waitress-0]
2025-07-07 16:28:06  web_server.py 81: INFO  Serving static file: css/936.cbeb820c.css [waitress-1]
2025-07-07 16:28:06  web_server.py 81: INFO  Serving static file: js/936.99b50d8f.js [waitress-0]
2025-07-07 16:28:06  web_server.py 78: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-3]
2025-07-07 16:28:06  web_server.py 81: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-3]
2025-07-07 16:28:06  EarlyBird.api.admin.settings 276: INFO  开始获取支付配置... [waitress-2]
2025-07-07 16:28:06  settings.py 276: INFO  开始获取支付配置... [waitress-2]
2025-07-07 16:28:06  EarlyBird.api.admin.settings 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-2]
2025-07-07 16:28:06  settings.py 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-2]
2025-07-07 16:28:06  EarlyBird.api.admin.settings 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-2]
2025-07-07 16:28:06  settings.py 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-2]
2025-07-07 16:28:06  web_server.py 78: INFO  Serving request for path: logo.png [waitress-3]
2025-07-07 16:28:06  web_server.py 81: INFO  Serving static file: logo.png [waitress-3]
2025-07-07 16:28:06  web_server.py 78: INFO  Serving request for path: admin/settings [waitress-1]
2025-07-07 16:28:06  web_server.py 84: INFO  Serving index.html for path: admin/settings [waitress-1]
2025-07-07 16:28:06  web_server.py 78: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-0]
2025-07-07 16:28:06  web_server.py 84: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-0]
2025-07-07 16:28:06  web_server.py 78: INFO  Serving request for path: js/chunk-vendors.7f82116b.js [waitress-2]
2025-07-07 16:28:06  web_server.py 81: INFO  Serving static file: js/chunk-vendors.7f82116b.js [waitress-2]
2025-07-07 16:28:06  web_server.py 78: INFO  Serving request for path: js/app.69d75006.js [waitress-2]
2025-07-07 16:28:06  web_server.py 78: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-3]
2025-07-07 16:28:06  web_server.py 81: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-3]
2025-07-07 16:28:06  web_server.py 81: INFO  Serving static file: js/app.69d75006.js [waitress-2]
2025-07-07 16:28:06  web_server.py 78: INFO  Serving request for path: css/app.2999c538.css [waitress-0]
2025-07-07 16:28:06  web_server.py 81: INFO  Serving static file: css/app.2999c538.css [waitress-0]
2025-07-07 16:28:06  EarlyBird.api.admin.settings 276: INFO  开始获取支付配置... [waitress-2]
2025-07-07 16:28:06  settings.py 276: INFO  开始获取支付配置... [waitress-2]
2025-07-07 16:28:06  EarlyBird.api.admin.settings 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-2]
2025-07-07 16:28:06  settings.py 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-2]
2025-07-07 16:28:06  EarlyBird.api.admin.settings 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-2]
2025-07-07 16:28:06  settings.py 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-2]
2025-07-07 16:28:49  web_server.py 78: INFO  Serving request for path: admin/settings [waitress-0]
2025-07-07 16:28:49  web_server.py 84: INFO  Serving index.html for path: admin/settings [waitress-0]
2025-07-07 16:28:49  web_server.py 78: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-1]
2025-07-07 16:28:49  web_server.py 84: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-1]
2025-07-07 16:28:49  web_server.py 78: INFO  Serving request for path: js/chunk-vendors.7f82116b.js [waitress-1]
2025-07-07 16:28:49  web_server.py 81: INFO  Serving static file: js/chunk-vendors.7f82116b.js [waitress-1]
2025-07-07 16:28:49  web_server.py 78: INFO  Serving request for path: js/app.69d75006.js [waitress-2]
2025-07-07 16:28:49  web_server.py 78: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-0]
2025-07-07 16:28:49  web_server.py 81: INFO  Serving static file: js/app.69d75006.js [waitress-2]
2025-07-07 16:28:49  web_server.py 78: INFO  Serving request for path: css/app.2999c538.css [waitress-3]
2025-07-07 16:28:49  web_server.py 81: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-0]
2025-07-07 16:28:49  web_server.py 81: INFO  Serving static file: css/app.2999c538.css [waitress-3]
2025-07-07 16:28:49  web_server.py 78: INFO  Serving request for path: css/936.cbeb820c.css [waitress-0]
2025-07-07 16:28:49  web_server.py 78: INFO  Serving request for path: js/936.99b50d8f.js [waitress-1]
2025-07-07 16:28:49  web_server.py 81: INFO  Serving static file: css/936.cbeb820c.css [waitress-0]
2025-07-07 16:28:49  web_server.py 81: INFO  Serving static file: js/936.99b50d8f.js [waitress-1]
2025-07-07 16:28:50  web_server.py 78: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-2]
2025-07-07 16:28:50  web_server.py 81: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-2]
2025-07-07 16:28:50  task.py 113: WARNING  Task queue depth is 1 [MainThread]
2025-07-07 16:28:50  EarlyBird.api.admin.settings 276: INFO  开始获取支付配置... [waitress-0]
2025-07-07 16:28:50  web_server.py 78: INFO  Serving request for path: fonts/element-icons.ff18efd1.woff [waitress-3]
2025-07-07 16:28:50  settings.py 276: INFO  开始获取支付配置... [waitress-0]
2025-07-07 16:28:50  EarlyBird.api.admin.settings 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-0]
2025-07-07 16:28:50  web_server.py 81: INFO  Serving static file: fonts/element-icons.ff18efd1.woff [waitress-3]
2025-07-07 16:28:50  settings.py 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-0]
2025-07-07 16:28:50  EarlyBird.api.admin.settings 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-0]
2025-07-07 16:28:50  settings.py 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-0]
2025-07-07 16:28:50  web_server.py 78: INFO  Serving request for path: logo.png [waitress-2]
2025-07-07 16:28:50  web_server.py 81: INFO  Serving static file: logo.png [waitress-2]
2025-07-07 16:29:01  web_server.py 78: INFO  Serving request for path: admin/settings [waitress-1]
2025-07-07 16:29:01  web_server.py 84: INFO  Serving index.html for path: admin/settings [waitress-1]
2025-07-07 16:29:01  web_server.py 78: INFO  Serving request for path: js/chunk-vendors.7f82116b.js [waitress-3]
2025-07-07 16:29:01  web_server.py 78: INFO  Serving request for path: js/app.69d75006.js [waitress-0]
2025-07-07 16:29:01  web_server.py 81: INFO  Serving static file: js/chunk-vendors.7f82116b.js [waitress-3]
2025-07-07 16:29:01  web_server.py 78: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-1]
2025-07-07 16:29:01  web_server.py 78: INFO  Serving request for path: css/app.2999c538.css [waitress-2]
2025-07-07 16:29:01  web_server.py 81: INFO  Serving static file: js/app.69d75006.js [waitress-0]
2025-07-07 16:29:01  web_server.py 81: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-1]
2025-07-07 16:29:01  web_server.py 81: INFO  Serving static file: css/app.2999c538.css [waitress-2]
2025-07-07 16:29:02  web_server.py 78: INFO  Serving request for path: css/936.cbeb820c.css [waitress-3]
2025-07-07 16:29:02  web_server.py 78: INFO  Serving request for path: js/936.99b50d8f.js [waitress-0]
2025-07-07 16:29:02  web_server.py 81: INFO  Serving static file: css/936.cbeb820c.css [waitress-3]
2025-07-07 16:29:02  web_server.py 81: INFO  Serving static file: js/936.99b50d8f.js [waitress-0]
2025-07-07 16:29:02  web_server.py 78: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-1]
2025-07-07 16:29:02  web_server.py 81: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-1]
2025-07-07 16:29:02  web_server.py 78: INFO  Serving request for path: logo.png [waitress-1]
2025-07-07 16:29:02  EarlyBird.api.admin.settings 276: INFO  开始获取支付配置... [waitress-3]
2025-07-07 16:29:02  web_server.py 81: INFO  Serving static file: logo.png [waitress-1]
2025-07-07 16:29:02  settings.py 276: INFO  开始获取支付配置... [waitress-3]
2025-07-07 16:29:02  EarlyBird.api.admin.settings 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-3]
2025-07-07 16:29:02  settings.py 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-3]
2025-07-07 16:29:02  EarlyBird.api.admin.settings 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-3]
2025-07-07 16:29:02  settings.py 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-3]
2025-07-07 16:31:45  web_server.py 78: INFO  Serving request for path: admin/settings [waitress-2]
2025-07-07 16:31:45  web_server.py 84: INFO  Serving index.html for path: admin/settings [waitress-2]
2025-07-07 16:31:45  web_server.py 78: INFO  Serving request for path: js/chunk-vendors.7f82116b.js [waitress-0]
2025-07-07 16:31:45  web_server.py 81: INFO  Serving static file: js/chunk-vendors.7f82116b.js [waitress-0]
2025-07-07 16:31:45  web_server.py 78: INFO  Serving request for path: js/app.a08b152e.js [waitress-1]
2025-07-07 16:31:45  web_server.py 78: INFO  Serving request for path: css/app.6d3395f4.css [waitress-3]
2025-07-07 16:31:45  web_server.py 81: INFO  Serving static file: js/app.a08b152e.js [waitress-1]
2025-07-07 16:31:45  web_server.py 81: INFO  Serving static file: css/app.6d3395f4.css [waitress-3]
2025-07-07 16:31:45  web_server.py 78: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-2]
2025-07-07 16:31:45  web_server.py 81: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-2]
2025-07-07 16:31:45  web_server.py 78: INFO  Serving request for path: css/936.cbeb820c.css [waitress-0]
2025-07-07 16:31:45  web_server.py 78: INFO  Serving request for path: js/936.99b50d8f.js [waitress-1]
2025-07-07 16:31:45  web_server.py 81: INFO  Serving static file: css/936.cbeb820c.css [waitress-0]
2025-07-07 16:31:45  web_server.py 81: INFO  Serving static file: js/936.99b50d8f.js [waitress-1]
2025-07-07 16:31:45  web_server.py 78: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-3]
2025-07-07 16:31:45  web_server.py 81: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-3]
2025-07-07 16:31:45  web_server.py 78: INFO  Serving request for path: fonts/element-icons.ff18efd1.woff [waitress-3]
2025-07-07 16:31:45  EarlyBird.api.admin.settings 276: INFO  开始获取支付配置... [waitress-0]
2025-07-07 16:31:45  web_server.py 81: INFO  Serving static file: fonts/element-icons.ff18efd1.woff [waitress-3]
2025-07-07 16:31:45  settings.py 276: INFO  开始获取支付配置... [waitress-0]
2025-07-07 16:31:45  EarlyBird.api.admin.settings 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-0]
2025-07-07 16:31:45  settings.py 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-0]
2025-07-07 16:31:45  EarlyBird.api.admin.settings 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-0]
2025-07-07 16:31:45  settings.py 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-0]
2025-07-07 16:31:46  web_server.py 78: INFO  Serving request for path: logo.png [waitress-2]
2025-07-07 16:31:46  web_server.py 81: INFO  Serving static file: logo.png [waitress-2]
2025-07-07 16:31:48  web_server.py 78: INFO  Serving request for path: js/388.6189b2b6.js [waitress-1]
2025-07-07 16:31:48  web_server.py 81: INFO  Serving static file: js/388.6189b2b6.js [waitress-1]
2025-07-07 16:31:49  web_server.py 78: INFO  Serving request for path: css/8.38fdf013.css [waitress-0]
2025-07-07 16:31:49  web_server.py 78: INFO  Serving request for path: js/8.a13b62ce.js [waitress-3]
2025-07-07 16:31:49  web_server.py 81: INFO  Serving static file: css/8.38fdf013.css [waitress-0]
2025-07-07 16:31:49  web_server.py 81: INFO  Serving static file: js/8.a13b62ce.js [waitress-3]
2025-07-07 16:31:52  EarlyBird.api.admin.settings 276: INFO  开始获取支付配置... [waitress-3]
2025-07-07 16:31:52  settings.py 276: INFO  开始获取支付配置... [waitress-3]
2025-07-07 16:31:52  EarlyBird.api.admin.settings 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-3]
2025-07-07 16:31:52  settings.py 284: INFO  管理员 admin(ID:1) 正在获取支付配置 [waitress-3]
2025-07-07 16:31:52  EarlyBird.api.admin.settings 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-3]
2025-07-07 16:31:52  settings.py 405: INFO  支付配置获取成功，wxpay字段包含 8 个配置项 [waitress-3]
2025-07-07 16:35:03  web_server.py 78: INFO  Serving request for path: admin/settings/model [waitress-2]
2025-07-07 16:35:03  web_server.py 84: INFO  Serving index.html for path: admin/settings/model [waitress-2]
2025-07-07 16:35:03  web_server.py 78: INFO  Serving request for path: js/chunk-vendors.7f82116b.js [waitress-1]
2025-07-07 16:35:03  web_server.py 78: INFO  Serving request for path: js/app.10d7be2c.js [waitress-3]
2025-07-07 16:35:03  web_server.py 81: INFO  Serving static file: js/chunk-vendors.7f82116b.js [waitress-1]
2025-07-07 16:35:03  web_server.py 81: INFO  Serving static file: js/app.10d7be2c.js [waitress-3]
2025-07-07 16:35:03  web_server.py 78: INFO  Serving request for path: css/app.2ea35194.css [waitress-0]
2025-07-07 16:35:03  web_server.py 78: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-2]
2025-07-07 16:35:03  web_server.py 81: INFO  Serving static file: css/app.2ea35194.css [waitress-0]
2025-07-07 16:35:03  web_server.py 81: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-2]
2025-07-07 16:35:03  web_server.py 78: INFO  Serving request for path: css/8.38fdf013.css [waitress-3]
2025-07-07 16:35:03  web_server.py 78: INFO  Serving request for path: js/8.a13b62ce.js [waitress-1]
2025-07-07 16:35:03  web_server.py 81: INFO  Serving static file: css/8.38fdf013.css [waitress-3]
2025-07-07 16:35:03  web_server.py 81: INFO  Serving static file: js/8.a13b62ce.js [waitress-1]
2025-07-07 16:35:03  web_server.py 78: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-2]
2025-07-07 16:35:03  web_server.py 81: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-2]
2025-07-07 16:35:03  web_server.py 78: INFO  Serving request for path: fonts/element-icons.ff18efd1.woff [waitress-1]
2025-07-07 16:35:03  web_server.py 81: INFO  Serving static file: fonts/element-icons.ff18efd1.woff [waitress-1]
2025-07-07 16:35:03  web_server.py 78: INFO  Serving request for path: logo.png [waitress-2]
2025-07-07 16:35:03  web_server.py 81: INFO  Serving static file: logo.png [waitress-2]
2025-07-07 16:35:05  web_server.py 78: INFO  Serving request for path: css/859.d8dc276c.css [waitress-0]
2025-07-07 16:35:05  web_server.py 78: INFO  Serving request for path: js/859.1a69a32e.js [waitress-3]
2025-07-07 16:35:05  web_server.py 81: INFO  Serving static file: css/859.d8dc276c.css [waitress-0]
2025-07-07 16:35:05  web_server.py 81: INFO  Serving static file: js/859.1a69a32e.js [waitress-3]
2025-07-07 16:35:07  web_server.py 78: INFO  Serving request for path: js/388.6189b2b6.js [waitress-1]
2025-07-07 16:35:07  web_server.py 81: INFO  Serving static file: js/388.6189b2b6.js [waitress-1]
2025-07-07 16:35:12  web_server.py 78: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-0]
2025-07-07 16:35:12  web_server.py 78: INFO  Serving request for path: css/app.2ea35194.css [waitress-1]
2025-07-07 16:35:12  web_server.py 78: INFO  Serving request for path: css/859.d8dc276c.css [waitress-2]
2025-07-07 16:35:12  web_server.py 78: INFO  Serving request for path: css/8.38fdf013.css [waitress-3]
2025-07-07 16:35:12  web_server.py 81: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-0]
2025-07-07 16:35:12  web_server.py 81: INFO  Serving static file: css/app.2ea35194.css [waitress-1]
2025-07-07 16:35:12  web_server.py 81: INFO  Serving static file: css/859.d8dc276c.css [waitress-2]
2025-07-07 16:35:12  web_server.py 81: INFO  Serving static file: css/8.38fdf013.css [waitress-3]
2025-07-07 16:35:12  web_server.py 78: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-0]
2025-07-07 16:35:12  web_server.py 84: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-0]
2025-07-07 16:39:00  web_server.py 78: INFO  Serving request for path: admin/settings/payment [waitress-1]
2025-07-07 16:39:00  web_server.py 84: INFO  Serving index.html for path: admin/settings/payment [waitress-1]
2025-07-07 16:39:00  web_server.py 78: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-2]
2025-07-07 16:39:00  web_server.py 84: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-2]
2025-07-07 16:39:00  web_server.py 78: INFO  Serving request for path: js/chunk-vendors.7f82116b.js [waitress-0]
2025-07-07 16:39:00  web_server.py 78: INFO  Serving request for path: js/app.10d7be2c.js [waitress-3]
2025-07-07 16:39:00  web_server.py 81: INFO  Serving static file: js/chunk-vendors.7f82116b.js [waitress-0]
2025-07-07 16:39:00  web_server.py 81: INFO  Serving static file: js/app.10d7be2c.js [waitress-3]
2025-07-07 16:39:00  web_server.py 78: INFO  Serving request for path: css/app.2ea35194.css [waitress-2]
2025-07-07 16:39:00  web_server.py 78: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-1]
2025-07-07 16:39:00  web_server.py 81: INFO  Serving static file: css/app.2ea35194.css [waitress-2]
2025-07-07 16:39:00  web_server.py 81: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-1]
2025-07-07 16:39:00  web_server.py 78: INFO  Serving request for path: js/388.6189b2b6.js [waitress-3]
2025-07-07 16:39:00  web_server.py 81: INFO  Serving static file: js/388.6189b2b6.js [waitress-3]
2025-07-07 16:39:00  web_server.py 78: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-0]
2025-07-07 16:39:00  web_server.py 81: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-0]
2025-07-07 16:39:00  web_server.py 78: INFO  Serving request for path: fonts/element-icons.ff18efd1.woff [waitress-2]
2025-07-07 16:39:00  web_server.py 81: INFO  Serving static file: fonts/element-icons.ff18efd1.woff [waitress-2]
2025-07-07 16:39:00  web_server.py 78: INFO  Serving request for path: logo.png [waitress-3]
2025-07-07 16:39:00  web_server.py 81: INFO  Serving static file: logo.png [waitress-3]
2025-07-07 16:39:02  web_server.py 78: INFO  Serving request for path: css/859.d8dc276c.css [waitress-0]
2025-07-07 16:39:02  web_server.py 78: INFO  Serving request for path: js/859.1a69a32e.js [waitress-1]
2025-07-07 16:39:02  web_server.py 81: INFO  Serving static file: css/859.d8dc276c.css [waitress-0]
2025-07-07 16:39:02  web_server.py 81: INFO  Serving static file: js/859.1a69a32e.js [waitress-1]
2025-07-07 16:39:03  web_server.py 78: INFO  Serving request for path: css/8.38fdf013.css [waitress-2]
2025-07-07 16:39:03  web_server.py 78: INFO  Serving request for path: js/8.a13b62ce.js [waitress-3]
2025-07-07 16:39:03  web_server.py 81: INFO  Serving static file: css/8.38fdf013.css [waitress-2]
2025-07-07 16:39:03  web_server.py 81: INFO  Serving static file: js/8.a13b62ce.js [waitress-3]
