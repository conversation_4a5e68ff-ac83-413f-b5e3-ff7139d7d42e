<template>
  <div class="flex">
    <div class="home-main grid-content bg-purple-light">
      <!-- VIP用户提示信息 -->
      <div class="vip-notice" v-if="isLoggedIn">
        <div class="notice-content">
          <div class="notice-icon">
            <i :class="isVip ? 'el-icon-star-on' : 'el-icon-star-off'"></i>
          </div>
          <div class="notice-text">
            <div class="notice-title">
              {{ isVip ? 'VIP会员' : '免费用户' }}
            </div>
            <div class="notice-desc">
              {{ isVip ? '您当前是VIP会员，可以创建最多5篇论文' : '您当前是免费用户，可以创建保存最多1篇论文，如果您需要多篇论文，可以先删除后再生成' }}
            </div>
            <div class="notice-desc" v-if="!isVip">
              VIP用户，可生成最长 10 个章节
            </div>
            <div class="notice-usage" v-if="isVip">
              <span class="usage-text">已创建：{{ thesisCount }} / 5</span>
              <el-progress 
                :percentage="(thesisCount / 5) * 100" 
                :stroke-width="6"
                :show-text="false"
                class="usage-progress"
              ></el-progress>
            </div>
            <div class="notice-usage" v-else>
              <span class="usage-text">已创建：{{ thesisCount }} / 1</span>
              <el-progress 
                :percentage="thesisCount * 100" 
                :stroke-width="6"
                :show-text="false"
                class="usage-progress"
              ></el-progress>
            </div>
          </div>
          <div class="notice-action" v-if="!isVip">
            <el-button type="warning" size="small" @click="showUpgradeInfo">
              升级VIP
            </el-button>
          </div>
        </div>
      </div>

      <!-- el-form 定义一个form,用model来便于后续数据引用 -->
      <el-form class="form-box" ref="form" :inline="true" :model="form">
        <!-- el-form-item 默认分配一个新的行row，label和label所代表的的项目在同一行 -->
        <el-form-item :label="$t('outlinePage.title')" required>
          <el-input
            v-model="form.title"
            style="width: 400px"
            placeholder="请填写论文标题"
          ></el-input>
        </el-form-item>
        <!-- el-form-item 默认分配一个新的行row，label和label所代表的的项目在同一行 -->
        <el-form-item :label="$t('outlinePage.level')" required>
          <el-radio-group v-model="form.level" size="small">
            <el-radio-button :label="$t('education.中学')"></el-radio-button>
            <el-radio-button :label="$t('education.大专')"></el-radio-button>
            <el-radio-button :label="$t('education.本科')"></el-radio-button>
            <el-radio-button :label="$t('education.硕士')"></el-radio-button>
            <el-radio-button :label="$t('education.博士')"></el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t('outlinePage.lang')" required>
          <el-radio-group v-model="form.lang" size="small">
            <el-radio-button
              v-for="lang in $t('languageList')"
              :key="lang"
              :label="lang"
              >{{ lang }}</el-radio-button
            >
          </el-radio-group>
        </el-form-item>
        <!-- <el-form-item :label="$t('outlinePage.length')" required>
          <el-radio-group v-model="form.length" size="small">
            <el-radio-button label="4000">约4000字左右</el-radio-button>
            <el-radio-button label="8000">约8000字左右</el-radio-button>
            <el-radio-button label="16000">约16000字左右</el-radio-button>
          </el-radio-group>
        </el-form-item> -->
        <el-form-item label="一级段落数量" required>
          <el-input-number
            v-model="form.paragraphCount"
            controls-position="right"
            :min="1"
            :max="12"
            :step="1"
          ></el-input-number>
        </el-form-item>
        <el-form-item label="每段中小标题的数量" required>
          <el-input-number
            v-model="form.secondParagraphCount"
            controls-position="right"
            :min="1"
            :max="10"
            :step="1"
          ></el-input-number>
        </el-form-item>

        <el-form-item style="margin-bottom: 0">
          <el-button
            type="primary"
            class="xiaolong-btn"
            @click="onSubmit"
            size="medium"
            >{{ $t("outlinePage.btnSubmit") }}</el-button
          >
        </el-form-item>
      </el-form>

      <div v-if="outlines && outlines.length > 0 && showHistory" class="history-notice">
        <i class="el-icon-time"></i>
        正在显示您的历史提纲记录，您可以重新生成新的提纲
      </div>

      <div class="result-box">
        <div class="result-item" v-for="(item, index) in outlines" :key="item.id">
          <div class="outline-title">
            <span class="t">
              {{ thesisType[index] }}
            </span>
            <el-button
              type="primary"
              class="xiaolong-btn"
              @click="selectForContent(index)"
            >
              {{ $t("outlinePage.btnSelectOutline") }}
            </el-button>
            <el-button type="primary" class="xiaolong-btn" @click="onSubmit">
              {{ $t("outlinePage.btnReSubmit") }}
            </el-button>
            <el-button
              type="primary"
              class="xiaolong-btn"
              @click="downloadOutline(index)"
            >
              下载
            </el-button>
          </div>

          <div class="outline-box" v-html="item" contenteditable="true"></div>
        </div>
      </div>

      <Placeholder v-if="!outlines || outlines.length == 0"></Placeholder>
    </div>

    <div class="home-sidebar grid-content bg-purple">
      <PageLeftBox />
    </div>
  </div>
</template>

<script>
const KEY_LAST_TITLE = "lastTitle";
const KEY_LAST_OUTLINE = "lastOutline";
const KEY_LAST_FORM = "lastTitleForm";

import { getOutline, select4ContentApi, exportOutline, getOutlineHistory, checkExistingThesis } from "@/api/generate";
import { Loading } from "element-ui";
import PageLeftBox from "../components/PageLeftBox";
import Placeholder from "../components/Placeholder";
import { safeJsonParse } from "@/utils";
import { mapGetters } from 'vuex';

export default {
  name: "GetOutline",
  components: {
    PageLeftBox,
    Placeholder,
  },
  computed: {
    ...mapGetters('user', ['userInfo', 'isLoggedIn', 'isVip']),
    thesisCount() {
      // 从用户信息中获取论文数量，如果没有则默认为0
      return this.userInfo ? (this.userInfo.thesis_count || 0) : 0;
    }
  },
  data() {
    return {
      thesisType: ["研究型", "数据型", "严谨型"],
      outlines: [],
      rawOutlines: [],
      Self_Define_Outline: "",
      form: {
        title: "",
        level: "本科",
        length: "4000",
        lang: "中文",
        paragraphCount: 6,
        secondParagraphCount: 3,
      },
      historyOutlines: [],
      showHistory: false,
    };
  },

  created() {
    this.loadUserHistory();
    
    let lastTitle = localStorage.getItem(KEY_LAST_TITLE);
    if (lastTitle) {
      const parsedTitle = safeJsonParse(lastTitle);
      if (parsedTitle) {
        if (this.form.lang == "English") {
          this.form.title = parsedTitle["tt"];
        } else {
          this.form.title = parsedTitle["kk"];
        }
      } else {
        localStorage.removeItem(KEY_LAST_TITLE);
      }
    }
    
    let lastTitleForm = localStorage.getItem(KEY_LAST_FORM);
    if (lastTitleForm) {
      const parsedForm = safeJsonParse(lastTitleForm);
      if (parsedForm) {
        this.form.lang = parsedForm["lang"];
        this.form.level = parsedForm["level"];
      } else {
        localStorage.removeItem(KEY_LAST_FORM);
      }
    }
    
    let lastOutline = localStorage.getItem(KEY_LAST_OUTLINE);
    if (lastOutline && this.outlines.length === 0) {
      const parsedOutline = safeJsonParse(lastOutline);
      if (parsedOutline) {
        this.outlines = parsedOutline["html"];
        this.rawOutlines = parsedOutline["raw"];
      } else {
        localStorage.removeItem(KEY_LAST_OUTLINE);
      }
    }
  },
  methods: {
    openLoginBox() {},
    closeLoginBox() {},

    showUpgradeInfo() {
      this.$router.push({
        path: "/my/userrank",
      });
    },

    generateHtml(jsonOutline, level = 1, titlePrefix = "") {
      let htmlResult = "";
      for (let index of jsonOutline.keys()) {
        let chapterNo = index + 1;
        let jsonNode = jsonOutline[index];
        if (level == 1) {
          htmlResult += `<div class='title-level1'><i>${titlePrefix}第${chapterNo}章</i> ${jsonNode["title"]}</div>`;
        } else {
          htmlResult += `<div class='title-level${level}'><i>${titlePrefix}${chapterNo}</i> ${jsonNode["title"]}</div>`;
        }
        if (jsonNode["subtitle"] && Array.isArray(jsonNode["subtitle"]) && jsonNode["subtitle"].length > 0) {
          htmlResult += this.generateHtml(
            jsonNode["subtitle"],
            level + 1,
            `${titlePrefix}${chapterNo}.`
          );
        }
      }
      return htmlResult;
    },
    handleGotoUserRank() {
      this.$router.push({
        path: "/my/userrank",
      });
    },
    downloadOutline(index) {
      let payload = {
        title: this.form.title,
        level: this.form.level,
        length: this.form.length,
        lang: this.form.lang,
        outline: this.rawOutlines[index],
      };
      exportOutline(payload)
        .then((res) => {
          console.log(res);
        })
        .catch((e) => {
          console.log(e);
        });
    },
    loadUserHistory() {
      // 从数据库加载用户历史大纲
      getOutlineHistory()
        .then(res => {
          if (res.is_success && res.data && res.data.length > 0) {
            this.historyOutlines = res.data;
            // 如果有历史记录且当前没有大纲，使用最新的一条
            if (this.outlines.length === 0) {
              const latestHistory = res.data[0];
              if (latestHistory.form_data) {
                // 恢复表单数据
                this.form.title = latestHistory.form_data.title || "";
                this.form.level = latestHistory.form_data.level || "本科";
                this.form.lang = latestHistory.form_data.lang || "中文";
                this.form.length = latestHistory.form_data.length || "4000";
                this.form.paragraphCount = latestHistory.form_data.paragraphCount || 6;
                this.form.secondParagraphCount = latestHistory.form_data.secondParagraphCount || 3;
              }
              // 恢复大纲结果
              if (latestHistory.raw_outlines) {
                this.rawOutlines = latestHistory.raw_outlines;
                // 生成HTML格式的大纲
                let htmlOutlines = [];
                for (let i in latestHistory.raw_outlines) {
                  let jsonOutline = latestHistory.raw_outlines[i];
                  let html = this.generateHtml(jsonOutline["subtitle"], 1);
                  htmlOutlines.push(html);
                }
                this.outlines = htmlOutlines;
              }
            }
          }
        })
        .catch(error => {
          console.error('加载历史大纲失败:', error);
        });
    },
    
    selectHistoryOutline(history) {
      if (history.form_data) {
        // 恢复表单数据
        this.form.title = history.form_data.title || "";
        this.form.level = history.form_data.level || "本科";
        this.form.lang = history.form_data.lang || "中文";
        this.form.length = history.form_data.length || "4000";
        this.form.paragraphCount = history.form_data.paragraphCount || 6;
        this.form.secondParagraphCount = history.form_data.secondParagraphCount || 3;
      }
      // 恢复大纲结果
      if (history.raw_outlines) {
        this.rawOutlines = history.raw_outlines;
        // 生成HTML格式的大纲
        let htmlOutlines = [];
        for (let i in history.raw_outlines) {
          let jsonOutline = history.raw_outlines[i];
          let html = this.generateHtml(jsonOutline["subtitle"], 1);
          htmlOutlines.push(html);
        }
        this.outlines = htmlOutlines;
      }
      this.showHistory = false;
    },
    onSubmit() {
      if (this.form.title == "") {
        this.$message({
          type: "error",
          message: "请填写你选定的论文题目，论文的题目不能为空",
        });

        return false;
      }
      let loading = this.$loading({
        text: "正在构建论文框架，生成专业提纲...",
        background: "#00000033",
      });

      localStorage.removeItem(KEY_LAST_OUTLINE);
      getOutline(this.form)
        .then((res) => {
          loading.close();

          if (!res.is_success) {
            this.$message({ type: "error", message: res.message });
            return;
          }
          let htmlOutlines = [];
          for (let i in res.data) {
            let jsonOutline = res.data[i];
            let html = this.generateHtml(jsonOutline["subtitle"], 1);
            htmlOutlines.push(html);
          }

          this.rawOutlines = res.data;
          this.outlines = htmlOutlines;
          this.showHistory = false;
          
          localStorage.setItem(
            KEY_LAST_OUTLINE,
            JSON.stringify({
              raw: res.data,
              html: htmlOutlines,
            })
          );
        })
        .catch((e) => {
          loading.close();
          this.$notify.error("遇到错误：" + e);
        });
    },

    selectForContent(index) {
      // 先检查用户是否已有论文
      this.checkExistingThesis().then(hasThesis => {
        if (hasThesis) {
          this.$confirm('您已经有一篇正在进行中的论文，继续操作将覆盖当前论文。是否继续?', '提示', {
            confirmButtonText: '继续',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            // 用户确认继续，执行选择提纲的逻辑
            this.processOutlineSelection(index);
          }).catch(() => {
            // 用户取消操作
            this.$message({
              type: 'info',
              message: '已取消操作'
            });
          });
        } else {
          // 没有现有论文，直接执行选择提纲的逻辑
          this.processOutlineSelection(index);
        }
      }).catch(error => {
        console.error("检查现有论文时出错:", error);
        // 出错时，仍然尝试执行选择提纲的逻辑
        this.processOutlineSelection(index);
      });
    },
    
    // 检查用户是否已有论文
    async checkExistingThesis() {
      try {
        // 调用API检查用户是否已有论文
        const response = await checkExistingThesis();
        
        if (response && response.is_success) {
          const data = response.data;
          console.log('检查论文状态结果:', data);
          
          // 如果用户已有论文且达到上限，显示提示
          if (data.thesis_count > 0 && !data.can_create) {
            const message = data.is_vip 
              ? `您已创建 ${data.thesis_count} 篇论文，达到上限 ${data.max_thesis}。请先删除部分论文再继续。`
              : '免费用户最多只能创建一篇论文，请先删除已有论文再继续。';
              
            this.$notify.warning({
              title: '提示',
              message: message,
              duration: 5000
            });
          }
          
          return data.has_thesis;
        }
        return false;
      } catch (error) {
        console.error("检查现有论文失败:", error);
        return false;
      }
    },
    
    // 处理提纲选择的核心逻辑
    processOutlineSelection(index) {
      // 确保选择的索引有效
      if (index < 0 || index >= this.rawOutlines.length) {
        this.$notify.error("提纲索引无效，请重新选择");
        return;
      }
      
      // 确保原始提纲数据有效
      if (!this.rawOutlines[index] || typeof this.rawOutlines[index] !== 'object') {
        this.$notify.error("提纲数据无效，请重新生成提纲");
        return;
      }
      
      // 添加详细日志，查看提纲数据结构
      console.log(`选择第${index+1}个提纲，数据结构:`, JSON.stringify(this.rawOutlines[index]));
      console.log(`提纲类型:`, typeof this.rawOutlines[index]);
      
      // 预处理提纲数据，确保数据结构正确
      let outlineData = JSON.parse(JSON.stringify(this.rawOutlines[index])); // 深拷贝，避免修改原始数据
      
      try {
        // 确保提纲是一个有效的对象
        if (!outlineData || typeof outlineData !== 'object' || Array.isArray(outlineData)) {
          console.error('提纲数据不是有效的对象:', outlineData);
          this.$notify.error("提纲数据格式错误，请重新生成提纲");
          return;
        }
        
        // 确保提纲有title字段
        if (!outlineData.title) {
          console.log('提纲缺少title字段，使用论文标题');
          outlineData.title = this.form.title;
        }
        
        // 确保提纲有subtitle字段且为数组
        if (!outlineData.subtitle || !Array.isArray(outlineData.subtitle)) {
          console.log('提纲缺少有效的subtitle字段，尝试修复');
          
          // 尝试从其他可能的字段获取subtitle内容
          const possibleFields = ['children', 'subheadings', 'sections', 'chapters', 'content'];
          let foundSubtitle = false;
          
          for (const field of possibleFields) {
            if (outlineData[field] && Array.isArray(outlineData[field])) {
              console.log(`使用${field}字段作为subtitle`);
              outlineData.subtitle = outlineData[field];
              foundSubtitle = true;
              break;
            }
          }
          
          // 如果没有找到有效的subtitle，创建一个空数组
          if (!foundSubtitle) {
            console.log('未找到有效的subtitle字段，创建空数组');
            outlineData.subtitle = [];
          }
        }
        
        // 递归处理所有节点，确保它们有id和title
        const processNode = (node) => {
          if (!node || typeof node !== 'object') {
            return { id: this.generateRandomId(), title: "无效节点", subtitle: [] };
          }
          
          // 确保节点有id
          if (!node.id) {
            node.id = this.generateRandomId();
          }
          
          // 确保节点有title
          if (!node.title) {
            // 尝试从其他字段获取标题
            const titleFields = ['name', 'heading', 'text'];
            let foundTitle = false;
            
            for (const field of titleFields) {
              if (node[field]) {
                node.title = node[field];
                foundTitle = true;
                break;
              }
            }
            
            // 如果没有找到标题，使用默认值
            if (!foundTitle) {
              node.title = '无标题章节';
            }
          }
          
          // 确保subtitle是数组
          if (!node.subtitle) {
            node.subtitle = [];
          } else if (!Array.isArray(node.subtitle)) {
            if (typeof node.subtitle === 'object') {
              node.subtitle = [node.subtitle]; // 将单个对象转换为数组
            } else {
              node.subtitle = []; // 其他情况创建空数组
            }
          }
          
          // 递归处理子节点
          node.subtitle = node.subtitle.map(subNode => processNode(subNode));
          
          return node;
        };
        
        // 处理提纲中的所有节点
        if (Array.isArray(outlineData.subtitle)) {
          outlineData.subtitle = outlineData.subtitle.map(node => processNode(node));
        }
        
        console.log('处理后的提纲数据:', JSON.stringify(outlineData));
        
        // 构建发送到后端的数据
        let payload = {
          title: this.form.title,
          level: this.form.level,
          length: this.form.length,
          lang: this.form.lang,
          outline: outlineData,
        };
        
        // 输出请求数据到控制台，便于调试
        console.log("发送到后端的提纲数据:", JSON.stringify(payload));
        
        let progress = 0;
        let loadingInstance = Loading.service({
          text: "正在准备生成论文内容...",
          background: "rgba(255, 255, 255, 0.6)",
          customClass: "loading-with-percentage"
        });

        // 创建进度更新函数
        const updateProgress = () => {
          if (progress < 90) {
            progress += Math.random() * 10;
            if (progress > 90) progress = 90;
            loadingInstance.setText(`正在准备生成论文内容...<br><span class="loading-percentage">${Math.floor(progress)}%</span>`);
          }
        };
        
        // 启动进度更新
        const progressInterval = setInterval(updateProgress, 300);

        select4ContentApi(payload)
          .then((res) => {
            clearInterval(progressInterval);
            if (res.is_success) {
              progress = 100;
              loadingInstance.setText(`正在准备生成论文内容...<br><span class="loading-percentage">${progress}%</span>`);
              setTimeout(() => {
                loadingInstance.close();
                this.$router.push({
                  path: "/paper/content",
                  query: { thesisId: res.data.thesisId },
                });
              }, 200);
            } else {
              loadingInstance.close();
              this.$notify.error("服务器错误，请稍后重试：" + res.message);
            }
          })
          .catch((e) => {
            clearInterval(progressInterval);
            loadingInstance.close();
            
            // 改进错误处理
            console.error("请求失败详情:", e);
            
            // 如果是字符串错误消息，直接显示
            if (typeof e === 'string') {
              this.$notify({
                title: '提示',
                message: e,
                type: 'warning',
                duration: 5000
              });
            } 
            // 如果是响应错误
            else if (e.response && e.response.status === 400) {
              // 获取错误消息
              let errorMsg = '请求参数错误';
              
              if (e.response.data && e.response.data.message) {
                errorMsg = e.response.data.message;
              }
              
              // 根据错误消息提供更友好的提示
              if (errorMsg.includes('免费用户最多只能创建一篇论文')) {
                this.$notify({
                  title: '提示',
                  message: '免费用户最多只能创建一篇论文，请先删除已有论文再继续',
                  type: 'warning',
                  duration: 5000
                });
              } else {
                this.$notify({
                  title: '提示',
                  message: '您已经选择了一个提纲生成论文，请先完成或删除当前论文再选择新的提纲',
                  type: 'warning',
                  duration: 5000
                });
              }
            } 
            // 其他错误
            else {
              this.$notify.error('请求失败，请稍后重试');
            }
          });
      } catch (error) {
        console.error("处理提纲数据时出错:", error);
        this.$notify.error("处理提纲数据时出错，请重新生成提纲");
      }
    },
    
    // 生成随机ID的辅助方法
    generateRandomId() {
      return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
    },
  },
};
</script>

<style lang="scss">
.flex {
  display: flex;
  flex-direction: row;
  height: 100%;
  background: #f8f9fa;
  overflow: hidden;
}

.home-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 24px;
  background: #f8f9fa;
  overflow: hidden;
  min-width: 0;
}

.form-box {
  flex-shrink: 0;
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  margin-bottom: 24px;
  width: 100%;

  .el-form-item {
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .el-input {
    .el-input__inner {
      border-radius: 8px;
      border: 1px solid #e0e3e7;
      transition: all 0.3s ease;

      &:hover, &:focus {
        border-color: #409EFF;
        box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.1);
      }
    }
  }

  .el-radio-button__inner {
    border-radius: 6px;
    margin: 0 4px;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-1px);
    }
  }

  .el-input-number {
    .el-input__inner {
      border-radius: 8px;
    }
  }

  .xiaolong-btn {
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #409EFF 0%, #66b1ff 100%);
    border: none;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
    }
  }
}

.result-box {
  flex: 1;
  display: flex;
  gap: 24px;
  overflow: auto;
  padding-bottom: 24px;
  min-height: 0;
  width: 100%;
}

.result-item {
  flex: 1;
  min-width: 360px;
  max-width: calc(33.33% - 16px);
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  border: none;
  transition: all 0.3s ease;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  }

  .outline-box {
    flex: 1;
    overflow-y: auto;
    color: #4a5568;
    font-size: 14px;
    padding: 20px 0;
    line-height: 1.8;
    
    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 2px;
      
      &:hover {
        background: #a8a8a8;
      }
    }

    i {
      color: #718096;
      padding-right: 1rem;
      font-style: normal;
    }

    .title-level1 {
      margin-top: 16px;
      font-size: 16px;
      color: #2d3748;
      font-weight: 600;
      padding: 8px 12px;
      background: #f7fafc;
      border-radius: 6px;
      transition: all 0.3s ease;

      &:hover {
        background: #edf2f7;
      }
    }

    .title-level2 {
      padding: 6px 12px 6px 32px;
      margin-top: 8px;
      border-left: 2px solid #e2e8f0;
    }

    .title-level3 {
      padding: 4px 12px 4px 48px;
      color: #718096;
    }

    .title-level4 {
      padding: 4px 12px 4px 64px;
      color: #a0aec0;
    }
  }
}

.outline-title {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  padding: 12px;
  border-radius: 8px;
  border: none;
  margin-bottom: 16px;
  flex-wrap: nowrap;

  .t {
    color: white;
    font-weight: 600;
    font-size: 16px;
    min-width: 60px;
    margin-right: 4px;
  }

  .xiaolong-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.4);
    color: white;
    padding: 6px 10px;
    border-radius: 6px;
    font-weight: 500;
    font-size: 13px;
    transition: all 0.3s ease;
    white-space: nowrap;
    flex-shrink: 0;

    &:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: translateY(-1px);
    }
  }
}

.history-notice {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  gap: 8px;

  i {
    font-size: 20px;
    color: #409EFF;
  }

  .text {
    font-size: 16px;
    color: #2d3748;
    font-weight: 500;
  }
}

.vip-notice {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 16px;
  border-radius: 12px;
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  gap: 12px;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);

  .notice-content {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 12px;

    .notice-icon {
      i {
        font-size: 28px;
        color: #FFD700;
      }
    }

    .notice-text {
      flex: 1;
      display: flex;
      flex-direction: column;

      .notice-title {
        font-size: 18px;
        color: #FFFFFF;
        font-weight: 600;
        margin-bottom: 4px;
      }

      .notice-desc {
        font-size: 14px;
        color: #FFFFFF;
        opacity: 0.9;
        margin-bottom: 8px;
      }

      .notice-usage {
        display: flex;
        align-items: center;
        gap: 12px;

        .usage-text {
          font-size: 14px;
          color: #FFFFFF;
          font-weight: 500;
          min-width: 80px;
        }

        .usage-progress {
          flex: 1;
          max-width: 200px;
        }
      }
    }

    .notice-action {
      .el-button {
        background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
        color: #FFFFFF;
        border: none;
        padding: 10px 20px;
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 16px rgba(255, 215, 0, 0.4);
        }
      }
    }
  }
}
</style>
