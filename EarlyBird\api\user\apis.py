from EarlyBird.api.user import user_blueprint
from flask import g, request, session
import logging
from EarlyBird.common import ApiResponse, Result
from EarlyBird.api.user.service import UserService
from EarlyBird.api.user.entity import VipLogListModel, ApiLogListModel, LoginModel, RegisterModel
from flask_pydantic import validate
from datetime import datetime

LOGGER = logging.getLogger(__name__)

@user_blueprint.route("/info", methods=["POST"])
def info():
    try:
        res: Result = UserService().get_user_info(g.userid)

        if res.isSucc():
            # 这个roles 的结构，必须有，前端有代码依赖
            return ApiResponse().set_data({"roles": [], "user": res.data}).json()

        return ApiResponse().error(res.message).json()
    except Exception as e:
        LOGGER.exception(e)
        return ApiResponse().error(str(e)).json()

@user_blueprint.route("/login", methods=["POST"])
@validate()
def login(body: LoginModel):
    """
    用户登录接口
    """
    try:
        LOGGER.info(f"用户登录请求: username={body.username}")
        
        # 清除之前的session数据，确保用户不会继承旧用户的数据
        session.clear()
        # 显式删除激活相关字段
        for key in [
            'activation_key', 'activation_time', 'expire_time', 'machine_code']:
            session.pop(key, None)
        LOGGER.info("已清除之前的session数据和激活相关字段")
        
        # 调用服务层处理登录
        res: Result = UserService().login(body.username, body.password)
        
        if res.isSucc():
            # 登录成功，设置session
            user = res.data
            session['user_id'] = user.id
            session['username'] = user.username
            session['login_time'] = datetime.now().isoformat()
            
            # 设置session为永久性
            session.permanent = True
            session.modified = True
            
            LOGGER.info(f"用户 {user.id} 登录成功，session已设置")
            LOGGER.info(f"Session内容: {dict(session)}")
            
            # 获取完整的用户信息
            user_info = user.get_info()
            
            # 返回用户信息，字段名与前端期望匹配
            return ApiResponse().set_data({
                "userId": user.id,  # 前端期望userId而不是id
                "username": user.username,
                "nickname": user.nickname or user.username,
                "isVip": user.isVip(),
                "headimg": user.headimg or "",
                "email": getattr(user, 'email', '') or "",
                # 添加VIP详细信息
                "vip_level": user_info.get("vip_level", 1),
                "vip_balance_days": user_info.get("vip_balance_days", ""),
                "vip_expire_at": user_info.get("vip_expire_at", ""),
                "vip_start_at": user_info.get("vip_start_at", ""),
                "create_time": user_info.get("create_time", ""),
                "last_login_time": user_info.get("last_login_time", "")
            }).json()
        
        LOGGER.warning(f"用户登录失败: {res.message}")
        return ApiResponse().error(res.message).json()
    except Exception as e:
        LOGGER.exception(f"用户登录异常: {e}")
        return ApiResponse().error(f"登录失败: {str(e)}").json()

@user_blueprint.route("/logout", methods=["POST"])
def logout():
    """
    用户退出登录接口
    """
    try:
        # 获取当前用户ID
        user_id = session.get('user_id')
        LOGGER.info(f"用户 {user_id} 退出登录")
        
        # 清除session
        session.clear()
        
        return ApiResponse().set_data({"success": True}).json()
    except Exception as e:
        LOGGER.exception(f"用户退出登录异常: {e}")
        return ApiResponse().error(f"退出失败: {str(e)}").json()

@user_blueprint.route("/register", methods=["POST"])
@validate()
def register(body: RegisterModel):
    """
    用户注册接口
    """
    try:
        LOGGER.info(f"用户注册请求: username={body.username}")
        
        # 验证两次密码是否一致
        if body.password != body.confirm_password:
            LOGGER.warning(f"用户注册失败: 两次密码不一致")
            return ApiResponse().error("两次输入的密码不一致").json()
        
        # 清除之前的session数据，确保新用户不会继承旧用户的数据
        session.clear()
        # 显式删除激活相关字段
        for key in [
            'activation_key', 'activation_time', 'expire_time', 'machine_code']:
            session.pop(key, None)
        LOGGER.info("已清除之前的session数据和激活相关字段")
        
        # 调用服务层处理注册
        res: Result = UserService().register(
            username=body.username,
            password=body.password,
            nickname=body.nickname,
            email=body.email
        )
        
        if res.isSucc():
            # 注册成功，设置session
            user = res.data
            session['user_id'] = user.id
            session['username'] = user.username
            session['login_time'] = datetime.now().isoformat()
            
            # 设置session为永久性
            session.permanent = True
            session.modified = True
            
            LOGGER.info(f"用户 {user.id} 注册成功并自动登录，session已设置")
            LOGGER.info(f"Session内容: {dict(session)}")
            
            # 获取完整的用户信息
            user_info = user.get_info()
            
            # 返回用户信息，字段名与前端期望匹配
            return ApiResponse().set_data({
                "userId": user.id,  # 前端期望userId而不是id
                "username": user.username,
                "nickname": user.nickname or user.username,
                "isVip": user.isVip(),
                "headimg": user.headimg or "",
                "email": getattr(user, 'email', '') or "",
                # 添加VIP详细信息
                "vip_level": user_info.get("vip_level", 1),
                "vip_balance_days": user_info.get("vip_balance_days", ""),
                "vip_expire_at": user_info.get("vip_expire_at", ""),
                "vip_start_at": user_info.get("vip_start_at", ""),
                "create_time": user_info.get("create_time", ""),
                "last_login_time": user_info.get("last_login_time", "")
            }).json()
        
        LOGGER.warning(f"用户注册失败: {res.message}")
        return ApiResponse().error(res.message).json()
    except Exception as e:
        LOGGER.exception(f"用户注册异常: {e}")
        return ApiResponse().error(f"注册失败: {str(e)}").json()

@user_blueprint.route("/checkLogin", methods=["POST"])
def check_login():
    """
    检查用户登录状态接口
    """
    try:
        user_id = session.get('user_id')
        username = session.get('username')
        login_time = session.get('login_time')
        
        LOGGER.info(f"检查登录状态 - user_id: {user_id}, username: {username}")
        LOGGER.info(f"完整Session内容: {dict(session)}")
        
        if user_id is None:
            return ApiResponse().error("未登录").json()
        
        # 尝试获取用户信息
        res: Result = UserService().get_user_info(user_id)
        
        if res.isSucc():
            return ApiResponse().set_data({
                "isLoggedIn": True,
                "userId": user_id,
                "username": username,
                "loginTime": login_time,
                "userInfo": res.data
            }).json()
        else:
            return ApiResponse().error("登录状态无效").json()
            
    except Exception as e:
        LOGGER.exception(f"检查登录状态异常: {e}")
        return ApiResponse().error(f"检查失败: {str(e)}").json()


