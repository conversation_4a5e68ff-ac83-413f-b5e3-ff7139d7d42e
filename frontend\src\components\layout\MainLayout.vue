<template>
  <div class="page-container">
    <div class="page-body">
      <div class="page-body-aside">
        <Navbar></Navbar>
      </div>
      <div class="page-body-main">
        <router-view />
      </div>
    </div>
  </div>
</template>

<script>
import Navbar from "@/components/Navbar.vue";
import Footer from "@/components/Footer.vue";
import Debug from "@/components/Debug.vue";

export default {
  components: {
    Navbar,
    Footer,
    Debug,
  },
  computed: {},
  methods: {
    handlerCloseWindow() {},
  },
};
</script>

<style lang="scss" scoped>
.page-container {
  margin: 0;
  height: 100vh;
  padding: 0;
  background-color: #f0f1f6;
  display: flex;
  flex-direction: column;
}

.page-body {
  flex: 1;
  display: flex;
  flex-direction: row;
  background-color: #f0f1f6;
  padding: 10px;
  margin: 0;
  overflow: hidden;
}

.page-body-aside {
  height: 100%;
  padding: 0;
  margin: 0;
  overflow: hidden;
  width: 160px;
  margin-right: 10px;
}

.page-body-main {
  padding: 0;
  margin: 0;
  height: 100%;
  flex: 1;
  overflow-y: auto;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}
</style>
