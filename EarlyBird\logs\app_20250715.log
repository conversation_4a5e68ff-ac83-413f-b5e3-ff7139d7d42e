2025-07-15 05:02:10  init_flask.py 27: INFO  {'CpuCount': 6, 'Mem': '32 GB'} [MainThread]
2025-07-15 05:02:10  web_server.py 32: INFO  Static directory: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\ui [MainThread]
2025-07-15 05:02:10  web_server.py 33: INFO  Static directory exists: True [MainThread]
2025-07-15 05:02:10  web_server.py 37: INFO  设置工作目录: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird [MainThread]
2025-07-15 05:02:10  EarlyBird.config.config 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-15 05:02:10  config.py 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-15 05:02:10  config.py 94: INFO  Database URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-15 05:02:12  EarlyBird.api.admin.app 39: INFO  ✅ 后台管理系统API已注册 [MainThread]
2025-07-15 05:02:12  app.py 39: INFO  ✅ 后台管理系统API已注册 [MainThread]
2025-07-15 05:02:12  EarlyBird 40: INFO  确保数据目录存在: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\data [MainThread]
2025-07-15 05:02:12  server.py 40: INFO  确保数据目录存在: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\data [MainThread]
2025-07-15 05:02:12  EarlyBird 44: INFO  开始执行数据库迁移... [MainThread]
2025-07-15 05:02:12  server.py 44: INFO  开始执行数据库迁移... [MainThread]
2025-07-15 05:02:12  config.py 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-15 05:02:12  config.py 94: INFO  Database URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-15 05:02:12  EarlyBird 53: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-15 05:02:12  server.py 53: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-15 05:02:12  EarlyBird 97: WARNING  ChatLog表不存在，将在应用启动时自动创建 [MainThread]
2025-07-15 05:02:12  server.py 97: WARNING  ChatLog表不存在，将在应用启动时自动创建 [MainThread]
2025-07-15 05:02:12  add_uid_to_chatlog.py 20: INFO  开始执行ChatLog表迁移... [MainThread]
2025-07-15 05:02:12  add_uid_to_chatlog.py 24: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-15 05:02:12  add_uid_to_chatlog.py 38: WARNING  ChatLog表不存在，可能需要先创建表 [MainThread]
2025-07-15 05:02:12  task_api_server.py 31: INFO  TaskApiServer start [MainThread]
2025-07-15 05:02:12  task_paper_generator.py 38: INFO  论文生成任务处理线程初始化 [MainThread]
2025-07-15 05:02:12  task_paper_generator.py 72: INFO  论文生成任务处理线程启动 [Thread-2]
2025-07-15 05:02:12  task_api_server.py 47: INFO  TaskApiServer listen on 127.0.0.1:3302 [Thread-1]
2025-07-15 05:02:12  task_api_server.py 17: INFO  _ApiServerThread start [Thread-3]
2025-07-15 05:36:36  init_flask.py 27: INFO  {'CpuCount': 6, 'Mem': '32 GB'} [MainThread]
2025-07-15 05:36:36  web_server.py 32: INFO  Static directory: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\ui [MainThread]
2025-07-15 05:36:36  web_server.py 33: INFO  Static directory exists: True [MainThread]
2025-07-15 05:36:36  web_server.py 37: INFO  设置工作目录: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird [MainThread]
2025-07-15 05:36:36  EarlyBird.config.config 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-15 05:36:36  config.py 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-15 05:36:36  config.py 94: INFO  Database URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-15 05:36:39  EarlyBird.api.admin.app 39: INFO  ✅ 后台管理系统API已注册 [MainThread]
2025-07-15 05:36:39  app.py 39: INFO  ✅ 后台管理系统API已注册 [MainThread]
2025-07-15 05:36:39  EarlyBird 40: INFO  确保数据目录存在: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\data [MainThread]
2025-07-15 05:36:39  server.py 40: INFO  确保数据目录存在: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\data [MainThread]
2025-07-15 05:36:39  EarlyBird 44: INFO  开始执行数据库迁移... [MainThread]
2025-07-15 05:36:39  server.py 44: INFO  开始执行数据库迁移... [MainThread]
2025-07-15 05:36:39  config.py 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-15 05:36:39  config.py 94: INFO  Database URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-15 05:36:39  EarlyBird 53: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-15 05:36:39  server.py 53: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-15 05:36:39  EarlyBird 97: WARNING  ChatLog表不存在，将在应用启动时自动创建 [MainThread]
2025-07-15 05:36:39  server.py 97: WARNING  ChatLog表不存在，将在应用启动时自动创建 [MainThread]
2025-07-15 05:36:39  add_uid_to_chatlog.py 20: INFO  开始执行ChatLog表迁移... [MainThread]
2025-07-15 05:36:39  add_uid_to_chatlog.py 24: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-15 05:36:39  add_uid_to_chatlog.py 38: WARNING  ChatLog表不存在，可能需要先创建表 [MainThread]
2025-07-15 05:36:39  task_api_server.py 31: INFO  TaskApiServer start [MainThread]
2025-07-15 05:36:39  task_paper_generator.py 38: INFO  论文生成任务处理线程初始化 [MainThread]
2025-07-15 05:36:39  task_paper_generator.py 72: INFO  论文生成任务处理线程启动 [Thread-2]
2025-07-15 05:36:39  task_api_server.py 47: INFO  TaskApiServer listen on 127.0.0.1:3302 [Thread-1]
2025-07-15 05:36:39  task_api_server.py 17: INFO  _ApiServerThread start [Thread-3]
2025-07-15 05:41:42  init_flask.py 27: INFO  {'CpuCount': 6, 'Mem': '32 GB'} [MainThread]
2025-07-15 05:41:42  web_server.py 32: INFO  Static directory: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\ui [MainThread]
2025-07-15 05:41:42  web_server.py 33: INFO  Static directory exists: True [MainThread]
2025-07-15 05:41:42  web_server.py 37: INFO  设置工作目录: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird [MainThread]
2025-07-15 05:41:42  EarlyBird.config.config 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-15 05:41:42  config.py 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-15 05:41:42  config.py 94: INFO  Database URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-15 05:41:44  EarlyBird.api.admin.app 39: INFO  ✅ 后台管理系统API已注册 [MainThread]
2025-07-15 05:41:44  app.py 39: INFO  ✅ 后台管理系统API已注册 [MainThread]
2025-07-15 05:41:44  EarlyBird 40: INFO  确保数据目录存在: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\data [MainThread]
2025-07-15 05:41:44  server.py 40: INFO  确保数据目录存在: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\data [MainThread]
2025-07-15 05:41:44  EarlyBird 44: INFO  开始执行数据库迁移... [MainThread]
2025-07-15 05:41:44  server.py 44: INFO  开始执行数据库迁移... [MainThread]
2025-07-15 05:41:44  config.py 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-15 05:41:44  config.py 94: INFO  Database URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-15 05:41:44  EarlyBird 53: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-15 05:41:44  server.py 53: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-15 05:41:44  EarlyBird 97: WARNING  ChatLog表不存在，将在应用启动时自动创建 [MainThread]
2025-07-15 05:41:44  server.py 97: WARNING  ChatLog表不存在，将在应用启动时自动创建 [MainThread]
2025-07-15 05:41:44  add_uid_to_chatlog.py 20: INFO  开始执行ChatLog表迁移... [MainThread]
2025-07-15 05:41:44  add_uid_to_chatlog.py 24: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-15 05:41:44  add_uid_to_chatlog.py 38: WARNING  ChatLog表不存在，可能需要先创建表 [MainThread]
2025-07-15 05:41:44  task_api_server.py 31: INFO  TaskApiServer start [MainThread]
2025-07-15 05:41:44  task_paper_generator.py 38: INFO  论文生成任务处理线程初始化 [MainThread]
2025-07-15 05:41:44  task_paper_generator.py 72: INFO  论文生成任务处理线程启动 [Thread-2]
2025-07-15 05:41:44  task_api_server.py 47: INFO  TaskApiServer listen on 127.0.0.1:3302 [Thread-1]
2025-07-15 05:41:44  task_api_server.py 17: INFO  _ApiServerThread start [Thread-3]
2025-07-15 10:14:59  web_server.py 73: INFO  Serving request for path: paper/content [waitress-0]
2025-07-15 10:14:59  web_server.py 85: INFO  Serving index.html for path: paper/content [waitress-0]
2025-07-15 10:14:59  web_server.py 73: INFO  Serving request for path: js/app.690768f9.js [waitress-2]
2025-07-15 10:14:59  web_server.py 73: INFO  Serving request for path: js/chunk-vendors.04aff60f.js [waitress-1]
2025-07-15 10:14:59  web_server.py 82: INFO  Serving static file: js/app.690768f9.js [waitress-2]
2025-07-15 10:14:59  web_server.py 82: INFO  Serving static file: js/chunk-vendors.04aff60f.js [waitress-1]
2025-07-15 10:14:59  web_server.py 73: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-3]
2025-07-15 10:14:59  web_server.py 73: INFO  Serving request for path: css/app.1e68ddae.css [waitress-0]
2025-07-15 10:14:59  task.py 113: WARNING  Task queue depth is 1 [MainThread]
2025-07-15 10:14:59  web_server.py 82: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-3]
2025-07-15 10:14:59  web_server.py 82: INFO  Serving static file: css/app.1e68ddae.css [waitress-0]
2025-07-15 10:14:59  web_server.py 73: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-3]
2025-07-15 10:14:59  web_server.py 85: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-3]
2025-07-15 10:14:59  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/user/info [waitress-2]
2025-07-15 10:14:59  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/user/info [waitress-2]
2025-07-15 10:14:59  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-2]
2025-07-15 10:14:59  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-2]
2025-07-15 10:14:59  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-2]
2025-07-15 10:14:59  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-2]
2025-07-15 10:14:59  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-15 10:14:59  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-15 10:14:59  web_server.py 73: INFO  Serving request for path: js/22.683bc166.js [waitress-0]
2025-07-15 10:14:59  web_server.py 73: INFO  Serving request for path: js/389.c4035ddd.js [waitress-2]
2025-07-15 10:14:59  task.py 113: WARNING  Task queue depth is 1 [MainThread]
2025-07-15 10:14:59  web_server.py 82: INFO  Serving static file: js/22.683bc166.js [waitress-0]
2025-07-15 10:14:59  web_server.py 73: INFO  Serving request for path: logo.png [waitress-3]
2025-07-15 10:14:59  web_server.py 73: INFO  Serving request for path: js/897.25207915.js [waitress-1]
2025-07-15 10:14:59  web_server.py 82: INFO  Serving static file: js/389.c4035ddd.js [waitress-2]
2025-07-15 10:14:59  web_server.py 82: INFO  Serving static file: logo.png [waitress-3]
2025-07-15 10:14:59  web_server.py 82: INFO  Serving static file: js/897.25207915.js [waitress-1]
2025-07-15 10:14:59  web_server.py 73: INFO  Serving request for path: css/389.34c20730.css [waitress-0]
2025-07-15 10:14:59  web_server.py 82: INFO  Serving static file: css/389.34c20730.css [waitress-0]
2025-07-15 10:14:59  web_server.py 73: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-1]
2025-07-15 10:14:59  web_server.py 82: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-1]
2025-07-15 10:14:59  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getList [waitress-2]
2025-07-15 10:14:59  web_server.py 73: INFO  Serving request for path: fonts/element-icons.ff18efd1.woff [waitress-0]
2025-07-15 10:14:59  app.py 1454: ERROR  Exception on /api/home/<USER>
Traceback (most recent call last):
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1900, in _execute_context
    self.dialect.do_execute(
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
  File "C:\Python311\Lib\site-packages\pymysql\cursors.py", line 148, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\cursors.py", line 310, in _query
    conn.query(q)
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 548, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 775, in _read_query_result
    result.read()
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 1156, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 725, in _read_packet
    packet.raise_for_error()
  File "C:\Python311\Lib\site-packages\pymysql\protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "C:\Python311\Lib\site-packages\pymysql\err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'earlybird_paper_setting.category' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python311\Lib\site-packages\flask\app.py", line 2070, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask\app.py", line 1515, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask_cors\extension.py", line 165, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask\app.py", line 1513, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask\app.py", line 1499, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**req.view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask_pydantic\core.py", line 249, in wrapper
    res = func(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^
  File "d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\api\home\apis.py", line 18, in getSetting
    res: Result = HomeService().getSetting()
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\api\home\service.py", line 30, in getSetting
    "modelName": self.getModelName(),
                 ^^^^^^^^^^^^^^^^^^^
  File "d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\api\home\service.py", line 21, in getModelName
    setting: Setting = Setting.query.filter(Setting.settingKey == "modelName").first()
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\orm\query.py", line 2824, in first
    return self.limit(1)._iter().first()
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\orm\query.py", line 2916, in _iter
    result = self.session.execute(
             ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\orm\session.py", line 1714, in execute
    result = conn._execute_20(statement, params or {}, execution_options)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1705, in _execute_20
    return meth(self, args_10style, kwargs_10style, execution_options)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\sql\elements.py", line 334, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1572, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1943, in _execute_context
    self._handle_dbapi_exception(
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 2124, in _handle_dbapi_exception
    util.raise_(
  File "C:\Python311\Lib\site-packages\sqlalchemy\util\compat.py", line 211, in raise_
    raise exception
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1900, in _execute_context
    self.dialect.do_execute(
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
  File "C:\Python311\Lib\site-packages\pymysql\cursors.py", line 148, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\cursors.py", line 310, in _query
    conn.query(q)
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 548, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 775, in _read_query_result
    result.read()
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 1156, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 725, in _read_packet
    packet.raise_for_error()
  File "C:\Python311\Lib\site-packages\pymysql\protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "C:\Python311\Lib\site-packages\pymysql\err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
sqlalchemy.exc.OperationalError: (pymysql.err.OperationalError) (1054, "Unknown column 'earlybird_paper_setting.category' in 'field list'")
[SQL: SELECT earlybird_paper_setting.id AS earlybird_paper_setting_id, earlybird_paper_setting.create_time AS earlybird_paper_setting_create_time, earlybird_paper_setting.update_time AS earlybird_paper_setting_update_time, earlybird_paper_setting.is_deleted AS earlybird_paper_setting_is_deleted, earlybird_paper_setting.status AS earlybird_paper_setting_status, earlybird_paper_setting.category AS earlybird_paper_setting_category, earlybird_paper_setting.`key` AS earlybird_paper_setting_key, earlybird_paper_setting.value AS earlybird_paper_setting_value, earlybird_paper_setting.description AS earlybird_paper_setting_description, earlybird_paper_setting.`settingKey` AS `earlybird_paper_setting_settingKey`, earlybird_paper_setting.`settingValue` AS `earlybird_paper_setting_settingValue` 
FROM earlybird_paper_setting 
WHERE earlybird_paper_setting.`settingKey` = %(settingKey_1)s 
 LIMIT %(param_1)s]
[parameters: {'settingKey_1': 'modelName', 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
2025-07-15 10:14:59  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getList [waitress-2]
2025-07-15 10:14:59  web_server.py 82: INFO  Serving static file: fonts/element-icons.ff18efd1.woff [waitress-0]
2025-07-15 10:14:59  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-2]
2025-07-15 10:14:59  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-2]
2025-07-15 10:14:59  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-2]
2025-07-15 10:14:59  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-2]
2025-07-15 10:14:59  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-15 10:14:59  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-15 10:14:59  EarlyBird.api.thesis.apis 92: INFO  用户 13 请求获取论文列表 [waitress-2]
2025-07-15 10:14:59  apis.py 92: INFO  用户 13 请求获取论文列表 [waitress-2]
2025-07-15 10:14:59  EarlyBird.api.thesis.service 548: INFO  查询论文列表，用户ID: 13 [waitress-2]
2025-07-15 10:14:59  service.py 548: INFO  查询论文列表，用户ID: 13 [waitress-2]
2025-07-15 10:14:59  EarlyBird.api.thesis.service 554: INFO  查询到 1 篇论文 [waitress-2]
2025-07-15 10:14:59  service.py 554: INFO  查询到 1 篇论文 [waitress-2]
2025-07-15 10:14:59  EarlyBird.api.thesis.service 564: INFO  论文ID: 29, 标题: 船舶结构完整性先进测试方法研究, 用户ID: 13 [waitress-2]
2025-07-15 10:14:59  service.py 564: INFO  论文ID: 29, 标题: 船舶结构完整性先进测试方法研究, 用户ID: 13 [waitress-2]
2025-07-15 10:14:59  EarlyBird.api.thesis.service 566: INFO  返回论文列表，共 1 篇 [waitress-2]
2025-07-15 10:14:59  service.py 566: INFO  返回论文列表，共 1 篇 [waitress-2]
2025-07-15 10:14:59  EarlyBird.api.thesis.apis 95: INFO  用户 13 获取论文列表成功，共 1 篇 [waitress-2]
2025-07-15 10:14:59  apis.py 95: INFO  用户 13 获取论文列表成功，共 1 篇 [waitress-2]
2025-07-15 10:14:59  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getDetail [waitress-1]
2025-07-15 10:14:59  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getDetail [waitress-1]
2025-07-15 10:14:59  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-1]
2025-07-15 10:14:59  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-1]
2025-07-15 10:14:59  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-1]
2025-07-15 10:14:59  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-1]
2025-07-15 10:14:59  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-15 10:14:59  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-15 10:14:59  EarlyBird.api.talk2ai.utils 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-1]
2025-07-15 10:14:59  utils.py 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-1]
2025-07-15 10:14:59  EarlyBird.api.talk2ai.utils 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-1]
2025-07-15 10:14:59  utils.py 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-1]
2025-07-15 10:14:59  EarlyBird.api.talk2ai.utils 161: INFO  提纲处理完成，根节点ID: W8ogm7FCJ8dsAkiHfnas5u [waitress-1]
2025-07-15 10:14:59  utils.py 161: INFO  提纲处理完成，根节点ID: W8ogm7FCJ8dsAkiHfnas5u [waitress-1]
2025-07-15 10:14:59  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getOutline [waitress-3]
2025-07-15 10:14:59  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getOutline [waitress-3]
2025-07-15 10:14:59  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-3]
2025-07-15 10:14:59  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-3]
2025-07-15 10:14:59  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-3]
2025-07-15 10:14:59  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-3]
2025-07-15 10:14:59  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-15 10:14:59  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-15 10:14:59  EarlyBird.api.talk2ai.utils 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-3]
2025-07-15 10:14:59  utils.py 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-3]
2025-07-15 10:14:59  EarlyBird.api.talk2ai.utils 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-3]
2025-07-15 10:14:59  utils.py 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-3]
2025-07-15 10:14:59  EarlyBird.api.talk2ai.utils 161: INFO  提纲处理完成，根节点ID: W8ogm7FCJ8dsAkiHfnas5u [waitress-3]
2025-07-15 10:14:59  utils.py 161: INFO  提纲处理完成，根节点ID: W8ogm7FCJ8dsAkiHfnas5u [waitress-3]
2025-07-15 10:15:01  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-0]
2025-07-15 10:15:01  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-0]
2025-07-15 10:15:01  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-0]
2025-07-15 10:15:01  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-0]
2025-07-15 10:15:01  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-0]
2025-07-15 10:15:01  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-0]
2025-07-15 10:15:01  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-15 10:15:01  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-15 10:15:01  EarlyBird.api.thesis.apis 145: INFO  用户 13 请求下载论文 29 [waitress-0]
2025-07-15 10:15:01  apis.py 145: INFO  用户 13 请求下载论文 29 [waitress-0]
2025-07-15 10:15:01  EarlyBird.api.thesis.service 296: INFO  用户 13 请求导出论文 29 [waitress-0]
2025-07-15 10:15:01  service.py 296: INFO  用户 13 请求导出论文 29 [waitress-0]
2025-07-15 10:15:01  EarlyBird.api.thesis.service 304: INFO  用户 13 VIP状态: False [waitress-0]
2025-07-15 10:15:01  service.py 304: INFO  用户 13 VIP状态: False [waitress-0]
2025-07-15 10:15:01  EarlyBird.api.thesis.service 309: INFO  论文下载收费功能是否启用: True [waitress-0]
2025-07-15 10:15:01  service.py 309: INFO  论文下载收费功能是否启用: True [waitress-0]
2025-07-15 10:15:01  EarlyBird.api.thesis.service 323: INFO  VIP用户是否免费下载: False [waitress-0]
2025-07-15 10:15:01  service.py 323: INFO  VIP用户是否免费下载: False [waitress-0]
2025-07-15 10:15:01  EarlyBird.api.thesis.service 340: INFO  首次下载是否免费: False [waitress-0]
2025-07-15 10:15:01  service.py 340: INFO  首次下载是否免费: False [waitress-0]
2025-07-15 10:15:01  EarlyBird.api.thesis.service 344: INFO  用户 13 是否有已支付的下载记录: False [waitress-0]
2025-07-15 10:15:01  service.py 344: INFO  用户 13 是否有已支付的下载记录: False [waitress-0]
2025-07-15 10:15:01  EarlyBird.api.thesis.service 426: INFO  用户 13 需要支付才能下载论文 29，价格: 10.0 [waitress-0]
2025-07-15 10:15:01  service.py 426: INFO  用户 13 需要支付才能下载论文 29，价格: 10.0 [waitress-0]
2025-07-15 10:15:01  EarlyBird.api.thesis.apis 150: INFO  用户 13 需要支付才能下载论文 29，价格: 10.0 [waitress-0]
2025-07-15 10:15:01  apis.py 150: INFO  用户 13 需要支付才能下载论文 29，价格: 10.0 [waitress-0]
2025-07-15 10:15:01  app.py 1454: ERROR  Exception on /api/thesis/downloadThesis [POST] [waitress-0]
Traceback (most recent call last):
  File "C:\Python311\Lib\site-packages\flask\app.py", line 2070, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask\app.py", line 1515, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask_cors\extension.py", line 165, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask\app.py", line 1513, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask\app.py", line 1499, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**req.view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask_pydantic\core.py", line 249, in wrapper
    res = func(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^
  File "d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\api\thesis\apis.py", line 152, in downloadThesis
    return ApiResponse(
           ^^^^^^^^^^^^
TypeError: ApiResponse.__init__() got an unexpected keyword argument 'is_success'
2025-07-15 10:15:05  web_server.py 73: INFO  Serving request for path: paper/content [waitress-2]
2025-07-15 10:15:05  web_server.py 85: INFO  Serving index.html for path: paper/content [waitress-2]
2025-07-15 10:15:05  web_server.py 73: INFO  Serving request for path: js/chunk-vendors.04aff60f.js [waitress-1]
2025-07-15 10:15:05  web_server.py 73: INFO  Serving request for path: js/app.690768f9.js [waitress-3]
2025-07-15 10:15:05  web_server.py 73: INFO  Serving request for path: css/app.1e68ddae.css [waitress-0]
2025-07-15 10:15:05  web_server.py 73: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-2]
2025-07-15 10:15:05  web_server.py 82: INFO  Serving static file: js/app.690768f9.js [waitress-3]
2025-07-15 10:15:05  web_server.py 82: INFO  Serving static file: js/chunk-vendors.04aff60f.js [waitress-1]
2025-07-15 10:15:05  web_server.py 82: INFO  Serving static file: css/app.1e68ddae.css [waitress-0]
2025-07-15 10:15:05  web_server.py 82: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-2]
2025-07-15 10:15:05  task.py 113: WARNING  Task queue depth is 1 [MainThread]
2025-07-15 10:15:05  web_server.py 73: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-1]
2025-07-15 10:15:05  web_server.py 85: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-1]
2025-07-15 10:15:05  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/user/info [waitress-3]
2025-07-15 10:15:05  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/user/info [waitress-3]
2025-07-15 10:15:05  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-3]
2025-07-15 10:15:05  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-3]
2025-07-15 10:15:05  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-3]
2025-07-15 10:15:05  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-3]
2025-07-15 10:15:05  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-15 10:15:05  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-15 10:15:05  web_server.py 73: INFO  Serving request for path: js/22.683bc166.js [waitress-2]
2025-07-15 10:15:05  web_server.py 82: INFO  Serving static file: js/22.683bc166.js [waitress-2]
2025-07-15 10:15:05  web_server.py 73: INFO  Serving request for path: css/389.34c20730.css [waitress-0]
2025-07-15 10:15:05  web_server.py 73: INFO  Serving request for path: js/897.25207915.js [waitress-1]
2025-07-15 10:15:05  web_server.py 82: INFO  Serving static file: css/389.34c20730.css [waitress-0]
2025-07-15 10:15:05  web_server.py 82: INFO  Serving static file: js/897.25207915.js [waitress-1]
2025-07-15 10:15:05  web_server.py 73: INFO  Serving request for path: logo.png [waitress-3]
2025-07-15 10:15:05  web_server.py 73: INFO  Serving request for path: js/389.c4035ddd.js [waitress-2]
2025-07-15 10:15:05  web_server.py 82: INFO  Serving static file: logo.png [waitress-3]
2025-07-15 10:15:05  web_server.py 82: INFO  Serving static file: js/389.c4035ddd.js [waitress-2]
2025-07-15 10:15:05  web_server.py 73: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-0]
2025-07-15 10:15:05  web_server.py 82: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-0]
2025-07-15 10:15:05  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getList [waitress-2]
2025-07-15 10:15:05  app.py 1454: ERROR  Exception on /api/home/<USER>
Traceback (most recent call last):
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1900, in _execute_context
    self.dialect.do_execute(
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
  File "C:\Python311\Lib\site-packages\pymysql\cursors.py", line 148, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\cursors.py", line 310, in _query
    conn.query(q)
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 548, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 775, in _read_query_result
    result.read()
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 1156, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 725, in _read_packet
    packet.raise_for_error()
  File "C:\Python311\Lib\site-packages\pymysql\protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "C:\Python311\Lib\site-packages\pymysql\err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'earlybird_paper_setting.category' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python311\Lib\site-packages\flask\app.py", line 2070, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask\app.py", line 1515, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask_cors\extension.py", line 165, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask\app.py", line 1513, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask\app.py", line 1499, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**req.view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask_pydantic\core.py", line 249, in wrapper
    res = func(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^
  File "d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\api\home\apis.py", line 18, in getSetting
    res: Result = HomeService().getSetting()
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\api\home\service.py", line 30, in getSetting
    "modelName": self.getModelName(),
                 ^^^^^^^^^^^^^^^^^^^
  File "d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\api\home\service.py", line 21, in getModelName
    setting: Setting = Setting.query.filter(Setting.settingKey == "modelName").first()
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\orm\query.py", line 2824, in first
    return self.limit(1)._iter().first()
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\orm\query.py", line 2916, in _iter
    result = self.session.execute(
             ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\orm\session.py", line 1714, in execute
    result = conn._execute_20(statement, params or {}, execution_options)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1705, in _execute_20
    return meth(self, args_10style, kwargs_10style, execution_options)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\sql\elements.py", line 334, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1572, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1943, in _execute_context
    self._handle_dbapi_exception(
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 2124, in _handle_dbapi_exception
    util.raise_(
  File "C:\Python311\Lib\site-packages\sqlalchemy\util\compat.py", line 211, in raise_
    raise exception
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1900, in _execute_context
    self.dialect.do_execute(
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
  File "C:\Python311\Lib\site-packages\pymysql\cursors.py", line 148, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\cursors.py", line 310, in _query
    conn.query(q)
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 548, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 775, in _read_query_result
    result.read()
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 1156, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 725, in _read_packet
    packet.raise_for_error()
  File "C:\Python311\Lib\site-packages\pymysql\protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "C:\Python311\Lib\site-packages\pymysql\err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
sqlalchemy.exc.OperationalError: (pymysql.err.OperationalError) (1054, "Unknown column 'earlybird_paper_setting.category' in 'field list'")
[SQL: SELECT earlybird_paper_setting.id AS earlybird_paper_setting_id, earlybird_paper_setting.create_time AS earlybird_paper_setting_create_time, earlybird_paper_setting.update_time AS earlybird_paper_setting_update_time, earlybird_paper_setting.is_deleted AS earlybird_paper_setting_is_deleted, earlybird_paper_setting.status AS earlybird_paper_setting_status, earlybird_paper_setting.category AS earlybird_paper_setting_category, earlybird_paper_setting.`key` AS earlybird_paper_setting_key, earlybird_paper_setting.value AS earlybird_paper_setting_value, earlybird_paper_setting.description AS earlybird_paper_setting_description, earlybird_paper_setting.`settingKey` AS `earlybird_paper_setting_settingKey`, earlybird_paper_setting.`settingValue` AS `earlybird_paper_setting_settingValue` 
FROM earlybird_paper_setting 
WHERE earlybird_paper_setting.`settingKey` = %(settingKey_1)s 
 LIMIT %(param_1)s]
[parameters: {'settingKey_1': 'modelName', 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
2025-07-15 10:15:05  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getList [waitress-2]
2025-07-15 10:15:05  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-2]
2025-07-15 10:15:05  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-2]
2025-07-15 10:15:05  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-2]
2025-07-15 10:15:05  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-2]
2025-07-15 10:15:05  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-15 10:15:05  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-15 10:15:05  EarlyBird.api.thesis.apis 92: INFO  用户 13 请求获取论文列表 [waitress-2]
2025-07-15 10:15:05  apis.py 92: INFO  用户 13 请求获取论文列表 [waitress-2]
2025-07-15 10:15:05  EarlyBird.api.thesis.service 548: INFO  查询论文列表，用户ID: 13 [waitress-2]
2025-07-15 10:15:05  service.py 548: INFO  查询论文列表，用户ID: 13 [waitress-2]
2025-07-15 10:15:05  EarlyBird.api.thesis.service 554: INFO  查询到 1 篇论文 [waitress-2]
2025-07-15 10:15:05  service.py 554: INFO  查询到 1 篇论文 [waitress-2]
2025-07-15 10:15:05  EarlyBird.api.thesis.service 564: INFO  论文ID: 29, 标题: 船舶结构完整性先进测试方法研究, 用户ID: 13 [waitress-2]
2025-07-15 10:15:05  service.py 564: INFO  论文ID: 29, 标题: 船舶结构完整性先进测试方法研究, 用户ID: 13 [waitress-2]
2025-07-15 10:15:05  EarlyBird.api.thesis.service 566: INFO  返回论文列表，共 1 篇 [waitress-2]
2025-07-15 10:15:05  service.py 566: INFO  返回论文列表，共 1 篇 [waitress-2]
2025-07-15 10:15:05  EarlyBird.api.thesis.apis 95: INFO  用户 13 获取论文列表成功，共 1 篇 [waitress-2]
2025-07-15 10:15:05  apis.py 95: INFO  用户 13 获取论文列表成功，共 1 篇 [waitress-2]
2025-07-15 10:15:05  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getDetail [waitress-3]
2025-07-15 10:15:05  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getDetail [waitress-3]
2025-07-15 10:15:05  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-3]
2025-07-15 10:15:05  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-3]
2025-07-15 10:15:05  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-3]
2025-07-15 10:15:05  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-3]
2025-07-15 10:15:05  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-15 10:15:05  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-15 10:15:05  EarlyBird.api.talk2ai.utils 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-3]
2025-07-15 10:15:05  utils.py 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-3]
2025-07-15 10:15:05  EarlyBird.api.talk2ai.utils 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-3]
2025-07-15 10:15:05  utils.py 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-3]
2025-07-15 10:15:05  EarlyBird.api.talk2ai.utils 161: INFO  提纲处理完成，根节点ID: W8ogm7FCJ8dsAkiHfnas5u [waitress-3]
2025-07-15 10:15:05  utils.py 161: INFO  提纲处理完成，根节点ID: W8ogm7FCJ8dsAkiHfnas5u [waitress-3]
2025-07-15 10:15:05  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getOutline [waitress-0]
2025-07-15 10:15:05  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getOutline [waitress-0]
2025-07-15 10:15:05  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-0]
2025-07-15 10:15:05  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-0]
2025-07-15 10:15:05  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-0]
2025-07-15 10:15:05  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-0]
2025-07-15 10:15:05  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-15 10:15:05  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-15 10:15:05  EarlyBird.api.talk2ai.utils 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-0]
2025-07-15 10:15:05  utils.py 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-0]
2025-07-15 10:15:05  EarlyBird.api.talk2ai.utils 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-0]
2025-07-15 10:15:05  utils.py 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-0]
2025-07-15 10:15:05  EarlyBird.api.talk2ai.utils 161: INFO  提纲处理完成，根节点ID: W8ogm7FCJ8dsAkiHfnas5u [waitress-0]
2025-07-15 10:15:05  utils.py 161: INFO  提纲处理完成，根节点ID: W8ogm7FCJ8dsAkiHfnas5u [waitress-0]
2025-07-15 10:15:13  init_flask.py 27: INFO  {'CpuCount': 6, 'Mem': '32 GB'} [MainThread]
2025-07-15 10:15:13  web_server.py 32: INFO  Static directory: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\ui [MainThread]
2025-07-15 10:15:13  web_server.py 33: INFO  Static directory exists: True [MainThread]
2025-07-15 10:15:13  web_server.py 37: INFO  设置工作目录: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird [MainThread]
2025-07-15 10:15:13  EarlyBird.config.config 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-15 10:15:13  config.py 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-15 10:15:13  config.py 94: INFO  Database URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-15 10:15:15  EarlyBird.api.admin.app 39: INFO  ✅ 后台管理系统API已注册 [MainThread]
2025-07-15 10:15:15  app.py 39: INFO  ✅ 后台管理系统API已注册 [MainThread]
2025-07-15 10:15:15  EarlyBird 40: INFO  确保数据目录存在: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\data [MainThread]
2025-07-15 10:15:15  server.py 40: INFO  确保数据目录存在: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\data [MainThread]
2025-07-15 10:15:15  EarlyBird 44: INFO  开始执行数据库迁移... [MainThread]
2025-07-15 10:15:15  server.py 44: INFO  开始执行数据库迁移... [MainThread]
2025-07-15 10:15:15  config.py 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-15 10:15:15  config.py 94: INFO  Database URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-15 10:15:15  EarlyBird 53: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-15 10:15:15  server.py 53: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-15 10:15:15  EarlyBird 97: WARNING  ChatLog表不存在，将在应用启动时自动创建 [MainThread]
2025-07-15 10:15:15  server.py 97: WARNING  ChatLog表不存在，将在应用启动时自动创建 [MainThread]
2025-07-15 10:15:15  add_uid_to_chatlog.py 20: INFO  开始执行ChatLog表迁移... [MainThread]
2025-07-15 10:15:15  add_uid_to_chatlog.py 24: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-15 10:15:15  add_uid_to_chatlog.py 38: WARNING  ChatLog表不存在，可能需要先创建表 [MainThread]
2025-07-15 10:15:15  task_api_server.py 31: INFO  TaskApiServer start [MainThread]
2025-07-15 10:15:15  task_paper_generator.py 38: INFO  论文生成任务处理线程初始化 [MainThread]
2025-07-15 10:15:15  task_paper_generator.py 72: INFO  论文生成任务处理线程启动 [Thread-2]
2025-07-15 10:15:15  task_api_server.py 47: INFO  TaskApiServer listen on 127.0.0.1:3302 [Thread-1]
2025-07-15 10:15:15  task_api_server.py 17: INFO  _ApiServerThread start [Thread-3]
2025-07-15 10:15:18  web_server.py 73: INFO  Serving request for path: paper/content [waitress-0]
2025-07-15 10:15:18  web_server.py 85: INFO  Serving index.html for path: paper/content [waitress-0]
2025-07-15 10:15:18  web_server.py 73: INFO  Serving request for path: js/chunk-vendors.04aff60f.js [waitress-1]
2025-07-15 10:15:18  web_server.py 82: INFO  Serving static file: js/chunk-vendors.04aff60f.js [waitress-1]
2025-07-15 10:15:18  web_server.py 73: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-2]
2025-07-15 10:15:18  web_server.py 73: INFO  Serving request for path: css/app.1e68ddae.css [waitress-3]
2025-07-15 10:15:18  web_server.py 82: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-2]
2025-07-15 10:15:18  web_server.py 73: INFO  Serving request for path: js/app.690768f9.js [waitress-1]
2025-07-15 10:15:18  web_server.py 82: INFO  Serving static file: css/app.1e68ddae.css [waitress-3]
2025-07-15 10:15:18  web_server.py 82: INFO  Serving static file: js/app.690768f9.js [waitress-1]
2025-07-15 10:15:18  web_server.py 73: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-0]
2025-07-15 10:15:18  web_server.py 85: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-0]
2025-07-15 10:15:18  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/user/info [waitress-2]
2025-07-15 10:15:18  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/user/info [waitress-2]
2025-07-15 10:15:18  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-2]
2025-07-15 10:15:18  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-2]
2025-07-15 10:15:18  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-2]
2025-07-15 10:15:18  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-2]
2025-07-15 10:15:18  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-15 10:15:18  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-15 10:15:18  web_server.py 73: INFO  Serving request for path: logo.png [waitress-3]
2025-07-15 10:15:18  web_server.py 82: INFO  Serving static file: logo.png [waitress-3]
2025-07-15 10:15:18  web_server.py 73: INFO  Serving request for path: js/22.683bc166.js [waitress-1]
2025-07-15 10:15:18  web_server.py 82: INFO  Serving static file: js/22.683bc166.js [waitress-1]
2025-07-15 10:15:18  web_server.py 73: INFO  Serving request for path: css/389.34c20730.css [waitress-0]
2025-07-15 10:15:18  web_server.py 73: INFO  Serving request for path: js/897.25207915.js [waitress-3]
2025-07-15 10:15:18  web_server.py 82: INFO  Serving static file: css/389.34c20730.css [waitress-0]
2025-07-15 10:15:18  web_server.py 73: INFO  Serving request for path: js/389.c4035ddd.js [waitress-2]
2025-07-15 10:15:18  web_server.py 82: INFO  Serving static file: js/897.25207915.js [waitress-3]
2025-07-15 10:15:18  web_server.py 82: INFO  Serving static file: js/389.c4035ddd.js [waitress-2]
2025-07-15 10:15:18  web_server.py 73: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-1]
2025-07-15 10:15:18  web_server.py 82: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-1]
2025-07-15 10:15:18  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getList [waitress-0]
2025-07-15 10:15:18  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getList [waitress-0]
2025-07-15 10:15:18  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-0]
2025-07-15 10:15:18  app.py 1454: ERROR  Exception on /api/home/<USER>
Traceback (most recent call last):
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1900, in _execute_context
    self.dialect.do_execute(
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
  File "C:\Python311\Lib\site-packages\pymysql\cursors.py", line 148, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\cursors.py", line 310, in _query
    conn.query(q)
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 548, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 775, in _read_query_result
    result.read()
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 1156, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 725, in _read_packet
    packet.raise_for_error()
  File "C:\Python311\Lib\site-packages\pymysql\protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "C:\Python311\Lib\site-packages\pymysql\err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'earlybird_paper_setting.category' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python311\Lib\site-packages\flask\app.py", line 2070, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask\app.py", line 1515, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask_cors\extension.py", line 165, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask\app.py", line 1513, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask\app.py", line 1499, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**req.view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask_pydantic\core.py", line 249, in wrapper
    res = func(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^
  File "d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\api\home\apis.py", line 18, in getSetting
    res: Result = HomeService().getSetting()
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\api\home\service.py", line 30, in getSetting
    "modelName": self.getModelName(),
                 ^^^^^^^^^^^^^^^^^^^
  File "d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\api\home\service.py", line 21, in getModelName
    setting: Setting = Setting.query.filter(Setting.settingKey == "modelName").first()
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\orm\query.py", line 2824, in first
    return self.limit(1)._iter().first()
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\orm\query.py", line 2916, in _iter
    result = self.session.execute(
             ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\orm\session.py", line 1714, in execute
    result = conn._execute_20(statement, params or {}, execution_options)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1705, in _execute_20
    return meth(self, args_10style, kwargs_10style, execution_options)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\sql\elements.py", line 334, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1572, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1943, in _execute_context
    self._handle_dbapi_exception(
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 2124, in _handle_dbapi_exception
    util.raise_(
  File "C:\Python311\Lib\site-packages\sqlalchemy\util\compat.py", line 211, in raise_
    raise exception
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1900, in _execute_context
    self.dialect.do_execute(
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
  File "C:\Python311\Lib\site-packages\pymysql\cursors.py", line 148, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\cursors.py", line 310, in _query
    conn.query(q)
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 548, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 775, in _read_query_result
    result.read()
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 1156, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 725, in _read_packet
    packet.raise_for_error()
  File "C:\Python311\Lib\site-packages\pymysql\protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "C:\Python311\Lib\site-packages\pymysql\err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
sqlalchemy.exc.OperationalError: (pymysql.err.OperationalError) (1054, "Unknown column 'earlybird_paper_setting.category' in 'field list'")
[SQL: SELECT earlybird_paper_setting.id AS earlybird_paper_setting_id, earlybird_paper_setting.create_time AS earlybird_paper_setting_create_time, earlybird_paper_setting.update_time AS earlybird_paper_setting_update_time, earlybird_paper_setting.is_deleted AS earlybird_paper_setting_is_deleted, earlybird_paper_setting.status AS earlybird_paper_setting_status, earlybird_paper_setting.category AS earlybird_paper_setting_category, earlybird_paper_setting.`key` AS earlybird_paper_setting_key, earlybird_paper_setting.value AS earlybird_paper_setting_value, earlybird_paper_setting.description AS earlybird_paper_setting_description, earlybird_paper_setting.`settingKey` AS `earlybird_paper_setting_settingKey`, earlybird_paper_setting.`settingValue` AS `earlybird_paper_setting_settingValue` 
FROM earlybird_paper_setting 
WHERE earlybird_paper_setting.`settingKey` = %(settingKey_1)s 
 LIMIT %(param_1)s]
[parameters: {'settingKey_1': 'modelName', 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
2025-07-15 10:15:18  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-0]
2025-07-15 10:15:18  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-0]
2025-07-15 10:15:18  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-0]
2025-07-15 10:15:18  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-15 10:15:18  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-15 10:15:18  EarlyBird.api.thesis.apis 92: INFO  用户 13 请求获取论文列表 [waitress-0]
2025-07-15 10:15:18  apis.py 92: INFO  用户 13 请求获取论文列表 [waitress-0]
2025-07-15 10:15:18  EarlyBird.api.thesis.service 548: INFO  查询论文列表，用户ID: 13 [waitress-0]
2025-07-15 10:15:18  service.py 548: INFO  查询论文列表，用户ID: 13 [waitress-0]
2025-07-15 10:15:18  EarlyBird.api.thesis.service 554: INFO  查询到 1 篇论文 [waitress-0]
2025-07-15 10:15:18  service.py 554: INFO  查询到 1 篇论文 [waitress-0]
2025-07-15 10:15:18  EarlyBird.api.thesis.service 564: INFO  论文ID: 29, 标题: 船舶结构完整性先进测试方法研究, 用户ID: 13 [waitress-0]
2025-07-15 10:15:18  service.py 564: INFO  论文ID: 29, 标题: 船舶结构完整性先进测试方法研究, 用户ID: 13 [waitress-0]
2025-07-15 10:15:18  EarlyBird.api.thesis.service 566: INFO  返回论文列表，共 1 篇 [waitress-0]
2025-07-15 10:15:18  service.py 566: INFO  返回论文列表，共 1 篇 [waitress-0]
2025-07-15 10:15:18  EarlyBird.api.thesis.apis 95: INFO  用户 13 获取论文列表成功，共 1 篇 [waitress-0]
2025-07-15 10:15:18  apis.py 95: INFO  用户 13 获取论文列表成功，共 1 篇 [waitress-0]
2025-07-15 10:15:18  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getDetail [waitress-2]
2025-07-15 10:15:18  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getDetail [waitress-2]
2025-07-15 10:15:18  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-2]
2025-07-15 10:15:18  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-2]
2025-07-15 10:15:18  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-2]
2025-07-15 10:15:18  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-2]
2025-07-15 10:15:18  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-15 10:15:18  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-15 10:15:19  EarlyBird.api.talk2ai.utils 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-2]
2025-07-15 10:15:19  utils.py 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-2]
2025-07-15 10:15:19  EarlyBird.api.talk2ai.utils 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-2]
2025-07-15 10:15:19  utils.py 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-2]
2025-07-15 10:15:19  EarlyBird.api.talk2ai.utils 161: INFO  提纲处理完成，根节点ID: W8ogm7FCJ8dsAkiHfnas5u [waitress-2]
2025-07-15 10:15:19  utils.py 161: INFO  提纲处理完成，根节点ID: W8ogm7FCJ8dsAkiHfnas5u [waitress-2]
2025-07-15 10:15:19  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getOutline [waitress-1]
2025-07-15 10:15:19  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getOutline [waitress-1]
2025-07-15 10:15:19  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-1]
2025-07-15 10:15:19  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-1]
2025-07-15 10:15:19  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-1]
2025-07-15 10:15:19  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-1]
2025-07-15 10:15:19  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-15 10:15:19  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-15 10:15:19  EarlyBird.api.talk2ai.utils 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-1]
2025-07-15 10:15:19  utils.py 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-1]
2025-07-15 10:15:19  EarlyBird.api.talk2ai.utils 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-1]
2025-07-15 10:15:19  utils.py 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-1]
2025-07-15 10:15:19  EarlyBird.api.talk2ai.utils 161: INFO  提纲处理完成，根节点ID: W8ogm7FCJ8dsAkiHfnas5u [waitress-1]
2025-07-15 10:15:19  utils.py 161: INFO  提纲处理完成，根节点ID: W8ogm7FCJ8dsAkiHfnas5u [waitress-1]
2025-07-15 10:15:19  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-3]
2025-07-15 10:15:19  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-3]
2025-07-15 10:15:19  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-3]
2025-07-15 10:15:19  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-3]
2025-07-15 10:15:19  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-3]
2025-07-15 10:15:19  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-3]
2025-07-15 10:15:19  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-15 10:15:19  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-15 10:15:19  EarlyBird.api.thesis.apis 145: INFO  用户 13 请求下载论文 29 [waitress-3]
2025-07-15 10:15:19  apis.py 145: INFO  用户 13 请求下载论文 29 [waitress-3]
2025-07-15 10:15:19  EarlyBird.api.thesis.service 296: INFO  用户 13 请求导出论文 29 [waitress-3]
2025-07-15 10:15:19  service.py 296: INFO  用户 13 请求导出论文 29 [waitress-3]
2025-07-15 10:15:19  EarlyBird.api.thesis.service 304: INFO  用户 13 VIP状态: False [waitress-3]
2025-07-15 10:15:19  service.py 304: INFO  用户 13 VIP状态: False [waitress-3]
2025-07-15 10:15:19  EarlyBird.api.thesis.service 309: INFO  论文下载收费功能是否启用: True [waitress-3]
2025-07-15 10:15:19  service.py 309: INFO  论文下载收费功能是否启用: True [waitress-3]
2025-07-15 10:15:19  EarlyBird.api.thesis.service 323: INFO  VIP用户是否免费下载: False [waitress-3]
2025-07-15 10:15:19  service.py 323: INFO  VIP用户是否免费下载: False [waitress-3]
2025-07-15 10:15:19  EarlyBird.api.thesis.service 340: INFO  首次下载是否免费: False [waitress-3]
2025-07-15 10:15:19  service.py 340: INFO  首次下载是否免费: False [waitress-3]
2025-07-15 10:15:19  EarlyBird.api.thesis.service 344: INFO  用户 13 是否有已支付的下载记录: False [waitress-3]
2025-07-15 10:15:19  service.py 344: INFO  用户 13 是否有已支付的下载记录: False [waitress-3]
2025-07-15 10:15:19  EarlyBird.api.thesis.service 426: INFO  用户 13 需要支付才能下载论文 29，价格: 10.0 [waitress-3]
2025-07-15 10:15:19  service.py 426: INFO  用户 13 需要支付才能下载论文 29，价格: 10.0 [waitress-3]
2025-07-15 10:15:19  EarlyBird.api.thesis.apis 150: INFO  用户 13 需要支付才能下载论文 29，价格: 10.0 [waitress-3]
2025-07-15 10:15:19  apis.py 150: INFO  用户 13 需要支付才能下载论文 29，价格: 10.0 [waitress-3]
2025-07-15 10:15:19  app.py 1454: ERROR  Exception on /api/thesis/downloadThesis [POST] [waitress-3]
Traceback (most recent call last):
  File "C:\Python311\Lib\site-packages\flask\app.py", line 2070, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask\app.py", line 1515, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask_cors\extension.py", line 165, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask\app.py", line 1513, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask\app.py", line 1499, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**req.view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask_pydantic\core.py", line 249, in wrapper
    res = func(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^
  File "d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\api\thesis\apis.py", line 152, in downloadThesis
    return ApiResponse(
           ^^^^^^^^^^^^
TypeError: ApiResponse.__init__() got an unexpected keyword argument 'is_success'
2025-07-15 10:17:53  init_flask.py 27: INFO  {'CpuCount': 6, 'Mem': '32 GB'} [MainThread]
2025-07-15 10:17:53  web_server.py 32: INFO  Static directory: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\ui [MainThread]
2025-07-15 10:17:53  web_server.py 33: INFO  Static directory exists: True [MainThread]
2025-07-15 10:17:53  web_server.py 37: INFO  设置工作目录: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird [MainThread]
2025-07-15 10:17:53  EarlyBird.config.config 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-15 10:17:53  config.py 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-15 10:17:53  config.py 94: INFO  Database URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-15 10:17:55  EarlyBird.api.admin.app 39: INFO  ✅ 后台管理系统API已注册 [MainThread]
2025-07-15 10:17:55  app.py 39: INFO  ✅ 后台管理系统API已注册 [MainThread]
2025-07-15 10:17:55  EarlyBird 40: INFO  确保数据目录存在: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\data [MainThread]
2025-07-15 10:17:55  server.py 40: INFO  确保数据目录存在: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\data [MainThread]
2025-07-15 10:17:55  EarlyBird 44: INFO  开始执行数据库迁移... [MainThread]
2025-07-15 10:17:55  server.py 44: INFO  开始执行数据库迁移... [MainThread]
2025-07-15 10:17:55  config.py 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-15 10:17:55  config.py 94: INFO  Database URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-15 10:17:55  EarlyBird 53: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-15 10:17:55  server.py 53: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-15 10:17:55  EarlyBird 97: WARNING  ChatLog表不存在，将在应用启动时自动创建 [MainThread]
2025-07-15 10:17:55  server.py 97: WARNING  ChatLog表不存在，将在应用启动时自动创建 [MainThread]
2025-07-15 10:17:55  add_uid_to_chatlog.py 20: INFO  开始执行ChatLog表迁移... [MainThread]
2025-07-15 10:17:55  add_uid_to_chatlog.py 24: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-15 10:17:55  add_uid_to_chatlog.py 38: WARNING  ChatLog表不存在，可能需要先创建表 [MainThread]
2025-07-15 10:17:55  task_api_server.py 31: INFO  TaskApiServer start [MainThread]
2025-07-15 10:17:55  task_paper_generator.py 38: INFO  论文生成任务处理线程初始化 [MainThread]
2025-07-15 10:17:55  task_paper_generator.py 72: INFO  论文生成任务处理线程启动 [Thread-2]
2025-07-15 10:17:55  task_api_server.py 47: INFO  TaskApiServer listen on 127.0.0.1:3302 [Thread-1]
2025-07-15 10:17:55  task_api_server.py 17: INFO  _ApiServerThread start [Thread-3]
2025-07-15 10:17:57  web_server.py 73: INFO  Serving request for path: paper/content [waitress-0]
2025-07-15 10:17:57  web_server.py 85: INFO  Serving index.html for path: paper/content [waitress-0]
2025-07-15 10:17:57  web_server.py 73: INFO  Serving request for path: js/chunk-vendors.04aff60f.js [waitress-1]
2025-07-15 10:17:57  web_server.py 82: INFO  Serving static file: js/chunk-vendors.04aff60f.js [waitress-1]
2025-07-15 10:17:57  web_server.py 73: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-2]
2025-07-15 10:17:57  web_server.py 73: INFO  Serving request for path: css/app.1e68ddae.css [waitress-0]
2025-07-15 10:17:57  web_server.py 73: INFO  Serving request for path: js/app.690768f9.js [waitress-3]
2025-07-15 10:17:57  web_server.py 82: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-2]
2025-07-15 10:17:57  web_server.py 82: INFO  Serving static file: css/app.1e68ddae.css [waitress-0]
2025-07-15 10:17:57  web_server.py 73: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-1]
2025-07-15 10:17:57  web_server.py 82: INFO  Serving static file: js/app.690768f9.js [waitress-3]
2025-07-15 10:17:57  web_server.py 85: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-1]
2025-07-15 10:17:57  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/user/info [waitress-2]
2025-07-15 10:17:57  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/user/info [waitress-2]
2025-07-15 10:17:57  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-2]
2025-07-15 10:17:57  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-2]
2025-07-15 10:17:57  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-2]
2025-07-15 10:17:57  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-2]
2025-07-15 10:17:57  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-15 10:17:57  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-15 10:17:57  web_server.py 73: INFO  Serving request for path: logo.png [waitress-0]
2025-07-15 10:17:57  web_server.py 82: INFO  Serving static file: logo.png [waitress-0]
2025-07-15 10:17:57  web_server.py 73: INFO  Serving request for path: js/22.683bc166.js [waitress-1]
2025-07-15 10:17:57  web_server.py 73: INFO  Serving request for path: js/897.25207915.js [waitress-3]
2025-07-15 10:17:57  web_server.py 82: INFO  Serving static file: js/22.683bc166.js [waitress-1]
2025-07-15 10:17:57  web_server.py 82: INFO  Serving static file: js/897.25207915.js [waitress-3]
2025-07-15 10:17:57  web_server.py 73: INFO  Serving request for path: css/389.34c20730.css [waitress-0]
2025-07-15 10:17:57  web_server.py 73: INFO  Serving request for path: js/389.c4035ddd.js [waitress-2]
2025-07-15 10:17:57  web_server.py 82: INFO  Serving static file: css/389.34c20730.css [waitress-0]
2025-07-15 10:17:57  web_server.py 82: INFO  Serving static file: js/389.c4035ddd.js [waitress-2]
2025-07-15 10:17:57  web_server.py 73: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-1]
2025-07-15 10:17:57  web_server.py 82: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-1]
2025-07-15 10:17:57  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getList [waitress-0]
2025-07-15 10:17:57  app.py 1454: ERROR  Exception on /api/home/<USER>
Traceback (most recent call last):
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1900, in _execute_context
    self.dialect.do_execute(
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
  File "C:\Python311\Lib\site-packages\pymysql\cursors.py", line 148, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\cursors.py", line 310, in _query
    conn.query(q)
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 548, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 775, in _read_query_result
    result.read()
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 1156, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 725, in _read_packet
    packet.raise_for_error()
  File "C:\Python311\Lib\site-packages\pymysql\protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "C:\Python311\Lib\site-packages\pymysql\err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'earlybird_paper_setting.category' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python311\Lib\site-packages\flask\app.py", line 2070, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask\app.py", line 1515, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask_cors\extension.py", line 165, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask\app.py", line 1513, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask\app.py", line 1499, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**req.view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask_pydantic\core.py", line 249, in wrapper
    res = func(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^
  File "d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\api\home\apis.py", line 18, in getSetting
    res: Result = HomeService().getSetting()
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\api\home\service.py", line 30, in getSetting
    "modelName": self.getModelName(),
                 ^^^^^^^^^^^^^^^^^^^
  File "d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\api\home\service.py", line 21, in getModelName
    setting: Setting = Setting.query.filter(Setting.settingKey == "modelName").first()
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\orm\query.py", line 2824, in first
    return self.limit(1)._iter().first()
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\orm\query.py", line 2916, in _iter
    result = self.session.execute(
             ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\orm\session.py", line 1714, in execute
    result = conn._execute_20(statement, params or {}, execution_options)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1705, in _execute_20
    return meth(self, args_10style, kwargs_10style, execution_options)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\sql\elements.py", line 334, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1572, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1943, in _execute_context
    self._handle_dbapi_exception(
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 2124, in _handle_dbapi_exception
    util.raise_(
  File "C:\Python311\Lib\site-packages\sqlalchemy\util\compat.py", line 211, in raise_
    raise exception
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1900, in _execute_context
    self.dialect.do_execute(
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
  File "C:\Python311\Lib\site-packages\pymysql\cursors.py", line 148, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\cursors.py", line 310, in _query
    conn.query(q)
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 548, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 775, in _read_query_result
    result.read()
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 1156, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 725, in _read_packet
    packet.raise_for_error()
  File "C:\Python311\Lib\site-packages\pymysql\protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "C:\Python311\Lib\site-packages\pymysql\err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
sqlalchemy.exc.OperationalError: (pymysql.err.OperationalError) (1054, "Unknown column 'earlybird_paper_setting.category' in 'field list'")
[SQL: SELECT earlybird_paper_setting.id AS earlybird_paper_setting_id, earlybird_paper_setting.create_time AS earlybird_paper_setting_create_time, earlybird_paper_setting.update_time AS earlybird_paper_setting_update_time, earlybird_paper_setting.is_deleted AS earlybird_paper_setting_is_deleted, earlybird_paper_setting.status AS earlybird_paper_setting_status, earlybird_paper_setting.category AS earlybird_paper_setting_category, earlybird_paper_setting.`key` AS earlybird_paper_setting_key, earlybird_paper_setting.value AS earlybird_paper_setting_value, earlybird_paper_setting.description AS earlybird_paper_setting_description, earlybird_paper_setting.`settingKey` AS `earlybird_paper_setting_settingKey`, earlybird_paper_setting.`settingValue` AS `earlybird_paper_setting_settingValue` 
FROM earlybird_paper_setting 
WHERE earlybird_paper_setting.`settingKey` = %(settingKey_1)s 
 LIMIT %(param_1)s]
[parameters: {'settingKey_1': 'modelName', 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
2025-07-15 10:17:57  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getList [waitress-0]
2025-07-15 10:17:57  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-0]
2025-07-15 10:17:57  web_server.py 73: INFO  Serving request for path: fonts/element-icons.ff18efd1.woff [waitress-2]
2025-07-15 10:17:57  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-0]
2025-07-15 10:17:57  web_server.py 82: INFO  Serving static file: fonts/element-icons.ff18efd1.woff [waitress-2]
2025-07-15 10:17:57  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-0]
2025-07-15 10:17:57  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-0]
2025-07-15 10:17:57  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-15 10:17:57  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-15 10:17:57  EarlyBird.api.thesis.apis 92: INFO  用户 13 请求获取论文列表 [waitress-0]
2025-07-15 10:17:57  apis.py 92: INFO  用户 13 请求获取论文列表 [waitress-0]
2025-07-15 10:17:57  EarlyBird.api.thesis.service 548: INFO  查询论文列表，用户ID: 13 [waitress-0]
2025-07-15 10:17:57  service.py 548: INFO  查询论文列表，用户ID: 13 [waitress-0]
2025-07-15 10:17:57  EarlyBird.api.thesis.service 554: INFO  查询到 1 篇论文 [waitress-0]
2025-07-15 10:17:57  service.py 554: INFO  查询到 1 篇论文 [waitress-0]
2025-07-15 10:17:57  EarlyBird.api.thesis.service 564: INFO  论文ID: 29, 标题: 船舶结构完整性先进测试方法研究, 用户ID: 13 [waitress-0]
2025-07-15 10:17:57  service.py 564: INFO  论文ID: 29, 标题: 船舶结构完整性先进测试方法研究, 用户ID: 13 [waitress-0]
2025-07-15 10:17:57  EarlyBird.api.thesis.service 566: INFO  返回论文列表，共 1 篇 [waitress-0]
2025-07-15 10:17:57  service.py 566: INFO  返回论文列表，共 1 篇 [waitress-0]
2025-07-15 10:17:57  EarlyBird.api.thesis.apis 95: INFO  用户 13 获取论文列表成功，共 1 篇 [waitress-0]
2025-07-15 10:17:57  apis.py 95: INFO  用户 13 获取论文列表成功，共 1 篇 [waitress-0]
2025-07-15 10:17:57  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getDetail [waitress-1]
2025-07-15 10:17:57  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getDetail [waitress-1]
2025-07-15 10:17:57  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-1]
2025-07-15 10:17:57  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-1]
2025-07-15 10:17:57  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-1]
2025-07-15 10:17:57  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-1]
2025-07-15 10:17:57  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-15 10:17:57  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-15 10:17:57  EarlyBird.api.talk2ai.utils 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-1]
2025-07-15 10:17:57  utils.py 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-1]
2025-07-15 10:17:57  EarlyBird.api.talk2ai.utils 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-1]
2025-07-15 10:17:57  utils.py 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-1]
2025-07-15 10:17:57  EarlyBird.api.talk2ai.utils 161: INFO  提纲处理完成，根节点ID: W8ogm7FCJ8dsAkiHfnas5u [waitress-1]
2025-07-15 10:17:57  utils.py 161: INFO  提纲处理完成，根节点ID: W8ogm7FCJ8dsAkiHfnas5u [waitress-1]
2025-07-15 10:17:57  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getOutline [waitress-3]
2025-07-15 10:17:57  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getOutline [waitress-3]
2025-07-15 10:17:57  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-3]
2025-07-15 10:17:57  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-3]
2025-07-15 10:17:57  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-3]
2025-07-15 10:17:57  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-3]
2025-07-15 10:17:57  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-15 10:17:57  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-15 10:17:57  EarlyBird.api.talk2ai.utils 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-3]
2025-07-15 10:17:57  utils.py 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-3]
2025-07-15 10:17:57  EarlyBird.api.talk2ai.utils 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-3]
2025-07-15 10:17:57  utils.py 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-3]
2025-07-15 10:17:57  EarlyBird.api.talk2ai.utils 161: INFO  提纲处理完成，根节点ID: W8ogm7FCJ8dsAkiHfnas5u [waitress-3]
2025-07-15 10:17:57  utils.py 161: INFO  提纲处理完成，根节点ID: W8ogm7FCJ8dsAkiHfnas5u [waitress-3]
2025-07-15 10:17:58  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-2]
2025-07-15 10:17:58  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-2]
2025-07-15 10:17:58  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-2]
2025-07-15 10:17:58  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-2]
2025-07-15 10:17:58  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-2]
2025-07-15 10:17:58  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-2]
2025-07-15 10:17:58  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-15 10:17:58  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-15 10:17:58  EarlyBird.api.thesis.apis 145: INFO  用户 13 请求下载论文 29 [waitress-2]
2025-07-15 10:17:58  apis.py 145: INFO  用户 13 请求下载论文 29 [waitress-2]
2025-07-15 10:17:58  EarlyBird.api.thesis.service 296: INFO  用户 13 请求导出论文 29 [waitress-2]
2025-07-15 10:17:58  service.py 296: INFO  用户 13 请求导出论文 29 [waitress-2]
2025-07-15 10:17:58  EarlyBird.api.thesis.service 304: INFO  用户 13 VIP状态: False [waitress-2]
2025-07-15 10:17:58  service.py 304: INFO  用户 13 VIP状态: False [waitress-2]
2025-07-15 10:17:58  EarlyBird.api.thesis.service 309: INFO  论文下载收费功能是否启用: True [waitress-2]
2025-07-15 10:17:58  service.py 309: INFO  论文下载收费功能是否启用: True [waitress-2]
2025-07-15 10:17:58  EarlyBird.api.thesis.service 323: INFO  VIP用户是否免费下载: False [waitress-2]
2025-07-15 10:17:58  service.py 323: INFO  VIP用户是否免费下载: False [waitress-2]
2025-07-15 10:17:58  EarlyBird.api.thesis.service 340: INFO  首次下载是否免费: False [waitress-2]
2025-07-15 10:17:58  service.py 340: INFO  首次下载是否免费: False [waitress-2]
2025-07-15 10:17:58  EarlyBird.api.thesis.service 344: INFO  用户 13 是否有已支付的下载记录: False [waitress-2]
2025-07-15 10:17:58  service.py 344: INFO  用户 13 是否有已支付的下载记录: False [waitress-2]
2025-07-15 10:17:58  EarlyBird.api.thesis.service 426: INFO  用户 13 需要支付才能下载论文 29，价格: 10.0 [waitress-2]
2025-07-15 10:17:58  service.py 426: INFO  用户 13 需要支付才能下载论文 29，价格: 10.0 [waitress-2]
2025-07-15 10:17:58  EarlyBird.api.thesis.apis 150: INFO  用户 13 需要支付才能下载论文 29，价格: 10.0 [waitress-2]
2025-07-15 10:17:58  apis.py 150: INFO  用户 13 需要支付才能下载论文 29，价格: 10.0 [waitress-2]
2025-07-15 10:17:58  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/payDownload [waitress-0]
2025-07-15 10:17:58  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/payDownload [waitress-0]
2025-07-15 10:17:58  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-0]
2025-07-15 10:17:58  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-0]
2025-07-15 10:17:58  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-0]
2025-07-15 10:17:58  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-0]
2025-07-15 10:17:58  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-15 10:17:58  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-15 10:18:04  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-1]
2025-07-15 10:18:04  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-1]
2025-07-15 10:18:04  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-1]
2025-07-15 10:18:04  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-1]
2025-07-15 10:18:04  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-1]
2025-07-15 10:18:04  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-1]
2025-07-15 10:18:04  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-15 10:18:04  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-15 10:18:04  EarlyBird.api.thesis.apis 145: INFO  用户 13 请求下载论文 29 [waitress-1]
2025-07-15 10:18:04  apis.py 145: INFO  用户 13 请求下载论文 29 [waitress-1]
2025-07-15 10:18:04  EarlyBird.api.thesis.service 296: INFO  用户 13 请求导出论文 29 [waitress-1]
2025-07-15 10:18:04  service.py 296: INFO  用户 13 请求导出论文 29 [waitress-1]
2025-07-15 10:18:04  EarlyBird.api.thesis.service 304: INFO  用户 13 VIP状态: False [waitress-1]
2025-07-15 10:18:04  service.py 304: INFO  用户 13 VIP状态: False [waitress-1]
2025-07-15 10:18:04  EarlyBird.api.thesis.service 309: INFO  论文下载收费功能是否启用: True [waitress-1]
2025-07-15 10:18:04  service.py 309: INFO  论文下载收费功能是否启用: True [waitress-1]
2025-07-15 10:18:04  EarlyBird.api.thesis.service 323: INFO  VIP用户是否免费下载: False [waitress-1]
2025-07-15 10:18:04  service.py 323: INFO  VIP用户是否免费下载: False [waitress-1]
2025-07-15 10:18:04  EarlyBird.api.thesis.service 340: INFO  首次下载是否免费: False [waitress-1]
2025-07-15 10:18:04  service.py 340: INFO  首次下载是否免费: False [waitress-1]
2025-07-15 10:18:04  EarlyBird.api.thesis.service 344: INFO  用户 13 是否有已支付的下载记录: True [waitress-1]
2025-07-15 10:18:04  service.py 344: INFO  用户 13 是否有已支付的下载记录: True [waitress-1]
2025-07-15 10:18:04  EarlyBird.api.thesis.service 362: INFO  用户 13 已有已支付的下载记录，可直接下载论文 29 [waitress-1]
2025-07-15 10:18:04  service.py 362: INFO  用户 13 已有已支付的下载记录，可直接下载论文 29 [waitress-1]
2025-07-15 10:18:04  EarlyBird.api.thesis.service 737: INFO  add Para:船舶结构完整性先进测试方法研究 [waitress-1]
2025-07-15 10:18:04  service.py 737: INFO  add Para:船舶结构完整性先进测试方法研究 [waitress-1]
2025-07-15 10:18:04  EarlyBird.api.thesis.service 737: INFO  add Para:绪论 [waitress-1]
2025-07-15 10:18:04  service.py 737: INFO  add Para:绪论 [waitress-1]
2025-07-15 10:18:04  EarlyBird.api.thesis.service 737: INFO  add Para:研究背景与现状 [waitress-1]
2025-07-15 10:18:04  service.py 737: INFO  add Para:研究背景与现状 [waitress-1]
2025-07-15 10:18:04  EarlyBird.api.thesis.service 737: INFO  add Para:船舶结构完整性数据现状 [waitress-1]
2025-07-15 10:18:04  service.py 737: INFO  add Para:船舶结构完整性数据现状 [waitress-1]
2025-07-15 10:18:04  EarlyBird.api.thesis.service 737: INFO  add Para:研究意义与目标 [waitress-1]
2025-07-15 10:18:04  service.py 737: INFO  add Para:研究意义与目标 [waitress-1]
2025-07-15 10:18:04  EarlyBird.api.thesis.service 737: INFO  add Para:理论基础与数据分析模型 [waitress-1]
2025-07-15 10:18:04  service.py 737: INFO  add Para:理论基础与数据分析模型 [waitress-1]
2025-07-15 10:18:04  EarlyBird.api.thesis.service 737: INFO  add Para:船舶结构完整性的理论框架 [waitress-1]
2025-07-15 10:18:04  service.py 737: INFO  add Para:船舶结构完整性的理论框架 [waitress-1]
2025-07-15 10:18:04  EarlyBird.api.thesis.service 737: INFO  add Para:基于大数据的船舶结构分析模型 [waitress-1]
2025-07-15 10:18:04  service.py 737: INFO  add Para:基于大数据的船舶结构分析模型 [waitress-1]
2025-07-15 10:18:04  EarlyBird.api.thesis.service 737: INFO  add Para:数据分析方法的选择与应用 [waitress-1]
2025-07-15 10:18:04  service.py 737: INFO  add Para:数据分析方法的选择与应用 [waitress-1]
2025-07-15 10:18:04  EarlyBird.api.thesis.service 737: INFO  add Para:实证研究：数据收集与处理 [waitress-1]
2025-07-15 10:18:04  service.py 737: INFO  add Para:实证研究：数据收集与处理 [waitress-1]
2025-07-15 10:18:04  EarlyBird.api.thesis.service 737: INFO  add Para:数据来源与采集方法 [waitress-1]
2025-07-15 10:18:04  service.py 737: INFO  add Para:数据来源与采集方法 [waitress-1]
2025-07-15 10:18:04  EarlyBird.api.thesis.service 737: INFO  add Para:数据清洗与预处理 [waitress-1]
2025-07-15 10:18:04  service.py 737: INFO  add Para:数据清洗与预处理 [waitress-1]
2025-07-15 10:18:04  EarlyBird.api.thesis.service 737: INFO  add Para:数据统计与初步分析 [waitress-1]
2025-07-15 10:18:04  service.py 737: INFO  add Para:数据统计与初步分析 [waitress-1]
2025-07-15 10:18:04  EarlyBird.api.thesis.service 737: INFO  add Para:案例分析：特定场景下的数据对比 [waitress-1]
2025-07-15 10:18:04  service.py 737: INFO  add Para:案例分析：特定场景下的数据对比 [waitress-1]
2025-07-15 10:18:04  EarlyBird.api.thesis.service 737: INFO  add Para:选取典型船舶结构案例 [waitress-1]
2025-07-15 10:18:04  service.py 737: INFO  add Para:选取典型船舶结构案例 [waitress-1]
2025-07-15 10:18:04  EarlyBird.api.thesis.service 737: INFO  add Para:不同测试方法的数据对比 [waitress-1]
2025-07-15 10:18:04  service.py 737: INFO  add Para:不同测试方法的数据对比 [waitress-1]
2025-07-15 10:18:04  EarlyBird.api.thesis.service 737: INFO  add Para:案例效果评估与反馈 [waitress-1]
2025-07-15 10:18:04  service.py 737: INFO  add Para:案例效果评估与反馈 [waitress-1]
2025-07-15 10:18:04  EarlyBird.api.thesis.service 737: INFO  add Para:数据展示与深度分析 [waitress-1]
2025-07-15 10:18:04  service.py 737: INFO  add Para:数据展示与深度分析 [waitress-1]
2025-07-15 10:18:04  EarlyBird.api.thesis.service 737: INFO  add Para:关键数据可视化展示 [waitress-1]
2025-07-15 10:18:04  service.py 737: INFO  add Para:关键数据可视化展示 [waitress-1]
2025-07-15 10:18:04  EarlyBird.api.thesis.service 737: INFO  add Para:异常数据检测与处理 [waitress-1]
2025-07-15 10:18:04  service.py 737: INFO  add Para:异常数据检测与处理 [waitress-1]
2025-07-15 10:18:04  EarlyBird.api.thesis.service 737: INFO  add Para:基于数据的深度洞察与解释 [waitress-1]
2025-07-15 10:18:04  service.py 737: INFO  add Para:基于数据的深度洞察与解释 [waitress-1]
2025-07-15 10:18:04  EarlyBird.api.thesis.service 737: INFO  add Para:结论与政策建议 [waitress-1]
2025-07-15 10:18:04  service.py 737: INFO  add Para:结论与政策建议 [waitress-1]
2025-07-15 10:18:04  EarlyBird.api.thesis.service 737: INFO  add Para:数据总结与研究发现 [waitress-1]
2025-07-15 10:18:04  service.py 737: INFO  add Para:数据总结与研究发现 [waitress-1]
2025-07-15 10:18:04  EarlyBird.api.thesis.service 737: INFO  add Para:对船舶设计与维护的政策建议 [waitress-1]
2025-07-15 10:18:04  service.py 737: INFO  add Para:对船舶设计与维护的政策建议 [waitress-1]
2025-07-15 10:18:04  EarlyBird.api.thesis.service 737: INFO  add Para:未来研究方向与展望 [waitress-1]
2025-07-15 10:18:04  service.py 737: INFO  add Para:未来研究方向与展望 [waitress-1]
2025-07-15 10:18:04  EarlyBird.common.docx 500: INFO  d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\download\[早鸟论文]_船舶结构完整性先进测试方法研究_V20250715_101804.docx [waitress-1]
2025-07-15 10:18:04  docx.py 500: INFO  d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\download\[早鸟论文]_船舶结构完整性先进测试方法研究_V20250715_101804.docx [waitress-1]
2025-07-15 10:18:04  EarlyBird.api.thesis.apis 162: INFO  用户 13 下载论文 29 成功 [waitress-1]
2025-07-15 10:18:04  apis.py 162: INFO  用户 13 下载论文 29 成功 [waitress-1]
2025-07-15 10:18:04  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/download [waitress-3]
2025-07-15 10:18:04  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/download [waitress-3]
2025-07-15 10:18:04  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-3]
2025-07-15 10:18:04  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-3]
2025-07-15 10:18:04  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-3]
2025-07-15 10:18:04  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-3]
2025-07-15 10:18:04  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-15 10:18:04  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-15 10:22:56  init_flask.py 27: INFO  {'CpuCount': 6, 'Mem': '32 GB'} [MainThread]
2025-07-15 10:22:56  web_server.py 32: INFO  Static directory: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\ui [MainThread]
2025-07-15 10:22:56  web_server.py 33: INFO  Static directory exists: True [MainThread]
2025-07-15 10:22:56  web_server.py 37: INFO  设置工作目录: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird [MainThread]
2025-07-15 10:22:57  EarlyBird.config.config 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-15 10:22:57  config.py 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-15 10:22:57  config.py 94: INFO  Database URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-15 10:22:59  EarlyBird.api.admin.app 39: INFO  ✅ 后台管理系统API已注册 [MainThread]
2025-07-15 10:22:59  app.py 39: INFO  ✅ 后台管理系统API已注册 [MainThread]
2025-07-15 10:22:59  EarlyBird 40: INFO  确保数据目录存在: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\data [MainThread]
2025-07-15 10:22:59  server.py 40: INFO  确保数据目录存在: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\data [MainThread]
2025-07-15 10:22:59  EarlyBird 44: INFO  开始执行数据库迁移... [MainThread]
2025-07-15 10:22:59  server.py 44: INFO  开始执行数据库迁移... [MainThread]
2025-07-15 10:22:59  config.py 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-15 10:22:59  config.py 94: INFO  Database URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-15 10:22:59  EarlyBird 53: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-15 10:22:59  server.py 53: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-15 10:22:59  EarlyBird 97: WARNING  ChatLog表不存在，将在应用启动时自动创建 [MainThread]
2025-07-15 10:22:59  server.py 97: WARNING  ChatLog表不存在，将在应用启动时自动创建 [MainThread]
2025-07-15 10:22:59  add_uid_to_chatlog.py 20: INFO  开始执行ChatLog表迁移... [MainThread]
2025-07-15 10:22:59  add_uid_to_chatlog.py 24: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-15 10:22:59  add_uid_to_chatlog.py 38: WARNING  ChatLog表不存在，可能需要先创建表 [MainThread]
2025-07-15 10:22:59  task_api_server.py 31: INFO  TaskApiServer start [MainThread]
2025-07-15 10:22:59  task_paper_generator.py 38: INFO  论文生成任务处理线程初始化 [MainThread]
2025-07-15 10:22:59  task_paper_generator.py 72: INFO  论文生成任务处理线程启动 [Thread-2]
2025-07-15 10:22:59  task_api_server.py 47: INFO  TaskApiServer listen on 127.0.0.1:3302 [Thread-1]
2025-07-15 10:22:59  task_api_server.py 17: INFO  _ApiServerThread start [Thread-3]
2025-07-15 10:22:59  web_server.py 73: INFO  Serving request for path: paper/content [waitress-0]
2025-07-15 10:22:59  web_server.py 85: INFO  Serving index.html for path: paper/content [waitress-0]
2025-07-15 10:22:59  web_server.py 73: INFO  Serving request for path: js/app.994e6d65.js [waitress-1]
2025-07-15 10:22:59  web_server.py 73: INFO  Serving request for path: js/chunk-vendors.04aff60f.js [waitress-2]
2025-07-15 10:22:59  web_server.py 73: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-3]
2025-07-15 10:22:59  web_server.py 82: INFO  Serving static file: js/app.994e6d65.js [waitress-1]
2025-07-15 10:22:59  web_server.py 73: INFO  Serving request for path: css/app.1e68ddae.css [waitress-0]
2025-07-15 10:22:59  web_server.py 82: INFO  Serving static file: js/chunk-vendors.04aff60f.js [waitress-2]
2025-07-15 10:22:59  web_server.py 82: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-3]
2025-07-15 10:22:59  web_server.py 82: INFO  Serving static file: css/app.1e68ddae.css [waitress-0]
2025-07-15 10:22:59  task.py 113: WARNING  Task queue depth is 1 [MainThread]
2025-07-15 10:22:59  web_server.py 73: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-3]
2025-07-15 10:22:59  web_server.py 85: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-3]
2025-07-15 10:23:00  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/user/info [waitress-1]
2025-07-15 10:23:00  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/user/info [waitress-1]
2025-07-15 10:23:00  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-1]
2025-07-15 10:23:00  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-1]
2025-07-15 10:23:00  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-1]
2025-07-15 10:23:00  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-1]
2025-07-15 10:23:00  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-15 10:23:00  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-15 10:23:00  web_server.py 73: INFO  Serving request for path: logo.png [waitress-0]
2025-07-15 10:23:00  web_server.py 82: INFO  Serving static file: logo.png [waitress-0]
2025-07-15 10:23:00  web_server.py 73: INFO  Serving request for path: js/22.683bc166.js [waitress-2]
2025-07-15 10:23:00  web_server.py 73: INFO  Serving request for path: css/389.407de41a.css [waitress-3]
2025-07-15 10:23:00  web_server.py 82: INFO  Serving static file: js/22.683bc166.js [waitress-2]
2025-07-15 10:23:00  web_server.py 73: INFO  Serving request for path: js/897.25207915.js [waitress-0]
2025-07-15 10:23:00  web_server.py 73: INFO  Serving request for path: js/389.159e8c49.js [waitress-1]
2025-07-15 10:23:00  web_server.py 82: INFO  Serving static file: css/389.407de41a.css [waitress-3]
2025-07-15 10:23:00  web_server.py 82: INFO  Serving static file: js/897.25207915.js [waitress-0]
2025-07-15 10:23:00  web_server.py 82: INFO  Serving static file: js/389.159e8c49.js [waitress-1]
2025-07-15 10:23:00  web_server.py 73: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-0]
2025-07-15 10:23:00  web_server.py 82: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-0]
2025-07-15 10:23:00  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getList [waitress-3]
2025-07-15 10:23:00  app.py 1454: ERROR  Exception on /api/home/<USER>
Traceback (most recent call last):
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1900, in _execute_context
    self.dialect.do_execute(
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
  File "C:\Python311\Lib\site-packages\pymysql\cursors.py", line 148, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\cursors.py", line 310, in _query
    conn.query(q)
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 548, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 775, in _read_query_result
    result.read()
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 1156, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 725, in _read_packet
    packet.raise_for_error()
  File "C:\Python311\Lib\site-packages\pymysql\protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "C:\Python311\Lib\site-packages\pymysql\err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'earlybird_paper_setting.category' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python311\Lib\site-packages\flask\app.py", line 2070, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask\app.py", line 1515, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask_cors\extension.py", line 165, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask\app.py", line 1513, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask\app.py", line 1499, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**req.view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask_pydantic\core.py", line 249, in wrapper
    res = func(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^
  File "d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\api\home\apis.py", line 18, in getSetting
    res: Result = HomeService().getSetting()
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\api\home\service.py", line 30, in getSetting
    "modelName": self.getModelName(),
                 ^^^^^^^^^^^^^^^^^^^
  File "d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\api\home\service.py", line 21, in getModelName
    setting: Setting = Setting.query.filter(Setting.settingKey == "modelName").first()
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\orm\query.py", line 2824, in first
    return self.limit(1)._iter().first()
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\orm\query.py", line 2916, in _iter
    result = self.session.execute(
             ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\orm\session.py", line 1714, in execute
    result = conn._execute_20(statement, params or {}, execution_options)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1705, in _execute_20
    return meth(self, args_10style, kwargs_10style, execution_options)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\sql\elements.py", line 334, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1572, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1943, in _execute_context
    self._handle_dbapi_exception(
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 2124, in _handle_dbapi_exception
    util.raise_(
  File "C:\Python311\Lib\site-packages\sqlalchemy\util\compat.py", line 211, in raise_
    raise exception
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1900, in _execute_context
    self.dialect.do_execute(
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
  File "C:\Python311\Lib\site-packages\pymysql\cursors.py", line 148, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\cursors.py", line 310, in _query
    conn.query(q)
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 548, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 775, in _read_query_result
    result.read()
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 1156, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 725, in _read_packet
    packet.raise_for_error()
  File "C:\Python311\Lib\site-packages\pymysql\protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "C:\Python311\Lib\site-packages\pymysql\err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
sqlalchemy.exc.OperationalError: (pymysql.err.OperationalError) (1054, "Unknown column 'earlybird_paper_setting.category' in 'field list'")
[SQL: SELECT earlybird_paper_setting.id AS earlybird_paper_setting_id, earlybird_paper_setting.create_time AS earlybird_paper_setting_create_time, earlybird_paper_setting.update_time AS earlybird_paper_setting_update_time, earlybird_paper_setting.is_deleted AS earlybird_paper_setting_is_deleted, earlybird_paper_setting.status AS earlybird_paper_setting_status, earlybird_paper_setting.category AS earlybird_paper_setting_category, earlybird_paper_setting.`key` AS earlybird_paper_setting_key, earlybird_paper_setting.value AS earlybird_paper_setting_value, earlybird_paper_setting.description AS earlybird_paper_setting_description, earlybird_paper_setting.`settingKey` AS `earlybird_paper_setting_settingKey`, earlybird_paper_setting.`settingValue` AS `earlybird_paper_setting_settingValue` 
FROM earlybird_paper_setting 
WHERE earlybird_paper_setting.`settingKey` = %(settingKey_1)s 
 LIMIT %(param_1)s]
[parameters: {'settingKey_1': 'modelName', 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
2025-07-15 10:23:00  web_server.py 73: INFO  Serving request for path: fonts/element-icons.ff18efd1.woff [waitress-1]
2025-07-15 10:23:00  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getList [waitress-3]
2025-07-15 10:23:00  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-3]
2025-07-15 10:23:00  web_server.py 82: INFO  Serving static file: fonts/element-icons.ff18efd1.woff [waitress-1]
2025-07-15 10:23:00  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-3]
2025-07-15 10:23:00  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-3]
2025-07-15 10:23:00  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-3]
2025-07-15 10:23:00  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-15 10:23:00  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-15 10:23:00  EarlyBird.api.thesis.apis 92: INFO  用户 13 请求获取论文列表 [waitress-3]
2025-07-15 10:23:00  apis.py 92: INFO  用户 13 请求获取论文列表 [waitress-3]
2025-07-15 10:23:00  EarlyBird.api.thesis.service 548: INFO  查询论文列表，用户ID: 13 [waitress-3]
2025-07-15 10:23:00  service.py 548: INFO  查询论文列表，用户ID: 13 [waitress-3]
2025-07-15 10:23:00  EarlyBird.api.thesis.service 554: INFO  查询到 1 篇论文 [waitress-3]
2025-07-15 10:23:00  service.py 554: INFO  查询到 1 篇论文 [waitress-3]
2025-07-15 10:23:00  EarlyBird.api.thesis.service 564: INFO  论文ID: 29, 标题: 船舶结构完整性先进测试方法研究, 用户ID: 13 [waitress-3]
2025-07-15 10:23:00  service.py 564: INFO  论文ID: 29, 标题: 船舶结构完整性先进测试方法研究, 用户ID: 13 [waitress-3]
2025-07-15 10:23:00  EarlyBird.api.thesis.service 566: INFO  返回论文列表，共 1 篇 [waitress-3]
2025-07-15 10:23:00  service.py 566: INFO  返回论文列表，共 1 篇 [waitress-3]
2025-07-15 10:23:00  EarlyBird.api.thesis.apis 95: INFO  用户 13 获取论文列表成功，共 1 篇 [waitress-3]
2025-07-15 10:23:00  apis.py 95: INFO  用户 13 获取论文列表成功，共 1 篇 [waitress-3]
2025-07-15 10:23:00  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getDetail [waitress-0]
2025-07-15 10:23:00  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getDetail [waitress-0]
2025-07-15 10:23:00  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-0]
2025-07-15 10:23:00  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-0]
2025-07-15 10:23:00  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-0]
2025-07-15 10:23:00  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-0]
2025-07-15 10:23:00  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-15 10:23:00  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-15 10:23:00  EarlyBird.api.talk2ai.utils 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-0]
2025-07-15 10:23:00  utils.py 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-0]
2025-07-15 10:23:00  EarlyBird.api.talk2ai.utils 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-0]
2025-07-15 10:23:00  utils.py 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-0]
2025-07-15 10:23:00  EarlyBird.api.talk2ai.utils 161: INFO  提纲处理完成，根节点ID: W8ogm7FCJ8dsAkiHfnas5u [waitress-0]
2025-07-15 10:23:00  utils.py 161: INFO  提纲处理完成，根节点ID: W8ogm7FCJ8dsAkiHfnas5u [waitress-0]
2025-07-15 10:23:00  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getOutline [waitress-2]
2025-07-15 10:23:00  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getOutline [waitress-2]
2025-07-15 10:23:00  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-2]
2025-07-15 10:23:00  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-2]
2025-07-15 10:23:00  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-2]
2025-07-15 10:23:00  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-2]
2025-07-15 10:23:00  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-15 10:23:00  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-15 10:23:00  EarlyBird.api.talk2ai.utils 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-2]
2025-07-15 10:23:00  utils.py 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-2]
2025-07-15 10:23:00  EarlyBird.api.talk2ai.utils 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-2]
2025-07-15 10:23:00  utils.py 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-2]
2025-07-15 10:23:00  EarlyBird.api.talk2ai.utils 161: INFO  提纲处理完成，根节点ID: W8ogm7FCJ8dsAkiHfnas5u [waitress-2]
2025-07-15 10:23:00  utils.py 161: INFO  提纲处理完成，根节点ID: W8ogm7FCJ8dsAkiHfnas5u [waitress-2]
2025-07-15 10:23:01  web_server.py 73: INFO  Serving request for path: paper/content [waitress-1]
2025-07-15 10:23:01  web_server.py 85: INFO  Serving index.html for path: paper/content [waitress-1]
2025-07-15 10:23:01  web_server.py 73: INFO  Serving request for path: js/chunk-vendors.04aff60f.js [waitress-3]
2025-07-15 10:23:01  web_server.py 82: INFO  Serving static file: js/chunk-vendors.04aff60f.js [waitress-3]
2025-07-15 10:23:01  web_server.py 73: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-0]
2025-07-15 10:23:01  web_server.py 82: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-0]
2025-07-15 10:23:01  web_server.py 73: INFO  Serving request for path: js/app.994e6d65.js [waitress-2]
2025-07-15 10:23:01  web_server.py 73: INFO  Serving request for path: css/app.1e68ddae.css [waitress-1]
2025-07-15 10:23:01  web_server.py 73: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-3]
2025-07-15 10:23:01  web_server.py 82: INFO  Serving static file: js/app.994e6d65.js [waitress-2]
2025-07-15 10:23:01  web_server.py 82: INFO  Serving static file: css/app.1e68ddae.css [waitress-1]
2025-07-15 10:23:01  web_server.py 85: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-3]
2025-07-15 10:23:01  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/user/info [waitress-0]
2025-07-15 10:23:01  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/user/info [waitress-0]
2025-07-15 10:23:01  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-0]
2025-07-15 10:23:01  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-0]
2025-07-15 10:23:01  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-0]
2025-07-15 10:23:01  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-0]
2025-07-15 10:23:01  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-15 10:23:01  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-15 10:23:01  web_server.py 73: INFO  Serving request for path: logo.png [waitress-2]
2025-07-15 10:23:01  web_server.py 82: INFO  Serving static file: logo.png [waitress-2]
2025-07-15 10:23:01  web_server.py 73: INFO  Serving request for path: js/22.683bc166.js [waitress-3]
2025-07-15 10:23:01  web_server.py 82: INFO  Serving static file: js/22.683bc166.js [waitress-3]
2025-07-15 10:23:01  web_server.py 73: INFO  Serving request for path: css/389.407de41a.css [waitress-1]
2025-07-15 10:23:01  web_server.py 73: INFO  Serving request for path: js/897.25207915.js [waitress-0]
2025-07-15 10:23:01  web_server.py 82: INFO  Serving static file: css/389.407de41a.css [waitress-1]
2025-07-15 10:23:01  web_server.py 82: INFO  Serving static file: js/897.25207915.js [waitress-0]
2025-07-15 10:23:01  web_server.py 73: INFO  Serving request for path: js/389.159e8c49.js [waitress-2]
2025-07-15 10:23:01  web_server.py 82: INFO  Serving static file: js/389.159e8c49.js [waitress-2]
2025-07-15 10:23:01  web_server.py 73: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-3]
2025-07-15 10:23:01  web_server.py 82: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-3]
2025-07-15 10:23:01  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getList [waitress-0]
2025-07-15 10:23:01  app.py 1454: ERROR  Exception on /api/home/<USER>
Traceback (most recent call last):
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1900, in _execute_context
    self.dialect.do_execute(
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
  File "C:\Python311\Lib\site-packages\pymysql\cursors.py", line 148, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\cursors.py", line 310, in _query
    conn.query(q)
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 548, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 775, in _read_query_result
    result.read()
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 1156, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 725, in _read_packet
    packet.raise_for_error()
  File "C:\Python311\Lib\site-packages\pymysql\protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "C:\Python311\Lib\site-packages\pymysql\err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'earlybird_paper_setting.category' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python311\Lib\site-packages\flask\app.py", line 2070, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask\app.py", line 1515, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask_cors\extension.py", line 165, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask\app.py", line 1513, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask\app.py", line 1499, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**req.view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask_pydantic\core.py", line 249, in wrapper
    res = func(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^
  File "d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\api\home\apis.py", line 18, in getSetting
    res: Result = HomeService().getSetting()
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\api\home\service.py", line 30, in getSetting
    "modelName": self.getModelName(),
                 ^^^^^^^^^^^^^^^^^^^
  File "d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\api\home\service.py", line 21, in getModelName
    setting: Setting = Setting.query.filter(Setting.settingKey == "modelName").first()
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\orm\query.py", line 2824, in first
    return self.limit(1)._iter().first()
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\orm\query.py", line 2916, in _iter
    result = self.session.execute(
             ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\orm\session.py", line 1714, in execute
    result = conn._execute_20(statement, params or {}, execution_options)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1705, in _execute_20
    return meth(self, args_10style, kwargs_10style, execution_options)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\sql\elements.py", line 334, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1572, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1943, in _execute_context
    self._handle_dbapi_exception(
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 2124, in _handle_dbapi_exception
    util.raise_(
  File "C:\Python311\Lib\site-packages\sqlalchemy\util\compat.py", line 211, in raise_
    raise exception
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1900, in _execute_context
    self.dialect.do_execute(
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
  File "C:\Python311\Lib\site-packages\pymysql\cursors.py", line 148, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\cursors.py", line 310, in _query
    conn.query(q)
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 548, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 775, in _read_query_result
    result.read()
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 1156, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 725, in _read_packet
    packet.raise_for_error()
  File "C:\Python311\Lib\site-packages\pymysql\protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "C:\Python311\Lib\site-packages\pymysql\err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
sqlalchemy.exc.OperationalError: (pymysql.err.OperationalError) (1054, "Unknown column 'earlybird_paper_setting.category' in 'field list'")
[SQL: SELECT earlybird_paper_setting.id AS earlybird_paper_setting_id, earlybird_paper_setting.create_time AS earlybird_paper_setting_create_time, earlybird_paper_setting.update_time AS earlybird_paper_setting_update_time, earlybird_paper_setting.is_deleted AS earlybird_paper_setting_is_deleted, earlybird_paper_setting.status AS earlybird_paper_setting_status, earlybird_paper_setting.category AS earlybird_paper_setting_category, earlybird_paper_setting.`key` AS earlybird_paper_setting_key, earlybird_paper_setting.value AS earlybird_paper_setting_value, earlybird_paper_setting.description AS earlybird_paper_setting_description, earlybird_paper_setting.`settingKey` AS `earlybird_paper_setting_settingKey`, earlybird_paper_setting.`settingValue` AS `earlybird_paper_setting_settingValue` 
FROM earlybird_paper_setting 
WHERE earlybird_paper_setting.`settingKey` = %(settingKey_1)s 
 LIMIT %(param_1)s]
[parameters: {'settingKey_1': 'modelName', 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
2025-07-15 10:23:01  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getList [waitress-0]
2025-07-15 10:23:01  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-0]
2025-07-15 10:23:01  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-0]
2025-07-15 10:23:01  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-0]
2025-07-15 10:23:01  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-0]
2025-07-15 10:23:01  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-15 10:23:01  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-15 10:23:01  EarlyBird.api.thesis.apis 92: INFO  用户 13 请求获取论文列表 [waitress-0]
2025-07-15 10:23:01  apis.py 92: INFO  用户 13 请求获取论文列表 [waitress-0]
2025-07-15 10:23:01  EarlyBird.api.thesis.service 548: INFO  查询论文列表，用户ID: 13 [waitress-0]
2025-07-15 10:23:01  service.py 548: INFO  查询论文列表，用户ID: 13 [waitress-0]
2025-07-15 10:23:01  EarlyBird.api.thesis.service 554: INFO  查询到 1 篇论文 [waitress-0]
2025-07-15 10:23:01  service.py 554: INFO  查询到 1 篇论文 [waitress-0]
2025-07-15 10:23:01  EarlyBird.api.thesis.service 564: INFO  论文ID: 29, 标题: 船舶结构完整性先进测试方法研究, 用户ID: 13 [waitress-0]
2025-07-15 10:23:01  service.py 564: INFO  论文ID: 29, 标题: 船舶结构完整性先进测试方法研究, 用户ID: 13 [waitress-0]
2025-07-15 10:23:01  EarlyBird.api.thesis.service 566: INFO  返回论文列表，共 1 篇 [waitress-0]
2025-07-15 10:23:01  service.py 566: INFO  返回论文列表，共 1 篇 [waitress-0]
2025-07-15 10:23:01  EarlyBird.api.thesis.apis 95: INFO  用户 13 获取论文列表成功，共 1 篇 [waitress-0]
2025-07-15 10:23:01  apis.py 95: INFO  用户 13 获取论文列表成功，共 1 篇 [waitress-0]
2025-07-15 10:23:01  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getDetail [waitress-2]
2025-07-15 10:23:01  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getDetail [waitress-2]
2025-07-15 10:23:01  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-2]
2025-07-15 10:23:01  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-2]
2025-07-15 10:23:01  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-2]
2025-07-15 10:23:01  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-2]
2025-07-15 10:23:01  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-15 10:23:01  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-15 10:23:01  EarlyBird.api.talk2ai.utils 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-2]
2025-07-15 10:23:01  utils.py 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-2]
2025-07-15 10:23:01  EarlyBird.api.talk2ai.utils 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-2]
2025-07-15 10:23:01  utils.py 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-2]
2025-07-15 10:23:01  EarlyBird.api.talk2ai.utils 161: INFO  提纲处理完成，根节点ID: W8ogm7FCJ8dsAkiHfnas5u [waitress-2]
2025-07-15 10:23:01  utils.py 161: INFO  提纲处理完成，根节点ID: W8ogm7FCJ8dsAkiHfnas5u [waitress-2]
2025-07-15 10:23:01  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getOutline [waitress-3]
2025-07-15 10:23:01  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getOutline [waitress-3]
2025-07-15 10:23:01  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-3]
2025-07-15 10:23:01  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-3]
2025-07-15 10:23:01  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-3]
2025-07-15 10:23:01  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-3]
2025-07-15 10:23:01  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-15 10:23:01  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-15 10:23:01  EarlyBird.api.talk2ai.utils 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-3]
2025-07-15 10:23:01  utils.py 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-3]
2025-07-15 10:23:01  EarlyBird.api.talk2ai.utils 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-3]
2025-07-15 10:23:01  utils.py 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-3]
2025-07-15 10:23:01  EarlyBird.api.talk2ai.utils 161: INFO  提纲处理完成，根节点ID: W8ogm7FCJ8dsAkiHfnas5u [waitress-3]
2025-07-15 10:23:01  utils.py 161: INFO  提纲处理完成，根节点ID: W8ogm7FCJ8dsAkiHfnas5u [waitress-3]
2025-07-15 10:23:02  web_server.py 73: INFO  Serving request for path: paper/content [waitress-1]
2025-07-15 10:23:02  web_server.py 85: INFO  Serving index.html for path: paper/content [waitress-1]
2025-07-15 10:23:02  web_server.py 73: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-0]
2025-07-15 10:23:02  web_server.py 85: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-0]
2025-07-15 10:23:02  task.py 113: WARNING  Task queue depth is 1 [MainThread]
2025-07-15 10:23:02  web_server.py 73: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-3]
2025-07-15 10:23:02  web_server.py 73: INFO  Serving request for path: css/app.1e68ddae.css [waitress-2]
2025-07-15 10:23:02  web_server.py 73: INFO  Serving request for path: js/chunk-vendors.04aff60f.js [waitress-1]
2025-07-15 10:23:02  web_server.py 73: INFO  Serving request for path: js/app.994e6d65.js [waitress-0]
2025-07-15 10:23:02  web_server.py 82: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-3]
2025-07-15 10:23:02  web_server.py 82: INFO  Serving static file: css/app.1e68ddae.css [waitress-2]
2025-07-15 10:23:02  web_server.py 82: INFO  Serving static file: js/chunk-vendors.04aff60f.js [waitress-1]
2025-07-15 10:23:02  web_server.py 82: INFO  Serving static file: js/app.994e6d65.js [waitress-0]
2025-07-15 10:23:02  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/user/info [waitress-3]
2025-07-15 10:23:02  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/user/info [waitress-3]
2025-07-15 10:23:02  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-3]
2025-07-15 10:23:02  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-3]
2025-07-15 10:23:02  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-3]
2025-07-15 10:23:02  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-3]
2025-07-15 10:23:02  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-15 10:23:02  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-15 10:23:02  web_server.py 73: INFO  Serving request for path: logo.png [waitress-2]
2025-07-15 10:23:02  web_server.py 82: INFO  Serving static file: logo.png [waitress-2]
2025-07-15 10:23:02  web_server.py 73: INFO  Serving request for path: js/22.683bc166.js [waitress-1]
2025-07-15 10:23:02  web_server.py 73: INFO  Serving request for path: js/897.25207915.js [waitress-0]
2025-07-15 10:23:02  web_server.py 82: INFO  Serving static file: js/22.683bc166.js [waitress-1]
2025-07-15 10:23:02  web_server.py 82: INFO  Serving static file: js/897.25207915.js [waitress-0]
2025-07-15 10:23:02  web_server.py 73: INFO  Serving request for path: js/389.159e8c49.js [waitress-2]
2025-07-15 10:23:02  web_server.py 73: INFO  Serving request for path: css/389.407de41a.css [waitress-3]
2025-07-15 10:23:02  web_server.py 82: INFO  Serving static file: js/389.159e8c49.js [waitress-2]
2025-07-15 10:23:02  web_server.py 82: INFO  Serving static file: css/389.407de41a.css [waitress-3]
2025-07-15 10:23:02  web_server.py 73: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-1]
2025-07-15 10:23:02  web_server.py 82: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-1]
2025-07-15 10:23:02  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getList [waitress-2]
2025-07-15 10:23:02  app.py 1454: ERROR  Exception on /api/home/<USER>
Traceback (most recent call last):
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1900, in _execute_context
    self.dialect.do_execute(
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
  File "C:\Python311\Lib\site-packages\pymysql\cursors.py", line 148, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\cursors.py", line 310, in _query
    conn.query(q)
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 548, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 775, in _read_query_result
    result.read()
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 1156, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 725, in _read_packet
    packet.raise_for_error()
  File "C:\Python311\Lib\site-packages\pymysql\protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "C:\Python311\Lib\site-packages\pymysql\err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'earlybird_paper_setting.category' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python311\Lib\site-packages\flask\app.py", line 2070, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask\app.py", line 1515, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask_cors\extension.py", line 165, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask\app.py", line 1513, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask\app.py", line 1499, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**req.view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask_pydantic\core.py", line 249, in wrapper
    res = func(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^
  File "d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\api\home\apis.py", line 18, in getSetting
    res: Result = HomeService().getSetting()
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\api\home\service.py", line 30, in getSetting
    "modelName": self.getModelName(),
                 ^^^^^^^^^^^^^^^^^^^
  File "d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\api\home\service.py", line 21, in getModelName
    setting: Setting = Setting.query.filter(Setting.settingKey == "modelName").first()
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\orm\query.py", line 2824, in first
    return self.limit(1)._iter().first()
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\orm\query.py", line 2916, in _iter
    result = self.session.execute(
             ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\orm\session.py", line 1714, in execute
    result = conn._execute_20(statement, params or {}, execution_options)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1705, in _execute_20
    return meth(self, args_10style, kwargs_10style, execution_options)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\sql\elements.py", line 334, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1572, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1943, in _execute_context
    self._handle_dbapi_exception(
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 2124, in _handle_dbapi_exception
    util.raise_(
  File "C:\Python311\Lib\site-packages\sqlalchemy\util\compat.py", line 211, in raise_
    raise exception
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1900, in _execute_context
    self.dialect.do_execute(
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
  File "C:\Python311\Lib\site-packages\pymysql\cursors.py", line 148, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\cursors.py", line 310, in _query
    conn.query(q)
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 548, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 775, in _read_query_result
    result.read()
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 1156, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 725, in _read_packet
    packet.raise_for_error()
  File "C:\Python311\Lib\site-packages\pymysql\protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "C:\Python311\Lib\site-packages\pymysql\err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
sqlalchemy.exc.OperationalError: (pymysql.err.OperationalError) (1054, "Unknown column 'earlybird_paper_setting.category' in 'field list'")
[SQL: SELECT earlybird_paper_setting.id AS earlybird_paper_setting_id, earlybird_paper_setting.create_time AS earlybird_paper_setting_create_time, earlybird_paper_setting.update_time AS earlybird_paper_setting_update_time, earlybird_paper_setting.is_deleted AS earlybird_paper_setting_is_deleted, earlybird_paper_setting.status AS earlybird_paper_setting_status, earlybird_paper_setting.category AS earlybird_paper_setting_category, earlybird_paper_setting.`key` AS earlybird_paper_setting_key, earlybird_paper_setting.value AS earlybird_paper_setting_value, earlybird_paper_setting.description AS earlybird_paper_setting_description, earlybird_paper_setting.`settingKey` AS `earlybird_paper_setting_settingKey`, earlybird_paper_setting.`settingValue` AS `earlybird_paper_setting_settingValue` 
FROM earlybird_paper_setting 
WHERE earlybird_paper_setting.`settingKey` = %(settingKey_1)s 
 LIMIT %(param_1)s]
[parameters: {'settingKey_1': 'modelName', 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
2025-07-15 10:23:02  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getList [waitress-2]
2025-07-15 10:23:02  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-2]
2025-07-15 10:23:02  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-2]
2025-07-15 10:23:02  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-2]
2025-07-15 10:23:02  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-2]
2025-07-15 10:23:02  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-15 10:23:02  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-15 10:23:02  EarlyBird.api.thesis.apis 92: INFO  用户 13 请求获取论文列表 [waitress-2]
2025-07-15 10:23:02  apis.py 92: INFO  用户 13 请求获取论文列表 [waitress-2]
2025-07-15 10:23:02  EarlyBird.api.thesis.service 548: INFO  查询论文列表，用户ID: 13 [waitress-2]
2025-07-15 10:23:02  service.py 548: INFO  查询论文列表，用户ID: 13 [waitress-2]
2025-07-15 10:23:02  EarlyBird.api.thesis.service 554: INFO  查询到 1 篇论文 [waitress-2]
2025-07-15 10:23:02  service.py 554: INFO  查询到 1 篇论文 [waitress-2]
2025-07-15 10:23:02  EarlyBird.api.thesis.service 564: INFO  论文ID: 29, 标题: 船舶结构完整性先进测试方法研究, 用户ID: 13 [waitress-2]
2025-07-15 10:23:02  service.py 564: INFO  论文ID: 29, 标题: 船舶结构完整性先进测试方法研究, 用户ID: 13 [waitress-2]
2025-07-15 10:23:02  EarlyBird.api.thesis.service 566: INFO  返回论文列表，共 1 篇 [waitress-2]
2025-07-15 10:23:02  service.py 566: INFO  返回论文列表，共 1 篇 [waitress-2]
2025-07-15 10:23:02  EarlyBird.api.thesis.apis 95: INFO  用户 13 获取论文列表成功，共 1 篇 [waitress-2]
2025-07-15 10:23:02  apis.py 95: INFO  用户 13 获取论文列表成功，共 1 篇 [waitress-2]
2025-07-15 10:23:02  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getDetail [waitress-3]
2025-07-15 10:23:02  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getDetail [waitress-3]
2025-07-15 10:23:02  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-3]
2025-07-15 10:23:02  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-3]
2025-07-15 10:23:02  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-3]
2025-07-15 10:23:02  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-3]
2025-07-15 10:23:02  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-15 10:23:02  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-15 10:23:02  EarlyBird.api.talk2ai.utils 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-3]
2025-07-15 10:23:02  utils.py 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-3]
2025-07-15 10:23:02  EarlyBird.api.talk2ai.utils 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-3]
2025-07-15 10:23:02  utils.py 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-3]
2025-07-15 10:23:02  EarlyBird.api.talk2ai.utils 161: INFO  提纲处理完成，根节点ID: W8ogm7FCJ8dsAkiHfnas5u [waitress-3]
2025-07-15 10:23:02  utils.py 161: INFO  提纲处理完成，根节点ID: W8ogm7FCJ8dsAkiHfnas5u [waitress-3]
2025-07-15 10:23:02  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getOutline [waitress-1]
2025-07-15 10:23:02  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getOutline [waitress-1]
2025-07-15 10:23:02  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-1]
2025-07-15 10:23:02  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-1]
2025-07-15 10:23:02  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-1]
2025-07-15 10:23:02  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-1]
2025-07-15 10:23:02  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-15 10:23:02  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-15 10:23:02  EarlyBird.api.talk2ai.utils 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-1]
2025-07-15 10:23:02  utils.py 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-1]
2025-07-15 10:23:02  EarlyBird.api.talk2ai.utils 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-1]
2025-07-15 10:23:02  utils.py 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-1]
2025-07-15 10:23:02  EarlyBird.api.talk2ai.utils 161: INFO  提纲处理完成，根节点ID: W8ogm7FCJ8dsAkiHfnas5u [waitress-1]
2025-07-15 10:23:02  utils.py 161: INFO  提纲处理完成，根节点ID: W8ogm7FCJ8dsAkiHfnas5u [waitress-1]
2025-07-15 10:23:03  web_server.py 73: INFO  Serving request for path: paper/content [waitress-0]
2025-07-15 10:23:03  web_server.py 85: INFO  Serving index.html for path: paper/content [waitress-0]
2025-07-15 10:23:03  web_server.py 73: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-2]
2025-07-15 10:23:03  web_server.py 85: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-2]
2025-07-15 10:23:03  web_server.py 73: INFO  Serving request for path: js/chunk-vendors.04aff60f.js [waitress-3]
2025-07-15 10:23:03  web_server.py 82: INFO  Serving static file: js/chunk-vendors.04aff60f.js [waitress-3]
2025-07-15 10:23:03  web_server.py 73: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-0]
2025-07-15 10:23:03  web_server.py 73: INFO  Serving request for path: css/app.1e68ddae.css [waitress-2]
2025-07-15 10:23:03  web_server.py 73: INFO  Serving request for path: js/app.994e6d65.js [waitress-1]
2025-07-15 10:23:03  web_server.py 82: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-0]
2025-07-15 10:23:03  web_server.py 82: INFO  Serving static file: css/app.1e68ddae.css [waitress-2]
2025-07-15 10:23:03  web_server.py 82: INFO  Serving static file: js/app.994e6d65.js [waitress-1]
2025-07-15 10:23:03  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/user/info [waitress-3]
2025-07-15 10:23:03  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/user/info [waitress-3]
2025-07-15 10:23:03  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-3]
2025-07-15 10:23:03  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-3]
2025-07-15 10:23:03  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-3]
2025-07-15 10:23:03  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-3]
2025-07-15 10:23:03  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-15 10:23:03  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-15 10:23:03  web_server.py 73: INFO  Serving request for path: logo.png [waitress-0]
2025-07-15 10:23:03  web_server.py 82: INFO  Serving static file: logo.png [waitress-0]
2025-07-15 10:23:03  web_server.py 73: INFO  Serving request for path: js/22.683bc166.js [waitress-1]
2025-07-15 10:23:03  web_server.py 82: INFO  Serving static file: js/22.683bc166.js [waitress-1]
2025-07-15 10:23:03  web_server.py 73: INFO  Serving request for path: css/389.407de41a.css [waitress-1]
2025-07-15 10:23:03  web_server.py 73: INFO  Serving request for path: js/897.25207915.js [waitress-3]
2025-07-15 10:23:03  web_server.py 82: INFO  Serving static file: css/389.407de41a.css [waitress-1]
2025-07-15 10:23:03  web_server.py 73: INFO  Serving request for path: js/389.159e8c49.js [waitress-0]
2025-07-15 10:23:03  web_server.py 82: INFO  Serving static file: js/897.25207915.js [waitress-3]
2025-07-15 10:23:03  web_server.py 82: INFO  Serving static file: js/389.159e8c49.js [waitress-0]
2025-07-15 10:23:03  web_server.py 73: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-2]
2025-07-15 10:23:03  web_server.py 82: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-2]
2025-07-15 10:23:03  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getList [waitress-3]
2025-07-15 10:23:03  app.py 1454: ERROR  Exception on /api/home/<USER>
Traceback (most recent call last):
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1900, in _execute_context
    self.dialect.do_execute(
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
  File "C:\Python311\Lib\site-packages\pymysql\cursors.py", line 148, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\cursors.py", line 310, in _query
    conn.query(q)
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 548, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 775, in _read_query_result
    result.read()
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 1156, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 725, in _read_packet
    packet.raise_for_error()
  File "C:\Python311\Lib\site-packages\pymysql\protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "C:\Python311\Lib\site-packages\pymysql\err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'earlybird_paper_setting.category' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python311\Lib\site-packages\flask\app.py", line 2070, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask\app.py", line 1515, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask_cors\extension.py", line 165, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask\app.py", line 1513, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask\app.py", line 1499, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**req.view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask_pydantic\core.py", line 249, in wrapper
    res = func(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^
  File "d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\api\home\apis.py", line 18, in getSetting
    res: Result = HomeService().getSetting()
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\api\home\service.py", line 30, in getSetting
    "modelName": self.getModelName(),
                 ^^^^^^^^^^^^^^^^^^^
  File "d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\api\home\service.py", line 21, in getModelName
    setting: Setting = Setting.query.filter(Setting.settingKey == "modelName").first()
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\orm\query.py", line 2824, in first
    return self.limit(1)._iter().first()
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\orm\query.py", line 2916, in _iter
    result = self.session.execute(
             ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\orm\session.py", line 1714, in execute
    result = conn._execute_20(statement, params or {}, execution_options)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1705, in _execute_20
    return meth(self, args_10style, kwargs_10style, execution_options)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\sql\elements.py", line 334, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1572, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1943, in _execute_context
    self._handle_dbapi_exception(
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 2124, in _handle_dbapi_exception
    util.raise_(
  File "C:\Python311\Lib\site-packages\sqlalchemy\util\compat.py", line 211, in raise_
    raise exception
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1900, in _execute_context
    self.dialect.do_execute(
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
  File "C:\Python311\Lib\site-packages\pymysql\cursors.py", line 148, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\cursors.py", line 310, in _query
    conn.query(q)
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 548, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 775, in _read_query_result
    result.read()
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 1156, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 725, in _read_packet
    packet.raise_for_error()
  File "C:\Python311\Lib\site-packages\pymysql\protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "C:\Python311\Lib\site-packages\pymysql\err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
sqlalchemy.exc.OperationalError: (pymysql.err.OperationalError) (1054, "Unknown column 'earlybird_paper_setting.category' in 'field list'")
[SQL: SELECT earlybird_paper_setting.id AS earlybird_paper_setting_id, earlybird_paper_setting.create_time AS earlybird_paper_setting_create_time, earlybird_paper_setting.update_time AS earlybird_paper_setting_update_time, earlybird_paper_setting.is_deleted AS earlybird_paper_setting_is_deleted, earlybird_paper_setting.status AS earlybird_paper_setting_status, earlybird_paper_setting.category AS earlybird_paper_setting_category, earlybird_paper_setting.`key` AS earlybird_paper_setting_key, earlybird_paper_setting.value AS earlybird_paper_setting_value, earlybird_paper_setting.description AS earlybird_paper_setting_description, earlybird_paper_setting.`settingKey` AS `earlybird_paper_setting_settingKey`, earlybird_paper_setting.`settingValue` AS `earlybird_paper_setting_settingValue` 
FROM earlybird_paper_setting 
WHERE earlybird_paper_setting.`settingKey` = %(settingKey_1)s 
 LIMIT %(param_1)s]
[parameters: {'settingKey_1': 'modelName', 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
2025-07-15 10:23:03  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getList [waitress-3]
2025-07-15 10:23:03  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-3]
2025-07-15 10:23:03  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-3]
2025-07-15 10:23:03  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-3]
2025-07-15 10:23:03  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-3]
2025-07-15 10:23:03  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-15 10:23:03  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-15 10:23:03  EarlyBird.api.thesis.apis 92: INFO  用户 13 请求获取论文列表 [waitress-3]
2025-07-15 10:23:03  apis.py 92: INFO  用户 13 请求获取论文列表 [waitress-3]
2025-07-15 10:23:03  EarlyBird.api.thesis.service 548: INFO  查询论文列表，用户ID: 13 [waitress-3]
2025-07-15 10:23:03  service.py 548: INFO  查询论文列表，用户ID: 13 [waitress-3]
2025-07-15 10:23:03  EarlyBird.api.thesis.service 554: INFO  查询到 1 篇论文 [waitress-3]
2025-07-15 10:23:03  service.py 554: INFO  查询到 1 篇论文 [waitress-3]
2025-07-15 10:23:03  EarlyBird.api.thesis.service 564: INFO  论文ID: 29, 标题: 船舶结构完整性先进测试方法研究, 用户ID: 13 [waitress-3]
2025-07-15 10:23:03  service.py 564: INFO  论文ID: 29, 标题: 船舶结构完整性先进测试方法研究, 用户ID: 13 [waitress-3]
2025-07-15 10:23:03  EarlyBird.api.thesis.service 566: INFO  返回论文列表，共 1 篇 [waitress-3]
2025-07-15 10:23:03  service.py 566: INFO  返回论文列表，共 1 篇 [waitress-3]
2025-07-15 10:23:03  EarlyBird.api.thesis.apis 95: INFO  用户 13 获取论文列表成功，共 1 篇 [waitress-3]
2025-07-15 10:23:03  apis.py 95: INFO  用户 13 获取论文列表成功，共 1 篇 [waitress-3]
2025-07-15 10:23:03  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getDetail [waitress-0]
2025-07-15 10:23:03  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getDetail [waitress-0]
2025-07-15 10:23:03  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-0]
2025-07-15 10:23:03  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-0]
2025-07-15 10:23:03  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-0]
2025-07-15 10:23:03  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-0]
2025-07-15 10:23:03  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-15 10:23:03  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-15 10:23:03  EarlyBird.api.talk2ai.utils 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-0]
2025-07-15 10:23:03  utils.py 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-0]
2025-07-15 10:23:03  EarlyBird.api.talk2ai.utils 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-0]
2025-07-15 10:23:03  utils.py 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-0]
2025-07-15 10:23:03  EarlyBird.api.talk2ai.utils 161: INFO  提纲处理完成，根节点ID: W8ogm7FCJ8dsAkiHfnas5u [waitress-0]
2025-07-15 10:23:03  utils.py 161: INFO  提纲处理完成，根节点ID: W8ogm7FCJ8dsAkiHfnas5u [waitress-0]
2025-07-15 10:23:03  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getOutline [waitress-2]
2025-07-15 10:23:03  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getOutline [waitress-2]
2025-07-15 10:23:03  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-2]
2025-07-15 10:23:03  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-2]
2025-07-15 10:23:03  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-2]
2025-07-15 10:23:03  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-2]
2025-07-15 10:23:03  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-15 10:23:03  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-15 10:23:03  EarlyBird.api.talk2ai.utils 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-2]
2025-07-15 10:23:03  utils.py 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-2]
2025-07-15 10:23:03  EarlyBird.api.talk2ai.utils 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-2]
2025-07-15 10:23:03  utils.py 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-2]
2025-07-15 10:23:03  EarlyBird.api.talk2ai.utils 161: INFO  提纲处理完成，根节点ID: W8ogm7FCJ8dsAkiHfnas5u [waitress-2]
2025-07-15 10:23:03  utils.py 161: INFO  提纲处理完成，根节点ID: W8ogm7FCJ8dsAkiHfnas5u [waitress-2]
2025-07-15 10:23:04  web_server.py 73: INFO  Serving request for path: paper/content [waitress-1]
2025-07-15 10:23:04  web_server.py 85: INFO  Serving index.html for path: paper/content [waitress-1]
2025-07-15 10:23:04  web_server.py 73: INFO  Serving request for path: js/chunk-vendors.04aff60f.js [waitress-3]
2025-07-15 10:23:04  web_server.py 82: INFO  Serving static file: js/chunk-vendors.04aff60f.js [waitress-3]
2025-07-15 10:23:04  web_server.py 73: INFO  Serving request for path: js/app.994e6d65.js [waitress-0]
2025-07-15 10:23:04  web_server.py 82: INFO  Serving static file: js/app.994e6d65.js [waitress-0]
2025-07-15 10:23:04  web_server.py 73: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-2]
2025-07-15 10:23:04  web_server.py 73: INFO  Serving request for path: css/app.1e68ddae.css [waitress-1]
2025-07-15 10:23:04  web_server.py 73: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-3]
2025-07-15 10:23:04  web_server.py 82: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-2]
2025-07-15 10:23:04  web_server.py 82: INFO  Serving static file: css/app.1e68ddae.css [waitress-1]
2025-07-15 10:23:04  web_server.py 85: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-3]
2025-07-15 10:23:04  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/user/info [waitress-0]
2025-07-15 10:23:04  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/user/info [waitress-0]
2025-07-15 10:23:04  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-0]
2025-07-15 10:23:04  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-0]
2025-07-15 10:23:04  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-0]
2025-07-15 10:23:04  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-0]
2025-07-15 10:23:04  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-15 10:23:04  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-15 10:23:04  web_server.py 73: INFO  Serving request for path: logo.png [waitress-2]
2025-07-15 10:23:04  web_server.py 82: INFO  Serving static file: logo.png [waitress-2]
2025-07-15 10:23:04  web_server.py 73: INFO  Serving request for path: js/22.683bc166.js [waitress-3]
2025-07-15 10:23:04  web_server.py 82: INFO  Serving static file: js/22.683bc166.js [waitress-3]
2025-07-15 10:23:04  web_server.py 73: INFO  Serving request for path: css/389.407de41a.css [waitress-1]
2025-07-15 10:23:04  web_server.py 73: INFO  Serving request for path: js/897.25207915.js [waitress-0]
2025-07-15 10:23:04  web_server.py 73: INFO  Serving request for path: js/389.159e8c49.js [waitress-2]
2025-07-15 10:23:04  web_server.py 82: INFO  Serving static file: css/389.407de41a.css [waitress-1]
2025-07-15 10:23:04  web_server.py 82: INFO  Serving static file: js/897.25207915.js [waitress-0]
2025-07-15 10:23:04  web_server.py 82: INFO  Serving static file: js/389.159e8c49.js [waitress-2]
2025-07-15 10:23:04  web_server.py 73: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-3]
2025-07-15 10:23:04  web_server.py 82: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-3]
2025-07-15 10:23:04  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getList [waitress-1]
2025-07-15 10:23:04  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getList [waitress-1]
2025-07-15 10:23:04  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-1]
2025-07-15 10:23:04  app.py 1454: ERROR  Exception on /api/home/<USER>
Traceback (most recent call last):
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1900, in _execute_context
    self.dialect.do_execute(
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
  File "C:\Python311\Lib\site-packages\pymysql\cursors.py", line 148, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\cursors.py", line 310, in _query
    conn.query(q)
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 548, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 775, in _read_query_result
    result.read()
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 1156, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 725, in _read_packet
    packet.raise_for_error()
  File "C:\Python311\Lib\site-packages\pymysql\protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "C:\Python311\Lib\site-packages\pymysql\err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'earlybird_paper_setting.category' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python311\Lib\site-packages\flask\app.py", line 2070, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask\app.py", line 1515, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask_cors\extension.py", line 165, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask\app.py", line 1513, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask\app.py", line 1499, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**req.view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask_pydantic\core.py", line 249, in wrapper
    res = func(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^
  File "d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\api\home\apis.py", line 18, in getSetting
    res: Result = HomeService().getSetting()
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\api\home\service.py", line 30, in getSetting
    "modelName": self.getModelName(),
                 ^^^^^^^^^^^^^^^^^^^
  File "d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\api\home\service.py", line 21, in getModelName
    setting: Setting = Setting.query.filter(Setting.settingKey == "modelName").first()
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\orm\query.py", line 2824, in first
    return self.limit(1)._iter().first()
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\orm\query.py", line 2916, in _iter
    result = self.session.execute(
             ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\orm\session.py", line 1714, in execute
    result = conn._execute_20(statement, params or {}, execution_options)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1705, in _execute_20
    return meth(self, args_10style, kwargs_10style, execution_options)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\sql\elements.py", line 334, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1572, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1943, in _execute_context
    self._handle_dbapi_exception(
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 2124, in _handle_dbapi_exception
    util.raise_(
  File "C:\Python311\Lib\site-packages\sqlalchemy\util\compat.py", line 211, in raise_
    raise exception
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1900, in _execute_context
    self.dialect.do_execute(
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
  File "C:\Python311\Lib\site-packages\pymysql\cursors.py", line 148, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\cursors.py", line 310, in _query
    conn.query(q)
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 548, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 775, in _read_query_result
    result.read()
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 1156, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 725, in _read_packet
    packet.raise_for_error()
  File "C:\Python311\Lib\site-packages\pymysql\protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "C:\Python311\Lib\site-packages\pymysql\err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
sqlalchemy.exc.OperationalError: (pymysql.err.OperationalError) (1054, "Unknown column 'earlybird_paper_setting.category' in 'field list'")
[SQL: SELECT earlybird_paper_setting.id AS earlybird_paper_setting_id, earlybird_paper_setting.create_time AS earlybird_paper_setting_create_time, earlybird_paper_setting.update_time AS earlybird_paper_setting_update_time, earlybird_paper_setting.is_deleted AS earlybird_paper_setting_is_deleted, earlybird_paper_setting.status AS earlybird_paper_setting_status, earlybird_paper_setting.category AS earlybird_paper_setting_category, earlybird_paper_setting.`key` AS earlybird_paper_setting_key, earlybird_paper_setting.value AS earlybird_paper_setting_value, earlybird_paper_setting.description AS earlybird_paper_setting_description, earlybird_paper_setting.`settingKey` AS `earlybird_paper_setting_settingKey`, earlybird_paper_setting.`settingValue` AS `earlybird_paper_setting_settingValue` 
FROM earlybird_paper_setting 
WHERE earlybird_paper_setting.`settingKey` = %(settingKey_1)s 
 LIMIT %(param_1)s]
[parameters: {'settingKey_1': 'modelName', 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
2025-07-15 10:23:04  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-1]
2025-07-15 10:23:04  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-1]
2025-07-15 10:23:04  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-1]
2025-07-15 10:23:04  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-15 10:23:04  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-15 10:23:04  EarlyBird.api.thesis.apis 92: INFO  用户 13 请求获取论文列表 [waitress-1]
2025-07-15 10:23:04  apis.py 92: INFO  用户 13 请求获取论文列表 [waitress-1]
2025-07-15 10:23:04  EarlyBird.api.thesis.service 548: INFO  查询论文列表，用户ID: 13 [waitress-1]
2025-07-15 10:23:04  service.py 548: INFO  查询论文列表，用户ID: 13 [waitress-1]
2025-07-15 10:23:04  EarlyBird.api.thesis.service 554: INFO  查询到 1 篇论文 [waitress-1]
2025-07-15 10:23:04  service.py 554: INFO  查询到 1 篇论文 [waitress-1]
2025-07-15 10:23:04  EarlyBird.api.thesis.service 564: INFO  论文ID: 29, 标题: 船舶结构完整性先进测试方法研究, 用户ID: 13 [waitress-1]
2025-07-15 10:23:04  service.py 564: INFO  论文ID: 29, 标题: 船舶结构完整性先进测试方法研究, 用户ID: 13 [waitress-1]
2025-07-15 10:23:04  EarlyBird.api.thesis.service 566: INFO  返回论文列表，共 1 篇 [waitress-1]
2025-07-15 10:23:04  service.py 566: INFO  返回论文列表，共 1 篇 [waitress-1]
2025-07-15 10:23:04  EarlyBird.api.thesis.apis 95: INFO  用户 13 获取论文列表成功，共 1 篇 [waitress-1]
2025-07-15 10:23:04  apis.py 95: INFO  用户 13 获取论文列表成功，共 1 篇 [waitress-1]
2025-07-15 10:23:04  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getDetail [waitress-2]
2025-07-15 10:23:04  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getDetail [waitress-2]
2025-07-15 10:23:04  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-2]
2025-07-15 10:23:04  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-2]
2025-07-15 10:23:04  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-2]
2025-07-15 10:23:04  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-2]
2025-07-15 10:23:04  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-15 10:23:04  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-15 10:23:04  EarlyBird.api.talk2ai.utils 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-2]
2025-07-15 10:23:04  utils.py 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-2]
2025-07-15 10:23:04  EarlyBird.api.talk2ai.utils 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-2]
2025-07-15 10:23:04  utils.py 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-2]
2025-07-15 10:23:04  EarlyBird.api.talk2ai.utils 161: INFO  提纲处理完成，根节点ID: W8ogm7FCJ8dsAkiHfnas5u [waitress-2]
2025-07-15 10:23:04  utils.py 161: INFO  提纲处理完成，根节点ID: W8ogm7FCJ8dsAkiHfnas5u [waitress-2]
2025-07-15 10:23:04  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getOutline [waitress-3]
2025-07-15 10:23:04  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getOutline [waitress-3]
2025-07-15 10:23:04  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-3]
2025-07-15 10:23:04  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-3]
2025-07-15 10:23:04  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-3]
2025-07-15 10:23:04  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-3]
2025-07-15 10:23:04  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-15 10:23:04  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-15 10:23:04  EarlyBird.api.talk2ai.utils 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-3]
2025-07-15 10:23:04  utils.py 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-3]
2025-07-15 10:23:04  EarlyBird.api.talk2ai.utils 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-3]
2025-07-15 10:23:04  utils.py 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-3]
2025-07-15 10:23:04  EarlyBird.api.talk2ai.utils 161: INFO  提纲处理完成，根节点ID: W8ogm7FCJ8dsAkiHfnas5u [waitress-3]
2025-07-15 10:23:04  utils.py 161: INFO  提纲处理完成，根节点ID: W8ogm7FCJ8dsAkiHfnas5u [waitress-3]
2025-07-15 10:23:05  web_server.py 73: INFO  Serving request for path: paper/content [waitress-0]
2025-07-15 10:23:05  web_server.py 85: INFO  Serving index.html for path: paper/content [waitress-0]
2025-07-15 10:23:05  web_server.py 73: INFO  Serving request for path: js/chunk-vendors.04aff60f.js [waitress-1]
2025-07-15 10:23:05  web_server.py 82: INFO  Serving static file: js/chunk-vendors.04aff60f.js [waitress-1]
2025-07-15 10:23:05  web_server.py 73: INFO  Serving request for path: css/app.1e68ddae.css [waitress-3]
2025-07-15 10:23:05  web_server.py 73: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-2]
2025-07-15 10:23:05  web_server.py 73: INFO  Serving request for path: js/app.994e6d65.js [waitress-0]
2025-07-15 10:23:05  web_server.py 73: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-1]
2025-07-15 10:23:05  web_server.py 82: INFO  Serving static file: css/app.1e68ddae.css [waitress-3]
2025-07-15 10:23:05  web_server.py 82: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-2]
2025-07-15 10:23:05  web_server.py 82: INFO  Serving static file: js/app.994e6d65.js [waitress-0]
2025-07-15 10:23:05  web_server.py 85: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-1]
2025-07-15 10:23:05  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/user/info [waitress-3]
2025-07-15 10:23:05  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/user/info [waitress-3]
2025-07-15 10:23:05  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-3]
2025-07-15 10:23:05  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-3]
2025-07-15 10:23:05  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-3]
2025-07-15 10:23:05  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-3]
2025-07-15 10:23:05  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-15 10:23:05  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-15 10:23:05  web_server.py 73: INFO  Serving request for path: logo.png [waitress-2]
2025-07-15 10:23:05  web_server.py 82: INFO  Serving static file: logo.png [waitress-2]
2025-07-15 10:23:05  web_server.py 73: INFO  Serving request for path: js/22.683bc166.js [waitress-0]
2025-07-15 10:23:05  web_server.py 82: INFO  Serving static file: js/22.683bc166.js [waitress-0]
2025-07-15 10:23:05  web_server.py 73: INFO  Serving request for path: css/389.407de41a.css [waitress-1]
2025-07-15 10:23:05  web_server.py 73: INFO  Serving request for path: js/897.25207915.js [waitress-3]
2025-07-15 10:23:05  web_server.py 73: INFO  Serving request for path: js/389.159e8c49.js [waitress-2]
2025-07-15 10:23:05  web_server.py 82: INFO  Serving static file: css/389.407de41a.css [waitress-1]
2025-07-15 10:23:05  web_server.py 82: INFO  Serving static file: js/897.25207915.js [waitress-3]
2025-07-15 10:23:05  web_server.py 82: INFO  Serving static file: js/389.159e8c49.js [waitress-2]
2025-07-15 10:23:05  web_server.py 73: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-0]
2025-07-15 10:23:05  web_server.py 82: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-0]
2025-07-15 10:23:05  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getList [waitress-3]
2025-07-15 10:23:05  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getList [waitress-3]
2025-07-15 10:23:05  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-3]
2025-07-15 10:23:05  app.py 1454: ERROR  Exception on /api/home/<USER>
Traceback (most recent call last):
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1900, in _execute_context
    self.dialect.do_execute(
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
  File "C:\Python311\Lib\site-packages\pymysql\cursors.py", line 148, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\cursors.py", line 310, in _query
    conn.query(q)
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 548, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 775, in _read_query_result
    result.read()
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 1156, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 725, in _read_packet
    packet.raise_for_error()
  File "C:\Python311\Lib\site-packages\pymysql\protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "C:\Python311\Lib\site-packages\pymysql\err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'earlybird_paper_setting.category' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python311\Lib\site-packages\flask\app.py", line 2070, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask\app.py", line 1515, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask_cors\extension.py", line 165, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask\app.py", line 1513, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask\app.py", line 1499, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**req.view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask_pydantic\core.py", line 249, in wrapper
    res = func(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^
  File "d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\api\home\apis.py", line 18, in getSetting
    res: Result = HomeService().getSetting()
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\api\home\service.py", line 30, in getSetting
    "modelName": self.getModelName(),
                 ^^^^^^^^^^^^^^^^^^^
  File "d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\api\home\service.py", line 21, in getModelName
    setting: Setting = Setting.query.filter(Setting.settingKey == "modelName").first()
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\orm\query.py", line 2824, in first
    return self.limit(1)._iter().first()
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\orm\query.py", line 2916, in _iter
    result = self.session.execute(
             ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\orm\session.py", line 1714, in execute
    result = conn._execute_20(statement, params or {}, execution_options)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1705, in _execute_20
    return meth(self, args_10style, kwargs_10style, execution_options)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\sql\elements.py", line 334, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1572, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1943, in _execute_context
    self._handle_dbapi_exception(
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 2124, in _handle_dbapi_exception
    util.raise_(
  File "C:\Python311\Lib\site-packages\sqlalchemy\util\compat.py", line 211, in raise_
    raise exception
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1900, in _execute_context
    self.dialect.do_execute(
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
  File "C:\Python311\Lib\site-packages\pymysql\cursors.py", line 148, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\cursors.py", line 310, in _query
    conn.query(q)
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 548, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 775, in _read_query_result
    result.read()
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 1156, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 725, in _read_packet
    packet.raise_for_error()
  File "C:\Python311\Lib\site-packages\pymysql\protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "C:\Python311\Lib\site-packages\pymysql\err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
sqlalchemy.exc.OperationalError: (pymysql.err.OperationalError) (1054, "Unknown column 'earlybird_paper_setting.category' in 'field list'")
[SQL: SELECT earlybird_paper_setting.id AS earlybird_paper_setting_id, earlybird_paper_setting.create_time AS earlybird_paper_setting_create_time, earlybird_paper_setting.update_time AS earlybird_paper_setting_update_time, earlybird_paper_setting.is_deleted AS earlybird_paper_setting_is_deleted, earlybird_paper_setting.status AS earlybird_paper_setting_status, earlybird_paper_setting.category AS earlybird_paper_setting_category, earlybird_paper_setting.`key` AS earlybird_paper_setting_key, earlybird_paper_setting.value AS earlybird_paper_setting_value, earlybird_paper_setting.description AS earlybird_paper_setting_description, earlybird_paper_setting.`settingKey` AS `earlybird_paper_setting_settingKey`, earlybird_paper_setting.`settingValue` AS `earlybird_paper_setting_settingValue` 
FROM earlybird_paper_setting 
WHERE earlybird_paper_setting.`settingKey` = %(settingKey_1)s 
 LIMIT %(param_1)s]
[parameters: {'settingKey_1': 'modelName', 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
2025-07-15 10:23:05  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-3]
2025-07-15 10:23:05  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-3]
2025-07-15 10:23:05  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-3]
2025-07-15 10:23:05  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-15 10:23:05  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-15 10:23:05  EarlyBird.api.thesis.apis 92: INFO  用户 13 请求获取论文列表 [waitress-3]
2025-07-15 10:23:05  apis.py 92: INFO  用户 13 请求获取论文列表 [waitress-3]
2025-07-15 10:23:05  EarlyBird.api.thesis.service 548: INFO  查询论文列表，用户ID: 13 [waitress-3]
2025-07-15 10:23:05  service.py 548: INFO  查询论文列表，用户ID: 13 [waitress-3]
2025-07-15 10:23:05  EarlyBird.api.thesis.service 554: INFO  查询到 1 篇论文 [waitress-3]
2025-07-15 10:23:05  service.py 554: INFO  查询到 1 篇论文 [waitress-3]
2025-07-15 10:23:05  EarlyBird.api.thesis.service 564: INFO  论文ID: 29, 标题: 船舶结构完整性先进测试方法研究, 用户ID: 13 [waitress-3]
2025-07-15 10:23:05  service.py 564: INFO  论文ID: 29, 标题: 船舶结构完整性先进测试方法研究, 用户ID: 13 [waitress-3]
2025-07-15 10:23:05  EarlyBird.api.thesis.service 566: INFO  返回论文列表，共 1 篇 [waitress-3]
2025-07-15 10:23:05  service.py 566: INFO  返回论文列表，共 1 篇 [waitress-3]
2025-07-15 10:23:05  EarlyBird.api.thesis.apis 95: INFO  用户 13 获取论文列表成功，共 1 篇 [waitress-3]
2025-07-15 10:23:05  apis.py 95: INFO  用户 13 获取论文列表成功，共 1 篇 [waitress-3]
2025-07-15 10:23:05  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getDetail [waitress-2]
2025-07-15 10:23:05  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getDetail [waitress-2]
2025-07-15 10:23:05  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-2]
2025-07-15 10:23:05  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-2]
2025-07-15 10:23:05  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-2]
2025-07-15 10:23:05  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-2]
2025-07-15 10:23:05  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-15 10:23:05  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-15 10:23:05  EarlyBird.api.talk2ai.utils 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-2]
2025-07-15 10:23:05  utils.py 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-2]
2025-07-15 10:23:05  EarlyBird.api.talk2ai.utils 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-2]
2025-07-15 10:23:05  utils.py 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-2]
2025-07-15 10:23:05  EarlyBird.api.talk2ai.utils 161: INFO  提纲处理完成，根节点ID: W8ogm7FCJ8dsAkiHfnas5u [waitress-2]
2025-07-15 10:23:05  utils.py 161: INFO  提纲处理完成，根节点ID: W8ogm7FCJ8dsAkiHfnas5u [waitress-2]
2025-07-15 10:23:05  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getOutline [waitress-0]
2025-07-15 10:23:05  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getOutline [waitress-0]
2025-07-15 10:23:05  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-0]
2025-07-15 10:23:05  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-0]
2025-07-15 10:23:05  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-0]
2025-07-15 10:23:05  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-0]
2025-07-15 10:23:05  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-15 10:23:05  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-15 10:23:05  EarlyBird.api.talk2ai.utils 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-0]
2025-07-15 10:23:05  utils.py 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-0]
2025-07-15 10:23:05  EarlyBird.api.talk2ai.utils 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-0]
2025-07-15 10:23:05  utils.py 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-0]
2025-07-15 10:23:05  EarlyBird.api.talk2ai.utils 161: INFO  提纲处理完成，根节点ID: W8ogm7FCJ8dsAkiHfnas5u [waitress-0]
2025-07-15 10:23:05  utils.py 161: INFO  提纲处理完成，根节点ID: W8ogm7FCJ8dsAkiHfnas5u [waitress-0]
2025-07-15 10:23:08  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-1]
2025-07-15 10:23:08  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-1]
2025-07-15 10:23:08  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-1]
2025-07-15 10:23:08  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-1]
2025-07-15 10:23:08  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-1]
2025-07-15 10:23:08  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-1]
2025-07-15 10:23:08  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-15 10:23:08  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-15 10:23:08  EarlyBird.api.thesis.apis 145: INFO  用户 13 请求下载论文 29 [waitress-1]
2025-07-15 10:23:08  apis.py 145: INFO  用户 13 请求下载论文 29 [waitress-1]
2025-07-15 10:23:08  EarlyBird.api.thesis.service 296: INFO  用户 13 请求导出论文 29 [waitress-1]
2025-07-15 10:23:08  service.py 296: INFO  用户 13 请求导出论文 29 [waitress-1]
2025-07-15 10:23:08  EarlyBird.api.thesis.service 304: INFO  用户 13 VIP状态: False [waitress-1]
2025-07-15 10:23:08  service.py 304: INFO  用户 13 VIP状态: False [waitress-1]
2025-07-15 10:23:08  EarlyBird.api.thesis.service 309: INFO  论文下载收费功能是否启用: True [waitress-1]
2025-07-15 10:23:08  service.py 309: INFO  论文下载收费功能是否启用: True [waitress-1]
2025-07-15 10:23:08  EarlyBird.api.thesis.service 323: INFO  VIP用户是否免费下载: False [waitress-1]
2025-07-15 10:23:08  service.py 323: INFO  VIP用户是否免费下载: False [waitress-1]
2025-07-15 10:23:08  EarlyBird.api.thesis.service 340: INFO  首次下载是否免费: False [waitress-1]
2025-07-15 10:23:08  service.py 340: INFO  首次下载是否免费: False [waitress-1]
2025-07-15 10:23:08  EarlyBird.api.thesis.service 344: INFO  用户 13 是否有已支付的下载记录: True [waitress-1]
2025-07-15 10:23:08  service.py 344: INFO  用户 13 是否有已支付的下载记录: True [waitress-1]
2025-07-15 10:23:08  EarlyBird.api.thesis.service 362: INFO  用户 13 已有已支付的下载记录，可直接下载论文 29 [waitress-1]
2025-07-15 10:23:08  service.py 362: INFO  用户 13 已有已支付的下载记录，可直接下载论文 29 [waitress-1]
2025-07-15 10:23:08  EarlyBird.api.thesis.service 737: INFO  add Para:船舶结构完整性先进测试方法研究 [waitress-1]
2025-07-15 10:23:08  service.py 737: INFO  add Para:船舶结构完整性先进测试方法研究 [waitress-1]
2025-07-15 10:23:08  EarlyBird.api.thesis.service 737: INFO  add Para:绪论 [waitress-1]
2025-07-15 10:23:08  service.py 737: INFO  add Para:绪论 [waitress-1]
2025-07-15 10:23:08  EarlyBird.api.thesis.service 737: INFO  add Para:研究背景与现状 [waitress-1]
2025-07-15 10:23:08  service.py 737: INFO  add Para:研究背景与现状 [waitress-1]
2025-07-15 10:23:08  EarlyBird.api.thesis.service 737: INFO  add Para:船舶结构完整性数据现状 [waitress-1]
2025-07-15 10:23:08  service.py 737: INFO  add Para:船舶结构完整性数据现状 [waitress-1]
2025-07-15 10:23:08  EarlyBird.api.thesis.service 737: INFO  add Para:研究意义与目标 [waitress-1]
2025-07-15 10:23:08  service.py 737: INFO  add Para:研究意义与目标 [waitress-1]
2025-07-15 10:23:08  EarlyBird.api.thesis.service 737: INFO  add Para:理论基础与数据分析模型 [waitress-1]
2025-07-15 10:23:08  service.py 737: INFO  add Para:理论基础与数据分析模型 [waitress-1]
2025-07-15 10:23:08  EarlyBird.api.thesis.service 737: INFO  add Para:船舶结构完整性的理论框架 [waitress-1]
2025-07-15 10:23:08  service.py 737: INFO  add Para:船舶结构完整性的理论框架 [waitress-1]
2025-07-15 10:23:08  EarlyBird.api.thesis.service 737: INFO  add Para:基于大数据的船舶结构分析模型 [waitress-1]
2025-07-15 10:23:08  service.py 737: INFO  add Para:基于大数据的船舶结构分析模型 [waitress-1]
2025-07-15 10:23:08  EarlyBird.api.thesis.service 737: INFO  add Para:数据分析方法的选择与应用 [waitress-1]
2025-07-15 10:23:08  service.py 737: INFO  add Para:数据分析方法的选择与应用 [waitress-1]
2025-07-15 10:23:08  EarlyBird.api.thesis.service 737: INFO  add Para:实证研究：数据收集与处理 [waitress-1]
2025-07-15 10:23:08  service.py 737: INFO  add Para:实证研究：数据收集与处理 [waitress-1]
2025-07-15 10:23:08  EarlyBird.api.thesis.service 737: INFO  add Para:数据来源与采集方法 [waitress-1]
2025-07-15 10:23:08  service.py 737: INFO  add Para:数据来源与采集方法 [waitress-1]
2025-07-15 10:23:08  EarlyBird.api.thesis.service 737: INFO  add Para:数据清洗与预处理 [waitress-1]
2025-07-15 10:23:08  service.py 737: INFO  add Para:数据清洗与预处理 [waitress-1]
2025-07-15 10:23:08  EarlyBird.api.thesis.service 737: INFO  add Para:数据统计与初步分析 [waitress-1]
2025-07-15 10:23:08  service.py 737: INFO  add Para:数据统计与初步分析 [waitress-1]
2025-07-15 10:23:08  EarlyBird.api.thesis.service 737: INFO  add Para:案例分析：特定场景下的数据对比 [waitress-1]
2025-07-15 10:23:08  service.py 737: INFO  add Para:案例分析：特定场景下的数据对比 [waitress-1]
2025-07-15 10:23:08  EarlyBird.api.thesis.service 737: INFO  add Para:选取典型船舶结构案例 [waitress-1]
2025-07-15 10:23:08  service.py 737: INFO  add Para:选取典型船舶结构案例 [waitress-1]
2025-07-15 10:23:08  EarlyBird.api.thesis.service 737: INFO  add Para:不同测试方法的数据对比 [waitress-1]
2025-07-15 10:23:08  service.py 737: INFO  add Para:不同测试方法的数据对比 [waitress-1]
2025-07-15 10:23:08  EarlyBird.api.thesis.service 737: INFO  add Para:案例效果评估与反馈 [waitress-1]
2025-07-15 10:23:08  service.py 737: INFO  add Para:案例效果评估与反馈 [waitress-1]
2025-07-15 10:23:08  EarlyBird.api.thesis.service 737: INFO  add Para:数据展示与深度分析 [waitress-1]
2025-07-15 10:23:08  service.py 737: INFO  add Para:数据展示与深度分析 [waitress-1]
2025-07-15 10:23:08  EarlyBird.api.thesis.service 737: INFO  add Para:关键数据可视化展示 [waitress-1]
2025-07-15 10:23:08  service.py 737: INFO  add Para:关键数据可视化展示 [waitress-1]
2025-07-15 10:23:08  EarlyBird.api.thesis.service 737: INFO  add Para:异常数据检测与处理 [waitress-1]
2025-07-15 10:23:08  service.py 737: INFO  add Para:异常数据检测与处理 [waitress-1]
2025-07-15 10:23:08  EarlyBird.api.thesis.service 737: INFO  add Para:基于数据的深度洞察与解释 [waitress-1]
2025-07-15 10:23:08  service.py 737: INFO  add Para:基于数据的深度洞察与解释 [waitress-1]
2025-07-15 10:23:08  EarlyBird.api.thesis.service 737: INFO  add Para:结论与政策建议 [waitress-1]
2025-07-15 10:23:08  service.py 737: INFO  add Para:结论与政策建议 [waitress-1]
2025-07-15 10:23:08  EarlyBird.api.thesis.service 737: INFO  add Para:数据总结与研究发现 [waitress-1]
2025-07-15 10:23:08  service.py 737: INFO  add Para:数据总结与研究发现 [waitress-1]
2025-07-15 10:23:08  EarlyBird.api.thesis.service 737: INFO  add Para:对船舶设计与维护的政策建议 [waitress-1]
2025-07-15 10:23:08  service.py 737: INFO  add Para:对船舶设计与维护的政策建议 [waitress-1]
2025-07-15 10:23:08  EarlyBird.api.thesis.service 737: INFO  add Para:未来研究方向与展望 [waitress-1]
2025-07-15 10:23:08  service.py 737: INFO  add Para:未来研究方向与展望 [waitress-1]
2025-07-15 10:23:08  EarlyBird.common.docx 500: INFO  d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\download\[早鸟论文]_船舶结构完整性先进测试方法研究_V20250715_102308.docx [waitress-1]
2025-07-15 10:23:08  docx.py 500: INFO  d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\download\[早鸟论文]_船舶结构完整性先进测试方法研究_V20250715_102308.docx [waitress-1]
2025-07-15 10:23:08  EarlyBird.api.thesis.apis 162: INFO  用户 13 下载论文 29 成功 [waitress-1]
2025-07-15 10:23:08  apis.py 162: INFO  用户 13 下载论文 29 成功 [waitress-1]
2025-07-15 10:23:08  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/download [waitress-3]
2025-07-15 10:23:08  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/download [waitress-3]
2025-07-15 10:23:08  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-3]
2025-07-15 10:23:08  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-3]
2025-07-15 10:23:08  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-3]
2025-07-15 10:23:08  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-3]
2025-07-15 10:23:08  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-15 10:23:08  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-15 10:30:10  web_server.py 73: INFO  Serving request for path: paper/content [waitress-2]
2025-07-15 10:30:10  web_server.py 85: INFO  Serving index.html for path: paper/content [waitress-2]
2025-07-15 10:30:10  web_server.py 73: INFO  Serving request for path: js/chunk-vendors.04aff60f.js [waitress-0]
2025-07-15 10:30:10  web_server.py 82: INFO  Serving static file: js/chunk-vendors.04aff60f.js [waitress-0]
2025-07-15 10:30:10  web_server.py 73: INFO  Serving request for path: js/app.7cfeed94.js [waitress-1]
2025-07-15 10:30:10  web_server.py 82: INFO  Serving static file: js/app.7cfeed94.js [waitress-1]
2025-07-15 10:30:10  web_server.py 73: INFO  Serving request for path: css/app.1e68ddae.css [waitress-2]
2025-07-15 10:30:10  web_server.py 73: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-3]
2025-07-15 10:30:10  task.py 113: WARNING  Task queue depth is 1 [MainThread]
2025-07-15 10:30:10  web_server.py 82: INFO  Serving static file: css/app.1e68ddae.css [waitress-2]
2025-07-15 10:30:10  web_server.py 82: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-3]
2025-07-15 10:30:10  web_server.py 73: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-1]
2025-07-15 10:30:10  web_server.py 85: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-1]
2025-07-15 10:30:11  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/user/info [waitress-3]
2025-07-15 10:30:11  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/user/info [waitress-3]
2025-07-15 10:30:11  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-3]
2025-07-15 10:30:11  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-3]
2025-07-15 10:30:11  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-3]
2025-07-15 10:30:11  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-3]
2025-07-15 10:30:11  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-15 10:30:11  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-15 10:30:11  web_server.py 73: INFO  Serving request for path: logo.png [waitress-2]
2025-07-15 10:30:11  web_server.py 82: INFO  Serving static file: logo.png [waitress-2]
2025-07-15 10:30:11  web_server.py 73: INFO  Serving request for path: js/22.683bc166.js [waitress-0]
2025-07-15 10:30:11  web_server.py 73: INFO  Serving request for path: js/897.25207915.js [waitress-1]
2025-07-15 10:30:11  web_server.py 73: INFO  Serving request for path: js/389.0b47414d.js [waitress-3]
2025-07-15 10:30:11  web_server.py 82: INFO  Serving static file: js/22.683bc166.js [waitress-0]
2025-07-15 10:30:11  web_server.py 73: INFO  Serving request for path: css/389.64f20d88.css [waitress-2]
2025-07-15 10:30:11  web_server.py 82: INFO  Serving static file: js/897.25207915.js [waitress-1]
2025-07-15 10:30:11  web_server.py 82: INFO  Serving static file: js/389.0b47414d.js [waitress-3]
2025-07-15 10:30:11  web_server.py 82: INFO  Serving static file: css/389.64f20d88.css [waitress-2]
2025-07-15 10:30:11  web_server.py 73: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-0]
2025-07-15 10:30:11  web_server.py 82: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-0]
2025-07-15 10:30:11  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getList [waitress-2]
2025-07-15 10:30:11  app.py 1454: ERROR  Exception on /api/home/<USER>
Traceback (most recent call last):
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1900, in _execute_context
    self.dialect.do_execute(
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
  File "C:\Python311\Lib\site-packages\pymysql\cursors.py", line 148, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\cursors.py", line 310, in _query
    conn.query(q)
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 548, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 775, in _read_query_result
    result.read()
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 1156, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 725, in _read_packet
    packet.raise_for_error()
  File "C:\Python311\Lib\site-packages\pymysql\protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "C:\Python311\Lib\site-packages\pymysql\err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'earlybird_paper_setting.category' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python311\Lib\site-packages\flask\app.py", line 2070, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask\app.py", line 1515, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask_cors\extension.py", line 165, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask\app.py", line 1513, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask\app.py", line 1499, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**req.view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask_pydantic\core.py", line 249, in wrapper
    res = func(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^
  File "d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\api\home\apis.py", line 18, in getSetting
    res: Result = HomeService().getSetting()
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\api\home\service.py", line 30, in getSetting
    "modelName": self.getModelName(),
                 ^^^^^^^^^^^^^^^^^^^
  File "d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\api\home\service.py", line 21, in getModelName
    setting: Setting = Setting.query.filter(Setting.settingKey == "modelName").first()
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\orm\query.py", line 2824, in first
    return self.limit(1)._iter().first()
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\orm\query.py", line 2916, in _iter
    result = self.session.execute(
             ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\orm\session.py", line 1714, in execute
    result = conn._execute_20(statement, params or {}, execution_options)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1705, in _execute_20
    return meth(self, args_10style, kwargs_10style, execution_options)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\sql\elements.py", line 334, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1572, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1943, in _execute_context
    self._handle_dbapi_exception(
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 2124, in _handle_dbapi_exception
    util.raise_(
  File "C:\Python311\Lib\site-packages\sqlalchemy\util\compat.py", line 211, in raise_
    raise exception
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1900, in _execute_context
    self.dialect.do_execute(
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
  File "C:\Python311\Lib\site-packages\pymysql\cursors.py", line 148, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\cursors.py", line 310, in _query
    conn.query(q)
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 548, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 775, in _read_query_result
    result.read()
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 1156, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 725, in _read_packet
    packet.raise_for_error()
  File "C:\Python311\Lib\site-packages\pymysql\protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "C:\Python311\Lib\site-packages\pymysql\err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
sqlalchemy.exc.OperationalError: (pymysql.err.OperationalError) (1054, "Unknown column 'earlybird_paper_setting.category' in 'field list'")
[SQL: SELECT earlybird_paper_setting.id AS earlybird_paper_setting_id, earlybird_paper_setting.create_time AS earlybird_paper_setting_create_time, earlybird_paper_setting.update_time AS earlybird_paper_setting_update_time, earlybird_paper_setting.is_deleted AS earlybird_paper_setting_is_deleted, earlybird_paper_setting.status AS earlybird_paper_setting_status, earlybird_paper_setting.category AS earlybird_paper_setting_category, earlybird_paper_setting.`key` AS earlybird_paper_setting_key, earlybird_paper_setting.value AS earlybird_paper_setting_value, earlybird_paper_setting.description AS earlybird_paper_setting_description, earlybird_paper_setting.`settingKey` AS `earlybird_paper_setting_settingKey`, earlybird_paper_setting.`settingValue` AS `earlybird_paper_setting_settingValue` 
FROM earlybird_paper_setting 
WHERE earlybird_paper_setting.`settingKey` = %(settingKey_1)s 
 LIMIT %(param_1)s]
[parameters: {'settingKey_1': 'modelName', 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
2025-07-15 10:30:11  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getList [waitress-2]
2025-07-15 10:30:11  web_server.py 73: INFO  Serving request for path: fonts/element-icons.ff18efd1.woff [waitress-3]
2025-07-15 10:30:11  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-2]
2025-07-15 10:30:11  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-2]
2025-07-15 10:30:11  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-2]
2025-07-15 10:30:11  web_server.py 82: INFO  Serving static file: fonts/element-icons.ff18efd1.woff [waitress-3]
2025-07-15 10:30:11  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-2]
2025-07-15 10:30:11  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-15 10:30:11  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-15 10:30:11  EarlyBird.api.thesis.apis 92: INFO  用户 13 请求获取论文列表 [waitress-2]
2025-07-15 10:30:11  apis.py 92: INFO  用户 13 请求获取论文列表 [waitress-2]
2025-07-15 10:30:11  EarlyBird.api.thesis.service 548: INFO  查询论文列表，用户ID: 13 [waitress-2]
2025-07-15 10:30:11  service.py 548: INFO  查询论文列表，用户ID: 13 [waitress-2]
2025-07-15 10:30:11  EarlyBird.api.thesis.service 554: INFO  查询到 1 篇论文 [waitress-2]
2025-07-15 10:30:11  service.py 554: INFO  查询到 1 篇论文 [waitress-2]
2025-07-15 10:30:11  EarlyBird.api.thesis.service 564: INFO  论文ID: 29, 标题: 船舶结构完整性先进测试方法研究, 用户ID: 13 [waitress-2]
2025-07-15 10:30:11  service.py 564: INFO  论文ID: 29, 标题: 船舶结构完整性先进测试方法研究, 用户ID: 13 [waitress-2]
2025-07-15 10:30:11  EarlyBird.api.thesis.service 566: INFO  返回论文列表，共 1 篇 [waitress-2]
2025-07-15 10:30:11  service.py 566: INFO  返回论文列表，共 1 篇 [waitress-2]
2025-07-15 10:30:11  EarlyBird.api.thesis.apis 95: INFO  用户 13 获取论文列表成功，共 1 篇 [waitress-2]
2025-07-15 10:30:11  apis.py 95: INFO  用户 13 获取论文列表成功，共 1 篇 [waitress-2]
2025-07-15 10:30:11  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getDetail [waitress-0]
2025-07-15 10:30:11  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getDetail [waitress-0]
2025-07-15 10:30:11  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-0]
2025-07-15 10:30:11  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-0]
2025-07-15 10:30:11  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-0]
2025-07-15 10:30:11  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-0]
2025-07-15 10:30:11  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-15 10:30:11  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-15 10:30:11  EarlyBird.api.talk2ai.utils 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-0]
2025-07-15 10:30:11  utils.py 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-0]
2025-07-15 10:30:11  EarlyBird.api.talk2ai.utils 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-0]
2025-07-15 10:30:11  utils.py 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-0]
2025-07-15 10:30:11  EarlyBird.api.talk2ai.utils 161: INFO  提纲处理完成，根节点ID: W8ogm7FCJ8dsAkiHfnas5u [waitress-0]
2025-07-15 10:30:11  utils.py 161: INFO  提纲处理完成，根节点ID: W8ogm7FCJ8dsAkiHfnas5u [waitress-0]
2025-07-15 10:30:11  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getOutline [waitress-1]
2025-07-15 10:30:11  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getOutline [waitress-1]
2025-07-15 10:30:11  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-1]
2025-07-15 10:30:11  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-1]
2025-07-15 10:30:11  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-1]
2025-07-15 10:30:11  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-1]
2025-07-15 10:30:11  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-15 10:30:11  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-15 10:30:11  EarlyBird.api.talk2ai.utils 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-1]
2025-07-15 10:30:11  utils.py 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-1]
2025-07-15 10:30:11  EarlyBird.api.talk2ai.utils 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-1]
2025-07-15 10:30:11  utils.py 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-1]
2025-07-15 10:30:11  EarlyBird.api.talk2ai.utils 161: INFO  提纲处理完成，根节点ID: W8ogm7FCJ8dsAkiHfnas5u [waitress-1]
2025-07-15 10:30:11  utils.py 161: INFO  提纲处理完成，根节点ID: W8ogm7FCJ8dsAkiHfnas5u [waitress-1]
2025-07-15 10:30:12  web_server.py 73: INFO  Serving request for path: paper/content [waitress-3]
2025-07-15 10:30:12  web_server.py 85: INFO  Serving index.html for path: paper/content [waitress-3]
2025-07-15 10:30:12  web_server.py 73: INFO  Serving request for path: js/chunk-vendors.04aff60f.js [waitress-2]
2025-07-15 10:30:12  web_server.py 73: INFO  Serving request for path: js/app.7cfeed94.js [waitress-1]
2025-07-15 10:30:12  web_server.py 73: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-0]
2025-07-15 10:30:12  web_server.py 73: INFO  Serving request for path: css/app.1e68ddae.css [waitress-3]
2025-07-15 10:30:12  task.py 113: WARNING  Task queue depth is 1 [MainThread]
2025-07-15 10:30:12  web_server.py 82: INFO  Serving static file: js/chunk-vendors.04aff60f.js [waitress-2]
2025-07-15 10:30:12  web_server.py 82: INFO  Serving static file: js/app.7cfeed94.js [waitress-1]
2025-07-15 10:30:12  web_server.py 82: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-0]
2025-07-15 10:30:12  web_server.py 82: INFO  Serving static file: css/app.1e68ddae.css [waitress-3]
2025-07-15 10:30:12  web_server.py 73: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-1]
2025-07-15 10:30:12  web_server.py 85: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-1]
2025-07-15 10:30:12  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/user/info [waitress-2]
2025-07-15 10:30:12  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/user/info [waitress-2]
2025-07-15 10:30:12  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-2]
2025-07-15 10:30:12  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-2]
2025-07-15 10:30:12  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-2]
2025-07-15 10:30:12  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-2]
2025-07-15 10:30:12  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-15 10:30:12  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-15 10:30:12  web_server.py 73: INFO  Serving request for path: logo.png [waitress-0]
2025-07-15 10:30:12  web_server.py 82: INFO  Serving static file: logo.png [waitress-0]
2025-07-15 10:30:12  web_server.py 73: INFO  Serving request for path: js/22.683bc166.js [waitress-3]
2025-07-15 10:30:12  web_server.py 73: INFO  Serving request for path: js/897.25207915.js [waitress-1]
2025-07-15 10:30:12  web_server.py 82: INFO  Serving static file: js/22.683bc166.js [waitress-3]
2025-07-15 10:30:12  web_server.py 73: INFO  Serving request for path: css/389.64f20d88.css [waitress-2]
2025-07-15 10:30:12  web_server.py 82: INFO  Serving static file: js/897.25207915.js [waitress-1]
2025-07-15 10:30:12  web_server.py 73: INFO  Serving request for path: js/389.0b47414d.js [waitress-0]
2025-07-15 10:30:12  web_server.py 82: INFO  Serving static file: css/389.64f20d88.css [waitress-2]
2025-07-15 10:30:12  web_server.py 82: INFO  Serving static file: js/389.0b47414d.js [waitress-0]
2025-07-15 10:30:12  web_server.py 73: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-3]
2025-07-15 10:30:12  web_server.py 82: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-3]
2025-07-15 10:30:12  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getList [waitress-0]
2025-07-15 10:30:12  app.py 1454: ERROR  Exception on /api/home/<USER>
Traceback (most recent call last):
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1900, in _execute_context
    self.dialect.do_execute(
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
  File "C:\Python311\Lib\site-packages\pymysql\cursors.py", line 148, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\cursors.py", line 310, in _query
    conn.query(q)
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 548, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 775, in _read_query_result
    result.read()
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 1156, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 725, in _read_packet
    packet.raise_for_error()
  File "C:\Python311\Lib\site-packages\pymysql\protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "C:\Python311\Lib\site-packages\pymysql\err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'earlybird_paper_setting.category' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python311\Lib\site-packages\flask\app.py", line 2070, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask\app.py", line 1515, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask_cors\extension.py", line 165, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask\app.py", line 1513, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask\app.py", line 1499, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**req.view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask_pydantic\core.py", line 249, in wrapper
    res = func(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^
  File "d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\api\home\apis.py", line 18, in getSetting
    res: Result = HomeService().getSetting()
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\api\home\service.py", line 30, in getSetting
    "modelName": self.getModelName(),
                 ^^^^^^^^^^^^^^^^^^^
  File "d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\api\home\service.py", line 21, in getModelName
    setting: Setting = Setting.query.filter(Setting.settingKey == "modelName").first()
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\orm\query.py", line 2824, in first
    return self.limit(1)._iter().first()
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\orm\query.py", line 2916, in _iter
    result = self.session.execute(
             ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\orm\session.py", line 1714, in execute
    result = conn._execute_20(statement, params or {}, execution_options)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1705, in _execute_20
    return meth(self, args_10style, kwargs_10style, execution_options)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\sql\elements.py", line 334, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1572, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1943, in _execute_context
    self._handle_dbapi_exception(
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 2124, in _handle_dbapi_exception
    util.raise_(
  File "C:\Python311\Lib\site-packages\sqlalchemy\util\compat.py", line 211, in raise_
    raise exception
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1900, in _execute_context
    self.dialect.do_execute(
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
  File "C:\Python311\Lib\site-packages\pymysql\cursors.py", line 148, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\cursors.py", line 310, in _query
    conn.query(q)
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 548, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 775, in _read_query_result
    result.read()
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 1156, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 725, in _read_packet
    packet.raise_for_error()
  File "C:\Python311\Lib\site-packages\pymysql\protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "C:\Python311\Lib\site-packages\pymysql\err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
sqlalchemy.exc.OperationalError: (pymysql.err.OperationalError) (1054, "Unknown column 'earlybird_paper_setting.category' in 'field list'")
[SQL: SELECT earlybird_paper_setting.id AS earlybird_paper_setting_id, earlybird_paper_setting.create_time AS earlybird_paper_setting_create_time, earlybird_paper_setting.update_time AS earlybird_paper_setting_update_time, earlybird_paper_setting.is_deleted AS earlybird_paper_setting_is_deleted, earlybird_paper_setting.status AS earlybird_paper_setting_status, earlybird_paper_setting.category AS earlybird_paper_setting_category, earlybird_paper_setting.`key` AS earlybird_paper_setting_key, earlybird_paper_setting.value AS earlybird_paper_setting_value, earlybird_paper_setting.description AS earlybird_paper_setting_description, earlybird_paper_setting.`settingKey` AS `earlybird_paper_setting_settingKey`, earlybird_paper_setting.`settingValue` AS `earlybird_paper_setting_settingValue` 
FROM earlybird_paper_setting 
WHERE earlybird_paper_setting.`settingKey` = %(settingKey_1)s 
 LIMIT %(param_1)s]
[parameters: {'settingKey_1': 'modelName', 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
2025-07-15 10:30:12  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getList [waitress-0]
2025-07-15 10:30:12  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-0]
2025-07-15 10:30:12  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-0]
2025-07-15 10:30:12  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-0]
2025-07-15 10:30:12  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-0]
2025-07-15 10:30:12  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-15 10:30:12  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-15 10:30:12  EarlyBird.api.thesis.apis 92: INFO  用户 13 请求获取论文列表 [waitress-0]
2025-07-15 10:30:12  apis.py 92: INFO  用户 13 请求获取论文列表 [waitress-0]
2025-07-15 10:30:12  EarlyBird.api.thesis.service 548: INFO  查询论文列表，用户ID: 13 [waitress-0]
2025-07-15 10:30:12  service.py 548: INFO  查询论文列表，用户ID: 13 [waitress-0]
2025-07-15 10:30:12  EarlyBird.api.thesis.service 554: INFO  查询到 1 篇论文 [waitress-0]
2025-07-15 10:30:12  service.py 554: INFO  查询到 1 篇论文 [waitress-0]
2025-07-15 10:30:12  EarlyBird.api.thesis.service 564: INFO  论文ID: 29, 标题: 船舶结构完整性先进测试方法研究, 用户ID: 13 [waitress-0]
2025-07-15 10:30:12  service.py 564: INFO  论文ID: 29, 标题: 船舶结构完整性先进测试方法研究, 用户ID: 13 [waitress-0]
2025-07-15 10:30:12  EarlyBird.api.thesis.service 566: INFO  返回论文列表，共 1 篇 [waitress-0]
2025-07-15 10:30:12  service.py 566: INFO  返回论文列表，共 1 篇 [waitress-0]
2025-07-15 10:30:12  EarlyBird.api.thesis.apis 95: INFO  用户 13 获取论文列表成功，共 1 篇 [waitress-0]
2025-07-15 10:30:12  apis.py 95: INFO  用户 13 获取论文列表成功，共 1 篇 [waitress-0]
2025-07-15 10:30:12  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getDetail [waitress-2]
2025-07-15 10:30:12  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getDetail [waitress-2]
2025-07-15 10:30:12  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-2]
2025-07-15 10:30:12  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-2]
2025-07-15 10:30:12  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-2]
2025-07-15 10:30:12  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-2]
2025-07-15 10:30:12  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-15 10:30:12  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-15 10:30:12  EarlyBird.api.talk2ai.utils 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-2]
2025-07-15 10:30:12  utils.py 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-2]
2025-07-15 10:30:12  EarlyBird.api.talk2ai.utils 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-2]
2025-07-15 10:30:12  utils.py 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-2]
2025-07-15 10:30:12  EarlyBird.api.talk2ai.utils 161: INFO  提纲处理完成，根节点ID: W8ogm7FCJ8dsAkiHfnas5u [waitress-2]
2025-07-15 10:30:12  utils.py 161: INFO  提纲处理完成，根节点ID: W8ogm7FCJ8dsAkiHfnas5u [waitress-2]
2025-07-15 10:30:12  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getOutline [waitress-3]
2025-07-15 10:30:12  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getOutline [waitress-3]
2025-07-15 10:30:12  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-3]
2025-07-15 10:30:12  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-3]
2025-07-15 10:30:12  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-3]
2025-07-15 10:30:12  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-3]
2025-07-15 10:30:12  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-15 10:30:12  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-15 10:30:12  EarlyBird.api.talk2ai.utils 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-3]
2025-07-15 10:30:12  utils.py 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-3]
2025-07-15 10:30:12  EarlyBird.api.talk2ai.utils 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-3]
2025-07-15 10:30:12  utils.py 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-3]
2025-07-15 10:30:12  EarlyBird.api.talk2ai.utils 161: INFO  提纲处理完成，根节点ID: W8ogm7FCJ8dsAkiHfnas5u [waitress-3]
2025-07-15 10:30:12  utils.py 161: INFO  提纲处理完成，根节点ID: W8ogm7FCJ8dsAkiHfnas5u [waitress-3]
2025-07-15 10:30:28  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-1]
2025-07-15 10:30:28  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-1]
2025-07-15 10:30:28  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-1]
2025-07-15 10:30:28  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-1]
2025-07-15 10:30:28  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-1]
2025-07-15 10:30:28  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-1]
2025-07-15 10:30:28  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-15 10:30:28  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-15 10:30:28  EarlyBird.api.thesis.apis 145: INFO  用户 13 请求下载论文 29 [waitress-1]
2025-07-15 10:30:28  apis.py 145: INFO  用户 13 请求下载论文 29 [waitress-1]
2025-07-15 10:30:28  EarlyBird.api.thesis.service 296: INFO  用户 13 请求导出论文 29 [waitress-1]
2025-07-15 10:30:28  service.py 296: INFO  用户 13 请求导出论文 29 [waitress-1]
2025-07-15 10:30:28  EarlyBird.api.thesis.service 304: INFO  用户 13 VIP状态: False [waitress-1]
2025-07-15 10:30:28  service.py 304: INFO  用户 13 VIP状态: False [waitress-1]
2025-07-15 10:30:28  EarlyBird.api.thesis.service 309: INFO  论文下载收费功能是否启用: True [waitress-1]
2025-07-15 10:30:28  service.py 309: INFO  论文下载收费功能是否启用: True [waitress-1]
2025-07-15 10:30:28  EarlyBird.api.thesis.service 323: INFO  VIP用户是否免费下载: False [waitress-1]
2025-07-15 10:30:28  service.py 323: INFO  VIP用户是否免费下载: False [waitress-1]
2025-07-15 10:30:28  EarlyBird.api.thesis.service 340: INFO  首次下载是否免费: False [waitress-1]
2025-07-15 10:30:28  service.py 340: INFO  首次下载是否免费: False [waitress-1]
2025-07-15 10:30:28  EarlyBird.api.thesis.service 344: INFO  用户 13 是否有已支付的下载记录: True [waitress-1]
2025-07-15 10:30:28  service.py 344: INFO  用户 13 是否有已支付的下载记录: True [waitress-1]
2025-07-15 10:30:28  EarlyBird.api.thesis.service 362: INFO  用户 13 已有已支付的下载记录，可直接下载论文 29 [waitress-1]
2025-07-15 10:30:28  service.py 362: INFO  用户 13 已有已支付的下载记录，可直接下载论文 29 [waitress-1]
2025-07-15 10:30:28  EarlyBird.api.thesis.service 737: INFO  add Para:船舶结构完整性先进测试方法研究 [waitress-1]
2025-07-15 10:30:28  service.py 737: INFO  add Para:船舶结构完整性先进测试方法研究 [waitress-1]
2025-07-15 10:30:28  EarlyBird.api.thesis.service 737: INFO  add Para:绪论 [waitress-1]
2025-07-15 10:30:28  service.py 737: INFO  add Para:绪论 [waitress-1]
2025-07-15 10:30:28  EarlyBird.api.thesis.service 737: INFO  add Para:研究背景与现状 [waitress-1]
2025-07-15 10:30:28  service.py 737: INFO  add Para:研究背景与现状 [waitress-1]
2025-07-15 10:30:28  EarlyBird.api.thesis.service 737: INFO  add Para:船舶结构完整性数据现状 [waitress-1]
2025-07-15 10:30:28  service.py 737: INFO  add Para:船舶结构完整性数据现状 [waitress-1]
2025-07-15 10:30:28  EarlyBird.api.thesis.service 737: INFO  add Para:研究意义与目标 [waitress-1]
2025-07-15 10:30:28  service.py 737: INFO  add Para:研究意义与目标 [waitress-1]
2025-07-15 10:30:28  EarlyBird.api.thesis.service 737: INFO  add Para:理论基础与数据分析模型 [waitress-1]
2025-07-15 10:30:28  service.py 737: INFO  add Para:理论基础与数据分析模型 [waitress-1]
2025-07-15 10:30:28  EarlyBird.api.thesis.service 737: INFO  add Para:船舶结构完整性的理论框架 [waitress-1]
2025-07-15 10:30:28  service.py 737: INFO  add Para:船舶结构完整性的理论框架 [waitress-1]
2025-07-15 10:30:28  EarlyBird.api.thesis.service 737: INFO  add Para:基于大数据的船舶结构分析模型 [waitress-1]
2025-07-15 10:30:28  service.py 737: INFO  add Para:基于大数据的船舶结构分析模型 [waitress-1]
2025-07-15 10:30:28  EarlyBird.api.thesis.service 737: INFO  add Para:数据分析方法的选择与应用 [waitress-1]
2025-07-15 10:30:28  service.py 737: INFO  add Para:数据分析方法的选择与应用 [waitress-1]
2025-07-15 10:30:28  EarlyBird.api.thesis.service 737: INFO  add Para:实证研究：数据收集与处理 [waitress-1]
2025-07-15 10:30:28  service.py 737: INFO  add Para:实证研究：数据收集与处理 [waitress-1]
2025-07-15 10:30:28  EarlyBird.api.thesis.service 737: INFO  add Para:数据来源与采集方法 [waitress-1]
2025-07-15 10:30:28  service.py 737: INFO  add Para:数据来源与采集方法 [waitress-1]
2025-07-15 10:30:28  EarlyBird.api.thesis.service 737: INFO  add Para:数据清洗与预处理 [waitress-1]
2025-07-15 10:30:28  service.py 737: INFO  add Para:数据清洗与预处理 [waitress-1]
2025-07-15 10:30:28  EarlyBird.api.thesis.service 737: INFO  add Para:数据统计与初步分析 [waitress-1]
2025-07-15 10:30:28  service.py 737: INFO  add Para:数据统计与初步分析 [waitress-1]
2025-07-15 10:30:28  EarlyBird.api.thesis.service 737: INFO  add Para:案例分析：特定场景下的数据对比 [waitress-1]
2025-07-15 10:30:28  service.py 737: INFO  add Para:案例分析：特定场景下的数据对比 [waitress-1]
2025-07-15 10:30:28  EarlyBird.api.thesis.service 737: INFO  add Para:选取典型船舶结构案例 [waitress-1]
2025-07-15 10:30:28  service.py 737: INFO  add Para:选取典型船舶结构案例 [waitress-1]
2025-07-15 10:30:28  EarlyBird.api.thesis.service 737: INFO  add Para:不同测试方法的数据对比 [waitress-1]
2025-07-15 10:30:28  service.py 737: INFO  add Para:不同测试方法的数据对比 [waitress-1]
2025-07-15 10:30:28  EarlyBird.api.thesis.service 737: INFO  add Para:案例效果评估与反馈 [waitress-1]
2025-07-15 10:30:28  service.py 737: INFO  add Para:案例效果评估与反馈 [waitress-1]
2025-07-15 10:30:28  EarlyBird.api.thesis.service 737: INFO  add Para:数据展示与深度分析 [waitress-1]
2025-07-15 10:30:28  service.py 737: INFO  add Para:数据展示与深度分析 [waitress-1]
2025-07-15 10:30:28  EarlyBird.api.thesis.service 737: INFO  add Para:关键数据可视化展示 [waitress-1]
2025-07-15 10:30:28  service.py 737: INFO  add Para:关键数据可视化展示 [waitress-1]
2025-07-15 10:30:28  EarlyBird.api.thesis.service 737: INFO  add Para:异常数据检测与处理 [waitress-1]
2025-07-15 10:30:28  service.py 737: INFO  add Para:异常数据检测与处理 [waitress-1]
2025-07-15 10:30:28  EarlyBird.api.thesis.service 737: INFO  add Para:基于数据的深度洞察与解释 [waitress-1]
2025-07-15 10:30:28  service.py 737: INFO  add Para:基于数据的深度洞察与解释 [waitress-1]
2025-07-15 10:30:28  EarlyBird.api.thesis.service 737: INFO  add Para:结论与政策建议 [waitress-1]
2025-07-15 10:30:28  service.py 737: INFO  add Para:结论与政策建议 [waitress-1]
2025-07-15 10:30:28  EarlyBird.api.thesis.service 737: INFO  add Para:数据总结与研究发现 [waitress-1]
2025-07-15 10:30:28  service.py 737: INFO  add Para:数据总结与研究发现 [waitress-1]
2025-07-15 10:30:28  EarlyBird.api.thesis.service 737: INFO  add Para:对船舶设计与维护的政策建议 [waitress-1]
2025-07-15 10:30:28  service.py 737: INFO  add Para:对船舶设计与维护的政策建议 [waitress-1]
2025-07-15 10:30:28  EarlyBird.api.thesis.service 737: INFO  add Para:未来研究方向与展望 [waitress-1]
2025-07-15 10:30:28  service.py 737: INFO  add Para:未来研究方向与展望 [waitress-1]
2025-07-15 10:30:28  EarlyBird.common.docx 500: INFO  d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\download\[早鸟论文]_船舶结构完整性先进测试方法研究_V20250715_103028.docx [waitress-1]
2025-07-15 10:30:28  docx.py 500: INFO  d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\download\[早鸟论文]_船舶结构完整性先进测试方法研究_V20250715_103028.docx [waitress-1]
2025-07-15 10:30:28  EarlyBird.api.thesis.apis 162: INFO  用户 13 下载论文 29 成功 [waitress-1]
2025-07-15 10:30:28  apis.py 162: INFO  用户 13 下载论文 29 成功 [waitress-1]
2025-07-15 10:30:28  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/download [waitress-0]
2025-07-15 10:30:28  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/download [waitress-0]
2025-07-15 10:30:28  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-0]
2025-07-15 10:30:28  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-0]
2025-07-15 10:30:28  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-0]
2025-07-15 10:30:28  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-0]
2025-07-15 10:30:28  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-15 10:30:28  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-15 10:36:05  init_flask.py 27: INFO  {'CpuCount': 6, 'Mem': '32 GB'} [MainThread]
2025-07-15 10:36:05  web_server.py 32: INFO  Static directory: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\ui [MainThread]
2025-07-15 10:36:05  web_server.py 33: INFO  Static directory exists: True [MainThread]
2025-07-15 10:36:05  web_server.py 37: INFO  设置工作目录: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird [MainThread]
2025-07-15 10:36:06  EarlyBird.config.config 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-15 10:36:06  config.py 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-15 10:36:06  config.py 94: INFO  Database URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-15 10:36:07  EarlyBird.api.admin.app 39: INFO  ✅ 后台管理系统API已注册 [MainThread]
2025-07-15 10:36:07  app.py 39: INFO  ✅ 后台管理系统API已注册 [MainThread]
2025-07-15 10:36:08  EarlyBird 40: INFO  确保数据目录存在: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\data [MainThread]
2025-07-15 10:36:08  server.py 40: INFO  确保数据目录存在: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\data [MainThread]
2025-07-15 10:36:08  EarlyBird 44: INFO  开始执行数据库迁移... [MainThread]
2025-07-15 10:36:08  server.py 44: INFO  开始执行数据库迁移... [MainThread]
2025-07-15 10:36:08  config.py 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-15 10:36:08  config.py 94: INFO  Database URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-15 10:36:08  EarlyBird 53: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-15 10:36:08  server.py 53: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-15 10:36:08  EarlyBird 97: WARNING  ChatLog表不存在，将在应用启动时自动创建 [MainThread]
2025-07-15 10:36:08  server.py 97: WARNING  ChatLog表不存在，将在应用启动时自动创建 [MainThread]
2025-07-15 10:36:08  add_uid_to_chatlog.py 20: INFO  开始执行ChatLog表迁移... [MainThread]
2025-07-15 10:36:08  add_uid_to_chatlog.py 24: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-15 10:36:08  add_uid_to_chatlog.py 38: WARNING  ChatLog表不存在，可能需要先创建表 [MainThread]
2025-07-15 10:36:08  task_api_server.py 31: INFO  TaskApiServer start [MainThread]
2025-07-15 10:36:08  task_paper_generator.py 38: INFO  论文生成任务处理线程初始化 [MainThread]
2025-07-15 10:36:08  task_paper_generator.py 72: INFO  论文生成任务处理线程启动 [Thread-2]
2025-07-15 10:36:08  task_api_server.py 47: INFO  TaskApiServer listen on 127.0.0.1:3302 [Thread-1]
2025-07-15 10:36:08  task_api_server.py 17: INFO  _ApiServerThread start [Thread-3]
2025-07-15 10:36:13  web_server.py 73: INFO  Serving request for path: paper/content [waitress-0]
2025-07-15 10:36:13  web_server.py 85: INFO  Serving index.html for path: paper/content [waitress-0]
2025-07-15 10:36:14  web_server.py 73: INFO  Serving request for path: js/chunk-vendors.04aff60f.js [waitress-1]
2025-07-15 10:36:14  web_server.py 82: INFO  Serving static file: js/chunk-vendors.04aff60f.js [waitress-1]
2025-07-15 10:36:14  web_server.py 73: INFO  Serving request for path: js/app.c8175a10.js [waitress-2]
2025-07-15 10:36:14  web_server.py 82: INFO  Serving static file: js/app.c8175a10.js [waitress-2]
2025-07-15 10:36:14  web_server.py 73: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-2]
2025-07-15 10:36:14  web_server.py 73: INFO  Serving request for path: css/app.1e68ddae.css [waitress-0]
2025-07-15 10:36:14  web_server.py 73: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-1]
2025-07-15 10:36:14  web_server.py 82: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-2]
2025-07-15 10:36:14  web_server.py 82: INFO  Serving static file: css/app.1e68ddae.css [waitress-0]
2025-07-15 10:36:14  web_server.py 85: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-1]
2025-07-15 10:36:14  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/user/info [waitress-3]
2025-07-15 10:36:14  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/user/info [waitress-3]
2025-07-15 10:36:14  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-3]
2025-07-15 10:36:14  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-3]
2025-07-15 10:36:14  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-3]
2025-07-15 10:36:14  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-3]
2025-07-15 10:36:14  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-15 10:36:14  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-15 10:36:14  web_server.py 73: INFO  Serving request for path: logo.png [waitress-2]
2025-07-15 10:36:14  web_server.py 82: INFO  Serving static file: logo.png [waitress-2]
2025-07-15 10:36:14  web_server.py 73: INFO  Serving request for path: js/22.683bc166.js [waitress-1]
2025-07-15 10:36:14  web_server.py 73: INFO  Serving request for path: js/897.25207915.js [waitress-0]
2025-07-15 10:36:14  web_server.py 73: INFO  Serving request for path: js/389.fc477d7f.js [waitress-2]
2025-07-15 10:36:14  web_server.py 82: INFO  Serving static file: js/22.683bc166.js [waitress-1]
2025-07-15 10:36:14  web_server.py 73: INFO  Serving request for path: css/389.a603f84b.css [waitress-3]
2025-07-15 10:36:14  web_server.py 82: INFO  Serving static file: js/897.25207915.js [waitress-0]
2025-07-15 10:36:14  web_server.py 82: INFO  Serving static file: js/389.fc477d7f.js [waitress-2]
2025-07-15 10:36:14  web_server.py 82: INFO  Serving static file: css/389.a603f84b.css [waitress-3]
2025-07-15 10:36:14  web_server.py 73: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-1]
2025-07-15 10:36:14  web_server.py 82: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-1]
2025-07-15 10:36:14  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getList [waitress-3]
2025-07-15 10:36:14  app.py 1454: ERROR  Exception on /api/home/<USER>
Traceback (most recent call last):
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1900, in _execute_context
    self.dialect.do_execute(
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
  File "C:\Python311\Lib\site-packages\pymysql\cursors.py", line 148, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\cursors.py", line 310, in _query
    conn.query(q)
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 548, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 775, in _read_query_result
    result.read()
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 1156, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 725, in _read_packet
    packet.raise_for_error()
  File "C:\Python311\Lib\site-packages\pymysql\protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "C:\Python311\Lib\site-packages\pymysql\err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'earlybird_paper_setting.category' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python311\Lib\site-packages\flask\app.py", line 2070, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask\app.py", line 1515, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask_cors\extension.py", line 165, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask\app.py", line 1513, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask\app.py", line 1499, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**req.view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask_pydantic\core.py", line 249, in wrapper
    res = func(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^
  File "d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\api\home\apis.py", line 18, in getSetting
    res: Result = HomeService().getSetting()
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\api\home\service.py", line 30, in getSetting
    "modelName": self.getModelName(),
                 ^^^^^^^^^^^^^^^^^^^
  File "d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\api\home\service.py", line 21, in getModelName
    setting: Setting = Setting.query.filter(Setting.settingKey == "modelName").first()
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\orm\query.py", line 2824, in first
    return self.limit(1)._iter().first()
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\orm\query.py", line 2916, in _iter
    result = self.session.execute(
             ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\orm\session.py", line 1714, in execute
    result = conn._execute_20(statement, params or {}, execution_options)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1705, in _execute_20
    return meth(self, args_10style, kwargs_10style, execution_options)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\sql\elements.py", line 334, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1572, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1943, in _execute_context
    self._handle_dbapi_exception(
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 2124, in _handle_dbapi_exception
    util.raise_(
  File "C:\Python311\Lib\site-packages\sqlalchemy\util\compat.py", line 211, in raise_
    raise exception
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1900, in _execute_context
    self.dialect.do_execute(
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
  File "C:\Python311\Lib\site-packages\pymysql\cursors.py", line 148, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\cursors.py", line 310, in _query
    conn.query(q)
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 548, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 775, in _read_query_result
    result.read()
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 1156, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 725, in _read_packet
    packet.raise_for_error()
  File "C:\Python311\Lib\site-packages\pymysql\protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "C:\Python311\Lib\site-packages\pymysql\err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
sqlalchemy.exc.OperationalError: (pymysql.err.OperationalError) (1054, "Unknown column 'earlybird_paper_setting.category' in 'field list'")
[SQL: SELECT earlybird_paper_setting.id AS earlybird_paper_setting_id, earlybird_paper_setting.create_time AS earlybird_paper_setting_create_time, earlybird_paper_setting.update_time AS earlybird_paper_setting_update_time, earlybird_paper_setting.is_deleted AS earlybird_paper_setting_is_deleted, earlybird_paper_setting.status AS earlybird_paper_setting_status, earlybird_paper_setting.category AS earlybird_paper_setting_category, earlybird_paper_setting.`key` AS earlybird_paper_setting_key, earlybird_paper_setting.value AS earlybird_paper_setting_value, earlybird_paper_setting.description AS earlybird_paper_setting_description, earlybird_paper_setting.`settingKey` AS `earlybird_paper_setting_settingKey`, earlybird_paper_setting.`settingValue` AS `earlybird_paper_setting_settingValue` 
FROM earlybird_paper_setting 
WHERE earlybird_paper_setting.`settingKey` = %(settingKey_1)s 
 LIMIT %(param_1)s]
[parameters: {'settingKey_1': 'modelName', 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
2025-07-15 10:36:14  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getList [waitress-3]
2025-07-15 10:36:14  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-3]
2025-07-15 10:36:14  web_server.py 73: INFO  Serving request for path: fonts/element-icons.ff18efd1.woff [waitress-2]
2025-07-15 10:36:14  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-3]
2025-07-15 10:36:14  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-3]
2025-07-15 10:36:14  web_server.py 82: INFO  Serving static file: fonts/element-icons.ff18efd1.woff [waitress-2]
2025-07-15 10:36:14  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-3]
2025-07-15 10:36:14  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-15 10:36:14  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-15 10:36:14  EarlyBird.api.thesis.apis 92: INFO  用户 13 请求获取论文列表 [waitress-3]
2025-07-15 10:36:14  apis.py 92: INFO  用户 13 请求获取论文列表 [waitress-3]
2025-07-15 10:36:14  EarlyBird.api.thesis.service 556: INFO  查询论文列表，用户ID: 13 [waitress-3]
2025-07-15 10:36:14  service.py 556: INFO  查询论文列表，用户ID: 13 [waitress-3]
2025-07-15 10:36:14  EarlyBird.api.thesis.service 562: INFO  查询到 1 篇论文 [waitress-3]
2025-07-15 10:36:14  service.py 562: INFO  查询到 1 篇论文 [waitress-3]
2025-07-15 10:36:14  EarlyBird.api.thesis.service 572: INFO  论文ID: 29, 标题: 船舶结构完整性先进测试方法研究, 用户ID: 13 [waitress-3]
2025-07-15 10:36:14  service.py 572: INFO  论文ID: 29, 标题: 船舶结构完整性先进测试方法研究, 用户ID: 13 [waitress-3]
2025-07-15 10:36:14  EarlyBird.api.thesis.service 574: INFO  返回论文列表，共 1 篇 [waitress-3]
2025-07-15 10:36:14  service.py 574: INFO  返回论文列表，共 1 篇 [waitress-3]
2025-07-15 10:36:14  EarlyBird.api.thesis.apis 95: INFO  用户 13 获取论文列表成功，共 1 篇 [waitress-3]
2025-07-15 10:36:14  apis.py 95: INFO  用户 13 获取论文列表成功，共 1 篇 [waitress-3]
2025-07-15 10:36:14  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getDetail [waitress-1]
2025-07-15 10:36:14  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getDetail [waitress-1]
2025-07-15 10:36:14  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-1]
2025-07-15 10:36:14  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-1]
2025-07-15 10:36:14  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-1]
2025-07-15 10:36:14  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-1]
2025-07-15 10:36:14  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-15 10:36:14  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-15 10:36:14  EarlyBird.api.talk2ai.utils 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-1]
2025-07-15 10:36:14  utils.py 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-1]
2025-07-15 10:36:14  EarlyBird.api.talk2ai.utils 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-1]
2025-07-15 10:36:14  utils.py 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-1]
2025-07-15 10:36:14  EarlyBird.api.talk2ai.utils 161: INFO  提纲处理完成，根节点ID: W8ogm7FCJ8dsAkiHfnas5u [waitress-1]
2025-07-15 10:36:14  utils.py 161: INFO  提纲处理完成，根节点ID: W8ogm7FCJ8dsAkiHfnas5u [waitress-1]
2025-07-15 10:36:14  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getOutline [waitress-0]
2025-07-15 10:36:14  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getOutline [waitress-0]
2025-07-15 10:36:14  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-0]
2025-07-15 10:36:14  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-0]
2025-07-15 10:36:14  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-0]
2025-07-15 10:36:14  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-0]
2025-07-15 10:36:14  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-15 10:36:14  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-15 10:36:14  EarlyBird.api.talk2ai.utils 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-0]
2025-07-15 10:36:14  utils.py 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-0]
2025-07-15 10:36:14  EarlyBird.api.talk2ai.utils 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-0]
2025-07-15 10:36:14  utils.py 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-0]
2025-07-15 10:36:14  EarlyBird.api.talk2ai.utils 161: INFO  提纲处理完成，根节点ID: W8ogm7FCJ8dsAkiHfnas5u [waitress-0]
2025-07-15 10:36:14  utils.py 161: INFO  提纲处理完成，根节点ID: W8ogm7FCJ8dsAkiHfnas5u [waitress-0]
2025-07-15 10:36:15  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-2]
2025-07-15 10:36:15  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-2]
2025-07-15 10:36:15  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-2]
2025-07-15 10:36:15  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-2]
2025-07-15 10:36:15  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-2]
2025-07-15 10:36:15  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-2]
2025-07-15 10:36:15  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-15 10:36:15  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-15 10:36:15  EarlyBird.api.thesis.apis 145: INFO  用户 13 请求下载论文 29 [waitress-2]
2025-07-15 10:36:15  apis.py 145: INFO  用户 13 请求下载论文 29 [waitress-2]
2025-07-15 10:36:15  EarlyBird.api.thesis.service 296: INFO  用户 13 请求导出论文 29 [waitress-2]
2025-07-15 10:36:15  service.py 296: INFO  用户 13 请求导出论文 29 [waitress-2]
2025-07-15 10:36:15  EarlyBird.api.thesis.service 304: INFO  用户 13 VIP状态: False [waitress-2]
2025-07-15 10:36:15  service.py 304: INFO  用户 13 VIP状态: False [waitress-2]
2025-07-15 10:36:15  EarlyBird.api.thesis.service 309: INFO  论文下载收费功能是否启用: True [waitress-2]
2025-07-15 10:36:15  service.py 309: INFO  论文下载收费功能是否启用: True [waitress-2]
2025-07-15 10:36:15  EarlyBird.api.thesis.service 323: INFO  VIP用户是否免费下载: False [waitress-2]
2025-07-15 10:36:15  service.py 323: INFO  VIP用户是否免费下载: False [waitress-2]
2025-07-15 10:36:15  EarlyBird.api.thesis.service 340: INFO  首次下载是否免费: False [waitress-2]
2025-07-15 10:36:15  service.py 340: INFO  首次下载是否免费: False [waitress-2]
2025-07-15 10:36:15  EarlyBird.api.thesis.service 350: INFO  用户 13 是否有真实支付的下载记录: True [waitress-2]
2025-07-15 10:36:15  service.py 350: INFO  用户 13 是否有真实支付的下载记录: True [waitress-2]
2025-07-15 10:36:15  EarlyBird.api.thesis.service 370: INFO  用户 13 已有已支付的下载记录，可直接下载论文 29 [waitress-2]
2025-07-15 10:36:15  service.py 370: INFO  用户 13 已有已支付的下载记录，可直接下载论文 29 [waitress-2]
2025-07-15 10:36:15  EarlyBird.api.thesis.service 745: INFO  add Para:船舶结构完整性先进测试方法研究 [waitress-2]
2025-07-15 10:36:15  service.py 745: INFO  add Para:船舶结构完整性先进测试方法研究 [waitress-2]
2025-07-15 10:36:15  EarlyBird.api.thesis.service 745: INFO  add Para:绪论 [waitress-2]
2025-07-15 10:36:15  service.py 745: INFO  add Para:绪论 [waitress-2]
2025-07-15 10:36:15  EarlyBird.api.thesis.service 745: INFO  add Para:研究背景与现状 [waitress-2]
2025-07-15 10:36:15  service.py 745: INFO  add Para:研究背景与现状 [waitress-2]
2025-07-15 10:36:15  EarlyBird.api.thesis.service 745: INFO  add Para:船舶结构完整性数据现状 [waitress-2]
2025-07-15 10:36:15  service.py 745: INFO  add Para:船舶结构完整性数据现状 [waitress-2]
2025-07-15 10:36:15  EarlyBird.api.thesis.service 745: INFO  add Para:研究意义与目标 [waitress-2]
2025-07-15 10:36:15  service.py 745: INFO  add Para:研究意义与目标 [waitress-2]
2025-07-15 10:36:15  EarlyBird.api.thesis.service 745: INFO  add Para:理论基础与数据分析模型 [waitress-2]
2025-07-15 10:36:15  service.py 745: INFO  add Para:理论基础与数据分析模型 [waitress-2]
2025-07-15 10:36:15  EarlyBird.api.thesis.service 745: INFO  add Para:船舶结构完整性的理论框架 [waitress-2]
2025-07-15 10:36:15  service.py 745: INFO  add Para:船舶结构完整性的理论框架 [waitress-2]
2025-07-15 10:36:15  EarlyBird.api.thesis.service 745: INFO  add Para:基于大数据的船舶结构分析模型 [waitress-2]
2025-07-15 10:36:15  service.py 745: INFO  add Para:基于大数据的船舶结构分析模型 [waitress-2]
2025-07-15 10:36:15  EarlyBird.api.thesis.service 745: INFO  add Para:数据分析方法的选择与应用 [waitress-2]
2025-07-15 10:36:15  service.py 745: INFO  add Para:数据分析方法的选择与应用 [waitress-2]
2025-07-15 10:36:15  EarlyBird.api.thesis.service 745: INFO  add Para:实证研究：数据收集与处理 [waitress-2]
2025-07-15 10:36:15  service.py 745: INFO  add Para:实证研究：数据收集与处理 [waitress-2]
2025-07-15 10:36:15  EarlyBird.api.thesis.service 745: INFO  add Para:数据来源与采集方法 [waitress-2]
2025-07-15 10:36:15  service.py 745: INFO  add Para:数据来源与采集方法 [waitress-2]
2025-07-15 10:36:15  EarlyBird.api.thesis.service 745: INFO  add Para:数据清洗与预处理 [waitress-2]
2025-07-15 10:36:15  service.py 745: INFO  add Para:数据清洗与预处理 [waitress-2]
2025-07-15 10:36:15  EarlyBird.api.thesis.service 745: INFO  add Para:数据统计与初步分析 [waitress-2]
2025-07-15 10:36:15  service.py 745: INFO  add Para:数据统计与初步分析 [waitress-2]
2025-07-15 10:36:15  EarlyBird.api.thesis.service 745: INFO  add Para:案例分析：特定场景下的数据对比 [waitress-2]
2025-07-15 10:36:15  service.py 745: INFO  add Para:案例分析：特定场景下的数据对比 [waitress-2]
2025-07-15 10:36:15  EarlyBird.api.thesis.service 745: INFO  add Para:选取典型船舶结构案例 [waitress-2]
2025-07-15 10:36:15  service.py 745: INFO  add Para:选取典型船舶结构案例 [waitress-2]
2025-07-15 10:36:15  EarlyBird.api.thesis.service 745: INFO  add Para:不同测试方法的数据对比 [waitress-2]
2025-07-15 10:36:15  service.py 745: INFO  add Para:不同测试方法的数据对比 [waitress-2]
2025-07-15 10:36:15  EarlyBird.api.thesis.service 745: INFO  add Para:案例效果评估与反馈 [waitress-2]
2025-07-15 10:36:15  service.py 745: INFO  add Para:案例效果评估与反馈 [waitress-2]
2025-07-15 10:36:15  EarlyBird.api.thesis.service 745: INFO  add Para:数据展示与深度分析 [waitress-2]
2025-07-15 10:36:15  service.py 745: INFO  add Para:数据展示与深度分析 [waitress-2]
2025-07-15 10:36:15  EarlyBird.api.thesis.service 745: INFO  add Para:关键数据可视化展示 [waitress-2]
2025-07-15 10:36:15  service.py 745: INFO  add Para:关键数据可视化展示 [waitress-2]
2025-07-15 10:36:15  EarlyBird.api.thesis.service 745: INFO  add Para:异常数据检测与处理 [waitress-2]
2025-07-15 10:36:15  service.py 745: INFO  add Para:异常数据检测与处理 [waitress-2]
2025-07-15 10:36:15  EarlyBird.api.thesis.service 745: INFO  add Para:基于数据的深度洞察与解释 [waitress-2]
2025-07-15 10:36:15  service.py 745: INFO  add Para:基于数据的深度洞察与解释 [waitress-2]
2025-07-15 10:36:15  EarlyBird.api.thesis.service 745: INFO  add Para:结论与政策建议 [waitress-2]
2025-07-15 10:36:15  service.py 745: INFO  add Para:结论与政策建议 [waitress-2]
2025-07-15 10:36:15  EarlyBird.api.thesis.service 745: INFO  add Para:数据总结与研究发现 [waitress-2]
2025-07-15 10:36:15  service.py 745: INFO  add Para:数据总结与研究发现 [waitress-2]
2025-07-15 10:36:15  EarlyBird.api.thesis.service 745: INFO  add Para:对船舶设计与维护的政策建议 [waitress-2]
2025-07-15 10:36:15  service.py 745: INFO  add Para:对船舶设计与维护的政策建议 [waitress-2]
2025-07-15 10:36:15  EarlyBird.api.thesis.service 745: INFO  add Para:未来研究方向与展望 [waitress-2]
2025-07-15 10:36:15  service.py 745: INFO  add Para:未来研究方向与展望 [waitress-2]
2025-07-15 10:36:15  EarlyBird.common.docx 500: INFO  d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\download\[早鸟论文]_船舶结构完整性先进测试方法研究_V20250715_103615.docx [waitress-2]
2025-07-15 10:36:15  docx.py 500: INFO  d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\download\[早鸟论文]_船舶结构完整性先进测试方法研究_V20250715_103615.docx [waitress-2]
2025-07-15 10:36:15  EarlyBird.api.thesis.apis 162: INFO  用户 13 下载论文 29 成功 [waitress-2]
2025-07-15 10:36:15  apis.py 162: INFO  用户 13 下载论文 29 成功 [waitress-2]
2025-07-15 10:36:15  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/download [waitress-3]
2025-07-15 10:36:15  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/download [waitress-3]
2025-07-15 10:36:15  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-3]
2025-07-15 10:36:15  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-3]
2025-07-15 10:36:15  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-3]
2025-07-15 10:36:15  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-3]
2025-07-15 10:36:15  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-15 10:36:15  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-15 10:36:27  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/deleteThesis [waitress-1]
2025-07-15 10:36:27  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/deleteThesis [waitress-1]
2025-07-15 10:36:27  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-1]
2025-07-15 10:36:27  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-1]
2025-07-15 10:36:27  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-1]
2025-07-15 10:36:27  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-1]
2025-07-15 10:36:27  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-15 10:36:27  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-15 10:36:28  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getList [waitress-0]
2025-07-15 10:36:28  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getList [waitress-0]
2025-07-15 10:36:28  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-0]
2025-07-15 10:36:28  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-0]
2025-07-15 10:36:28  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-0]
2025-07-15 10:36:28  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-0]
2025-07-15 10:36:28  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-15 10:36:28  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-15 10:36:28  EarlyBird.api.thesis.apis 92: INFO  用户 13 请求获取论文列表 [waitress-0]
2025-07-15 10:36:28  apis.py 92: INFO  用户 13 请求获取论文列表 [waitress-0]
2025-07-15 10:36:28  EarlyBird.api.thesis.service 556: INFO  查询论文列表，用户ID: 13 [waitress-0]
2025-07-15 10:36:28  service.py 556: INFO  查询论文列表，用户ID: 13 [waitress-0]
2025-07-15 10:36:28  EarlyBird.api.thesis.service 562: INFO  查询到 0 篇论文 [waitress-0]
2025-07-15 10:36:28  service.py 562: INFO  查询到 0 篇论文 [waitress-0]
2025-07-15 10:36:28  EarlyBird.api.thesis.service 574: INFO  返回论文列表，共 0 篇 [waitress-0]
2025-07-15 10:36:28  service.py 574: INFO  返回论文列表，共 0 篇 [waitress-0]
2025-07-15 10:36:28  EarlyBird.api.thesis.apis 95: INFO  用户 13 获取论文列表成功，共 0 篇 [waitress-0]
2025-07-15 10:36:28  apis.py 95: INFO  用户 13 获取论文列表成功，共 0 篇 [waitress-0]
2025-07-15 10:36:29  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/talk/getOutlineHistory [waitress-2]
2025-07-15 10:36:29  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/talk/getOutlineHistory [waitress-2]
2025-07-15 10:36:29  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-2]
2025-07-15 10:36:29  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-2]
2025-07-15 10:36:29  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-2]
2025-07-15 10:36:29  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-2]
2025-07-15 10:36:29  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-15 10:36:29  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-15 10:36:29  EarlyBird.api.talk2ai.service 953: INFO  🔍 查询用户 13 的提纲历史记录 [waitress-2]
2025-07-15 10:36:29  service.py 953: INFO  🔍 查询用户 13 的提纲历史记录 [waitress-2]
2025-07-15 10:36:29  EarlyBird.api.talk2ai.service 975: INFO  📝 用户 13 的提纲历史记录：共 1 条，当前页 1 条 [waitress-2]
2025-07-15 10:36:29  service.py 975: INFO  📝 用户 13 的提纲历史记录：共 1 条，当前页 1 条 [waitress-2]
2025-07-15 10:36:31  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/talk/checkExistingThesis [waitress-3]
2025-07-15 10:36:31  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/talk/checkExistingThesis [waitress-3]
2025-07-15 10:36:31  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-3]
2025-07-15 10:36:31  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-3]
2025-07-15 10:36:31  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-3]
2025-07-15 10:36:31  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-3]
2025-07-15 10:36:31  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-15 10:36:31  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-15 10:36:31  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/talk/select4Content [waitress-1]
2025-07-15 10:36:31  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/talk/select4Content [waitress-1]
2025-07-15 10:36:31  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-1]
2025-07-15 10:36:31  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-1]
2025-07-15 10:36:31  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-1]
2025-07-15 10:36:31  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-1]
2025-07-15 10:36:31  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-15 10:36:31  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-15 10:36:31  EarlyBird.api.talk2ai.apis 128: INFO  Received select4Content request with body type: <class 'EarlyBird.api.talk2ai.dantic.ParamSelect4Content'> [waitress-1]
2025-07-15 10:36:31  apis.py 128: INFO  Received select4Content request with body type: <class 'EarlyBird.api.talk2ai.dantic.ParamSelect4Content'> [waitress-1]
2025-07-15 10:36:31  EarlyBird.api.talk2ai.apis 132: INFO  Request title: 船舶结构完整性先进测试方法研究, level: 本科, lang: 中文 [waitress-1]
2025-07-15 10:36:31  apis.py 132: INFO  Request title: 船舶结构完整性先进测试方法研究, level: 本科, lang: 中文 [waitress-1]
2025-07-15 10:36:31  EarlyBird.api.talk2ai.apis 133: INFO  Outline type: <class 'dict'> [waitress-1]
2025-07-15 10:36:31  apis.py 133: INFO  Outline type: <class 'dict'> [waitress-1]
2025-07-15 10:36:31  EarlyBird.api.talk2ai.apis 154: INFO  Outline keys: dict_keys(['subtitle', 'title']) [waitress-1]
2025-07-15 10:36:31  apis.py 154: INFO  Outline keys: dict_keys(['subtitle', 'title']) [waitress-1]
2025-07-15 10:36:31  EarlyBird.api.talk2ai.apis 170: INFO  VIP状态来源 - g.isVip: False, session有激活码: False [waitress-1]
2025-07-15 10:36:31  apis.py 170: INFO  VIP状态来源 - g.isVip: False, session有激活码: False [waitress-1]
2025-07-15 10:36:31  EarlyBird.api.talk2ai.apis 184: INFO  用户对象VIP状态检查: False [waitress-1]
2025-07-15 10:36:31  apis.py 184: INFO  用户对象VIP状态检查: False [waitress-1]
2025-07-15 10:36:31  EarlyBird.api.talk2ai.apis 188: INFO  最终设置的用户状态 - User ID: 13, VIP: False [waitress-1]
2025-07-15 10:36:31  apis.py 188: INFO  最终设置的用户状态 - User ID: 13, VIP: False [waitress-1]
2025-07-15 10:36:31  EarlyBird.api.talk2ai.apis 195: INFO  Processing select4Content for user 13, title: 船舶结构完整性先进测试方法研究 [waitress-1]
2025-07-15 10:36:31  apis.py 195: INFO  Processing select4Content for user 13, title: 船舶结构完整性先进测试方法研究 [waitress-1]
2025-07-15 10:36:31  EarlyBird.api.talk2ai.service 523: INFO  用户 13 已有 0 篇论文 [waitress-1]
2025-07-15 10:36:31  service.py 523: INFO  用户 13 已有 0 篇论文 [waitress-1]
2025-07-15 10:36:31  EarlyBird.api.talk2ai.service 524: INFO  用户 13 的VIP状态: False [waitress-1]
2025-07-15 10:36:31  service.py 524: INFO  用户 13 的VIP状态: False [waitress-1]
2025-07-15 10:36:31  EarlyBird.api.talk2ai.service 529: INFO  用户 13 在g对象中的VIP状态: False [waitress-1]
2025-07-15 10:36:31  service.py 529: INFO  用户 13 在g对象中的VIP状态: False [waitress-1]
2025-07-15 10:36:31  EarlyBird.api.talk2ai.service 546: INFO  用户对象 13 的VIP状态: False [waitress-1]
2025-07-15 10:36:31  service.py 546: INFO  用户对象 13 的VIP状态: False [waitress-1]
2025-07-15 10:36:31  EarlyBird.api.talk2ai.service 552: INFO  最终判断用户 13 的VIP状态: False [waitress-1]
2025-07-15 10:36:31  service.py 552: INFO  最终判断用户 13 的VIP状态: False [waitress-1]
2025-07-15 10:36:31  EarlyBird.api.talk2ai.service 578: INFO  接收到的提纲数据类型: <class 'dict'> [waitress-1]
2025-07-15 10:36:31  service.py 578: INFO  接收到的提纲数据类型: <class 'dict'> [waitress-1]
2025-07-15 10:36:31  EarlyBird.api.talk2ai.service 580: INFO  提纲数据键: dict_keys(['subtitle', 'title']) [waitress-1]
2025-07-15 10:36:31  service.py 580: INFO  提纲数据键: dict_keys(['subtitle', 'title']) [waitress-1]
2025-07-15 10:36:31  EarlyBird.api.talk2ai.service 581: INFO  提纲数据内容: {"subtitle": [{"subtitle": [{"subtitle": [], "title": "研究背景与问题提出", "id": "wtd1eda2bzl6c2os18hzq8"}, {"subtitle": [], "title": "国内外船舶结构完整性测试方法的数据现状", "id": "n1rze1kjhbc3oet90fep"}, {"subtitle": [], "ti... [waitress-1]
2025-07-15 10:36:31  service.py 581: INFO  提纲数据内容: {"subtitle": [{"subtitle": [{"subtitle": [], "title": "研究背景与问题提出", "id": "wtd1eda2bzl6c2os18hzq8"}, {"subtitle": [], "title": "国内外船舶结构完整性测试方法的数据现状", "id": "n1rze1kjhbc3oet90fep"}, {"subtitle": [], "ti... [waitress-1]
2025-07-15 10:36:31  EarlyBird.api.talk2ai.utils 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-1]
2025-07-15 10:36:31  utils.py 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-1]
2025-07-15 10:36:31  EarlyBird.api.talk2ai.utils 55: INFO  提纲数据键: dict_keys(['subtitle', 'title']) [waitress-1]
2025-07-15 10:36:31  utils.py 55: INFO  提纲数据键: dict_keys(['subtitle', 'title']) [waitress-1]
2025-07-15 10:36:31  EarlyBird.api.talk2ai.utils 161: INFO  提纲处理完成，根节点ID: AuzkidondS7xotd5Jv4hiq [waitress-1]
2025-07-15 10:36:31  utils.py 161: INFO  提纲处理完成，根节点ID: AuzkidondS7xotd5Jv4hiq [waitress-1]
2025-07-15 10:36:31  EarlyBird.api.talk2ai.service 677: INFO  提纲处理完成，ID: AuzkidondS7xotd5Jv4hiq [waitress-1]
2025-07-15 10:36:31  service.py 677: INFO  提纲处理完成，ID: AuzkidondS7xotd5Jv4hiq [waitress-1]
2025-07-15 10:36:31  EarlyBird.api.talk2ai.service 687: INFO  成功解析提纲，找到 25 个段落标题 [waitress-1]
2025-07-15 10:36:31  service.py 687: INFO  成功解析提纲，找到 25 个段落标题 [waitress-1]
2025-07-15 10:36:31  EarlyBird.api.talk2ai.service 703: INFO  已创建论文记录，ID: 30 [waitress-1]
2025-07-15 10:36:31  service.py 703: INFO  已创建论文记录，ID: 30 [waitress-1]
2025-07-15 10:36:31  EarlyBird.api.talk2ai.service 717: INFO  已保存 25 个段落记录 [waitress-1]
2025-07-15 10:36:31  service.py 717: INFO  已保存 25 个段落记录 [waitress-1]
2025-07-15 10:36:31  EarlyBird.api.talk2ai.apis 201: INFO  select4Content success for user 13, thesis ID: 30 [waitress-1]
2025-07-15 10:36:31  apis.py 201: INFO  select4Content success for user 13, thesis ID: 30 [waitress-1]
2025-07-15 10:36:32  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getList [waitress-0]
2025-07-15 10:36:32  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getList [waitress-0]
2025-07-15 10:36:32  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-0]
2025-07-15 10:36:32  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-0]
2025-07-15 10:36:32  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-0]
2025-07-15 10:36:32  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-0]
2025-07-15 10:36:32  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-15 10:36:32  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-15 10:36:32  EarlyBird.api.thesis.apis 92: INFO  用户 13 请求获取论文列表 [waitress-0]
2025-07-15 10:36:32  apis.py 92: INFO  用户 13 请求获取论文列表 [waitress-0]
2025-07-15 10:36:32  EarlyBird.api.thesis.service 556: INFO  查询论文列表，用户ID: 13 [waitress-0]
2025-07-15 10:36:32  service.py 556: INFO  查询论文列表，用户ID: 13 [waitress-0]
2025-07-15 10:36:32  EarlyBird.api.thesis.service 562: INFO  查询到 1 篇论文 [waitress-0]
2025-07-15 10:36:32  service.py 562: INFO  查询到 1 篇论文 [waitress-0]
2025-07-15 10:36:32  EarlyBird.api.thesis.service 572: INFO  论文ID: 30, 标题: 船舶结构完整性先进测试方法研究, 用户ID: 13 [waitress-0]
2025-07-15 10:36:32  service.py 572: INFO  论文ID: 30, 标题: 船舶结构完整性先进测试方法研究, 用户ID: 13 [waitress-0]
2025-07-15 10:36:32  EarlyBird.api.thesis.service 574: INFO  返回论文列表，共 1 篇 [waitress-0]
2025-07-15 10:36:32  service.py 574: INFO  返回论文列表，共 1 篇 [waitress-0]
2025-07-15 10:36:32  EarlyBird.api.thesis.apis 95: INFO  用户 13 获取论文列表成功，共 1 篇 [waitress-0]
2025-07-15 10:36:32  apis.py 95: INFO  用户 13 获取论文列表成功，共 1 篇 [waitress-0]
2025-07-15 10:36:32  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getDetail [waitress-2]
2025-07-15 10:36:32  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getDetail [waitress-2]
2025-07-15 10:36:32  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-2]
2025-07-15 10:36:32  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-2]
2025-07-15 10:36:32  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-2]
2025-07-15 10:36:32  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-2]
2025-07-15 10:36:32  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-15 10:36:32  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-15 10:36:32  EarlyBird.api.talk2ai.utils 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-2]
2025-07-15 10:36:32  utils.py 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-2]
2025-07-15 10:36:32  EarlyBird.api.talk2ai.utils 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-2]
2025-07-15 10:36:32  utils.py 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-2]
2025-07-15 10:36:32  EarlyBird.api.talk2ai.utils 161: INFO  提纲处理完成，根节点ID: AuzkidondS7xotd5Jv4hiq [waitress-2]
2025-07-15 10:36:32  utils.py 161: INFO  提纲处理完成，根节点ID: AuzkidondS7xotd5Jv4hiq [waitress-2]
2025-07-15 10:36:32  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getOutline [waitress-3]
2025-07-15 10:36:32  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getOutline [waitress-3]
2025-07-15 10:36:32  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-3]
2025-07-15 10:36:32  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-3]
2025-07-15 10:36:32  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-3]
2025-07-15 10:36:32  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-3]
2025-07-15 10:36:32  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-15 10:36:32  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-15 10:36:32  EarlyBird.api.talk2ai.utils 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-3]
2025-07-15 10:36:32  utils.py 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-3]
2025-07-15 10:36:32  EarlyBird.api.talk2ai.utils 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-3]
2025-07-15 10:36:32  utils.py 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-3]
2025-07-15 10:36:32  EarlyBird.api.talk2ai.utils 161: INFO  提纲处理完成，根节点ID: AuzkidondS7xotd5Jv4hiq [waitress-3]
2025-07-15 10:36:32  utils.py 161: INFO  提纲处理完成，根节点ID: AuzkidondS7xotd5Jv4hiq [waitress-3]
2025-07-15 10:36:34  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-1]
2025-07-15 10:36:34  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-1]
2025-07-15 10:36:34  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-1]
2025-07-15 10:36:34  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-1]
2025-07-15 10:36:34  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-1]
2025-07-15 10:36:34  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-1]
2025-07-15 10:36:34  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-15 10:36:34  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-15 10:36:34  EarlyBird.api.thesis.apis 145: INFO  用户 13 请求下载论文 30 [waitress-1]
2025-07-15 10:36:34  apis.py 145: INFO  用户 13 请求下载论文 30 [waitress-1]
2025-07-15 10:36:34  EarlyBird.api.thesis.service 296: INFO  用户 13 请求导出论文 30 [waitress-1]
2025-07-15 10:36:34  service.py 296: INFO  用户 13 请求导出论文 30 [waitress-1]
2025-07-15 10:36:34  EarlyBird.api.thesis.service 304: INFO  用户 13 VIP状态: False [waitress-1]
2025-07-15 10:36:34  service.py 304: INFO  用户 13 VIP状态: False [waitress-1]
2025-07-15 10:36:34  EarlyBird.api.thesis.service 309: INFO  论文下载收费功能是否启用: True [waitress-1]
2025-07-15 10:36:34  service.py 309: INFO  论文下载收费功能是否启用: True [waitress-1]
2025-07-15 10:36:34  EarlyBird.api.thesis.service 323: INFO  VIP用户是否免费下载: False [waitress-1]
2025-07-15 10:36:34  service.py 323: INFO  VIP用户是否免费下载: False [waitress-1]
2025-07-15 10:36:34  EarlyBird.api.thesis.service 340: INFO  首次下载是否免费: False [waitress-1]
2025-07-15 10:36:34  service.py 340: INFO  首次下载是否免费: False [waitress-1]
2025-07-15 10:36:34  EarlyBird.api.thesis.service 350: INFO  用户 13 是否有真实支付的下载记录: True [waitress-1]
2025-07-15 10:36:34  service.py 350: INFO  用户 13 是否有真实支付的下载记录: True [waitress-1]
2025-07-15 10:36:34  EarlyBird.api.thesis.service 434: INFO  用户 13 需要支付才能下载论文 30，价格: 10.0 [waitress-1]
2025-07-15 10:36:34  service.py 434: INFO  用户 13 需要支付才能下载论文 30，价格: 10.0 [waitress-1]
2025-07-15 10:36:34  EarlyBird.api.thesis.apis 150: INFO  用户 13 需要支付才能下载论文 30，价格: 10.0 [waitress-1]
2025-07-15 10:36:34  apis.py 150: INFO  用户 13 需要支付才能下载论文 30，价格: 10.0 [waitress-1]
2025-07-15 10:36:36  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-0]
2025-07-15 10:36:36  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-0]
2025-07-15 10:36:36  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-0]
2025-07-15 10:36:36  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-0]
2025-07-15 10:36:36  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-0]
2025-07-15 10:36:36  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-0]
2025-07-15 10:36:36  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-15 10:36:36  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-15 10:36:36  EarlyBird.api.thesis.apis 145: INFO  用户 13 请求下载论文 30 [waitress-0]
2025-07-15 10:36:36  apis.py 145: INFO  用户 13 请求下载论文 30 [waitress-0]
2025-07-15 10:36:36  EarlyBird.api.thesis.service 296: INFO  用户 13 请求导出论文 30 [waitress-0]
2025-07-15 10:36:36  service.py 296: INFO  用户 13 请求导出论文 30 [waitress-0]
2025-07-15 10:36:36  EarlyBird.api.thesis.service 304: INFO  用户 13 VIP状态: False [waitress-0]
2025-07-15 10:36:36  service.py 304: INFO  用户 13 VIP状态: False [waitress-0]
2025-07-15 10:36:36  EarlyBird.api.thesis.service 309: INFO  论文下载收费功能是否启用: True [waitress-0]
2025-07-15 10:36:36  service.py 309: INFO  论文下载收费功能是否启用: True [waitress-0]
2025-07-15 10:36:36  EarlyBird.api.thesis.service 323: INFO  VIP用户是否免费下载: False [waitress-0]
2025-07-15 10:36:36  service.py 323: INFO  VIP用户是否免费下载: False [waitress-0]
2025-07-15 10:36:36  EarlyBird.api.thesis.service 340: INFO  首次下载是否免费: False [waitress-0]
2025-07-15 10:36:36  service.py 340: INFO  首次下载是否免费: False [waitress-0]
2025-07-15 10:36:36  EarlyBird.api.thesis.service 350: INFO  用户 13 是否有真实支付的下载记录: True [waitress-0]
2025-07-15 10:36:36  service.py 350: INFO  用户 13 是否有真实支付的下载记录: True [waitress-0]
2025-07-15 10:36:36  EarlyBird.api.thesis.service 434: INFO  用户 13 需要支付才能下载论文 30，价格: 10.0 [waitress-0]
2025-07-15 10:36:36  service.py 434: INFO  用户 13 需要支付才能下载论文 30，价格: 10.0 [waitress-0]
2025-07-15 10:36:36  EarlyBird.api.thesis.apis 150: INFO  用户 13 需要支付才能下载论文 30，价格: 10.0 [waitress-0]
2025-07-15 10:36:36  apis.py 150: INFO  用户 13 需要支付才能下载论文 30，价格: 10.0 [waitress-0]
2025-07-15 10:36:37  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-2]
2025-07-15 10:36:37  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-2]
2025-07-15 10:36:37  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-2]
2025-07-15 10:36:37  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-2]
2025-07-15 10:36:37  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-2]
2025-07-15 10:36:37  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-2]
2025-07-15 10:36:37  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-15 10:36:37  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-15 10:36:37  EarlyBird.api.thesis.apis 145: INFO  用户 13 请求下载论文 30 [waitress-2]
2025-07-15 10:36:37  apis.py 145: INFO  用户 13 请求下载论文 30 [waitress-2]
2025-07-15 10:36:37  EarlyBird.api.thesis.service 296: INFO  用户 13 请求导出论文 30 [waitress-2]
2025-07-15 10:36:37  service.py 296: INFO  用户 13 请求导出论文 30 [waitress-2]
2025-07-15 10:36:37  EarlyBird.api.thesis.service 304: INFO  用户 13 VIP状态: False [waitress-2]
2025-07-15 10:36:37  service.py 304: INFO  用户 13 VIP状态: False [waitress-2]
2025-07-15 10:36:37  EarlyBird.api.thesis.service 309: INFO  论文下载收费功能是否启用: True [waitress-2]
2025-07-15 10:36:37  service.py 309: INFO  论文下载收费功能是否启用: True [waitress-2]
2025-07-15 10:36:37  EarlyBird.api.thesis.service 323: INFO  VIP用户是否免费下载: False [waitress-2]
2025-07-15 10:36:37  service.py 323: INFO  VIP用户是否免费下载: False [waitress-2]
2025-07-15 10:36:37  EarlyBird.api.thesis.service 340: INFO  首次下载是否免费: False [waitress-2]
2025-07-15 10:36:37  service.py 340: INFO  首次下载是否免费: False [waitress-2]
2025-07-15 10:36:37  EarlyBird.api.thesis.service 350: INFO  用户 13 是否有真实支付的下载记录: True [waitress-2]
2025-07-15 10:36:37  service.py 350: INFO  用户 13 是否有真实支付的下载记录: True [waitress-2]
2025-07-15 10:36:37  EarlyBird.api.thesis.service 434: INFO  用户 13 需要支付才能下载论文 30，价格: 10.0 [waitress-2]
2025-07-15 10:36:37  service.py 434: INFO  用户 13 需要支付才能下载论文 30，价格: 10.0 [waitress-2]
2025-07-15 10:36:37  EarlyBird.api.thesis.apis 150: INFO  用户 13 需要支付才能下载论文 30，价格: 10.0 [waitress-2]
2025-07-15 10:36:37  apis.py 150: INFO  用户 13 需要支付才能下载论文 30，价格: 10.0 [waitress-2]
2025-07-15 10:36:38  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-3]
2025-07-15 10:36:38  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-3]
2025-07-15 10:36:38  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-3]
2025-07-15 10:36:38  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-3]
2025-07-15 10:36:38  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-3]
2025-07-15 10:36:38  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-3]
2025-07-15 10:36:38  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-15 10:36:38  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-15 10:36:38  EarlyBird.api.thesis.apis 145: INFO  用户 13 请求下载论文 30 [waitress-3]
2025-07-15 10:36:38  apis.py 145: INFO  用户 13 请求下载论文 30 [waitress-3]
2025-07-15 10:36:38  EarlyBird.api.thesis.service 296: INFO  用户 13 请求导出论文 30 [waitress-3]
2025-07-15 10:36:38  service.py 296: INFO  用户 13 请求导出论文 30 [waitress-3]
2025-07-15 10:36:38  EarlyBird.api.thesis.service 304: INFO  用户 13 VIP状态: False [waitress-3]
2025-07-15 10:36:38  service.py 304: INFO  用户 13 VIP状态: False [waitress-3]
2025-07-15 10:36:38  EarlyBird.api.thesis.service 309: INFO  论文下载收费功能是否启用: True [waitress-3]
2025-07-15 10:36:38  service.py 309: INFO  论文下载收费功能是否启用: True [waitress-3]
2025-07-15 10:36:38  EarlyBird.api.thesis.service 323: INFO  VIP用户是否免费下载: False [waitress-3]
2025-07-15 10:36:38  service.py 323: INFO  VIP用户是否免费下载: False [waitress-3]
2025-07-15 10:36:38  EarlyBird.api.thesis.service 340: INFO  首次下载是否免费: False [waitress-3]
2025-07-15 10:36:38  service.py 340: INFO  首次下载是否免费: False [waitress-3]
2025-07-15 10:36:38  EarlyBird.api.thesis.service 350: INFO  用户 13 是否有真实支付的下载记录: True [waitress-3]
2025-07-15 10:36:38  service.py 350: INFO  用户 13 是否有真实支付的下载记录: True [waitress-3]
2025-07-15 10:36:38  EarlyBird.api.thesis.service 434: INFO  用户 13 需要支付才能下载论文 30，价格: 10.0 [waitress-3]
2025-07-15 10:36:38  service.py 434: INFO  用户 13 需要支付才能下载论文 30，价格: 10.0 [waitress-3]
2025-07-15 10:36:38  EarlyBird.api.thesis.apis 150: INFO  用户 13 需要支付才能下载论文 30，价格: 10.0 [waitress-3]
2025-07-15 10:36:38  apis.py 150: INFO  用户 13 需要支付才能下载论文 30，价格: 10.0 [waitress-3]
2025-07-15 10:36:38  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-1]
2025-07-15 10:36:38  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-1]
2025-07-15 10:36:38  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-1]
2025-07-15 10:36:38  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-1]
2025-07-15 10:36:38  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-1]
2025-07-15 10:36:38  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-1]
2025-07-15 10:36:38  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-15 10:36:38  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-15 10:36:38  EarlyBird.api.thesis.apis 145: INFO  用户 13 请求下载论文 30 [waitress-1]
2025-07-15 10:36:38  apis.py 145: INFO  用户 13 请求下载论文 30 [waitress-1]
2025-07-15 10:36:38  EarlyBird.api.thesis.service 296: INFO  用户 13 请求导出论文 30 [waitress-1]
2025-07-15 10:36:38  service.py 296: INFO  用户 13 请求导出论文 30 [waitress-1]
2025-07-15 10:36:38  EarlyBird.api.thesis.service 304: INFO  用户 13 VIP状态: False [waitress-1]
2025-07-15 10:36:38  service.py 304: INFO  用户 13 VIP状态: False [waitress-1]
2025-07-15 10:36:38  EarlyBird.api.thesis.service 309: INFO  论文下载收费功能是否启用: True [waitress-1]
2025-07-15 10:36:38  service.py 309: INFO  论文下载收费功能是否启用: True [waitress-1]
2025-07-15 10:36:38  EarlyBird.api.thesis.service 323: INFO  VIP用户是否免费下载: False [waitress-1]
2025-07-15 10:36:38  service.py 323: INFO  VIP用户是否免费下载: False [waitress-1]
2025-07-15 10:36:38  EarlyBird.api.thesis.service 340: INFO  首次下载是否免费: False [waitress-1]
2025-07-15 10:36:38  service.py 340: INFO  首次下载是否免费: False [waitress-1]
2025-07-15 10:36:38  EarlyBird.api.thesis.service 350: INFO  用户 13 是否有真实支付的下载记录: True [waitress-1]
2025-07-15 10:36:38  service.py 350: INFO  用户 13 是否有真实支付的下载记录: True [waitress-1]
2025-07-15 10:36:38  EarlyBird.api.thesis.service 434: INFO  用户 13 需要支付才能下载论文 30，价格: 10.0 [waitress-1]
2025-07-15 10:36:38  service.py 434: INFO  用户 13 需要支付才能下载论文 30，价格: 10.0 [waitress-1]
2025-07-15 10:36:38  EarlyBird.api.thesis.apis 150: INFO  用户 13 需要支付才能下载论文 30，价格: 10.0 [waitress-1]
2025-07-15 10:36:38  apis.py 150: INFO  用户 13 需要支付才能下载论文 30，价格: 10.0 [waitress-1]
2025-07-15 10:36:39  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-0]
2025-07-15 10:36:39  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-0]
2025-07-15 10:36:39  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-0]
2025-07-15 10:36:39  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-0]
2025-07-15 10:36:39  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-0]
2025-07-15 10:36:39  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-0]
2025-07-15 10:36:39  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-15 10:36:39  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-15 10:36:39  EarlyBird.api.thesis.apis 145: INFO  用户 13 请求下载论文 30 [waitress-0]
2025-07-15 10:36:39  apis.py 145: INFO  用户 13 请求下载论文 30 [waitress-0]
2025-07-15 10:36:39  EarlyBird.api.thesis.service 296: INFO  用户 13 请求导出论文 30 [waitress-0]
2025-07-15 10:36:39  service.py 296: INFO  用户 13 请求导出论文 30 [waitress-0]
2025-07-15 10:36:39  EarlyBird.api.thesis.service 304: INFO  用户 13 VIP状态: False [waitress-0]
2025-07-15 10:36:39  service.py 304: INFO  用户 13 VIP状态: False [waitress-0]
2025-07-15 10:36:39  EarlyBird.api.thesis.service 309: INFO  论文下载收费功能是否启用: True [waitress-0]
2025-07-15 10:36:39  service.py 309: INFO  论文下载收费功能是否启用: True [waitress-0]
2025-07-15 10:36:39  EarlyBird.api.thesis.service 323: INFO  VIP用户是否免费下载: False [waitress-0]
2025-07-15 10:36:39  service.py 323: INFO  VIP用户是否免费下载: False [waitress-0]
2025-07-15 10:36:39  EarlyBird.api.thesis.service 340: INFO  首次下载是否免费: False [waitress-0]
2025-07-15 10:36:39  service.py 340: INFO  首次下载是否免费: False [waitress-0]
2025-07-15 10:36:39  EarlyBird.api.thesis.service 350: INFO  用户 13 是否有真实支付的下载记录: True [waitress-0]
2025-07-15 10:36:39  service.py 350: INFO  用户 13 是否有真实支付的下载记录: True [waitress-0]
2025-07-15 10:36:39  EarlyBird.api.thesis.service 434: INFO  用户 13 需要支付才能下载论文 30，价格: 10.0 [waitress-0]
2025-07-15 10:36:39  service.py 434: INFO  用户 13 需要支付才能下载论文 30，价格: 10.0 [waitress-0]
2025-07-15 10:36:39  EarlyBird.api.thesis.apis 150: INFO  用户 13 需要支付才能下载论文 30，价格: 10.0 [waitress-0]
2025-07-15 10:36:39  apis.py 150: INFO  用户 13 需要支付才能下载论文 30，价格: 10.0 [waitress-0]
2025-07-15 10:36:40  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-2]
2025-07-15 10:36:40  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-2]
2025-07-15 10:36:40  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-2]
2025-07-15 10:36:40  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-2]
2025-07-15 10:36:40  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-2]
2025-07-15 10:36:40  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-2]
2025-07-15 10:36:40  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-15 10:36:40  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-15 10:36:40  EarlyBird.api.thesis.apis 145: INFO  用户 13 请求下载论文 30 [waitress-2]
2025-07-15 10:36:40  apis.py 145: INFO  用户 13 请求下载论文 30 [waitress-2]
2025-07-15 10:36:40  EarlyBird.api.thesis.service 296: INFO  用户 13 请求导出论文 30 [waitress-2]
2025-07-15 10:36:40  service.py 296: INFO  用户 13 请求导出论文 30 [waitress-2]
2025-07-15 10:36:40  EarlyBird.api.thesis.service 304: INFO  用户 13 VIP状态: False [waitress-2]
2025-07-15 10:36:40  service.py 304: INFO  用户 13 VIP状态: False [waitress-2]
2025-07-15 10:36:40  EarlyBird.api.thesis.service 309: INFO  论文下载收费功能是否启用: True [waitress-2]
2025-07-15 10:36:40  service.py 309: INFO  论文下载收费功能是否启用: True [waitress-2]
2025-07-15 10:36:40  EarlyBird.api.thesis.service 323: INFO  VIP用户是否免费下载: False [waitress-2]
2025-07-15 10:36:40  service.py 323: INFO  VIP用户是否免费下载: False [waitress-2]
2025-07-15 10:36:40  EarlyBird.api.thesis.service 340: INFO  首次下载是否免费: False [waitress-2]
2025-07-15 10:36:40  service.py 340: INFO  首次下载是否免费: False [waitress-2]
2025-07-15 10:36:40  EarlyBird.api.thesis.service 350: INFO  用户 13 是否有真实支付的下载记录: True [waitress-2]
2025-07-15 10:36:40  service.py 350: INFO  用户 13 是否有真实支付的下载记录: True [waitress-2]
2025-07-15 10:36:40  EarlyBird.api.thesis.service 434: INFO  用户 13 需要支付才能下载论文 30，价格: 10.0 [waitress-2]
2025-07-15 10:36:40  service.py 434: INFO  用户 13 需要支付才能下载论文 30，价格: 10.0 [waitress-2]
2025-07-15 10:36:40  EarlyBird.api.thesis.apis 150: INFO  用户 13 需要支付才能下载论文 30，价格: 10.0 [waitress-2]
2025-07-15 10:36:40  apis.py 150: INFO  用户 13 需要支付才能下载论文 30，价格: 10.0 [waitress-2]
2025-07-15 10:36:40  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-3]
2025-07-15 10:36:40  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-3]
2025-07-15 10:36:40  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-3]
2025-07-15 10:36:40  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-3]
2025-07-15 10:36:40  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-3]
2025-07-15 10:36:40  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-3]
2025-07-15 10:36:40  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-15 10:36:40  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-15 10:36:40  EarlyBird.api.thesis.apis 145: INFO  用户 13 请求下载论文 30 [waitress-3]
2025-07-15 10:36:40  apis.py 145: INFO  用户 13 请求下载论文 30 [waitress-3]
2025-07-15 10:36:40  EarlyBird.api.thesis.service 296: INFO  用户 13 请求导出论文 30 [waitress-3]
2025-07-15 10:36:40  service.py 296: INFO  用户 13 请求导出论文 30 [waitress-3]
2025-07-15 10:36:40  EarlyBird.api.thesis.service 304: INFO  用户 13 VIP状态: False [waitress-3]
2025-07-15 10:36:40  service.py 304: INFO  用户 13 VIP状态: False [waitress-3]
2025-07-15 10:36:40  EarlyBird.api.thesis.service 309: INFO  论文下载收费功能是否启用: True [waitress-3]
2025-07-15 10:36:40  service.py 309: INFO  论文下载收费功能是否启用: True [waitress-3]
2025-07-15 10:36:40  EarlyBird.api.thesis.service 323: INFO  VIP用户是否免费下载: False [waitress-3]
2025-07-15 10:36:40  service.py 323: INFO  VIP用户是否免费下载: False [waitress-3]
2025-07-15 10:36:40  EarlyBird.api.thesis.service 340: INFO  首次下载是否免费: False [waitress-3]
2025-07-15 10:36:40  service.py 340: INFO  首次下载是否免费: False [waitress-3]
2025-07-15 10:36:40  EarlyBird.api.thesis.service 350: INFO  用户 13 是否有真实支付的下载记录: True [waitress-3]
2025-07-15 10:36:40  service.py 350: INFO  用户 13 是否有真实支付的下载记录: True [waitress-3]
2025-07-15 10:36:41  EarlyBird.api.thesis.service 434: INFO  用户 13 需要支付才能下载论文 30，价格: 10.0 [waitress-3]
2025-07-15 10:36:41  service.py 434: INFO  用户 13 需要支付才能下载论文 30，价格: 10.0 [waitress-3]
2025-07-15 10:36:41  EarlyBird.api.thesis.apis 150: INFO  用户 13 需要支付才能下载论文 30，价格: 10.0 [waitress-3]
2025-07-15 10:36:41  apis.py 150: INFO  用户 13 需要支付才能下载论文 30，价格: 10.0 [waitress-3]
2025-07-15 10:42:31  init_flask.py 27: INFO  {'CpuCount': 6, 'Mem': '32 GB'} [MainThread]
2025-07-15 10:42:31  web_server.py 32: INFO  Static directory: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\ui [MainThread]
2025-07-15 10:42:31  web_server.py 33: INFO  Static directory exists: True [MainThread]
2025-07-15 10:42:31  web_server.py 37: INFO  设置工作目录: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird [MainThread]
2025-07-15 10:42:31  EarlyBird.config.config 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-15 10:42:31  config.py 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-15 10:42:32  config.py 94: INFO  Database URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-15 10:42:34  EarlyBird.api.admin.app 39: INFO  ✅ 后台管理系统API已注册 [MainThread]
2025-07-15 10:42:34  app.py 39: INFO  ✅ 后台管理系统API已注册 [MainThread]
2025-07-15 10:42:34  EarlyBird 40: INFO  确保数据目录存在: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\data [MainThread]
2025-07-15 10:42:34  server.py 40: INFO  确保数据目录存在: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\data [MainThread]
2025-07-15 10:42:34  EarlyBird 44: INFO  开始执行数据库迁移... [MainThread]
2025-07-15 10:42:34  server.py 44: INFO  开始执行数据库迁移... [MainThread]
2025-07-15 10:42:34  config.py 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-15 10:42:34  config.py 94: INFO  Database URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-15 10:42:34  EarlyBird 53: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-15 10:42:34  server.py 53: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-15 10:42:34  EarlyBird 97: WARNING  ChatLog表不存在，将在应用启动时自动创建 [MainThread]
2025-07-15 10:42:34  server.py 97: WARNING  ChatLog表不存在，将在应用启动时自动创建 [MainThread]
2025-07-15 10:42:34  add_uid_to_chatlog.py 20: INFO  开始执行ChatLog表迁移... [MainThread]
2025-07-15 10:42:34  add_uid_to_chatlog.py 24: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-15 10:42:34  add_uid_to_chatlog.py 38: WARNING  ChatLog表不存在，可能需要先创建表 [MainThread]
2025-07-15 10:42:34  task_api_server.py 31: INFO  TaskApiServer start [MainThread]
2025-07-15 10:42:34  task_paper_generator.py 38: INFO  论文生成任务处理线程初始化 [MainThread]
2025-07-15 10:42:34  task_paper_generator.py 72: INFO  论文生成任务处理线程启动 [Thread-2]
2025-07-15 10:42:34  task_api_server.py 47: INFO  TaskApiServer listen on 127.0.0.1:3302 [Thread-1]
2025-07-15 10:42:34  task_api_server.py 17: INFO  _ApiServerThread start [Thread-3]
2025-07-15 10:43:02  web_server.py 73: INFO  Serving request for path: paper/content [waitress-0]
2025-07-15 10:43:02  web_server.py 85: INFO  Serving index.html for path: paper/content [waitress-0]
2025-07-15 10:43:02  web_server.py 73: INFO  Serving request for path: js/app.70a2873c.js [waitress-1]
2025-07-15 10:43:02  web_server.py 73: INFO  Serving request for path: js/chunk-vendors.04aff60f.js [waitress-2]
2025-07-15 10:43:02  web_server.py 82: INFO  Serving static file: js/app.70a2873c.js [waitress-1]
2025-07-15 10:43:02  web_server.py 73: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-3]
2025-07-15 10:43:02  web_server.py 82: INFO  Serving static file: js/chunk-vendors.04aff60f.js [waitress-2]
2025-07-15 10:43:02  web_server.py 73: INFO  Serving request for path: css/app.1e68ddae.css [waitress-0]
2025-07-15 10:43:02  web_server.py 82: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-3]
2025-07-15 10:43:02  web_server.py 82: INFO  Serving static file: css/app.1e68ddae.css [waitress-0]
2025-07-15 10:43:02  web_server.py 73: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-3]
2025-07-15 10:43:02  web_server.py 85: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-3]
2025-07-15 10:43:02  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/user/info [waitress-1]
2025-07-15 10:43:02  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/user/info [waitress-1]
2025-07-15 10:43:02  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-1]
2025-07-15 10:43:02  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-1]
2025-07-15 10:43:02  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-1]
2025-07-15 10:43:02  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-1]
2025-07-15 10:43:02  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-15 10:43:02  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-15 10:43:02  web_server.py 73: INFO  Serving request for path: logo.png [waitress-0]
2025-07-15 10:43:02  web_server.py 82: INFO  Serving static file: logo.png [waitress-0]
2025-07-15 10:43:03  web_server.py 73: INFO  Serving request for path: js/897.25207915.js [waitress-3]
2025-07-15 10:43:03  web_server.py 73: INFO  Serving request for path: css/389.3a83adf7.css [waitress-0]
2025-07-15 10:43:03  web_server.py 73: INFO  Serving request for path: js/22.683bc166.js [waitress-2]
2025-07-15 10:43:03  web_server.py 82: INFO  Serving static file: js/897.25207915.js [waitress-3]
2025-07-15 10:43:03  web_server.py 73: INFO  Serving request for path: js/389.26da6a15.js [waitress-1]
2025-07-15 10:43:03  web_server.py 82: INFO  Serving static file: css/389.3a83adf7.css [waitress-0]
2025-07-15 10:43:03  web_server.py 82: INFO  Serving static file: js/22.683bc166.js [waitress-2]
2025-07-15 10:43:03  web_server.py 82: INFO  Serving static file: js/389.26da6a15.js [waitress-1]
2025-07-15 10:43:03  web_server.py 73: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-2]
2025-07-15 10:43:03  web_server.py 82: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-2]
2025-07-15 10:43:03  web_server.py 73: INFO  Serving request for path: fonts/element-icons.ff18efd1.woff [waitress-3]
2025-07-15 10:43:03  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getList [waitress-1]
2025-07-15 10:43:03  app.py 1454: ERROR  Exception on /api/home/<USER>
Traceback (most recent call last):
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1900, in _execute_context
    self.dialect.do_execute(
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
  File "C:\Python311\Lib\site-packages\pymysql\cursors.py", line 148, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\cursors.py", line 310, in _query
    conn.query(q)
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 548, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 775, in _read_query_result
    result.read()
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 1156, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 725, in _read_packet
    packet.raise_for_error()
  File "C:\Python311\Lib\site-packages\pymysql\protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "C:\Python311\Lib\site-packages\pymysql\err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'earlybird_paper_setting.category' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python311\Lib\site-packages\flask\app.py", line 2070, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask\app.py", line 1515, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask_cors\extension.py", line 165, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask\app.py", line 1513, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask\app.py", line 1499, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**req.view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask_pydantic\core.py", line 249, in wrapper
    res = func(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^
  File "d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\api\home\apis.py", line 18, in getSetting
    res: Result = HomeService().getSetting()
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\api\home\service.py", line 30, in getSetting
    "modelName": self.getModelName(),
                 ^^^^^^^^^^^^^^^^^^^
  File "d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\api\home\service.py", line 21, in getModelName
    setting: Setting = Setting.query.filter(Setting.settingKey == "modelName").first()
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\orm\query.py", line 2824, in first
    return self.limit(1)._iter().first()
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\orm\query.py", line 2916, in _iter
    result = self.session.execute(
             ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\orm\session.py", line 1714, in execute
    result = conn._execute_20(statement, params or {}, execution_options)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1705, in _execute_20
    return meth(self, args_10style, kwargs_10style, execution_options)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\sql\elements.py", line 334, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1572, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1943, in _execute_context
    self._handle_dbapi_exception(
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 2124, in _handle_dbapi_exception
    util.raise_(
  File "C:\Python311\Lib\site-packages\sqlalchemy\util\compat.py", line 211, in raise_
    raise exception
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1900, in _execute_context
    self.dialect.do_execute(
  File "C:\Python311\Lib\site-packages\sqlalchemy\engine\default.py", line 736, in do_execute
    cursor.execute(statement, parameters)
  File "C:\Python311\Lib\site-packages\pymysql\cursors.py", line 148, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\cursors.py", line 310, in _query
    conn.query(q)
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 548, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 775, in _read_query_result
    result.read()
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 1156, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\pymysql\connections.py", line 725, in _read_packet
    packet.raise_for_error()
  File "C:\Python311\Lib\site-packages\pymysql\protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "C:\Python311\Lib\site-packages\pymysql\err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
sqlalchemy.exc.OperationalError: (pymysql.err.OperationalError) (1054, "Unknown column 'earlybird_paper_setting.category' in 'field list'")
[SQL: SELECT earlybird_paper_setting.id AS earlybird_paper_setting_id, earlybird_paper_setting.create_time AS earlybird_paper_setting_create_time, earlybird_paper_setting.update_time AS earlybird_paper_setting_update_time, earlybird_paper_setting.is_deleted AS earlybird_paper_setting_is_deleted, earlybird_paper_setting.status AS earlybird_paper_setting_status, earlybird_paper_setting.category AS earlybird_paper_setting_category, earlybird_paper_setting.`key` AS earlybird_paper_setting_key, earlybird_paper_setting.value AS earlybird_paper_setting_value, earlybird_paper_setting.description AS earlybird_paper_setting_description, earlybird_paper_setting.`settingKey` AS `earlybird_paper_setting_settingKey`, earlybird_paper_setting.`settingValue` AS `earlybird_paper_setting_settingValue` 
FROM earlybird_paper_setting 
WHERE earlybird_paper_setting.`settingKey` = %(settingKey_1)s 
 LIMIT %(param_1)s]
[parameters: {'settingKey_1': 'modelName', 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/14/e3q8)
2025-07-15 10:43:03  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getList [waitress-1]
2025-07-15 10:43:03  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-1]
2025-07-15 10:43:03  web_server.py 82: INFO  Serving static file: fonts/element-icons.ff18efd1.woff [waitress-3]
2025-07-15 10:43:03  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-1]
2025-07-15 10:43:03  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-1]
2025-07-15 10:43:03  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-1]
2025-07-15 10:43:03  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-15 10:43:03  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-15 10:43:03  EarlyBird.api.thesis.apis 92: INFO  用户 13 请求获取论文列表 [waitress-1]
2025-07-15 10:43:03  apis.py 92: INFO  用户 13 请求获取论文列表 [waitress-1]
2025-07-15 10:43:03  EarlyBird.api.thesis.service 566: INFO  查询论文列表，用户ID: 13 [waitress-1]
2025-07-15 10:43:03  service.py 566: INFO  查询论文列表，用户ID: 13 [waitress-1]
2025-07-15 10:43:03  EarlyBird.api.thesis.service 572: INFO  查询到 1 篇论文 [waitress-1]
2025-07-15 10:43:03  service.py 572: INFO  查询到 1 篇论文 [waitress-1]
2025-07-15 10:43:03  EarlyBird.api.thesis.service 582: INFO  论文ID: 30, 标题: 船舶结构完整性先进测试方法研究, 用户ID: 13 [waitress-1]
2025-07-15 10:43:03  service.py 582: INFO  论文ID: 30, 标题: 船舶结构完整性先进测试方法研究, 用户ID: 13 [waitress-1]
2025-07-15 10:43:03  EarlyBird.api.thesis.service 584: INFO  返回论文列表，共 1 篇 [waitress-1]
2025-07-15 10:43:03  service.py 584: INFO  返回论文列表，共 1 篇 [waitress-1]
2025-07-15 10:43:03  EarlyBird.api.thesis.apis 95: INFO  用户 13 获取论文列表成功，共 1 篇 [waitress-1]
2025-07-15 10:43:03  apis.py 95: INFO  用户 13 获取论文列表成功，共 1 篇 [waitress-1]
2025-07-15 10:43:03  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getDetail [waitress-2]
2025-07-15 10:43:03  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getDetail [waitress-2]
2025-07-15 10:43:03  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-2]
2025-07-15 10:43:03  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-2]
2025-07-15 10:43:03  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-2]
2025-07-15 10:43:03  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-2]
2025-07-15 10:43:03  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-15 10:43:03  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-15 10:43:03  EarlyBird.api.talk2ai.utils 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-2]
2025-07-15 10:43:03  utils.py 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-2]
2025-07-15 10:43:03  EarlyBird.api.talk2ai.utils 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-2]
2025-07-15 10:43:03  utils.py 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-2]
2025-07-15 10:43:03  EarlyBird.api.talk2ai.utils 161: INFO  提纲处理完成，根节点ID: AuzkidondS7xotd5Jv4hiq [waitress-2]
2025-07-15 10:43:03  utils.py 161: INFO  提纲处理完成，根节点ID: AuzkidondS7xotd5Jv4hiq [waitress-2]
2025-07-15 10:43:03  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getOutline [waitress-0]
2025-07-15 10:43:03  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getOutline [waitress-0]
2025-07-15 10:43:03  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-0]
2025-07-15 10:43:03  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-0]
2025-07-15 10:43:03  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-0]
2025-07-15 10:43:03  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-0]
2025-07-15 10:43:03  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-15 10:43:03  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-15 10:43:03  EarlyBird.api.talk2ai.utils 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-0]
2025-07-15 10:43:03  utils.py 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-0]
2025-07-15 10:43:03  EarlyBird.api.talk2ai.utils 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-0]
2025-07-15 10:43:03  utils.py 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-0]
2025-07-15 10:43:03  EarlyBird.api.talk2ai.utils 161: INFO  提纲处理完成，根节点ID: AuzkidondS7xotd5Jv4hiq [waitress-0]
2025-07-15 10:43:03  utils.py 161: INFO  提纲处理完成，根节点ID: AuzkidondS7xotd5Jv4hiq [waitress-0]
2025-07-15 10:43:05  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-3]
2025-07-15 10:43:05  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-3]
2025-07-15 10:43:05  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-3]
2025-07-15 10:43:05  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-3]
2025-07-15 10:43:05  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-3]
2025-07-15 10:43:05  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-3]
2025-07-15 10:43:05  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-15 10:43:05  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-15 10:43:05  EarlyBird.api.thesis.apis 145: INFO  用户 13 请求下载论文 30 [waitress-3]
2025-07-15 10:43:05  apis.py 145: INFO  用户 13 请求下载论文 30 [waitress-3]
2025-07-15 10:43:05  EarlyBird.api.thesis.service 296: INFO  用户 13 请求导出论文 30 [waitress-3]
2025-07-15 10:43:05  service.py 296: INFO  用户 13 请求导出论文 30 [waitress-3]
2025-07-15 10:43:05  EarlyBird.api.thesis.service 304: INFO  用户 13 VIP状态: False [waitress-3]
2025-07-15 10:43:05  service.py 304: INFO  用户 13 VIP状态: False [waitress-3]
2025-07-15 10:43:05  EarlyBird.api.thesis.service 309: INFO  论文下载收费功能是否启用: True [waitress-3]
2025-07-15 10:43:05  service.py 309: INFO  论文下载收费功能是否启用: True [waitress-3]
2025-07-15 10:43:05  EarlyBird.api.thesis.service 323: INFO  VIP用户是否免费下载: False [waitress-3]
2025-07-15 10:43:05  service.py 323: INFO  VIP用户是否免费下载: False [waitress-3]
2025-07-15 10:43:05  EarlyBird.api.thesis.service 340: INFO  首次下载是否免费: False [waitress-3]
2025-07-15 10:43:05  service.py 340: INFO  首次下载是否免费: False [waitress-3]
2025-07-15 10:43:05  EarlyBird.api.thesis.service 350: INFO  用户 13 是否有真实支付的下载记录: True [waitress-3]
2025-07-15 10:43:05  service.py 350: INFO  用户 13 是否有真实支付的下载记录: True [waitress-3]
2025-07-15 10:43:05  EarlyBird.api.thesis.service 436: INFO  从配置中读取到论文下载价格: 10.0 [waitress-3]
2025-07-15 10:43:05  service.py 436: INFO  从配置中读取到论文下载价格: 10.0 [waitress-3]
2025-07-15 10:43:05  EarlyBird.api.thesis.service 444: INFO  用户 13 需要支付才能下载论文 30，价格: 10.0 [waitress-3]
2025-07-15 10:43:05  service.py 444: INFO  用户 13 需要支付才能下载论文 30，价格: 10.0 [waitress-3]
2025-07-15 10:43:05  EarlyBird.api.thesis.apis 150: INFO  用户 13 需要支付才能下载论文 30，价格: 10.0 [waitress-3]
2025-07-15 10:43:05  apis.py 150: INFO  用户 13 需要支付才能下载论文 30，价格: 10.0 [waitress-3]
2025-07-15 10:43:05  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/payDownload [waitress-1]
2025-07-15 10:43:05  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/payDownload [waitress-1]
2025-07-15 10:43:05  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-1]
2025-07-15 10:43:05  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-1]
2025-07-15 10:43:05  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-1]
2025-07-15 10:43:05  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-1]
2025-07-15 10:43:05  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-15 10:43:05  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-15 10:43:06  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-2]
2025-07-15 10:43:06  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-2]
2025-07-15 10:43:06  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-2]
2025-07-15 10:43:06  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-2]
2025-07-15 10:43:06  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-2]
2025-07-15 10:43:06  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-2]
2025-07-15 10:43:06  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-15 10:43:06  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-15 10:43:06  EarlyBird.api.thesis.apis 145: INFO  用户 13 请求下载论文 30 [waitress-2]
2025-07-15 10:43:06  apis.py 145: INFO  用户 13 请求下载论文 30 [waitress-2]
2025-07-15 10:43:06  EarlyBird.api.thesis.service 296: INFO  用户 13 请求导出论文 30 [waitress-2]
2025-07-15 10:43:06  service.py 296: INFO  用户 13 请求导出论文 30 [waitress-2]
2025-07-15 10:43:06  EarlyBird.api.thesis.service 304: INFO  用户 13 VIP状态: False [waitress-2]
2025-07-15 10:43:06  service.py 304: INFO  用户 13 VIP状态: False [waitress-2]
2025-07-15 10:43:06  EarlyBird.api.thesis.service 309: INFO  论文下载收费功能是否启用: True [waitress-2]
2025-07-15 10:43:06  service.py 309: INFO  论文下载收费功能是否启用: True [waitress-2]
2025-07-15 10:43:06  EarlyBird.api.thesis.service 323: INFO  VIP用户是否免费下载: False [waitress-2]
2025-07-15 10:43:06  service.py 323: INFO  VIP用户是否免费下载: False [waitress-2]
2025-07-15 10:43:06  EarlyBird.api.thesis.service 340: INFO  首次下载是否免费: False [waitress-2]
2025-07-15 10:43:06  service.py 340: INFO  首次下载是否免费: False [waitress-2]
2025-07-15 10:43:06  EarlyBird.api.thesis.service 350: INFO  用户 13 是否有真实支付的下载记录: True [waitress-2]
2025-07-15 10:43:06  service.py 350: INFO  用户 13 是否有真实支付的下载记录: True [waitress-2]
2025-07-15 10:43:06  EarlyBird.api.thesis.service 370: INFO  用户 13 已有已支付的下载记录，可直接下载论文 30 [waitress-2]
2025-07-15 10:43:06  service.py 370: INFO  用户 13 已有已支付的下载记录，可直接下载论文 30 [waitress-2]
2025-07-15 10:43:06  EarlyBird.api.thesis.service 755: INFO  add Para:船舶结构完整性先进测试方法研究 [waitress-2]
2025-07-15 10:43:06  service.py 755: INFO  add Para:船舶结构完整性先进测试方法研究 [waitress-2]
2025-07-15 10:43:06  EarlyBird.api.thesis.service 755: INFO  add Para:绪论 [waitress-2]
2025-07-15 10:43:06  service.py 755: INFO  add Para:绪论 [waitress-2]
2025-07-15 10:43:06  EarlyBird.api.thesis.service 755: INFO  add Para:研究背景与问题提出 [waitress-2]
2025-07-15 10:43:06  service.py 755: INFO  add Para:研究背景与问题提出 [waitress-2]
2025-07-15 10:43:06  EarlyBird.api.thesis.service 755: INFO  add Para:国内外船舶结构完整性测试方法的数据现状 [waitress-2]
2025-07-15 10:43:06  service.py 755: INFO  add Para:国内外船舶结构完整性测试方法的数据现状 [waitress-2]
2025-07-15 10:43:06  EarlyBird.api.thesis.service 755: INFO  add Para:本研究的意义及数据需求分析 [waitress-2]
2025-07-15 10:43:06  service.py 755: INFO  add Para:本研究的意义及数据需求分析 [waitress-2]
2025-07-15 10:43:06  EarlyBird.api.thesis.service 755: INFO  add Para:理论基础与数据模型构建 [waitress-2]
2025-07-15 10:43:06  service.py 755: INFO  add Para:理论基础与数据模型构建 [waitress-2]
2025-07-15 10:43:06  EarlyBird.api.thesis.service 755: INFO  add Para:船舶结构完整性的基本理论框架及其量化指标 [waitress-2]
2025-07-15 10:43:06  service.py 755: INFO  add Para:船舶结构完整性的基本理论框架及其量化指标 [waitress-2]
2025-07-15 10:43:06  EarlyBird.api.thesis.service 755: INFO  add Para:基于大数据的船舶结构完整性预测模型设计 [waitress-2]
2025-07-15 10:43:06  service.py 755: INFO  add Para:基于大数据的船舶结构完整性预测模型设计 [waitress-2]
2025-07-15 10:43:06  EarlyBird.api.thesis.service 755: INFO  add Para:用于评估船舶结构安全性的新型数据分析方法介绍 [waitress-2]
2025-07-15 10:43:06  service.py 755: INFO  add Para:用于评估船舶结构安全性的新型数据分析方法介绍 [waitress-2]
2025-07-15 10:43:06  EarlyBird.api.thesis.service 755: INFO  add Para:实证研究：数据收集与处理 [waitress-2]
2025-07-15 10:43:06  service.py 755: INFO  add Para:实证研究：数据收集与处理 [waitress-2]
2025-07-15 10:43:06  EarlyBird.api.thesis.service 755: INFO  add Para:实验数据采集方案的设计及实施细节 [waitress-2]
2025-07-15 10:43:06  service.py 755: INFO  add Para:实验数据采集方案的设计及实施细节 [waitress-2]
2025-07-15 10:43:06  EarlyBird.api.thesis.service 755: INFO  add Para:采用不同技术手段获取船舶结构状态信息的方法对比 [waitress-2]
2025-07-15 10:43:06  service.py 755: INFO  add Para:采用不同技术手段获取船舶结构状态信息的方法对比 [waitress-2]
2025-07-15 10:43:06  EarlyBird.api.thesis.service 755: INFO  add Para:数据清洗、预处理过程中的挑战与解决方案 [waitress-2]
2025-07-15 10:43:06  service.py 755: INFO  add Para:数据清洗、预处理过程中的挑战与解决方案 [waitress-2]
2025-07-15 10:43:06  EarlyBird.api.thesis.service 755: INFO  add Para:案例分析：具体应用实例 [waitress-2]
2025-07-15 10:43:06  service.py 755: INFO  add Para:案例分析：具体应用实例 [waitress-2]
2025-07-15 10:43:06  EarlyBird.api.thesis.service 755: INFO  add Para:选取代表性船舶作为研究对象的理由说明 [waitress-2]
2025-07-15 10:43:06  service.py 755: INFO  add Para:选取代表性船舶作为研究对象的理由说明 [waitress-2]
2025-07-15 10:43:06  EarlyBird.api.thesis.service 755: INFO  add Para:运用所提方法对实际案例进行深入剖析 [waitress-2]
2025-07-15 10:43:06  service.py 755: INFO  add Para:运用所提方法对实际案例进行深入剖析 [waitress-2]
2025-07-15 10:43:06  EarlyBird.api.thesis.service 755: INFO  add Para:通过历史数据验证新方法的有效性 [waitress-2]
2025-07-15 10:43:06  service.py 755: INFO  add Para:通过历史数据验证新方法的有效性 [waitress-2]
2025-07-15 10:43:06  EarlyBird.api.thesis.service 755: INFO  add Para:数据展示与结果讨论 [waitress-2]
2025-07-15 10:43:06  service.py 755: INFO  add Para:数据展示与结果讨论 [waitress-2]
2025-07-15 10:43:06  EarlyBird.api.thesis.service 755: INFO  add Para:关键性能参数随时间变化趋势图解读 [waitress-2]
2025-07-15 10:43:06  service.py 755: INFO  add Para:关键性能参数随时间变化趋势图解读 [waitress-2]
2025-07-15 10:43:06  EarlyBird.api.thesis.service 755: INFO  add Para:不同条件下船舶结构响应特征的统计分析 [waitress-2]
2025-07-15 10:43:06  service.py 755: INFO  add Para:不同条件下船舶结构响应特征的统计分析 [waitress-2]
2025-07-15 10:43:06  EarlyBird.api.thesis.service 755: INFO  add Para:将研究成果与现有文献报道的结果做对比评价 [waitress-2]
2025-07-15 10:43:06  service.py 755: INFO  add Para:将研究成果与现有文献报道的结果做对比评价 [waitress-2]
2025-07-15 10:43:06  EarlyBird.api.thesis.service 755: INFO  add Para:结论与展望 [waitress-2]
2025-07-15 10:43:06  service.py 755: INFO  add Para:结论与展望 [waitress-2]
2025-07-15 10:43:06  EarlyBird.api.thesis.service 755: INFO  add Para:研究发现总结及对未来工作的启示 [waitress-2]
2025-07-15 10:43:06  service.py 755: INFO  add Para:研究发现总结及对未来工作的启示 [waitress-2]
2025-07-15 10:43:06  EarlyBird.api.thesis.service 755: INFO  add Para:针对提高船舶安全性提出的政策建议 [waitress-2]
2025-07-15 10:43:06  service.py 755: INFO  add Para:针对提高船舶安全性提出的政策建议 [waitress-2]
2025-07-15 10:43:06  EarlyBird.api.thesis.service 755: INFO  add Para:探索更广泛应用场景的可能性 [waitress-2]
2025-07-15 10:43:06  service.py 755: INFO  add Para:探索更广泛应用场景的可能性 [waitress-2]
2025-07-15 10:43:06  EarlyBird.common.docx 500: INFO  d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\download\[早鸟论文]_船舶结构完整性先进测试方法研究_V20250715_104306.docx [waitress-2]
2025-07-15 10:43:06  docx.py 500: INFO  d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\download\[早鸟论文]_船舶结构完整性先进测试方法研究_V20250715_104306.docx [waitress-2]
2025-07-15 10:43:07  EarlyBird.api.thesis.apis 162: INFO  用户 13 下载论文 30 成功 [waitress-2]
2025-07-15 10:43:07  apis.py 162: INFO  用户 13 下载论文 30 成功 [waitress-2]
2025-07-15 10:43:07  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/download [waitress-0]
2025-07-15 10:43:07  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/download [waitress-0]
2025-07-15 10:43:07  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-0]
2025-07-15 10:43:07  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-0]
2025-07-15 10:43:07  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-0]
2025-07-15 10:43:07  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-0]
2025-07-15 10:43:07  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-15 10:43:07  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-15 10:43:08  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-3]
2025-07-15 10:43:08  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-3]
2025-07-15 10:43:08  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-3]
2025-07-15 10:43:08  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-3]
2025-07-15 10:43:08  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-3]
2025-07-15 10:43:08  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-3]
2025-07-15 10:43:08  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-15 10:43:08  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-15 10:43:08  EarlyBird.api.thesis.apis 145: INFO  用户 13 请求下载论文 30 [waitress-3]
2025-07-15 10:43:08  apis.py 145: INFO  用户 13 请求下载论文 30 [waitress-3]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 296: INFO  用户 13 请求导出论文 30 [waitress-3]
2025-07-15 10:43:08  service.py 296: INFO  用户 13 请求导出论文 30 [waitress-3]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 304: INFO  用户 13 VIP状态: False [waitress-3]
2025-07-15 10:43:08  service.py 304: INFO  用户 13 VIP状态: False [waitress-3]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 309: INFO  论文下载收费功能是否启用: True [waitress-3]
2025-07-15 10:43:08  service.py 309: INFO  论文下载收费功能是否启用: True [waitress-3]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 323: INFO  VIP用户是否免费下载: False [waitress-3]
2025-07-15 10:43:08  service.py 323: INFO  VIP用户是否免费下载: False [waitress-3]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 340: INFO  首次下载是否免费: False [waitress-3]
2025-07-15 10:43:08  service.py 340: INFO  首次下载是否免费: False [waitress-3]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 350: INFO  用户 13 是否有真实支付的下载记录: True [waitress-3]
2025-07-15 10:43:08  service.py 350: INFO  用户 13 是否有真实支付的下载记录: True [waitress-3]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 370: INFO  用户 13 已有已支付的下载记录，可直接下载论文 30 [waitress-3]
2025-07-15 10:43:08  service.py 370: INFO  用户 13 已有已支付的下载记录，可直接下载论文 30 [waitress-3]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 755: INFO  add Para:船舶结构完整性先进测试方法研究 [waitress-3]
2025-07-15 10:43:08  service.py 755: INFO  add Para:船舶结构完整性先进测试方法研究 [waitress-3]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 755: INFO  add Para:绪论 [waitress-3]
2025-07-15 10:43:08  service.py 755: INFO  add Para:绪论 [waitress-3]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 755: INFO  add Para:研究背景与问题提出 [waitress-3]
2025-07-15 10:43:08  service.py 755: INFO  add Para:研究背景与问题提出 [waitress-3]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 755: INFO  add Para:国内外船舶结构完整性测试方法的数据现状 [waitress-3]
2025-07-15 10:43:08  service.py 755: INFO  add Para:国内外船舶结构完整性测试方法的数据现状 [waitress-3]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 755: INFO  add Para:本研究的意义及数据需求分析 [waitress-3]
2025-07-15 10:43:08  service.py 755: INFO  add Para:本研究的意义及数据需求分析 [waitress-3]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 755: INFO  add Para:理论基础与数据模型构建 [waitress-3]
2025-07-15 10:43:08  service.py 755: INFO  add Para:理论基础与数据模型构建 [waitress-3]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 755: INFO  add Para:船舶结构完整性的基本理论框架及其量化指标 [waitress-3]
2025-07-15 10:43:08  service.py 755: INFO  add Para:船舶结构完整性的基本理论框架及其量化指标 [waitress-3]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 755: INFO  add Para:基于大数据的船舶结构完整性预测模型设计 [waitress-3]
2025-07-15 10:43:08  service.py 755: INFO  add Para:基于大数据的船舶结构完整性预测模型设计 [waitress-3]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 755: INFO  add Para:用于评估船舶结构安全性的新型数据分析方法介绍 [waitress-3]
2025-07-15 10:43:08  service.py 755: INFO  add Para:用于评估船舶结构安全性的新型数据分析方法介绍 [waitress-3]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 755: INFO  add Para:实证研究：数据收集与处理 [waitress-3]
2025-07-15 10:43:08  service.py 755: INFO  add Para:实证研究：数据收集与处理 [waitress-3]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 755: INFO  add Para:实验数据采集方案的设计及实施细节 [waitress-3]
2025-07-15 10:43:08  service.py 755: INFO  add Para:实验数据采集方案的设计及实施细节 [waitress-3]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 755: INFO  add Para:采用不同技术手段获取船舶结构状态信息的方法对比 [waitress-3]
2025-07-15 10:43:08  service.py 755: INFO  add Para:采用不同技术手段获取船舶结构状态信息的方法对比 [waitress-3]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 755: INFO  add Para:数据清洗、预处理过程中的挑战与解决方案 [waitress-3]
2025-07-15 10:43:08  service.py 755: INFO  add Para:数据清洗、预处理过程中的挑战与解决方案 [waitress-3]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 755: INFO  add Para:案例分析：具体应用实例 [waitress-3]
2025-07-15 10:43:08  service.py 755: INFO  add Para:案例分析：具体应用实例 [waitress-3]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 755: INFO  add Para:选取代表性船舶作为研究对象的理由说明 [waitress-3]
2025-07-15 10:43:08  service.py 755: INFO  add Para:选取代表性船舶作为研究对象的理由说明 [waitress-3]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 755: INFO  add Para:运用所提方法对实际案例进行深入剖析 [waitress-3]
2025-07-15 10:43:08  service.py 755: INFO  add Para:运用所提方法对实际案例进行深入剖析 [waitress-3]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 755: INFO  add Para:通过历史数据验证新方法的有效性 [waitress-3]
2025-07-15 10:43:08  service.py 755: INFO  add Para:通过历史数据验证新方法的有效性 [waitress-3]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 755: INFO  add Para:数据展示与结果讨论 [waitress-3]
2025-07-15 10:43:08  service.py 755: INFO  add Para:数据展示与结果讨论 [waitress-3]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 755: INFO  add Para:关键性能参数随时间变化趋势图解读 [waitress-3]
2025-07-15 10:43:08  service.py 755: INFO  add Para:关键性能参数随时间变化趋势图解读 [waitress-3]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 755: INFO  add Para:不同条件下船舶结构响应特征的统计分析 [waitress-3]
2025-07-15 10:43:08  service.py 755: INFO  add Para:不同条件下船舶结构响应特征的统计分析 [waitress-3]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 755: INFO  add Para:将研究成果与现有文献报道的结果做对比评价 [waitress-3]
2025-07-15 10:43:08  service.py 755: INFO  add Para:将研究成果与现有文献报道的结果做对比评价 [waitress-3]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 755: INFO  add Para:结论与展望 [waitress-3]
2025-07-15 10:43:08  service.py 755: INFO  add Para:结论与展望 [waitress-3]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 755: INFO  add Para:研究发现总结及对未来工作的启示 [waitress-3]
2025-07-15 10:43:08  service.py 755: INFO  add Para:研究发现总结及对未来工作的启示 [waitress-3]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 755: INFO  add Para:针对提高船舶安全性提出的政策建议 [waitress-3]
2025-07-15 10:43:08  service.py 755: INFO  add Para:针对提高船舶安全性提出的政策建议 [waitress-3]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 755: INFO  add Para:探索更广泛应用场景的可能性 [waitress-3]
2025-07-15 10:43:08  service.py 755: INFO  add Para:探索更广泛应用场景的可能性 [waitress-3]
2025-07-15 10:43:08  EarlyBird.common.docx 500: INFO  d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\download\[早鸟论文]_船舶结构完整性先进测试方法研究_V20250715_104308.docx [waitress-3]
2025-07-15 10:43:08  docx.py 500: INFO  d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\download\[早鸟论文]_船舶结构完整性先进测试方法研究_V20250715_104308.docx [waitress-3]
2025-07-15 10:43:08  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-15 10:43:08  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/paymentStatus [waitress-1]
2025-07-15 10:43:08  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-1]
2025-07-15 10:43:08  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-1]
2025-07-15 10:43:08  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-1]
2025-07-15 10:43:08  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-1]
2025-07-15 10:43:08  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-15 10:43:08  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-15 10:43:08  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/confirmPayment [waitress-2]
2025-07-15 10:43:08  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/confirmPayment [waitress-2]
2025-07-15 10:43:08  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-2]
2025-07-15 10:43:08  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-2]
2025-07-15 10:43:08  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-2]
2025-07-15 10:43:08  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-2]
2025-07-15 10:43:08  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-15 10:43:08  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-15 10:43:08  EarlyBird.api.thesis.apis 182: INFO  用户 13 确认支付订单 thesis_download_33047ed687a5 并下载论文 30 [waitress-2]
2025-07-15 10:43:08  apis.py 182: INFO  用户 13 确认支付订单 thesis_download_33047ed687a5 并下载论文 30 [waitress-2]
2025-07-15 10:43:08  EarlyBird.api.thesis.apis 186: WARNING  用户 13 确认支付失败: 支付订单不存在 [waitress-2]
2025-07-15 10:43:08  apis.py 186: WARNING  用户 13 确认支付失败: 支付订单不存在 [waitress-2]
2025-07-15 10:43:08  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-0]
2025-07-15 10:43:08  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-0]
2025-07-15 10:43:08  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-0]
2025-07-15 10:43:08  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-0]
2025-07-15 10:43:08  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-0]
2025-07-15 10:43:08  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-0]
2025-07-15 10:43:08  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-15 10:43:08  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-15 10:43:08  EarlyBird.api.thesis.apis 145: INFO  用户 13 请求下载论文 30 [waitress-0]
2025-07-15 10:43:08  apis.py 145: INFO  用户 13 请求下载论文 30 [waitress-0]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 296: INFO  用户 13 请求导出论文 30 [waitress-0]
2025-07-15 10:43:08  service.py 296: INFO  用户 13 请求导出论文 30 [waitress-0]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 304: INFO  用户 13 VIP状态: False [waitress-0]
2025-07-15 10:43:08  service.py 304: INFO  用户 13 VIP状态: False [waitress-0]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 309: INFO  论文下载收费功能是否启用: True [waitress-0]
2025-07-15 10:43:08  service.py 309: INFO  论文下载收费功能是否启用: True [waitress-0]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 323: INFO  VIP用户是否免费下载: False [waitress-0]
2025-07-15 10:43:08  service.py 323: INFO  VIP用户是否免费下载: False [waitress-0]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 340: INFO  首次下载是否免费: False [waitress-0]
2025-07-15 10:43:08  service.py 340: INFO  首次下载是否免费: False [waitress-0]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 350: INFO  用户 13 是否有真实支付的下载记录: True [waitress-0]
2025-07-15 10:43:08  service.py 350: INFO  用户 13 是否有真实支付的下载记录: True [waitress-0]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 370: INFO  用户 13 已有已支付的下载记录，可直接下载论文 30 [waitress-0]
2025-07-15 10:43:08  service.py 370: INFO  用户 13 已有已支付的下载记录，可直接下载论文 30 [waitress-0]
2025-07-15 10:43:08  EarlyBird.api.thesis.apis 162: INFO  用户 13 下载论文 30 成功 [waitress-3]
2025-07-15 10:43:08  apis.py 162: INFO  用户 13 下载论文 30 成功 [waitress-3]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 755: INFO  add Para:船舶结构完整性先进测试方法研究 [waitress-0]
2025-07-15 10:43:08  service.py 755: INFO  add Para:船舶结构完整性先进测试方法研究 [waitress-0]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 755: INFO  add Para:绪论 [waitress-0]
2025-07-15 10:43:08  service.py 755: INFO  add Para:绪论 [waitress-0]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 755: INFO  add Para:研究背景与问题提出 [waitress-0]
2025-07-15 10:43:08  service.py 755: INFO  add Para:研究背景与问题提出 [waitress-0]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 755: INFO  add Para:国内外船舶结构完整性测试方法的数据现状 [waitress-0]
2025-07-15 10:43:08  service.py 755: INFO  add Para:国内外船舶结构完整性测试方法的数据现状 [waitress-0]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 755: INFO  add Para:本研究的意义及数据需求分析 [waitress-0]
2025-07-15 10:43:08  service.py 755: INFO  add Para:本研究的意义及数据需求分析 [waitress-0]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 755: INFO  add Para:理论基础与数据模型构建 [waitress-0]
2025-07-15 10:43:08  service.py 755: INFO  add Para:理论基础与数据模型构建 [waitress-0]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 755: INFO  add Para:船舶结构完整性的基本理论框架及其量化指标 [waitress-0]
2025-07-15 10:43:08  service.py 755: INFO  add Para:船舶结构完整性的基本理论框架及其量化指标 [waitress-0]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 755: INFO  add Para:基于大数据的船舶结构完整性预测模型设计 [waitress-0]
2025-07-15 10:43:08  service.py 755: INFO  add Para:基于大数据的船舶结构完整性预测模型设计 [waitress-0]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 755: INFO  add Para:用于评估船舶结构安全性的新型数据分析方法介绍 [waitress-0]
2025-07-15 10:43:08  service.py 755: INFO  add Para:用于评估船舶结构安全性的新型数据分析方法介绍 [waitress-0]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 755: INFO  add Para:实证研究：数据收集与处理 [waitress-0]
2025-07-15 10:43:08  service.py 755: INFO  add Para:实证研究：数据收集与处理 [waitress-0]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 755: INFO  add Para:实验数据采集方案的设计及实施细节 [waitress-0]
2025-07-15 10:43:08  service.py 755: INFO  add Para:实验数据采集方案的设计及实施细节 [waitress-0]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 755: INFO  add Para:采用不同技术手段获取船舶结构状态信息的方法对比 [waitress-0]
2025-07-15 10:43:08  service.py 755: INFO  add Para:采用不同技术手段获取船舶结构状态信息的方法对比 [waitress-0]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 755: INFO  add Para:数据清洗、预处理过程中的挑战与解决方案 [waitress-0]
2025-07-15 10:43:08  service.py 755: INFO  add Para:数据清洗、预处理过程中的挑战与解决方案 [waitress-0]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 755: INFO  add Para:案例分析：具体应用实例 [waitress-0]
2025-07-15 10:43:08  service.py 755: INFO  add Para:案例分析：具体应用实例 [waitress-0]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 755: INFO  add Para:选取代表性船舶作为研究对象的理由说明 [waitress-0]
2025-07-15 10:43:08  service.py 755: INFO  add Para:选取代表性船舶作为研究对象的理由说明 [waitress-0]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 755: INFO  add Para:运用所提方法对实际案例进行深入剖析 [waitress-0]
2025-07-15 10:43:08  service.py 755: INFO  add Para:运用所提方法对实际案例进行深入剖析 [waitress-0]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 755: INFO  add Para:通过历史数据验证新方法的有效性 [waitress-0]
2025-07-15 10:43:08  service.py 755: INFO  add Para:通过历史数据验证新方法的有效性 [waitress-0]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 755: INFO  add Para:数据展示与结果讨论 [waitress-0]
2025-07-15 10:43:08  service.py 755: INFO  add Para:数据展示与结果讨论 [waitress-0]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 755: INFO  add Para:关键性能参数随时间变化趋势图解读 [waitress-0]
2025-07-15 10:43:08  service.py 755: INFO  add Para:关键性能参数随时间变化趋势图解读 [waitress-0]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 755: INFO  add Para:不同条件下船舶结构响应特征的统计分析 [waitress-0]
2025-07-15 10:43:08  service.py 755: INFO  add Para:不同条件下船舶结构响应特征的统计分析 [waitress-0]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 755: INFO  add Para:将研究成果与现有文献报道的结果做对比评价 [waitress-0]
2025-07-15 10:43:08  service.py 755: INFO  add Para:将研究成果与现有文献报道的结果做对比评价 [waitress-0]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 755: INFO  add Para:结论与展望 [waitress-0]
2025-07-15 10:43:08  service.py 755: INFO  add Para:结论与展望 [waitress-0]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 755: INFO  add Para:研究发现总结及对未来工作的启示 [waitress-0]
2025-07-15 10:43:08  service.py 755: INFO  add Para:研究发现总结及对未来工作的启示 [waitress-0]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 755: INFO  add Para:针对提高船舶安全性提出的政策建议 [waitress-0]
2025-07-15 10:43:08  service.py 755: INFO  add Para:针对提高船舶安全性提出的政策建议 [waitress-0]
2025-07-15 10:43:08  EarlyBird.api.thesis.service 755: INFO  add Para:探索更广泛应用场景的可能性 [waitress-0]
2025-07-15 10:43:08  service.py 755: INFO  add Para:探索更广泛应用场景的可能性 [waitress-0]
2025-07-15 10:43:08  EarlyBird.common.docx 500: INFO  d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\download\[早鸟论文]_船舶结构完整性先进测试方法研究_V20250715_104308.docx [waitress-0]
2025-07-15 10:43:08  docx.py 500: INFO  d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\download\[早鸟论文]_船舶结构完整性先进测试方法研究_V20250715_104308.docx [waitress-0]
2025-07-15 10:43:08  EarlyBird.api.thesis.apis 162: INFO  用户 13 下载论文 30 成功 [waitress-0]
2025-07-15 10:43:08  apis.py 162: INFO  用户 13 下载论文 30 成功 [waitress-0]
2025-07-15 10:43:08  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/download [waitress-1]
2025-07-15 10:43:08  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/download [waitress-1]
2025-07-15 10:43:08  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-1]
2025-07-15 10:43:08  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-1]
2025-07-15 10:43:08  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-1]
2025-07-15 10:43:08  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-1]
2025-07-15 10:43:08  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-15 10:43:08  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-15 10:53:30  init_flask.py 27: INFO  {'CpuCount': 6, 'Mem': '32 GB'} [MainThread]
2025-07-15 10:53:30  web_server.py 32: INFO  Static directory: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\ui [MainThread]
2025-07-15 10:53:30  web_server.py 33: INFO  Static directory exists: True [MainThread]
2025-07-15 10:53:30  web_server.py 37: INFO  设置工作目录: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird [MainThread]
2025-07-15 10:53:30  EarlyBird.config.config 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-15 10:53:30  config.py 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-15 10:53:30  config.py 94: INFO  Database URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-15 10:53:32  EarlyBird.api.admin.app 39: INFO  ✅ 后台管理系统API已注册 [MainThread]
2025-07-15 10:53:32  app.py 39: INFO  ✅ 后台管理系统API已注册 [MainThread]
2025-07-15 10:53:32  EarlyBird 40: INFO  确保数据目录存在: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\data [MainThread]
2025-07-15 10:53:32  server.py 40: INFO  确保数据目录存在: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\data [MainThread]
2025-07-15 10:53:32  EarlyBird 44: INFO  开始执行数据库迁移... [MainThread]
2025-07-15 10:53:32  server.py 44: INFO  开始执行数据库迁移... [MainThread]
2025-07-15 10:53:32  config.py 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-15 10:53:32  config.py 94: INFO  Database URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-15 10:53:32  EarlyBird 53: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-15 10:53:32  server.py 53: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-15 10:53:32  EarlyBird 97: WARNING  ChatLog表不存在，将在应用启动时自动创建 [MainThread]
2025-07-15 10:53:32  server.py 97: WARNING  ChatLog表不存在，将在应用启动时自动创建 [MainThread]
2025-07-15 10:53:32  add_uid_to_chatlog.py 20: INFO  开始执行ChatLog表迁移... [MainThread]
2025-07-15 10:53:32  add_uid_to_chatlog.py 24: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-15 10:53:32  add_uid_to_chatlog.py 38: WARNING  ChatLog表不存在，可能需要先创建表 [MainThread]
2025-07-15 10:53:32  task_api_server.py 31: INFO  TaskApiServer start [MainThread]
2025-07-15 10:53:32  task_paper_generator.py 38: INFO  论文生成任务处理线程初始化 [MainThread]
2025-07-15 10:53:32  task_paper_generator.py 72: INFO  论文生成任务处理线程启动 [Thread-2]
2025-07-15 10:53:32  task_api_server.py 47: INFO  TaskApiServer listen on 127.0.0.1:3302 [Thread-1]
2025-07-15 10:53:32  task_api_server.py 17: INFO  _ApiServerThread start [Thread-3]
2025-07-15 10:53:37  web_server.py 73: INFO  Serving request for path: paper/content [waitress-0]
2025-07-15 10:53:37  web_server.py 85: INFO  Serving index.html for path: paper/content [waitress-0]
2025-07-15 10:53:37  web_server.py 73: INFO  Serving request for path: js/app.67c683d7.js [waitress-1]
2025-07-15 10:53:37  web_server.py 73: INFO  Serving request for path: js/chunk-vendors.04aff60f.js [waitress-2]
2025-07-15 10:53:37  web_server.py 82: INFO  Serving static file: js/app.67c683d7.js [waitress-1]
2025-07-15 10:53:37  web_server.py 82: INFO  Serving static file: js/chunk-vendors.04aff60f.js [waitress-2]
2025-07-15 10:53:37  web_server.py 73: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-3]
2025-07-15 10:53:37  web_server.py 73: INFO  Serving request for path: css/app.1e68ddae.css [waitress-0]
2025-07-15 10:53:37  web_server.py 82: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-3]
2025-07-15 10:53:37  web_server.py 82: INFO  Serving static file: css/app.1e68ddae.css [waitress-0]
2025-07-15 10:53:37  web_server.py 73: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-1]
2025-07-15 10:53:37  web_server.py 85: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-1]
2025-07-15 10:53:38  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/user/info [waitress-3]
2025-07-15 10:53:38  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/user/info [waitress-3]
2025-07-15 10:53:38  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-3]
2025-07-15 10:53:38  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-3]
2025-07-15 10:53:38  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-3]
2025-07-15 10:53:38  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-3]
2025-07-15 10:53:38  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-15 10:53:38  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-15 10:53:38  web_server.py 73: INFO  Serving request for path: logo.png [waitress-0]
2025-07-15 10:53:38  web_server.py 82: INFO  Serving static file: logo.png [waitress-0]
2025-07-15 10:53:38  web_server.py 73: INFO  Serving request for path: css/389.ae47f7d6.css [waitress-1]
2025-07-15 10:53:38  web_server.py 73: INFO  Serving request for path: js/22.683bc166.js [waitress-2]
2025-07-15 10:53:38  web_server.py 73: INFO  Serving request for path: js/897.25207915.js [waitress-0]
2025-07-15 10:53:38  web_server.py 73: INFO  Serving request for path: js/389.3d99d45c.js [waitress-3]
2025-07-15 10:53:38  web_server.py 82: INFO  Serving static file: css/389.ae47f7d6.css [waitress-1]
2025-07-15 10:53:38  web_server.py 82: INFO  Serving static file: js/22.683bc166.js [waitress-2]
2025-07-15 10:53:38  web_server.py 82: INFO  Serving static file: js/897.25207915.js [waitress-0]
2025-07-15 10:53:38  web_server.py 82: INFO  Serving static file: js/389.3d99d45c.js [waitress-3]
2025-07-15 10:53:38  web_server.py 73: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-1]
2025-07-15 10:53:38  web_server.py 82: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-1]
2025-07-15 10:53:38  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getList [waitress-0]
2025-07-15 10:53:38  service.py 28: ERROR  获取模型名称失败: (pymysql.err.OperationalError) (1054, "Unknown column 'earlybird_paper_setting.category' in 'field list'")
[SQL: SELECT earlybird_paper_setting.id AS earlybird_paper_setting_id, earlybird_paper_setting.create_time AS earlybird_paper_setting_create_time, earlybird_paper_setting.update_time AS earlybird_paper_setting_update_time, earlybird_paper_setting.is_deleted AS earlybird_paper_setting_is_deleted, earlybird_paper_setting.status AS earlybird_paper_setting_status, earlybird_paper_setting.category AS earlybird_paper_setting_category, earlybird_paper_setting.`key` AS earlybird_paper_setting_key, earlybird_paper_setting.value AS earlybird_paper_setting_value, earlybird_paper_setting.description AS earlybird_paper_setting_description, earlybird_paper_setting.`settingKey` AS `earlybird_paper_setting_settingKey`, earlybird_paper_setting.`settingValue` AS `earlybird_paper_setting_settingValue` 
FROM earlybird_paper_setting 
WHERE earlybird_paper_setting.`settingKey` = %(settingKey_1)s 
 LIMIT %(param_1)s]
[parameters: {'settingKey_1': 'modelName', 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/14/e3q8) [waitress-2]
2025-07-15 10:53:38  web_server.py 73: INFO  Serving request for path: fonts/element-icons.ff18efd1.woff [waitress-3]
2025-07-15 10:53:38  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getList [waitress-0]
2025-07-15 10:53:38  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-0]
2025-07-15 10:53:38  web_server.py 82: INFO  Serving static file: fonts/element-icons.ff18efd1.woff [waitress-3]
2025-07-15 10:53:38  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-0]
2025-07-15 10:53:38  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-0]
2025-07-15 10:53:38  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-0]
2025-07-15 10:53:38  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-15 10:53:38  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-0]
2025-07-15 10:53:38  EarlyBird.api.thesis.apis 92: INFO  用户 13 请求获取论文列表 [waitress-0]
2025-07-15 10:53:38  apis.py 92: INFO  用户 13 请求获取论文列表 [waitress-0]
2025-07-15 10:53:38  EarlyBird.api.thesis.service 577: INFO  查询论文列表，用户ID: 13 [waitress-0]
2025-07-15 10:53:38  service.py 577: INFO  查询论文列表，用户ID: 13 [waitress-0]
2025-07-15 10:53:38  EarlyBird.api.thesis.service 583: INFO  查询到 1 篇论文 [waitress-0]
2025-07-15 10:53:38  service.py 583: INFO  查询到 1 篇论文 [waitress-0]
2025-07-15 10:53:38  EarlyBird.api.thesis.service 593: INFO  论文ID: 30, 标题: 船舶结构完整性先进测试方法研究, 用户ID: 13 [waitress-0]
2025-07-15 10:53:38  service.py 593: INFO  论文ID: 30, 标题: 船舶结构完整性先进测试方法研究, 用户ID: 13 [waitress-0]
2025-07-15 10:53:38  EarlyBird.api.thesis.service 595: INFO  返回论文列表，共 1 篇 [waitress-0]
2025-07-15 10:53:38  service.py 595: INFO  返回论文列表，共 1 篇 [waitress-0]
2025-07-15 10:53:38  EarlyBird.api.thesis.apis 95: INFO  用户 13 获取论文列表成功，共 1 篇 [waitress-0]
2025-07-15 10:53:38  apis.py 95: INFO  用户 13 获取论文列表成功，共 1 篇 [waitress-0]
2025-07-15 10:53:38  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getDetail [waitress-1]
2025-07-15 10:53:38  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getDetail [waitress-1]
2025-07-15 10:53:38  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-1]
2025-07-15 10:53:38  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-1]
2025-07-15 10:53:38  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-1]
2025-07-15 10:53:38  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-1]
2025-07-15 10:53:38  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-15 10:53:38  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-1]
2025-07-15 10:53:38  EarlyBird.api.talk2ai.utils 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-1]
2025-07-15 10:53:38  utils.py 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-1]
2025-07-15 10:53:38  EarlyBird.api.talk2ai.utils 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-1]
2025-07-15 10:53:38  utils.py 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-1]
2025-07-15 10:53:38  EarlyBird.api.talk2ai.utils 161: INFO  提纲处理完成，根节点ID: AuzkidondS7xotd5Jv4hiq [waitress-1]
2025-07-15 10:53:38  utils.py 161: INFO  提纲处理完成，根节点ID: AuzkidondS7xotd5Jv4hiq [waitress-1]
2025-07-15 10:53:38  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getOutline [waitress-2]
2025-07-15 10:53:38  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/getOutline [waitress-2]
2025-07-15 10:53:38  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-2]
2025-07-15 10:53:38  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-2]
2025-07-15 10:53:38  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-2]
2025-07-15 10:53:38  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-2]
2025-07-15 10:53:38  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-15 10:53:38  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-2]
2025-07-15 10:53:38  EarlyBird.api.talk2ai.utils 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-2]
2025-07-15 10:53:38  utils.py 34: INFO  开始处理提纲数据，类型: <class 'dict'> [waitress-2]
2025-07-15 10:53:38  EarlyBird.api.talk2ai.utils 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-2]
2025-07-15 10:53:38  utils.py 55: INFO  提纲数据键: dict_keys(['id', 'text', 'title', 'subtitle']) [waitress-2]
2025-07-15 10:53:38  EarlyBird.api.talk2ai.utils 161: INFO  提纲处理完成，根节点ID: AuzkidondS7xotd5Jv4hiq [waitress-2]
2025-07-15 10:53:38  utils.py 161: INFO  提纲处理完成，根节点ID: AuzkidondS7xotd5Jv4hiq [waitress-2]
2025-07-15 10:53:39  EarlyBird.ExtendRegister.hook_register 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-3]
2025-07-15 10:53:39  hook_register.py 129: INFO  🔍 钩子函数调试 - 路径: /api/thesis/downloadThesis [waitress-3]
2025-07-15 10:53:39  EarlyBird.ExtendRegister.hook_register 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-3]
2025-07-15 10:53:39  hook_register.py 130: INFO  🔍 Session内容: {'_permanent': True, 'login_time': '2025-07-14T22:15:27.057270', 'user_id': 13, 'username': 'test1003'} [waitress-3]
2025-07-15 10:53:39  EarlyBird.ExtendRegister.hook_register 131: INFO  🔍 提取的user_id: 13 [waitress-3]
2025-07-15 10:53:39  hook_register.py 131: INFO  🔍 提取的user_id: 13 [waitress-3]
2025-07-15 10:53:39  EarlyBird.ExtendRegister.hook_register 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-15 10:53:39  hook_register.py 132: INFO  🔍 Session ID: N/A [waitress-3]
2025-07-15 10:53:39  EarlyBird.api.thesis.apis 145: INFO  用户 13 请求下载论文 30 [waitress-3]
2025-07-15 10:53:39  apis.py 145: INFO  用户 13 请求下载论文 30 [waitress-3]
2025-07-15 10:53:39  EarlyBird.api.thesis.service 296: INFO  用户 13 请求导出论文 30 [waitress-3]
2025-07-15 10:53:39  service.py 296: INFO  用户 13 请求导出论文 30 [waitress-3]
2025-07-15 10:53:39  EarlyBird.api.thesis.service 306: INFO  用户 13 VIP状态: <bound method User.isVip of {"accessToken": null, "phone": null, "vip_expire_at": null, "refreshToken": null, "gender": 3, "balance": null, "update_time": "2025-07-14 22:15:27", "nickname": "\u7528\u6237_test1003", "email": null, "id": 13, "headimg": null, "is_lock": false, "invite_code": null, "is_deleted": 0, "activation_key": null, "last_login_time": null, "from_user_id": null, "status": 1, "username": "test1003", "last_login_ip": null, "openPlatform": "wx", "password_hash": "scrypt:32768:8:1$EEsynwJfRiZUcaQh$20c2c645597067f50199fbc94bb8c62886fe1f60c9257a4cbeec8795a4d5bb2e2374d4b9ff88c2652fe1d232b77a11aa3423266b2cfbde8f006940c8d14edb22", "vip_level": 1, "create_time": "2025-07-14 22:15:27", "openId": null, "realname": null, "vip_start_at": null}> [waitress-3]
2025-07-15 10:53:39  service.py 306: INFO  用户 13 VIP状态: <bound method User.isVip of {"accessToken": null, "phone": null, "vip_expire_at": null, "refreshToken": null, "gender": 3, "balance": null, "update_time": "2025-07-14 22:15:27", "nickname": "\u7528\u6237_test1003", "email": null, "id": 13, "headimg": null, "is_lock": false, "invite_code": null, "is_deleted": 0, "activation_key": null, "last_login_time": null, "from_user_id": null, "status": 1, "username": "test1003", "last_login_ip": null, "openPlatform": "wx", "password_hash": "scrypt:32768:8:1$EEsynwJfRiZUcaQh$20c2c645597067f50199fbc94bb8c62886fe1f60c9257a4cbeec8795a4d5bb2e2374d4b9ff88c2652fe1d232b77a11aa3423266b2cfbde8f006940c8d14edb22", "vip_level": 1, "create_time": "2025-07-14 22:15:27", "openId": null, "realname": null, "vip_start_at": null}> [waitress-3]
2025-07-15 10:53:39  EarlyBird.api.thesis.service 311: INFO  论文下载收费功能是否启用: None [waitress-3]
2025-07-15 10:53:39  service.py 311: INFO  论文下载收费功能是否启用: None [waitress-3]
2025-07-15 10:53:39  EarlyBird.api.thesis.service 315: INFO  论文下载收费功能未启用，用户 13 可以免费下载论文 30 [waitress-3]
2025-07-15 10:53:39  service.py 315: INFO  论文下载收费功能未启用，用户 13 可以免费下载论文 30 [waitress-3]
2025-07-15 10:53:39  EarlyBird.api.thesis.service 766: INFO  add Para:船舶结构完整性先进测试方法研究 [waitress-3]
2025-07-15 10:53:39  service.py 766: INFO  add Para:船舶结构完整性先进测试方法研究 [waitress-3]
2025-07-15 10:53:39  EarlyBird.api.thesis.service 766: INFO  add Para:绪论 [waitress-3]
2025-07-15 10:53:39  service.py 766: INFO  add Para:绪论 [waitress-3]
2025-07-15 10:53:39  EarlyBird.api.thesis.service 766: INFO  add Para:研究背景与问题提出 [waitress-3]
2025-07-15 10:53:39  service.py 766: INFO  add Para:研究背景与问题提出 [waitress-3]
2025-07-15 10:53:39  EarlyBird.api.thesis.service 766: INFO  add Para:国内外船舶结构完整性测试方法的数据现状 [waitress-3]
2025-07-15 10:53:39  service.py 766: INFO  add Para:国内外船舶结构完整性测试方法的数据现状 [waitress-3]
2025-07-15 10:53:39  EarlyBird.api.thesis.service 766: INFO  add Para:本研究的意义及数据需求分析 [waitress-3]
2025-07-15 10:53:39  service.py 766: INFO  add Para:本研究的意义及数据需求分析 [waitress-3]
2025-07-15 10:53:39  EarlyBird.api.thesis.service 766: INFO  add Para:理论基础与数据模型构建 [waitress-3]
2025-07-15 10:53:39  service.py 766: INFO  add Para:理论基础与数据模型构建 [waitress-3]
2025-07-15 10:53:39  EarlyBird.api.thesis.service 766: INFO  add Para:船舶结构完整性的基本理论框架及其量化指标 [waitress-3]
2025-07-15 10:53:39  service.py 766: INFO  add Para:船舶结构完整性的基本理论框架及其量化指标 [waitress-3]
2025-07-15 10:53:39  EarlyBird.api.thesis.service 766: INFO  add Para:基于大数据的船舶结构完整性预测模型设计 [waitress-3]
2025-07-15 10:53:39  service.py 766: INFO  add Para:基于大数据的船舶结构完整性预测模型设计 [waitress-3]
2025-07-15 10:53:39  EarlyBird.api.thesis.service 766: INFO  add Para:用于评估船舶结构安全性的新型数据分析方法介绍 [waitress-3]
2025-07-15 10:53:39  service.py 766: INFO  add Para:用于评估船舶结构安全性的新型数据分析方法介绍 [waitress-3]
2025-07-15 10:53:39  EarlyBird.api.thesis.service 766: INFO  add Para:实证研究：数据收集与处理 [waitress-3]
2025-07-15 10:53:39  service.py 766: INFO  add Para:实证研究：数据收集与处理 [waitress-3]
2025-07-15 10:53:39  EarlyBird.api.thesis.service 766: INFO  add Para:实验数据采集方案的设计及实施细节 [waitress-3]
2025-07-15 10:53:39  service.py 766: INFO  add Para:实验数据采集方案的设计及实施细节 [waitress-3]
2025-07-15 10:53:39  EarlyBird.api.thesis.service 766: INFO  add Para:采用不同技术手段获取船舶结构状态信息的方法对比 [waitress-3]
2025-07-15 10:53:39  service.py 766: INFO  add Para:采用不同技术手段获取船舶结构状态信息的方法对比 [waitress-3]
2025-07-15 10:53:39  EarlyBird.api.thesis.service 766: INFO  add Para:数据清洗、预处理过程中的挑战与解决方案 [waitress-3]
2025-07-15 10:53:39  service.py 766: INFO  add Para:数据清洗、预处理过程中的挑战与解决方案 [waitress-3]
2025-07-15 10:53:39  EarlyBird.api.thesis.service 766: INFO  add Para:案例分析：具体应用实例 [waitress-3]
2025-07-15 10:53:39  service.py 766: INFO  add Para:案例分析：具体应用实例 [waitress-3]
2025-07-15 10:53:39  EarlyBird.api.thesis.service 766: INFO  add Para:选取代表性船舶作为研究对象的理由说明 [waitress-3]
2025-07-15 10:53:39  service.py 766: INFO  add Para:选取代表性船舶作为研究对象的理由说明 [waitress-3]
2025-07-15 10:53:39  EarlyBird.api.thesis.service 766: INFO  add Para:运用所提方法对实际案例进行深入剖析 [waitress-3]
2025-07-15 10:53:39  service.py 766: INFO  add Para:运用所提方法对实际案例进行深入剖析 [waitress-3]
2025-07-15 10:53:39  EarlyBird.api.thesis.service 766: INFO  add Para:通过历史数据验证新方法的有效性 [waitress-3]
2025-07-15 10:53:39  service.py 766: INFO  add Para:通过历史数据验证新方法的有效性 [waitress-3]
2025-07-15 10:53:39  EarlyBird.api.thesis.service 766: INFO  add Para:数据展示与结果讨论 [waitress-3]
2025-07-15 10:53:39  service.py 766: INFO  add Para:数据展示与结果讨论 [waitress-3]
2025-07-15 10:53:39  EarlyBird.api.thesis.service 766: INFO  add Para:关键性能参数随时间变化趋势图解读 [waitress-3]
2025-07-15 10:53:39  service.py 766: INFO  add Para:关键性能参数随时间变化趋势图解读 [waitress-3]
2025-07-15 10:53:39  EarlyBird.api.thesis.service 766: INFO  add Para:不同条件下船舶结构响应特征的统计分析 [waitress-3]
2025-07-15 10:53:39  service.py 766: INFO  add Para:不同条件下船舶结构响应特征的统计分析 [waitress-3]
2025-07-15 10:53:39  EarlyBird.api.thesis.service 766: INFO  add Para:将研究成果与现有文献报道的结果做对比评价 [waitress-3]
2025-07-15 10:53:39  service.py 766: INFO  add Para:将研究成果与现有文献报道的结果做对比评价 [waitress-3]
2025-07-15 10:53:39  EarlyBird.api.thesis.service 766: INFO  add Para:结论与展望 [waitress-3]
2025-07-15 10:53:39  service.py 766: INFO  add Para:结论与展望 [waitress-3]
2025-07-15 10:53:39  EarlyBird.api.thesis.service 766: INFO  add Para:研究发现总结及对未来工作的启示 [waitress-3]
2025-07-15 10:53:39  service.py 766: INFO  add Para:研究发现总结及对未来工作的启示 [waitress-3]
2025-07-15 10:53:39  EarlyBird.api.thesis.service 766: INFO  add Para:针对提高船舶安全性提出的政策建议 [waitress-3]
2025-07-15 10:53:39  service.py 766: INFO  add Para:针对提高船舶安全性提出的政策建议 [waitress-3]
2025-07-15 10:53:39  EarlyBird.api.thesis.service 766: INFO  add Para:探索更广泛应用场景的可能性 [waitress-3]
2025-07-15 10:53:39  service.py 766: INFO  add Para:探索更广泛应用场景的可能性 [waitress-3]
2025-07-15 10:53:39  EarlyBird.common.docx 500: INFO  d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\download\[早鸟论文]_船舶结构完整性先进测试方法研究_V20250715_105339.docx [waitress-3]
2025-07-15 10:53:39  docx.py 500: INFO  d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\download\[早鸟论文]_船舶结构完整性先进测试方法研究_V20250715_105339.docx [waitress-3]
2025-07-15 10:53:39  EarlyBird.api.thesis.apis 158: INFO  用户 13 下载论文 30 成功 [waitress-3]
2025-07-15 10:53:39  apis.py 158: INFO  用户 13 下载论文 30 成功 [waitress-3]
2025-07-15 10:53:39  app.py 1454: ERROR  Exception on /api/thesis/downloadThesis [POST] [waitress-3]
Traceback (most recent call last):
  File "C:\Python311\Lib\site-packages\flask\app.py", line 2070, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask\app.py", line 1515, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask_cors\extension.py", line 165, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask\app.py", line 1513, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask\app.py", line 1499, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**req.view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python311\Lib\site-packages\flask_pydantic\core.py", line 249, in wrapper
    res = func(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^
  File "d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\api\thesis\apis.py", line 159, in downloadThesis
    return ApiResponse().success(res.data).json()
           ^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'ApiResponse' object has no attribute 'success'
