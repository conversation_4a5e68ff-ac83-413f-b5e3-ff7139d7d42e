<template>
  <section class="my-navbar">
    <div class="my-top">
      <div class="logo" @click="handlerHomePage">
        <img class="sidebar-logo" :src="logo" />
        <span>{{ $t("menu.slogan") }}</span>
      </div>

      <div class="menu">
        <div v-for="link in navLinks" :key="link.path">
          <!-- 如果有子菜单 -->
          <div 
            v-if="link.children" 
            class="menu-item parent-menu" 
            :class="{'has-active-child': hasActiveChild(link)}"
            @click="toggleSubMenu(link)"
          >
            <i :class="link.icon"></i>
            {{ link.title }}
            <i class="el-icon-arrow-down submenu-icon" :class="{'is-active': openSubMenus.includes(link.path)}"></i>
          </div>
          
          <!-- 子菜单项 -->
          <div v-if="link.children && openSubMenus.includes(link.path)" class="submenu">
            <NavLink 
              class="submenu-item"
              :index="subLink.path"
              v-for="subLink in link.children"
              :key="subLink.path"
            >
              <i :class="subLink.icon"></i>
              {{ subLink.title }}
            </NavLink>
          </div>
          
          <!-- 如果没有子菜单 -->
          <NavLink
            v-if="!link.children"
            class="menu-item"
            :index="link.path"
          >
            <i :class="link.icon"></i>
            {{ link.title }}
          </NavLink>
        </div>
      </div>
      <div class="setting">
        <!-- 用户信息区域 -->
        <div class="user-section">
          <UserInfo />
        </div>
        
        <div class="setting-item">
          <i class="el-icon-message"></i>
          语言: {{ displayLanguage }}
        </div>
        <div class="setting-item" v-if="setting.modelName">
          <i class="el-icon-cpu"></i>
          当前模型: {{ setting.modelName }}
        </div>
      </div>
    </div>
  </section>
</template>

<script>
import Logo from "@/assets/images/logo.png";
import NavLink from "./NavLink.vue";
import UserInfo from "./UserInfo.vue";
import Config from "@/settings";
import menuList from "@/utils/menu";
import { getSetting } from "@/api/setting";
import i18n from "@/i18n";

export default {
  data() {
    return {
      logo: Logo,
      navLinks: menuList,
      setting: {},
      currentLang: '',
      openSubMenus: [], // 存储当前打开的子菜单路径
    };
  },
  components: {
    NavLink,
    UserInfo
  },
  created() {
    this.handleGetSetting();
    this.currentLang = i18n.locale;
    
    // 根据当前路由自动展开子菜单
    this.initOpenSubMenus();
  },
  computed: {
    displayLanguage() {
      return this.currentLang === 'zh' ? '中文' : '英文';
    }
  },
  methods: {
    hasActiveChild(link) {
      if (!link.children) return false;
      return link.children.some(child => this.$route.path === child.path);
    },
    initOpenSubMenus() {
      // 查找当前路由所在的父菜单，并自动展开
      const currentPath = this.$route.path;
      this.navLinks.forEach(link => {
        if (link.children) {
          const hasActiveChild = link.children.some(child => child.path === currentPath);
          if (hasActiveChild && !this.openSubMenus.includes(link.path)) {
            this.openSubMenus.push(link.path);
          }
        }
      });
    },
    toggleSubMenu(link) {
      const index = this.openSubMenus.indexOf(link.path);
      if (index > -1) {
        this.openSubMenus.splice(index, 1);
      } else {
        this.openSubMenus.push(link.path);
      }
    },
    handleGetSetting() {
      getSetting({}).then((res) => {
        this.setting = {
          uiLang: res.data.uiLang,
          modelName: res.data.modelName
        };
        // 更新当前语言
        this.currentLang = res.data.uiLang || 'zh';
      });
    },
    handlerHomePage() {
      this.$router.push({
        path: "/",
      });
    }
  },
  watch: {
    '$route'(to) {
      // 路由变化时，检查是否需要自动展开子菜单
      this.navLinks.forEach(link => {
        if (link.children) {
          const hasActiveChild = link.children.some(child => child.path === to.path);
          const isOpen = this.openSubMenus.includes(link.path);
          
          if (hasActiveChild && !isOpen) {
            this.openSubMenus.push(link.path);
          }
        }
      });
    }
  }
};
</script>

<style lang="scss">
.my-navbar {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: linear-gradient(180deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 16px;
  color: #232425;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  width: 100%;
}

.setting {
  margin-top: 10px;
  padding: 12px;
  display: flex;
  flex-direction: column;
  font-size: 13px;
  color: #666;
  gap: 12px;
  border-top: 1px solid #eee;

  .user-section {
    padding: 6px 0 6px 2px;
    border-bottom: 1px solid #eee;
    margin-bottom: 10px;
    min-height: 58px;
    display: flex;
    align-items: center;
    width: 100%;
    overflow: hidden;
  }

  .setting-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border-radius: 8px;
    transition: all 0.3s ease;
    
    i {
      color: #1890ff;
      font-size: 16px;
    }

    &:hover {
      background: rgba(24, 144, 255, 0.1);
    }
  }
}

.my-top {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  padding: 12px;

  .logo {
    width: 100%;
    padding: 0;
    margin-bottom: 12px;
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    transition: transform 0.3s ease;

    &:hover {
      transform: scale(1.02);
    }

    img {
      height: 28px;
      width: auto;
      max-width: 100%;
      object-fit: contain;
    }

    span {
      display: block;
      font-size: 12px;
      margin-top: 6px;
      color: #666;
      font-weight: 500;
      white-space: nowrap;
    }
  }
}

.menu {
  flex: 1;
  padding: 0;
  margin-top: 4px;
  width: 100%;
  
  .menu-item {
    padding: 10px;
    margin: 2px 0;
    border-radius: 8px;
    cursor: pointer;
    font-size: 13px;
    color: #666;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    width: 100%;
    
    i {
      margin-right: 8px;
      font-size: 16px;
      color: #999;
      transition: all 0.3s ease;
      width: 18px;
      text-align: center;
      flex-shrink: 0;
    }

    &:hover {
      background: rgba(24, 144, 255, 0.1);
      color: #1890ff;
      
      i {
        color: #1890ff;
      }
    }

    &.selected {
      background: #1890ff;
      color: white;
      font-weight: 500;
      
      i {
        color: white;
      }
    }
    
    &.parent-menu {
      display: flex;
      justify-content: space-between;
      background-color: #f5f7fa;
      font-weight: 500;
      
      .submenu-icon {
        margin-left: auto;
        margin-right: 0;
        transition: transform 0.3s;
        
        &.is-active {
          transform: rotate(180deg);
        }
      }
      
      &.has-active-child {
        background: rgba(24, 144, 255, 0.05);
        color: #1890ff;
        
        i {
          color: #1890ff;
        }
      }
    }
  }
  
  .submenu {
    margin-left: 12px;
    margin-top: 4px;
    margin-bottom: 4px;
    
    .submenu-item {
      padding: 10px 10px 10px 20px;
      margin: 4px 0;
      border-radius: 8px;
      font-size: 13px;
      transition: all 0.3s ease;
      
      i {
        font-size: 15px;
      }
      
      &:hover {
        background: rgba(24, 144, 255, 0.1);
        color: #1890ff;
        transform: translateX(5px);
        
        i {
          color: #1890ff;
        }
      }
      
      &.selected {
        background: rgba(24, 144, 255, 0.15);
        color: #1890ff;
        font-weight: 500;
        
        i {
          color: #1890ff;
        }
      }
    }
  }
}

.span-menu-item {
  color: #000 !important;
  padding-left: 24px;
  padding-top: 10px;
  padding-bottom: 10px;

  i {
    margin-right: 8px;
  }
}

.span-menu-item:hover {
  background: #f1f1f1;
}

.user-headimg {
  height: 30px;
  width: 30px;
  display: inline;
  border-radius: 30px;
  border: 1px solid #ccc;
  vertical-align: bottom;
  margin-right: 6px;
}
</style>
