from EarlyBird.api.generate import generate_blueprint
from flask_pydantic import validate
from EarlyBird.common import ApiResponse, Result
from docx.shared import Pt
from docx.oxml.ns import qn
from flask import send_file, g, request
import os
import time
import markdown
import uuid
import json
from EarlyBird.model.report_history import ReportHistory
from EarlyBird.common.database import db
import logging
from EarlyBird.common.docx import ThesisDocx
from EarlyBird.service.generate_service import GenerateService
from pydantic import BaseModel
from typing import Optional

logger = logging.getLogger(__name__)

# 表格内容生成参数模型 - 简化版本
class ParamTableContent(BaseModel):
    content: str  # 要生成表格的内容文本
    rows: Optional[int] = None  # 期望行数，None表示自动调整
    columns: Optional[int] = None  # 期望列数，None表示自动调整
    requirements: Optional[str] = ""  # 生成要求

@generate_blueprint.route("/exportReportDocx", methods=["POST"])
@validate()
def exportReportDocx(body: dict):
    try:
        import os
        import time
        
        report_type = body.get("reportType", "daily")
        report_data = body.get("reportData", {})
        report_content = body.get("reportContent", "")
        
        title = report_data.get("title", "工作报告")
        
        # 创建Word文档
        docx = ThesisDocx()
        
        # 添加封面
        docx.addCover(title)
        docx.addPageBreak()
        
        # 添加正文
        docx.addContentParagraph(title, "", 1)
        
        # 处理Markdown内容，按段落添加
        paragraphs = report_content.split("\n\n")
        for para in paragraphs:
            if para.strip().startswith("# "):
                # 一级标题
                heading = para.strip()[2:].strip()
                docx.addContentParagraph(heading, "", 2)
            elif para.strip().startswith("## "):
                # 二级标题
                heading = para.strip()[3:].strip()
                docx.addContentParagraph(heading, "", 3)
            elif para.strip().startswith("### "):
                # 三级标题
                heading = para.strip()[4:].strip()
                docx.addContentParagraph(heading, "", 4)
            else:
                # 普通段落
                paragraph = docx.doc.add_paragraph()
                paragraph.paragraph_format.line_spacing = 1.5
                paragraph.paragraph_format.space_after = Pt(10)
                paragraph.paragraph_format.first_line_indent = paragraph.style.font.size * 2
                run = paragraph.add_run(para.strip())
                run.font.size = Pt(12)
                run.font.name = "宋体"
                run._element.rPr.rFonts.set(qn("w:eastAsia"), "宋体")
        
        # 导出文档
        base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        time_str = time.strftime("%Y%m%d_%H%M%S", time.localtime())
        
        report_type_names = {
            "daily": "日报",
            "weekly": "周报",
            "monthly": "月报",
            "summary": "总结"
        }
        report_type_name = report_type_names.get(report_type, "报告")
        
        file_name = f"{report_type_name}_{title}_{time_str}.docx"
        result = docx.export(file_name, base_dir)
        
        if result.is_success():
            return ApiResponse().set_data(result.data).json()
        else:
            return ApiResponse().error(result.message).json()
    except Exception as e:
        import traceback
        traceback.print_exc()
        return ApiResponse().error(str(e)).json()

@generate_blueprint.route("/exportReportHtml", methods=["POST"])
@validate()
def exportReportHtml(body: dict):
    try:
        import os
        import time
        
        report_type = body.get("reportType", "daily")
        report_data = body.get("reportData", {})
        report_content = body.get("reportContent", "")
        
        title = report_data.get("title", "工作报告")
        
        # 将Markdown转换为HTML
        html_content = markdown.markdown(report_content)
        
        # 添加HTML头部和样式
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>{title}</title>
            <style>
                body {{
                    font-family: "微软雅黑", "宋体", Arial, sans-serif;
                    line-height: 1.6;
                    color: #333;
                    max-width: 800px;
                    margin: 0 auto;
                    padding: 20px;
                }}
                h1 {{
                    text-align: center;
                    font-size: 24px;
                    margin-top: 20px;
                    margin-bottom: 20px;
                    font-weight: bold;
                }}
                h2 {{
                    font-size: 20px;
                    margin-top: 18px;
                    margin-bottom: 15px;
                    border-bottom: 1px solid #eaecef;
                    padding-bottom: 5px;
                }}
                h3 {{
                    font-size: 18px;
                    margin-top: 15px;
                    margin-bottom: 10px;
                }}
                p {{
                    margin-bottom: 15px;
                    text-indent: 2em;
                }}
                ul, ol {{
                    padding-left: 2em;
                }}
                .report-header {{
                    text-align: center;
                    margin-bottom: 30px;
                }}
                .report-meta {{
                    text-align: center;
                    color: #666;
                    margin-bottom: 30px;
                }}
                .report-footer {{
                    margin-top: 50px;
                    text-align: center;
                    color: #666;
                    font-size: 14px;
                }}
            </style>
        </head>
        <body>
            <div class="report-header">
                <h1>{title}</h1>
            </div>
            <div class="report-meta">
                <p>生成时间：{time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())}</p>
            </div>
            <div class="report-content">
                {html_content}
            </div>
            <div class="report-footer">
                <p>本报告由AI助手自动生成</p>
            </div>
        </body>
        </html>
        """
        
        # 创建下载目录
        base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        download_dir = os.path.join(base_dir, "resources", "download")
        os.makedirs(download_dir, exist_ok=True)
        
        # 生成文件名
        time_str = time.strftime("%Y%m%d_%H%M%S", time.localtime())
        report_type_names = {
            "daily": "日报",
            "weekly": "周报",
            "monthly": "月报",
            "summary": "总结"
        }
        report_type_name = report_type_names.get(report_type, "报告")
        
        file_name = f"{report_type_name}_{title}_{time_str}.html"
        file_path = os.path.join(download_dir, file_name)
        
        # 保存HTML文件
        with open(file_path, "w", encoding="utf-8") as f:
            f.write(html)
        
        return ApiResponse().set_data({"file": file_name}).json()
    except Exception as e:
        import traceback
        traceback.print_exc()
        return ApiResponse().error(str(e)).json()

@generate_blueprint.route("/saveReportHistory", methods=["POST"])
@validate()
def saveReportHistory(body: dict):
    """保存或更新报告历史记录"""
    try:
        # 获取当前用户ID - 统一使用g.userid
        user_id = g.userid if hasattr(g, 'userid') else 0
        logger.info(f"保存报告历史记录，用户ID: {user_id}, g对象属性: {dir(g)}")
        
        # 获取请求参数
        report_id = body.get("id", None)  # 如果有ID，则为更新操作
        report_type = body.get("reportType", "daily")
        report_data = body.get("reportData", {})
        report_content = body.get("reportContent", "")
        
        # 检查内容是否为空
        if not report_content:
            logger.warning("报告内容为空，尝试从请求体中获取完整数据")
            # 尝试从请求中获取原始数据
            try:
                raw_data = request.get_json(force=True)
                report_content = raw_data.get("reportContent", "")
                logger.info(f"从原始请求中获取到内容，长度: {len(report_content)}")
            except Exception as e:
                logger.error(f"获取原始请求数据失败: {str(e)}")
        
        logger.info(f"报告数据: ID={report_id}, 类型={report_type}, 标题={report_data.get('title')}, 内容长度={len(report_content)}")
        
        if report_id:
            logger.info(f"更新已有报告，ID: {report_id}")
            # 更新已有记录
            report_history = ReportHistory.query.get(report_id)
            if not report_history:
                logger.warning(f"报告ID {report_id} 不存在")
                return ApiResponse().error("记录不存在").json()
            
            # 检查权限（只能更新自己的记录）
            if user_id and report_history.uid != user_id:
                logger.warning(f"用户 {user_id} 尝试更新其他用户的报告 {report_id}")
                return ApiResponse().error("无权操作此记录").json()
                
            # 更新记录
            report_history.report_type = report_type
            report_history.title = report_data.get("title", "未命名报告")
            report_history.content = report_content
            report_history.form_data = json.dumps(report_data)
            
            # 保存到数据库
            db.session.commit()
            logger.info(f"成功更新报告 {report_id}")
            
            return ApiResponse().set_data(report_history.to_dict()).json()
        else:
            logger.info(f"创建新报告，类型: {report_type}, 用户ID: {user_id}")
            # 创建新记录
            report_history = ReportHistory()
            report_history.uid = user_id
            report_history.report_type = report_type
            report_history.title = report_data.get("title", "未命名报告")
            report_history.content = report_content
            report_history.form_data = json.dumps(report_data)
            
            # 保存到数据库
            report_history.save()
            logger.info(f"成功创建新报告，ID: {report_history.id}, 用户ID: {report_history.uid}")
            
            return ApiResponse().set_data(report_history.to_dict()).json()
            
    except Exception as e:
        logger.exception(f"保存报告历史记录失败: {str(e)}")
        return ApiResponse().error(f"保存失败: {str(e)}").json()

@generate_blueprint.route("/getReportHistory", methods=["POST"])
@validate()
def getReportHistory(body: dict):
    """获取用户的报告历史记录"""
    try:
        # 获取当前用户ID - 统一使用g.userid
        user_id = g.userid if hasattr(g, 'userid') else 0
        logger.info(f"获取报告历史记录，用户ID: {user_id}, g对象属性: {dir(g)}")
        
        # 分页参数
        page = body.get("page", 1)
        page_size = body.get("pageSize", 20)
        
        # 查询条件
        report_type = body.get("reportType", None)
        report_id = body.get("id", None)  # 添加ID过滤
        
        # 构建查询
        query = ReportHistory.query.filter(ReportHistory.is_deleted == 0)
        
        # 始终按用户ID过滤，确保用户只能看到自己的记录
        if user_id:
            logger.info(f"按用户ID {user_id} 过滤报告")
            query = query.filter(ReportHistory.uid == user_id)
        else:
            logger.warning("未提供用户ID，将返回空列表")
            return ApiResponse().set_data({
                "total": 0,
                "list": [],
                "page": page,
                "pageSize": page_size
            }).json()
        
        # 如果指定了ID，按ID过滤
        if report_id:
            logger.info(f"按报告ID {report_id} 过滤")
            query = query.filter(ReportHistory.id == report_id)
        
        # 如果指定了报告类型，按类型过滤
        if report_type:
            logger.info(f"按报告类型 {report_type} 过滤")
            query = query.filter(ReportHistory.report_type == report_type)
        
        # 按创建时间倒序排列
        query = query.order_by(ReportHistory.create_time.desc())
        
        # 计算总数
        total = query.count()
        logger.info(f"查询到 {total} 条报告记录")
        
        # 分页查询
        offset = (page - 1) * page_size
        reports = query.offset(offset).limit(page_size).all()
        
        # 转换为字典列表
        report_list = [report.to_dict() for report in reports]
        
        logger.info(f"返回第 {page} 页，共 {len(report_list)} 条记录")
        
        return ApiResponse().set_data({
            "total": total,
            "list": report_list,
            "page": page,
            "pageSize": page_size
        }).json()
        
    except Exception as e:
        logger.exception(f"获取报告历史记录失败: {str(e)}")
        return ApiResponse().error(f"获取失败: {str(e)}").json()

@generate_blueprint.route("/deleteReportHistory", methods=["POST"])
@validate()
def deleteReportHistory(body: dict):
    """删除报告历史记录"""
    try:
        # 获取当前用户ID - 统一使用g.userid
        user_id = g.userid if hasattr(g, 'userid') else 0
        logger.info(f"删除报告历史记录，用户ID: {user_id}")
        
        # 获取报告ID
        report_id = body.get("id")
        if not report_id:
            return ApiResponse().error("缺少报告ID").json()
        
        # 查询报告记录
        report_history = ReportHistory.query.get(report_id)
        if not report_history:
            return ApiResponse().error("记录不存在").json()
        
        # 检查权限（只能删除自己的记录）
        if user_id and report_history.uid != user_id:
            logger.warning(f"用户 {user_id} 尝试删除其他用户的报告 {report_id}")
            return ApiResponse().error("无权操作此记录").json()
        
        # 软删除（设置is_deleted为1）
        report_history.is_deleted = 1
        db.session.commit()
        
        logger.info(f"成功删除报告 {report_id}")
        return ApiResponse().set_data({"success": True}).json()
        
    except Exception as e:
        logger.exception(f"删除报告历史记录失败: {str(e)}")
        return ApiResponse().error(f"删除失败: {str(e)}").json()

@generate_blueprint.route("/tableContent", methods=["POST"])
@validate()
def generate_table_content(body: ParamTableContent):
    """生成表格内容"""
    try:
        logger.info(f"收到表格内容生成请求，参数: {body}")
        
        # 验证必要参数
        if not body.content or body.content.strip() == "":
            logger.error("内容文本不能为空")
            return ApiResponse().error("内容文本不能为空").json()
        
        # 验证行数和列数范围
        if body.rows is not None:
            try:
                rows = int(body.rows)
                if rows == 0:
                    # 0表示自动调整
                    body.rows = 0
                elif rows < 1 or rows > 10:
                    logger.error(f"行数必须在1-10之间，当前值: {rows}")
                    return ApiResponse().error("行数必须在1-10之间").json()
                else:
                    body.rows = rows
            except (ValueError, TypeError):
                logger.error(f"行数必须是整数，当前值: {body.rows}")
                return ApiResponse().error("行数必须是整数").json()
        
        if body.columns is not None:
            try:
                columns = int(body.columns)
                if columns == 0:
                    # 0表示自动调整
                    body.columns = 0
                elif columns < 1 or columns > 8:
                    logger.error(f"列数必须在1-8之间，当前值: {columns}")
                    return ApiResponse().error("列数必须在1-8之间").json()
                else:
                    body.columns = columns
            except (ValueError, TypeError):
                logger.error(f"列数必须是整数，当前值: {body.columns}")
                return ApiResponse().error("列数必须是整数").json()
        
        logger.info(f"参数验证通过，开始生成表格，内容长度: {len(body.content)}")
        
        # 调用服务生成表格内容
        res: Result = GenerateService().generate_table_from_content(body)
        
        if res.isSucc():
            logger.info("表格内容生成成功")
            return ApiResponse().set_data(res.data).json()
        else:
            logger.error(f"表格内容生成失败: {res.message}")
            return ApiResponse().error(res.message).json()
            
    except Exception as e:
        logger.exception(f"生成表格内容时发生异常: {str(e)}")
        return ApiResponse().error(f"生成表格内容失败: {str(e)}").json() 