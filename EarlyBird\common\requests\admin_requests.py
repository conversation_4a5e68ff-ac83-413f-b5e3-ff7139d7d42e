"""
管理员相关请求模型
定义管理员API的request模型和schema
"""

from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime


class AdminLoginRequest(BaseModel):
    """管理员登录请求模型"""
    username: str = Field(..., description="管理员用户名")
    password: str = Field(..., description="管理员密码")


class AdminCreateRequest(BaseModel):
    """创建管理员请求模型"""
    username: str = Field(..., description="管理员用户名")
    password: str = Field(..., description="管理员密码")
    realname: Optional[str] = Field(None, description="真实姓名")
    email: Optional[str] = Field(None, description="邮箱")
    phone: Optional[str] = Field(None, description="手机号")
    role: str = Field("admin", description="角色")
    is_superadmin: bool = Field(False, description="是否为超级管理员")
    permissions: Optional[Dict[str, List[str]]] = Field(None, description="权限列表")
    is_active: bool = Field(True, description="是否激活")


class AdminUpdateRequest(BaseModel):
    """更新管理员请求模型"""
    realname: Optional[str] = Field(None, description="真实姓名")
    email: Optional[str] = Field(None, description="邮箱")
    phone: Optional[str] = Field(None, description="手机号")
    role: Optional[str] = Field(None, description="角色")
    is_superadmin: Optional[bool] = Field(None, description="是否为超级管理员")
    permissions: Optional[Dict[str, List[str]]] = Field(None, description="权限列表")
    is_active: Optional[bool] = Field(None, description="是否激活")


class AdminPasswordResetRequest(BaseModel):
    """管理员密码重置请求模型"""
    new_password: str = Field(..., description="新密码")


class AdminListRequest(BaseModel):
    """管理员列表查询请求模型"""
    page: int = Field(1, description="页码")
    size: int = Field(10, description="每页数量")
    username: Optional[str] = Field(None, description="用户名筛选")
    role: Optional[str] = Field(None, description="角色筛选")
    is_active: Optional[bool] = Field(None, description="激活状态筛选")


class UserListRequest(BaseModel):
    """用户列表查询请求模型"""
    page: int = Field(1, description="页码")
    size: int = Field(10, description="每页数量")
    username: Optional[str] = Field(None, description="用户名筛选")
    email: Optional[str] = Field(None, description="邮箱筛选")
    is_vip: Optional[bool] = Field(None, description="VIP状态筛选")


class UserUpdateRequest(BaseModel):
    """用户更新请求模型"""
    username: Optional[str] = Field(None, description="用户名")
    email: Optional[str] = Field(None, description="邮箱")
    nickname: Optional[str] = Field(None, description="昵称")
    is_active: Optional[bool] = Field(None, description="是否激活")


class UserVipRequest(BaseModel):
    """用户VIP设置请求模型"""
    vip_level: int = Field(..., description="VIP等级")
    vip_expire_time: Optional[datetime] = Field(None, description="VIP过期时间")


class ThesisListRequest(BaseModel):
    """论文列表查询请求模型"""
    page: int = Field(1, description="页码")
    size: int = Field(10, description="每页数量")
    title: Optional[str] = Field(None, description="标题筛选")
    author: Optional[str] = Field(None, description="作者筛选")
    user_id: Optional[int] = Field(None, description="用户ID筛选")


class PaymentConfigRequest(BaseModel):
    """支付配置请求模型"""
    name: str = Field(..., description="配置名称")
    config_key: str = Field(..., description="配置键")
    config_value: str = Field(..., description="配置值")
    description: Optional[str] = Field(None, description="配置描述")


class WeChatPayConfigRequest(BaseModel):
    """微信支付配置请求模型"""
    name: str = Field(..., description="配置名称")
    app_id: str = Field(..., description="微信应用ID")
    mch_id: str = Field(..., description="商户号")
    api_v3_key: str = Field(..., description="API v3密钥")
    serial_no: str = Field(..., description="证书序列号")
    private_key_path: str = Field(..., description="私钥文件路径")
    notify_url: str = Field(..., description="回调通知地址")
    is_enabled: bool = Field(True, description="是否启用")
    is_test_mode: bool = Field(False, description="是否测试模式")
    remark: Optional[str] = Field(None, description="备注")


class SystemSettingRequest(BaseModel):
    """系统设置请求模型"""
    setting_key: str = Field(..., description="设置键")
    setting_value: Any = Field(..., description="设置值")
    description: Optional[str] = Field(None, description="设置描述")


class ApiTestRequest(BaseModel):
    """API测试请求模型"""
    api_type: str = Field(..., description="API类型")
    api_url: str = Field(..., description="API地址")
    api_key: str = Field(..., description="API密钥")


class StatsQueryRequest(BaseModel):
    """统计查询请求模型"""
    start_date: Optional[str] = Field(None, description="开始日期")
    end_date: Optional[str] = Field(None, description="结束日期")
    type: Optional[str] = Field(None, description="统计类型")
