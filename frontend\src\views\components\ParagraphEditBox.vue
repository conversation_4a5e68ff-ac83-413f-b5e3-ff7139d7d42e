<template>
  <!-- Form -->
  <el-dialog title="手动编辑段落" top="5vh" height="80vh" :visible="isShow" center @close="dialogClose" :close-on-click-modal="false" :before-close="discard">
    <el-form label-position="top">
      <el-form-item label="段落标题" required>
        <el-input v-model="form.title" autocomplete="off"></el-input>
      </el-form-item>
      
      <el-form-item label="段落内容" required>
        <el-input type="textarea" v-model="form.text" autocomplete="off" style="height: 400px;" @input="onContentChange"></el-input>
      </el-form-item>
    </el-form>
    
    <div slot="footer" class="dialog-footer">
      <el-button @click="discard">放弃修改</el-button>
      <el-button type="primary" @click="save">保存内容</el-button>
    </div>
  </el-dialog>
</template>
<script>
import { saveSingleParagraph } from "@/api/thesis.js";

export default {
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
    para: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      form: {
        id: 0,
        title: "",
        text: "",
      },
    };
  },
  watch: {
    para(newVal, oldVal) {
      this.form = newVal;
    },
  },
  mounted() {
    console.log('ParagraphEditBox mounted');
  },
  methods: {
    resetForm() {
      this.$set(this.form, {
        id: 0,
        title: "",
        text: "",
      });
    },
    
    onContentChange() {
      // 内容变化时的处理
    },
    
    discard() {
      this.$confirm("此操作将放弃当前修改的内容,是否继续？", "提示", {
        confirmButtonText: "确定放弃",
        cancelButtonText: "继续编辑",
        type: "warning",
      }).then((res) => this.dialogClose());
    },
    save() {
      let loadding = this.$loading({ text: "保存中，请稍等" });
      saveSingleParagraph(this.form)
        .then((res) => {
          this.$notify.success({ title: "成功", message: "保存成功" });
          this.dialogClose();
          loadding.close();
          this.$emit("onParaUpdated");
        })
        .catch((e) => {
          this.$notify.error({ title: "错误", message: JSON.stringify(e) });
          loadding.close();
        });
    },
    dialogClose() {
      console.log("close");
      this.resetForm();
      this.$emit("onClose");
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-textarea {
  textarea {
    height: 400px !important;
  }
}

.table-toolbar {
  margin-bottom: 15px;
  padding: 15px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 8px;
  border: 1px solid #dee2e6;
  display: block !important;
  
  .el-button-group {
    margin-bottom: 10px;
    display: flex !important;
    flex-wrap: wrap;
    gap: 5px;
    
    .el-button {
      margin-right: 5px;
      border-radius: 4px;
      display: inline-block !important;
      
      &:last-child {
        margin-right: 0;
      }
    }
  }
  
  .table-toolbar-tips {
    color: #6c757d;
    font-size: 12px;
    line-height: 1.4;
    
    small {
      display: block;
      margin-top: 5px;
    }
  }
}

// 确保按钮组正确显示
::v-deep .el-button-group {
  display: flex !important;
  
  .el-button {
    display: inline-block !important;
  }
}
</style>