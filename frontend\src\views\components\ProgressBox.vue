<template>
  <div class="progress-box-wrapper" v-if="isShow">

    <div class="progress-box_bg"></div>
    <div :class="isFold?'progress-box progress-box_fold':'progress-box progress-box_unfold'">
      <div class="progress-box_title">
        <i class="el-icon-loading"></i>AI引擎逐个段落生成内容。
        <small style="display: block;margin-top:10px;">共 {{ totalParasCount }} 段,已完成 {{ doneParasCount }} 段</small>
      </div>
      <div :class="'progress-box_body ' + bodyFadeClass">
        <div :class="'task ' + p.state" v-for="(p,index) in progressList" :key="p.paraId">
          <div class="task-name">{{ (index+1) }}. {{ p.title }}</div>
          <div class="task-state">
            <i class="el-icon-close" v-if="p.state == 'e'" alt="生成错误"></i>
            <i class="el-icon-check" v-if="p.state == 's'" alt="已经生成完毕"></i>
            <i class="el-icon-timer" v-if="p.state == 'w'" alt="等待生成"></i>
            <i class="el-icon-loading" v-if="p.state == 'r'" alt="生成中"></i>
          </div>
        </div>

      </div>
      <div class="progress-box_bottom">
        <el-button type="success" plain @click.stop="handlerFold" v-if="!isFold">继续等待</el-button>
        <el-button type="success" plain @click.stop="handlerUnfold" v-if="isFold">查看内容生成进度</el-button>
        <el-button type="warning" @click.stop="handlerStop">终止生成</el-button>
      </div>

    </div>
  </div>
</template>

<script>
export default {
  props: {
    progressList: {
      default: [],
      type: Array,
    },
  },
  data() {
    return {
      title: "",
      tasks: [],
      isFold: false,
      bodyFadeClass: "",
    };
  },
  computed: {
    isShow() {
      return this.progressList && this.progressList.length > 0;
    },
    totalParasCount() {
      return this.progressList ? this.progressList.length : 0;
    },
    doneParasCount() {
      if (!this.progressList) return 0;
      let r = 0;
      for (let i in this.progressList) {
        if (this.progressList[i]["state"] != "w") {
          r += 1;
        }
      }
      return r;
    },
  },
  watch: {
    isFold(newVal, oldVal) {
      if (newVal) {
        this.bodyFadeClass = "fadeout";
      } else {
        this.bodyFadeClass = "fadein";
      }
    },
    progressList(newVal, oldVal) {
      if (newVal.length == 0 && oldVal.length > 0) {
        this.$confirm("任务运行结束", "提示", {
          confirmButtonText: "确定",
          showCancelButton: false,
          type: "success",
          center: true,
        });
      }
    },
  },
  methods: {
    handlerFold() {
      this.isFold = true;
    },
    handlerUnfold() {
      this.isFold = false;
    },
    handlerStop() {},
  },
};
</script>

<style lang="scss" scoped>
.progress-box_bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: #000000aa;
  z-index: 10;
}
.progress-box_unfold {
  bottom: 50vh;
  right: 50vw;
  width: 360px;
  height: 80vh;
  margin-bottom: -40vh;
  margin-right: -180px;
}
.progress-box_fold {
  bottom: 10px;
  right: 10px;
  width: 360px;
  height: 150px;
  margin-bottom: 0;
  margin-right: 0;
  .progress-box_title {
    border: none !important;
  }
  .progress-box_body {
    min-height: 0;
    height: 0;
    display: none;
  }
}
.progress-box {
  position: fixed;
  background: #fff;
  border-radius: 5px;
  z-index: 11;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  transition-property: top, left, bottom, right, height, margin-right,
    margin-bottom;
  transition-duration: 0.4s;

  .progress-box_title {
    padding: 20px;
    border-bottom: 1px solid #eee;
    text-align: center;

    i {
      margin-right: 10px;
    }
  }
  .progress-box_body {
    flex: 1;
    padding: 0;
    height: 300px;
    overflow: hidden;
    overflow-y: scroll;
    .task.s {
      background: #fff;
    }
    .task {
      display: flex;
      border-bottom: 1px solid #f1f1f1;
      padding: 4px 20px;
      background: #fafafa;
      .task-name {
        flex: 1;
        overflow: hidden;
        white-space: nowrap;
        line-height: 34px;
      }
      .task-state {
        padding: 5px;
        i {
          font-weight: bolder;
          font-size: 20px;
        }
        i.el-icon-check {
          color: green;
        }
        i.el-icon-loading {
          color: red;
        }
        i.el-icon-timer {
          color: #0083b0;
        }
      }
    }
  }
  .progress-box_bottom {
    display: flex;
    padding: 20px;
    border-top: 1px solid #ccc;
    .el-button {
      flex: 1;
    }
  }
}

.fadeout {
  animation: fadeout 1s;
}
.fadein {
  display: block;
  animation: fadein 1s;
}
/* 移出动画 */
@keyframes fadeout {
  0% {
    opacity: 1;
    display: block;
  }
  100% {
    opacity: 0;
    display: block;
  }
}
/* 进入动画 */
@keyframes fadein {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
</style>