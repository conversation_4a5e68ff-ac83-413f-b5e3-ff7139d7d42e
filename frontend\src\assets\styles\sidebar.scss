#app {

  .main-container {
    min-height: 100%;
    transition: margin-left .28s;
    margin-left: $sideBarWidth;
    position: relative;
  }

  .sidebar-container {
    transition: width 0.28s;
    width: $sideBarWidth !important;
    background-color: $menuBg;
    height: 100%;
    position: fixed;
    font-size: 0;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 1001;
    overflow: hidden;

    // reset element-ui css
    .horizontal-collapse-transition {
      transition: 0s width ease-in-out, 0s padding-left ease-in-out, 0s padding-right ease-in-out;
    }

    .scrollbar-wrapper {
      overflow-x: hidden !important;
    }

    .el-scrollbar__bar.is-vertical {
      right: 0;
    }

    .el-scrollbar {
      height: 100%;
    }

    &.has-logo {
      .el-scrollbar {
        height: calc(100% - 50px);
      }
    }

    .is-horizontal {
      display: none;
    }

    a {
      display: inline-block;
      width: 100%;
      overflow: hidden;
    }

    .svg-icon {
      margin-right: 16px;
    }

    .el-menu {
      border: none;
      height: 100%;
      width: 100% !important;
    }

    // menu hover
    .submenu-title-noDropdown,
    .el-submenu__title {
      &:hover {
        background-color: $menuHover !important;
      }
    }

    .is-active>.el-submenu__title {
      color: $subMenuActiveText !important;
    }

    & .nest-menu .el-submenu>.el-submenu__title,
    & .el-submenu .el-menu-item {
      min-width: $sideBarWidth !important;
      background-color: $subMenuBg !important;

      &:hover {
        background-color: $subMenuHover !important;
      }
    }
  }

  .hideSidebar {
    .sidebar-container {
      width: 54px !important;
    }

    .main-container {
      margin-left: 54px;
    }

    .submenu-title-noDropdown {
      padding: 0 !important;
      position: relative;

      .el-tooltip {
        padding: 0 !important;

        .svg-icon {
          margin-left: 20px;
        }
      }
    }

    .el-submenu {
      overflow: hidden;

      &>.el-submenu__title {
        padding: 0 !important;

        .svg-icon {
          margin-left: 20px;
        }

        .el-submenu__icon-arrow {
          display: none;
        }
      }
    }

    .el-menu--collapse {
      .el-submenu {
        &>.el-submenu__title {
          &>span {
            height: 0;
            width: 0;
            overflow: hidden;
            visibility: hidden;
            display: inline-block;
          }
        }
      }
    }
  }

  .el-menu--collapse .el-menu .el-submenu {
    min-width: $sideBarWidth !important;
  }

  // mobile responsive
  .mobile {
    .main-container {
      margin-left: 0;
    }

    .sidebar-container {
      transition: transform .28s;
      width: $sideBarWidth !important;
    }

    &.hideSidebar {
      .sidebar-container {
        pointer-events: none;
        transition-duration: 0.3s;
        transform: translate3d(-$sideBarWidth, 0, 0);
      }
    }
  }

  .withoutAnimation {

    .main-container,
    .sidebar-container {
      transition: none;
    }
  }
}

// when menu collapsed
.el-menu--vertical {
  &>.el-menu {
    .svg-icon {
      margin-right: 16px;
    }
  }

  .nest-menu .el-submenu>.el-submenu__title,
  .el-menu-item {
    &:hover {
      // you can use $subMenuHover
      background-color: $menuHover !important;
    }
  }

  // the scroll bar appears when the subMenu is too long
  >.el-menu--popup {
    max-height: 100vh;
    overflow-y: auto;

    &::-webkit-scrollbar-track-piece {
      background: #d3dce6;
    }

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #99a9bf;
      border-radius: 20px;
    }
  }
}

// 强制覆盖菜单样式 - 确保AdminLayout的设计生效
.el-menu {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
}

.el-menu-item,
.el-submenu__title {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  color: #cbd5e1 !important;
}

.el-menu-item:hover,
.el-submenu__title:hover {
  background: rgba(255, 255, 255, 0.08) !important;
  color: #f1f5f9 !important;
  border: none !important;
  box-shadow: none !important;
}

.el-menu-item.is-active,
.el-submenu__title.is-active {
  background: rgba(255, 255, 255, 0.12) !important;
  color: #f1f5f9 !important;
  border: none !important;
  box-shadow: none !important;
}

// 覆盖所有可能的Element UI菜单样式
.el-menu--vertical,
.el-menu--horizontal,
.el-menu--popup {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
}

.el-menu--vertical .el-menu-item,
.el-menu--horizontal .el-menu-item,
.el-menu--popup .el-menu-item {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  color: #cbd5e1 !important;
}

.el-menu--vertical .el-menu-item:hover,
.el-menu--horizontal .el-menu-item:hover,
.el-menu--popup .el-menu-item:hover {
  background: rgba(255, 255, 255, 0.08) !important;
  color: #f1f5f9 !important;
  border: none !important;
  box-shadow: none !important;
}

.el-menu--vertical .el-menu-item.is-active,
.el-menu--horizontal .el-menu-item.is-active,
.el-menu--popup .el-menu-item.is-active {
  background: rgba(255, 255, 255, 0.12) !important;
  color: #f1f5f9 !important;
  border: none !important;
  box-shadow: none !important;
}

// 强制覆盖Element UI的默认活跃状态样式
.el-menu-item.is-active,
.el-submenu__title.is-active {
  background: rgba(255, 255, 255, 0.12) !important;
  color: #f1f5f9 !important;
  box-shadow: none !important;
  border: none !important;
}

// 覆盖sidebar.scss中的默认样式
#app .sidebar-container {
  .el-menu {
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
  }
  
  .submenu-title-noDropdown,
  .el-submenu__title {
    &:hover {
      background: rgba(255, 255, 255, 0.08) !important;
      color: #f1f5f9 !important;
    }
  }
  
  .is-active>.el-submenu__title {
    background: rgba(255, 255, 255, 0.12) !important;
    color: #f1f5f9 !important;
  }
  
  & .nest-menu .el-submenu>.el-submenu__title,
  & .el-submenu .el-menu-item {
    background: transparent !important;
    
    &:hover {
      background: rgba(255, 255, 255, 0.08) !important;
      color: #f1f5f9 !important;
    }
    
    &.is-active {
      background: rgba(255, 255, 255, 0.12) !important;
      color: #f1f5f9 !important;
    }
  }
}
