from werkzeug.security import generate_password_hash, check_password_hash

from EarlyBird.common.libs.BaseModel import BaseModel
from sqlalchemy import (
    Integer,
    String,
    Boolean,
    Column,
    JSON,
    Enum,
    SmallInteger,
    DateTime,
    Table,
    BIGINT,
    ForeignKey,
    MetaData,
    TEXT,
)
from sqlalchemy.orm import relationship
import json
from . import TABLE_PREFIX


class OutlineHistory(BaseModel):

    __tablename__ = f"{TABLE_PREFIX}outline_history"
    __table_args__ = {"comment": "用户提纲历史记录"}

    uid = Column(Integer, nullable=False, comment="用户id")
    title = Column(String(500), comment="论文标题")
    level = Column(String(100), comment="学历水平")
    lang = Column(String(100), comment="语言")
    length = Column(String(100), comment="篇幅长度")
    paragraph_count = Column(Integer, comment="段落数量")
    second_paragraph_count = Column(Integer, comment="二级段落数量")
    outlines = Column(JSON, comment="生成的提纲列表(HTML格式)")
    raw_outlines = Column(JSON, comment="生成的提纲列表(原始JSON格式)")
    form_data = Column(JSON, comment="表单数据")

    def __repr__(self):
        return json.dumps(
            {
                "uid": self.uid,
                "title": self.title,
                "level": self.level,
                "lang": self.lang,
                "length": self.length,
                "id": self.id,
            },
            ensure_ascii=False
        ) 