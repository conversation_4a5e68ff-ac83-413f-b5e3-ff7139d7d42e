"use strict";(self["webpackChunkopen_essay_gui"]=self["webpackChunkopen_essay_gui"]||[]).push([[795],{4795:(t,a,s)=>{s.r(a),s.d(a,{default:()=>n});var e=function(){var t=this,a=t._self._c;return a("div",{staticClass:"admin-stats-users"},[a("el-card",[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("用户统计")]),a("div",{staticClass:"header-right"},[a("el-radio-group",{attrs:{size:"small"},on:{change:t.fetchUserStats},model:{value:t.period,callback:function(a){t.period=a},expression:"period"}},[a("el-radio-button",{attrs:{label:"7d"}},[t._v("最近7天")]),a("el-radio-button",{attrs:{label:"30d"}},[t._v("最近30天")]),a("el-radio-button",{attrs:{label:"90d"}},[t._v("最近90天")])],1),a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{size:"small",type:"primary",icon:"el-icon-refresh"},on:{click:t.fetchUserStats}},[t._v(" 刷新 ")])],1)]),a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}]},[a("div",{staticClass:"stat-cards"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:6}},[a("el-card",{staticClass:"stat-card",attrs:{shadow:"hover"}},[a("div",{staticClass:"stat-card-title"},[t._v("总用户数")]),a("div",{staticClass:"stat-card-value"},[t._v(t._s(t.statsData.total_users||0))])])],1),a("el-col",{attrs:{span:6}},[a("el-card",{staticClass:"stat-card",attrs:{shadow:"hover"}},[a("div",{staticClass:"stat-card-title"},[t._v("VIP用户")]),a("div",{staticClass:"stat-card-value"},[t._v(t._s(t.statsData.vip_users||0))])])],1),a("el-col",{attrs:{span:6}},[a("el-card",{staticClass:"stat-card",attrs:{shadow:"hover"}},[a("div",{staticClass:"stat-card-title"},[t._v("新增用户")]),a("div",{staticClass:"stat-card-value"},[t._v(t._s(t.statsData.new_users||0))])])],1),a("el-col",{attrs:{span:6}},[a("el-card",{staticClass:"stat-card",attrs:{shadow:"hover"}},[a("div",{staticClass:"stat-card-title"},[t._v("锁定用户")]),a("div",{staticClass:"stat-card-value"},[t._v(t._s(t.statsData.locked_users||0))])])],1)],1)],1),a("div",{staticClass:"chart-container"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:16}},[a("el-card",{staticClass:"chart-card",attrs:{shadow:"hover"}},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("每日新增用户趋势")])]),a("div",{staticClass:"chart",staticStyle:{height:"300px"},attrs:{id:"daily-users-chart"}})])],1),a("el-col",{attrs:{span:8}},[a("el-card",{staticClass:"chart-card",attrs:{shadow:"hover"}},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("用户性别分布")])]),a("div",{staticClass:"chart",staticStyle:{height:"300px"},attrs:{id:"gender-chart"}})])],1)],1),a("el-row",{staticStyle:{"margin-top":"20px"},attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-card",{staticClass:"chart-card",attrs:{shadow:"hover"}},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("VIP等级分布")])]),a("div",{staticClass:"chart",staticStyle:{height:"300px"},attrs:{id:"vip-level-chart"}})])],1),a("el-col",{attrs:{span:12}},[a("el-card",{staticClass:"chart-card",attrs:{shadow:"hover"}},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("用户数据表格")])]),a("el-table",{staticStyle:{width:"100%"},attrs:{data:t.tableData}},[a("el-table-column",{attrs:{prop:"date",label:"日期",width:"180"}}),a("el-table-column",{attrs:{prop:"count",label:"新增用户数"}})],1)],1)],1)],1)],1)])])],1)},r=[],i=s(9192),l=s(9393);const c={name:"AdminStatsUsers",data(){return{loading:!1,period:"7d",statsData:{},tableData:[],charts:{dailyUsersChart:null,genderChart:null,vipLevelChart:null}}},mounted(){this.fetchUserStats(),window.addEventListener("resize",this.resizeCharts)},beforeDestroy(){window.removeEventListener("resize",this.resizeCharts),Object.keys(this.charts).forEach((t=>{this.charts[t]&&this.charts[t].dispose()}))},methods:{async fetchUserStats(){this.loading=!0;try{const t=await i.bk.getUserStats({period:this.period});t.success?(this.statsData=t.data,this.tableData=this.statsData.daily_stats||[],this.$nextTick((()=>{this.initCharts()}))):this.$message.error(t.message||"获取用户统计数据失败")}catch(t){this.$message.error("获取用户统计数据失败："+t.message)}finally{this.loading=!1}},initCharts(){this.initDailyUsersChart(),this.initGenderChart(),this.initVipLevelChart()},initDailyUsersChart(){const t=document.getElementById("daily-users-chart");if(!t)return;this.charts.dailyUsersChart&&this.charts.dailyUsersChart.dispose(),this.charts.dailyUsersChart=l.init(t);const a=this.statsData.daily_stats||[],s=a.map((t=>t.date)),e=a.map((t=>t.count)),r={tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:[{type:"category",data:s,axisLabel:{rotate:45}}],yAxis:[{type:"value"}],series:[{name:"新增用户",data:e,type:"line",smooth:!0,itemStyle:{color:"#409EFF"},areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"rgba(64, 158, 255, 0.7)"},{offset:1,color:"rgba(64, 158, 255, 0.1)"}]}}}]};this.charts.dailyUsersChart.setOption(r)},initGenderChart(){const t=document.getElementById("gender-chart");if(!t)return;this.charts.genderChart&&this.charts.genderChart.dispose(),this.charts.genderChart=l.init(t);const a=this.statsData.gender_stats||[],s=a.map((t=>{let a=t.gender||"未知";return"male"===a&&(a="男"),"female"===a&&(a="女"),{name:a,value:t.count}})),e={tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",right:10,top:"center",data:s.map((t=>t.name))},series:[{name:"性别分布",type:"pie",radius:["50%","70%"],avoidLabelOverlap:!1,label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:"16",fontWeight:"bold"}},labelLine:{show:!1},data:s}]};this.charts.genderChart.setOption(e)},initVipLevelChart(){const t=document.getElementById("vip-level-chart");if(!t)return;this.charts.vipLevelChart&&this.charts.vipLevelChart.dispose(),this.charts.vipLevelChart=l.init(t);const a=this.statsData.vip_level_stats||[],s=a.map((t=>({name:t.level?`VIP${t.level}`:"普通用户",value:t.count}))),e={tooltip:{trigger:"item"},legend:{top:"5%",left:"center"},series:[{name:"VIP等级",type:"pie",radius:["40%","70%"],avoidLabelOverlap:!1,itemStyle:{borderRadius:10,borderColor:"#fff",borderWidth:2},label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:"16",fontWeight:"bold"}},labelLine:{show:!1},data:s}]};this.charts.vipLevelChart.setOption(e)},resizeCharts(){Object.keys(this.charts).forEach((t=>{this.charts[t]&&this.charts[t].resize()}))}}},o=c;var d=s(1656),h=(0,d.A)(o,e,r,!1,null,"696c1eb6",null);const n=h.exports}}]);