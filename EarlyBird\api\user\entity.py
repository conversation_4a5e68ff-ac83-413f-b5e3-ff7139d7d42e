from pydantic import BaseModel
from typing import Optional
from flask import current_app


class VipLogListModel(BaseModel):
    """VIP日志列表模型"""
    page: int = 1
    size: int = 10


class ApiLogListModel(BaseModel):
    """API日志列表模型"""
    page: int = 1
    size: int = 10


class LoginModel(BaseModel):
    """用户登录模型"""
    username: str
    password: str


class RegisterModel(BaseModel):
    """用户注册模型"""
    username: str
    password: str
    confirm_password: str
    nickname: Optional[str] = None
    email: Optional[str] = None
