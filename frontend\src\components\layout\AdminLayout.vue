<template>
  <div class="admin-layout">
    <!-- 侧边栏 -->
    <el-aside :width="isCollapse ? '64px' : '240px'" class="sidebar">
      <div class="sidebar-header">
        <img src="@/assets/images/logo.png" alt="Logo" class="logo">
        <span v-show="!isCollapse" class="title">早鸟论文管理</span>
      </div>
      
      <el-menu
        :default-active="$route.path"
        :collapse="isCollapse"
        :unique-opened="true"
        background-color="#001529"
        text-color="#a6adb4"
        active-text-color="#1890ff"
        router
        class="sidebar-menu"
      >
        <el-menu-item index="/admin/dashboard" class="menu-item">
          <i class="el-icon-s-home"></i>
          <span slot="title">数据概览</span>
        </el-menu-item>
        
        <el-submenu index="user" class="submenu">
          <template slot="title">
            <i class="el-icon-user"></i>
            <span>用户管理</span>
          </template>
          <el-menu-item index="/admin/users" class="submenu-item">用户列表</el-menu-item>
          <el-menu-item v-if="isSuperAdmin" index="/admin/users/accounts" class="submenu-item">账号管理</el-menu-item>
        </el-submenu>
        
        <el-submenu index="thesis" class="submenu">
          <template slot="title">
            <i class="el-icon-document"></i>
            <span>论文管理</span>
          </template>
          <el-menu-item index="/admin/thesis/list" class="submenu-item">论文列表</el-menu-item>
        </el-submenu>
        
        <el-submenu index="stats" class="submenu">
          <template slot="title">
            <i class="el-icon-s-data"></i>
            <span>系统统计</span>
          </template>
          <el-menu-item index="/admin/stats/overview" class="submenu-item">数据概览</el-menu-item>
          <el-menu-item index="/admin/stats/users" class="submenu-item">用户统计</el-menu-item>
          <el-menu-item index="/admin/stats/thesis" class="submenu-item">论文统计</el-menu-item>
          <el-menu-item index="/admin/stats/chat" class="submenu-item">聊天统计</el-menu-item>
        </el-submenu>
        
        <!-- 系统设置菜单，含二级菜单 -->
        <el-submenu index="settings" class="submenu">
          <template slot="title">
          <i class="el-icon-setting"></i>
            <span>系统设置</span>
          </template>
          <el-menu-item index="/admin/settings">基础设置</el-menu-item>
          <el-menu-item index="/admin/settings/payment">支付配置</el-menu-item>
          <el-menu-item index="/admin/settings/model">大模型配置</el-menu-item>
          <el-menu-item index="/admin/settings/orders">订单管理</el-menu-item>
        </el-submenu>
      </el-menu>
    </el-aside>
    
    <!-- 主内容区 -->
    <el-container class="main-container">
      <!-- 顶部导航栏 -->
      <el-header class="header">
        <div class="header-left">
          <el-button
            type="text"
            @click="toggleCollapse"
            class="collapse-btn"
          >
            <i :class="isCollapse ? 'el-icon-s-unfold' : 'el-icon-s-fold'"></i>
          </el-button>
          <el-breadcrumb separator="/" class="breadcrumb">
            <el-breadcrumb-item :to="{ path: '/admin/dashboard' }">首页</el-breadcrumb-item>
            <el-breadcrumb-item v-if="$route.meta.title">{{ $route.meta.title }}</el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        
        <div class="header-right">
          <el-dropdown @command="handleCommand" trigger="click">
            <div class="user-info">
              <el-avatar size="small" icon="el-icon-user"></el-avatar>
              <span class="username">管理员</span>
              <i class="el-icon-arrow-down el-icon--right"></i>
            </div>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="profile">
                <i class="el-icon-user"></i> 个人资料
              </el-dropdown-item>
              <el-dropdown-item command="logout" divided>
                <i class="el-icon-switch-button"></i> 退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </el-header>
      
      <!-- 内容区域 -->
      <el-main class="main-content">
        <router-view />
      </el-main>
    </el-container>
    
    <!-- 修改密码对话框 -->
    <el-dialog
      title="修改密码"
      :visible.sync="passwordDialogVisible"
      width="400px"
      @close="resetPasswordForm"
    >
      <el-form
        ref="passwordForm"
        :model="passwordForm"
        :rules="passwordRules"
        label-width="100px"
      >
        <el-form-item label="旧密码" prop="old_password">
          <el-input
            v-model="passwordForm.old_password"
            type="password"
            show-password
            placeholder="请输入旧密码"
          />
        </el-form-item>
        <el-form-item label="新密码" prop="new_password">
          <el-input
            v-model="passwordForm.new_password"
            type="password"
            show-password
            placeholder="请输入新密码"
          />
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="passwordDialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="passwordLoading" @click="handleChangePassword">
          确认修改
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex'
import Cookies from 'js-cookie'

export default {
  name: 'AdminLayout',
  data() {
    return {
      isCollapse: false,
      passwordDialogVisible: false,
      passwordLoading: false,
      passwordForm: {
        old_password: '',
        new_password: ''
      },
      passwordRules: {
        old_password: [
          { required: true, message: '请输入旧密码', trigger: 'blur' }
        ],
        new_password: [
          { required: true, message: '请输入新密码', trigger: 'blur' },
          { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    ...mapGetters('admin', ['adminInfo']),
    
    currentPage() {
      const routeMap = {
        '/admin/dashboard': '数据概览',
        '/admin/users': '用户管理',
        '/admin/users/stats': '用户统计',
        '/admin/users/accounts': '账号管理',
        '/admin/users/accounts/create': '创建账号',
        '/admin/users/accounts/edit': '编辑账号',
        '/admin/thesis': '论文管理',
        '/admin/thesis/stats': '论文统计',
        '/admin/stats/overview': '数据概览',
        '/admin/stats/users': '用户统计',
        '/admin/stats/thesis': '论文统计',
        '/admin/stats/chat': '聊天统计',
        '/admin/settings': '系统设置'
      }
      return routeMap[this.$route.path] || ''
    },
    isSuperAdmin() {
      return this.adminInfo && this.adminInfo.is_superadmin
    }
  },
  mounted() {
    // 获取管理员信息
    this.getAdminProfile()
  },
  methods: {
    ...mapActions('admin', ['adminLogout', 'changePassword', 'getAdminProfile']),
    
    // 切换侧边栏
    toggleSidebar() {
      this.isCollapse = !this.isCollapse
    },
    
    // 处理下拉菜单命令
    async handleCommand(command) {
      switch (command) {
        case 'profile':
          this.$message.info('个人资料功能开发中...')
          break
        case 'logout':
          await this.handleLogout()
          break
      }
    },
    
    // 处理退出登录
    async handleLogout() {
      try {
        await this.$confirm('确定要退出登录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        // 显示加载提示
        const loading = this.$loading({
          lock: true,
          text: '正在退出...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })
        
        try {
          // 确保登出前获取最新的token
          const adminToken = this.$store.getters['admin/adminToken'] || 
                            Cookies.get('admin_token') || 
                            localStorage.getItem('admin_token')
          
          if (adminToken) {
            // 手动设置请求头
            this.$axios.defaults.headers.common['Authorization'] = `Bearer ${adminToken}`
          }
          
          const result = await this.adminLogout()
          this.$message.success(result.message || '退出成功')
        } catch (error) {
          console.error('退出时发生错误:', error)
          // 即使出错也继续退出
          this.$message.info('已退出登录')
        } finally {
          loading.close()
          // 无论成功失败都跳转到登录页
          this.$router.push('/admin/login')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('退出失败:', error)
          this.$message.error('退出失败，请稍后重试')
        }
      }
    },
    
    // 处理修改密码
    async handleChangePassword() {
      try {
        const valid = await this.$refs.passwordForm.validate()
        if (!valid) return
        
        this.passwordLoading = true
        const result = await this.changePassword(this.passwordForm)
        
        if (result.success) {
          this.$message.success(result.message || '密码修改成功')
          this.passwordDialogVisible = false
          this.resetPasswordForm()
        } else {
          this.$message.error(result.message || '密码修改失败')
        }
      } catch (error) {
        console.error('修改密码失败:', error)
        this.$message.error('修改密码失败，请稍后重试')
      } finally {
        this.passwordLoading = false
      }
    },
    
    // 重置密码表单
    resetPasswordForm() {
      this.passwordForm = {
        old_password: '',
        new_password: ''
      }
      this.$refs.passwordForm && this.$refs.passwordForm.resetFields()
    },
    
    // 切换折叠状态
    toggleCollapse() {
      this.isCollapse = !this.isCollapse
    }
  }
}
</script>

<style lang="scss" scoped>
.admin-layout {
  height: 100vh;
  display: flex;
  background: #f8fafc;
  
  .sidebar {
    background: linear-gradient(180deg, #1e293b 0%, #334155 100%);
    box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
    transition: width 0.3s ease;
    position: relative;
    z-index: 1000;
    
    .sidebar-header {
      height: 70px;
      display: flex;
      align-items: center;
      padding: 0 20px;
      background: rgba(255, 255, 255, 0.05);
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      
      .logo {
        width: 36px;
        height: 36px;
        object-fit: contain;
        border-radius: 8px;
        background: white;
        padding: 4px;
        margin-right: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
      
      .title {
        color: white;
        font-size: 16px;
        font-weight: 600;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    
    .sidebar-menu {
      border: none;
      background: transparent;
      
      .menu-item {
        margin: 4px 12px;
        border-radius: 8px;
        height: 48px;
        line-height: 48px;
        color: #cbd5e1;
        transition: all 0.3s;
        
        &:hover {
          background: rgba(102, 126, 234, 0.1) !important;
          color: #667eea !important;
        }
        
        &.is-active {
          background: linear-gradient(90deg, #667eea 0%, #764ba2 100%) !important;
          color: white !important;
          box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }
        
        i {
          margin-right: 12px;
          font-size: 18px;
        }
      }
      
      .submenu {
        .el-submenu__title {
          margin: 4px 12px;
          border-radius: 8px;
          height: 48px;
          line-height: 48px;
          color: #cbd5e1;
          transition: all 0.3s;
          
          &:hover {
            background: rgba(102, 126, 234, 0.1) !important;
            color: #667eea !important;
          }
          
          i {
            margin-right: 12px;
            font-size: 18px;
          }
        }
        
        .el-menu {
          background: transparent !important;
          border: none !important;
          box-shadow: none !important;
          
          .submenu-item {
            margin: 2px 12px 2px 36px;
            border-radius: 8px;
            height: 44px;
            line-height: 44px;
            color: #94a3b8;
            transition: all 0.3s;
            
            &:hover {
              background: rgba(102, 126, 234, 0.1) !important;
              color: #667eea !important;
            }
            
            &.is-active {
              background: linear-gradient(90deg, #667eea 0%, #764ba2 100%) !important;
              color: white !important;
              box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
            }
          }
        }
      }
    }
  }
  
  .main-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #f8fafc;
    
    .header {
      background: white;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 32px;
      height: 70px;
      position: relative;
      z-index: 999;
      border-bottom: 1px solid #f1f5f9;
      
      .header-left {
        display: flex;
        align-items: center;
        
        .collapse-btn {
          margin-right: 20px;
          font-size: 20px;
          color: #64748b;
          padding: 10px;
          border-radius: 8px;
          border: none;
          background: none;
          cursor: pointer;
          transition: all 0.3s;
          
          &:hover {
            background: #f1f5f9;
            color: #667eea;
          }
        }
        
        .breadcrumb {
          font-size: 16px;
          font-weight: 600;
          color: #1e293b;
        }
      }
      
      .header-right {
        .user-info {
          display: flex;
          align-items: center;
          cursor: pointer;
          padding: 12px 16px;
          border-radius: 8px;
          transition: all 0.3s;
          background: #f8fafc;
          border: 1px solid #e2e8f0;
          
          &:hover {
            background: #f1f5f9;
            border-color: #cbd5e1;
          }
          
          .el-avatar {
            margin-right: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          }
          
          .username {
            margin: 0 8px;
            color: #1e293b;
            font-size: 14px;
            font-weight: 600;
          }
          
          i {
            color: #64748b;
            font-size: 12px;
          }
        }
      }
    }
    
    .main-content {
      flex: 1;
      overflow-y: auto;
      background: #f8fafc;
      
      .content-card {
        background: white;
        border-radius: 16px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        border: 1px solid #f1f5f9;
        min-height: calc(100vh - 140px);
        margin: 24px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .admin-layout {
    .sidebar {
      position: fixed;
      z-index: 1000;
      height: 100vh;
      transform: translateX(-100%);
      transition: transform 0.3s ease;
      
      &.show {
        transform: translateX(0);
      }
    }
    
    .main-container {
      margin-left: 0;
      
      .header {
        padding: 0 20px;
        height: 60px;
        
        .header-left {
          .collapse-btn {
            margin-right: 16px;
            font-size: 18px;
            padding: 8px;
          }
          
          .breadcrumb {
            font-size: 14px;
          }
        }
        
        .header-right {
          .user-info {
            padding: 8px 12px;
            
            .username {
              display: none;
            }
          }
        }
      }
      
      .main-content {
        .content-card {
          margin: 16px;
          border-radius: 12px;
        }
      }
    }
  }
}

// 优化二级菜单缩进
::v-deep .el-menu--inline .el-menu-item {
  padding-left: 32px !important;
}
// 优化三级菜单缩进（如有）
::v-deep .el-menu--inline .el-menu--inline .el-menu-item {
  padding-left: 48px !important;
}
</style> 