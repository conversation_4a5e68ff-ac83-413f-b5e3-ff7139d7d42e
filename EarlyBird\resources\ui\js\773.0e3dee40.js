"use strict";(self["webpackChunkopen_essay_gui"]=self["webpackChunkopen_essay_gui"]||[]).push([[773],{6154:(e,s,r)=>{r.r(s),r.d(s,{default:()=>c});var a=function(){var e=this,s=e._self._c;return s("div",{staticClass:"admin-account-edit-container"},[s("el-card",{staticClass:"box-card"},[s("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[s("span",[e._v(e._s(e.isEdit?"编辑管理员":"创建管理员"))])]),s("el-form",{ref:"form",staticClass:"form",attrs:{model:e.form,rules:e.rules,"label-width":"120px"}},[s("el-form-item",{attrs:{label:"用户名",prop:"username",disabled:e.isEdit}},[s("el-input",{attrs:{disabled:e.isEdit,placeholder:"请输入用户名"},model:{value:e.form.username,callback:function(s){e.$set(e.form,"username",s)},expression:"form.username"}})],1),e.isEdit?e._e():s("el-form-item",{attrs:{label:"密码",prop:"password"}},[s("el-input",{attrs:{type:"password","show-password":"",placeholder:"请输入密码"},model:{value:e.form.password,callback:function(s){e.$set(e.form,"password",s)},expression:"form.password"}})],1),e.isEdit?e._e():s("el-form-item",{attrs:{label:"确认密码",prop:"confirm_password"}},[s("el-input",{attrs:{type:"password","show-password":"",placeholder:"请确认密码"},model:{value:e.form.confirm_password,callback:function(s){e.$set(e.form,"confirm_password",s)},expression:"form.confirm_password"}})],1),s("el-form-item",{attrs:{label:"姓名",prop:"realname"}},[s("el-input",{attrs:{placeholder:"请输入姓名"},model:{value:e.form.realname,callback:function(s){e.$set(e.form,"realname",s)},expression:"form.realname"}})],1),s("el-form-item",{attrs:{label:"邮箱",prop:"email"}},[s("el-input",{attrs:{placeholder:"请输入邮箱"},model:{value:e.form.email,callback:function(s){e.$set(e.form,"email",s)},expression:"form.email"}})],1),s("el-form-item",{attrs:{label:"电话",prop:"phone"}},[s("el-input",{attrs:{placeholder:"请输入电话"},model:{value:e.form.phone,callback:function(s){e.$set(e.form,"phone",s)},expression:"form.phone"}})],1),s("el-form-item",{attrs:{label:"状态",prop:"is_active"}},[s("el-switch",{attrs:{"active-text":"启用","inactive-text":"禁用"},model:{value:e.form.is_active,callback:function(s){e.$set(e.form,"is_active",s)},expression:"form.is_active"}})],1),s("el-form-item",[s("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("保存")]),s("el-button",{on:{click:e.cancel}},[e._v("取消")])],1)],1)],1)],1)},t=[],i=r(9192);const o={name:"AdminAccountEdit",data(){const e=(e,s,r)=>{s!==this.form.password?r(new Error("两次输入密码不一致")):r()};return{isEdit:!1,adminId:null,form:{username:"",password:"",confirm_password:"",realname:"",email:"",phone:"",is_active:!0},rules:{username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:20,message:"长度在 3 到 20 个字符",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,message:"密码长度不能少于6个字符",trigger:"blur"}],confirm_password:[{required:!0,message:"请确认密码",trigger:"blur"},{validator:e,trigger:"blur"}],email:[{type:"email",message:"请输入正确的邮箱地址",trigger:"blur"}],phone:[{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}]}}},created(){this.$route.params.id&&(this.isEdit=!0,this.adminId=this.$route.params.id,this.getAdminDetail())},methods:{getAdminDetail(){i.jC.getDetail(this.adminId).then((e=>{if(e.success){const s=e.data;this.form={username:s.username,realname:s.realname||"",email:s.email||"",phone:s.phone||"",is_active:s.is_active}}else this.$message.error(e.message||"获取管理员信息失败")}))},submitForm(){this.$refs.form.validate((e=>{if(e)if(this.isEdit){const e={realname:this.form.realname,email:this.form.email,phone:this.form.phone,is_active:this.form.is_active};i.jC.update(this.adminId,e).then((e=>{e.success?(this.$message.success("管理员信息更新成功"),this.$router.push({name:"AdminAccountsList"})):this.$message.error(e.message||"管理员信息更新失败")}))}else{const e={username:this.form.username,password:this.form.password,realname:this.form.realname,email:this.form.email,phone:this.form.phone,is_active:this.form.is_active};i.jC.create(e).then((e=>{e.success?(this.$message.success("管理员创建成功"),this.$router.push({name:"AdminAccountsList"})):this.$message.error(e.message||"管理员创建失败")}))}}))},cancel(){this.$router.push({name:"AdminAccountsList"})}}},m=o;var l=r(1656),n=(0,l.A)(m,a,t,!1,null,"2ae70c77",null);const c=n.exports}}]);