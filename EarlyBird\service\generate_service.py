import logging
import random
import os
from EarlyBird.common import Result
from EarlyBird.common.ai import getAdapter, AiQuery

logger = logging.getLogger(__name__)

class GenerateService:
    """生成服务类"""
    
    def __init__(self):
        # 支持通过环境变量AI_MODEL切换大模型，默认qianwen
        model_name = os.environ.get('AI_MODEL', 'qianwen')
        self.ai_adapter = getAdapter(model_name)
    
    def generate_table_content(self, params):
        """生成表格内容
        
        Args:
            params: ParamTableContent对象，包含表格生成参数
            
        Returns:
            Result: 包含生成的表格数据
        """
        try:
            logger.info(f"开始生成表格内容，参数: {params}")
            
            # 限制行数在合理范围内
            if params.rows > 5:
                params.rows = 5
                logger.info(f"行数超过5，自动调整为5行")
            
            # 根据数据类型和主题生成表格内容
            table_data = self._generate_table_data(params)
            
            result_data = {
                "tableData": table_data,
                "title": params.theme,
                "style": "standard"
            }
            
            logger.info("表格内容生成成功")
            return Result.success(result_data)
            
        except Exception as e:
            logger.error(f"生成表格内容失败: {str(e)}")
            return Result.error(f"生成表格内容失败: {str(e)}")
    
    def _generate_table_data(self, params):
        """根据参数生成表格数据
        
        Args:
            params: ParamTableContent对象
            
        Returns:
            list: 二维数组，表示表格数据
        """
        rows = params.rows
        columns = params.columns
        data_type = params.dataType
        theme = params.theme
        requirements = params.requirements
        
        # 生成表头
        headers = self._generate_headers(columns, theme, data_type)
        
        # 生成表格内容
        table_data = [headers]
        
        for i in range(rows):
            row_data = []
            for j in range(columns):
                cell_content = self._generate_cell_content(i, j, data_type, theme, requirements)
                row_data.append(cell_content)
            table_data.append(row_data)
        
        return table_data
    
    def _generate_headers(self, columns, theme, data_type):
        """生成表头
        
        Args:
            columns: 列数
            theme: 主题
            data_type: 数据类型
            
        Returns:
            list: 表头数组
        """
        headers = []
        
        if data_type == "numeric":
            # 数值数据表头
            header_templates = [
                ["项目", "数值", "百分比", "增长率"],
                ["指标", "当前值", "目标值", "完成率"],
                ["类别", "数量", "金额", "占比"],
                ["时间", "销售额", "利润", "利润率"]
            ]
        elif data_type == "text":
            # 文本数据表头
            header_templates = [
                ["项目", "描述", "状态", "备注"],
                ["功能", "说明", "优先级", "负责人"],
                ["问题", "原因", "解决方案", "状态"],
                ["任务", "内容", "进度", "结果"]
            ]
        elif data_type == "comparison":
            # 对比数据表头
            header_templates = [
                ["项目", "方案A", "方案B", "推荐"],
                ["指标", "对照组", "实验组", "差异"],
                ["产品", "价格", "功能", "评分"],
                ["方法", "优点", "缺点", "适用场景"]
            ]
        else:
            # 混合数据表头
            header_templates = [
                ["项目", "类型", "数值", "说明"],
                ["名称", "分类", "数量", "备注"],
                ["指标", "单位", "数值", "状态"],
                ["项目", "描述", "进度", "结果"]
            ]
        
        # 根据主题选择合适的模板
        template = random.choice(header_templates)
        
        # 根据列数调整表头
        if columns <= len(template):
            headers = template[:columns]
        else:
            headers = template + [f"列{i+1}" for i in range(len(template), columns)]
        
        return headers
    
    def _generate_cell_content(self, row, col, data_type, theme, requirements):
        """生成单元格内容
        
        Args:
            row: 行索引
            col: 列索引
            data_type: 数据类型
            theme: 主题
            requirements: 要求
            
        Returns:
            str: 单元格内容
        """
        if data_type == "numeric":
            # 生成数值数据
            if col == 0:
                # 第一列通常是项目名称，根据主题生成更具体的项目名
                project_names = self._get_project_names_by_theme(theme, row)
                return project_names[row] if row < len(project_names) else f"项目{row+1}"
            else:
                # 其他列生成数值
                if "百分比" in theme or "率" in theme:
                    return f"{random.randint(60, 100)}%"
                elif "金额" in theme or "价格" in theme:
                    return f"¥{random.randint(100, 10000)}"
                else:
                    return str(random.randint(10, 1000))
        
        elif data_type == "text":
            # 生成文本数据
            if col == 0:
                project_names = self._get_project_names_by_theme(theme, row)
                return project_names[row] if row < len(project_names) else f"项目{row+1}"
            elif col == 1:
                descriptions = self._get_descriptions_by_theme(theme, row)
                return descriptions[row] if row < len(descriptions) else f"这是项目{row+1}的详细描述"
            elif col == 2:
                statuses = ["进行中", "已完成", "待开始", "已暂停"]
                return random.choice(statuses)
            else:
                return f"备注信息{row+1}"
        
        elif data_type == "comparison":
            # 生成对比数据
            if col == 0:
                comparison_items = self._get_comparison_items_by_theme(theme, row)
                return comparison_items[row] if row < len(comparison_items) else f"对比项{row+1}"
            elif col == 1:
                return f"方案A-{row+1}"
            elif col == 2:
                return f"方案B-{row+1}"
            else:
                recommendations = ["推荐A", "推荐B", "均可", "需评估"]
                return random.choice(recommendations)
        
        else:
            # 生成混合数据
            if col == 0:
                project_names = self._get_project_names_by_theme(theme, row)
                return project_names[row] if row < len(project_names) else f"项目{row+1}"
            elif col == 1:
                types = ["类型A", "类型B", "类型C", "类型D"]
                return random.choice(types)
            elif col == 2:
                return str(random.randint(10, 100))
            else:
                return f"说明{row+1}"
    
    def _get_project_names_by_theme(self, theme, max_count=5):
        """根据主题获取项目名称列表"""
        theme_lower = theme.lower()
        
        if "研究" in theme_lower or "实验" in theme_lower:
            return ["实验组A", "实验组B", "对照组", "空白组", "验证组"]
        elif "调查" in theme_lower or "问卷" in theme_lower:
            return ["问题1", "问题2", "问题3", "问题4", "问题5"]
        elif "对比" in theme_lower or "比较" in theme_lower:
            return ["指标1", "指标2", "指标3", "指标4", "指标5"]
        elif "预算" in theme_lower or "成本" in theme_lower:
            return ["人工成本", "材料成本", "设备成本", "管理成本", "其他成本"]
        elif "时间" in theme_lower or "进度" in theme_lower:
            return ["阶段1", "阶段2", "阶段3", "阶段4", "阶段5"]
        elif "数据" in theme_lower or "分析" in theme_lower:
            return ["数据项1", "数据项2", "数据项3", "数据项4", "数据项5"]
        else:
            return ["核心指标", "重要指标", "辅助指标", "参考指标", "综合指标"]
    
    def _get_descriptions_by_theme(self, theme, max_count=5):
        """根据主题获取描述列表"""
        theme_lower = theme.lower()
        
        if "研究" in theme_lower or "实验" in theme_lower:
            return [
                "实验组A的详细实验过程和数据收集",
                "实验组B的对比实验设置和结果",
                "对照组的标准实验流程",
                "空白组的基准数据记录",
                "验证组的重复实验验证"
            ]
        elif "调查" in theme_lower or "问卷" in theme_lower:
            return [
                "关于用户满意度的调查问题",
                "关于产品功能的评价问题",
                "关于服务质量的反馈问题",
                "关于改进建议的收集问题",
                "关于整体体验的综合问题"
            ]
        else:
            return [
                f"项目{row+1}的详细说明和背景介绍",
                f"项目{row+1}的具体实施步骤",
                f"项目{row+1}的关键要点总结",
                f"项目{row+1}的注意事项提醒",
                f"项目{row+1}的预期效果评估"
            ]
    
    def _get_comparison_items_by_theme(self, theme, max_count=5):
        """根据主题获取对比项目列表"""
        theme_lower = theme.lower()
        
        if "方案" in theme_lower:
            return ["技术方案", "成本方案", "时间方案", "质量方案", "风险方案"]
        elif "产品" in theme_lower:
            return ["功能对比", "性能对比", "价格对比", "服务对比", "口碑对比"]
        elif "方法" in theme_lower:
            return ["传统方法", "创新方法", "混合方法", "优化方法", "替代方法"]
        else:
            return ["对比维度1", "对比维度2", "对比维度3", "对比维度4", "对比维度5"]
    
    def generate_table_from_content(self, params):
        """根据内容文本生成表格
        
        Args:
            params: ParamTableContent对象，包含内容文本和表格参数
            
        Returns:
            Result: 包含生成的表格数据
        """
        try:
            logger.info(f"开始根据内容生成表格，内容长度: {len(params.content)}")
            prompt = self._build_table_prompt(params)
            # 用统一的query接口调用真实大模型
            ai_query = AiQuery(userMessage=prompt)
            ai_result = self.ai_adapter.query(ai_query)
            if not ai_result.isValid or not ai_result.text or not ai_result.text.strip():
                logger.error(f"AI返回空响应或失败: {ai_result.errMessage}")
                return Result.error(f"AI生成失败: {ai_result.errMessage or '无返回内容'}")
            # 解析AI返回的表格数据
            table_data = self._parse_table_response(ai_result.text, params.rows, params.columns)
            
            # 提取标题和实际表格数据
            table_title = "智能生成表格"
            actual_table_data = table_data
            
            # 检查第一行是否包含标题
            if table_data and len(table_data) > 0 and len(table_data[0]) > 0:
                first_cell = table_data[0][0]
                if first_cell.startswith('__TITLE__'):
                    table_title = first_cell.replace('__TITLE__', '')
                    actual_table_data = table_data[1:]  # 移除标题行
            
            result_data = {
                "tableData": actual_table_data,
                "title": table_title,
                "style": "standard"
            }
            logger.info("表格内容生成成功")
            return Result.success(result_data)
        except Exception as e:
            logger.error(f"根据内容生成表格失败: {str(e)}")
            return Result.error(f"生成表格失败: {str(e)}")
    
    def _build_table_prompt(self, params):
        """构建AI提示词"""
        # 判断是否需要自动调整表格大小
        auto_rows = params.rows == 0 or params.rows is None
        auto_columns = params.columns == 0 or params.columns is None
        
        if auto_rows and auto_columns:
            size_instruction = "请根据内容自动确定合适的表格行数和列数"
        elif auto_rows:
            size_instruction = f"表格应该有{params.columns}列，行数请根据内容自动确定"
        elif auto_columns:
            size_instruction = f"表格应该有{params.rows}行，列数请根据内容自动确定"
        else:
            size_instruction = f"表格应该有{params.rows}行，{params.columns}列"
        
        prompt = f"""
请根据以下内容生成一个表格，要求：
1. {size_instruction}
2. 从内容中提取关键信息作为表格数据
3. 第一行作为表头，其余行作为数据
4. 表格内容要准确反映原文信息
5. 只返回一个表格，不要返回多个表格
6. 严格按照CSV格式返回，每行用逗号分隔，不要包含空行或说明文字
7. 请为表格生成一个简洁、准确的标题，标题应该概括表格的主要内容

内容：
{params.content}

额外要求：{params.requirements if params.requirements else '无'}

请按以下格式返回（不要包含任何其他文字）：
标题：表格标题
表头1,表头2,表头3
数据1,数据2,数据3
数据4,数据5,数据6
...
"""
        return prompt
    
    def _parse_table_response(self, response, expected_rows, expected_columns):
        """解析AI返回的表格数据"""
        try:
            lines = response.strip().split('\n')
            table_data = []
            current_table = []
            table_title = "智能生成表格"  # 默认标题
            
            for line in lines:
                line = line.strip()
                if not line:
                    # 空行表示表格分隔，保存当前表格
                    if current_table:
                        if len(current_table) > len(table_data):
                            table_data = current_table
                        current_table = []
                    continue
                
                # 解析标题行
                if line.startswith('标题：'):
                    table_title = line.replace('标题：', '').strip()
                    continue
                
                # 跳过说明文字行
                if line.startswith('虽然') or line.startswith('请') or line.startswith('额外') or '要求' in line:
                    continue
                
                # 检查是否是表格行（包含逗号）
                if ',' in line:
                    # 按逗号分割，处理可能的引号
                    row = []
                    current_cell = ""
                    in_quotes = False
                    
                    for char in line:
                        if char == '"':
                            in_quotes = not in_quotes
                        elif char == ',' and not in_quotes:
                            row.append(current_cell.strip())
                            current_cell = ""
                        else:
                            current_cell += char
                    
                    # 添加最后一个单元格
                    row.append(current_cell.strip())
                    
                    # 过滤掉空行（所有单元格都为空的行）
                    if any(cell.strip() for cell in row):
                        current_table.append(row)
            
            # 处理最后一个表格
            if current_table and len(current_table) > len(table_data):
                table_data = current_table
            
            # 如果没有找到有效表格，尝试直接解析
            if not table_data:
                for line in lines:
                    line = line.strip()
                    if line and ',' in line and not line.startswith('标题：'):
                        # 按逗号分割
                        row = [cell.strip() for cell in line.split(',')]
                        if any(cell for cell in row):  # 确保不是空行
                            table_data.append(row)
            
            logger.info(f"解析到表格数据，原始行数: {len(table_data)}, 标题: {table_title}")
            
            # 检查是否需要自动调整
            auto_rows = expected_rows == 0 or expected_rows is None
            auto_columns = expected_columns == 0 or expected_columns is None
            
            # 如果行列数都是自动调整，直接返回AI解析的表格
            if auto_rows and auto_columns:
                logger.info(f"自动调整模式，直接返回AI解析的表格，行数: {len(table_data)}, 列数: {len(table_data[0]) if table_data else 0}")
                # 将标题添加到表格数据中，作为特殊标记
                if table_data:
                    table_data.insert(0, [f"__TITLE__{table_title}"])
                return table_data
            
            # 优先保留AI生成的实际数据，只在数据不足时才进行调整
            if table_data:
                # 调整列数
                if not auto_columns:
                    # 如果AI生成的列数不足，才补充
                    for i, row in enumerate(table_data):
                        while len(row) < expected_columns:
                            row.append(f"列{len(row)+1}")
                        # 如果AI生成的列数超过期望，保留前expected_columns列
                        if len(row) > expected_columns:
                            table_data[i] = row[:expected_columns]
                
                # 调整行数
                if not auto_rows:
                    # expected_rows是总行数（包括表头），所以数据行数是expected_rows-1
                    expected_data_rows = expected_rows - 1
                    actual_data_rows = len(table_data) - 1  # 减去表头
                    
                    if actual_data_rows < expected_data_rows:
                        # 数据行数不足，补充数据行
                        while len(table_data) < expected_rows:
                            new_row = [f"行{len(table_data)}列{j+1}" for j in range(expected_columns if not auto_columns else len(table_data[0]))]
                            table_data.append(new_row)
                    elif actual_data_rows > expected_data_rows:
                        # 数据行数过多，保留表头+期望的数据行数
                        table_data = table_data[:expected_rows]
                
                logger.info(f"最终表格数据，行数: {len(table_data)}, 列数: {len(table_data[0]) if table_data else 0}")
                # 将标题添加到表格数据中，作为特殊标记
                if table_data:
                    table_data.insert(0, [f"__TITLE__{table_title}"])
                return table_data
            else:
                logger.warning("未解析到有效表格数据，使用默认表格")
                # 如果都是自动调整，使用合理的默认值
                if auto_rows and auto_columns:
                    result = self._generate_default_table(3, 3)  # 默认3行3列
                    result.insert(0, [f"__TITLE__{table_title}"])
                    return result
                elif auto_rows:
                    result = self._generate_default_table(3, expected_columns)
                    result.insert(0, [f"__TITLE__{table_title}"])
                    return result
                elif auto_columns:
                    result = self._generate_default_table(expected_rows, 3)
                    result.insert(0, [f"__TITLE__{table_title}"])
                    return result
                else:
                    result = self._generate_default_table(expected_rows, expected_columns)
                    result.insert(0, [f"__TITLE__{table_title}"])
                    return result
            
        except Exception as e:
            logger.error(f"解析表格响应失败: {str(e)}")
            # 返回默认表格
            if auto_rows and auto_columns:
                result = self._generate_default_table(3, 3)
                result.insert(0, [f"__TITLE__{table_title}"])
                return result
            elif auto_rows:
                result = self._generate_default_table(3, expected_columns)
                result.insert(0, [f"__TITLE__{table_title}"])
                return result
            elif auto_columns:
                result = self._generate_default_table(expected_rows, 3)
                result.insert(0, [f"__TITLE__{table_title}"])
                return result
            else:
                result = self._generate_default_table(expected_rows, expected_columns)
                result.insert(0, [f"__TITLE__{table_title}"])
                return result
    
    def _generate_default_table(self, rows, columns):
        """生成默认表格"""
        table_data = []
        
        # 生成表头
        header = [f"列{i+1}" for i in range(columns)]
        table_data.append(header)
        
        # 生成数据行 - rows是总行数，所以数据行数是rows-1
        for i in range(rows - 1):
            row = [f"数据{i+1}-{j+1}" for j in range(columns)]
            table_data.append(row)
        
        return table_data 