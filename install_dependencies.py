#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python依赖批量安装脚本
自动安装项目所需的所有Python依赖包
"""

import subprocess
import sys
import os
import platform

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ 错误: 需要Python 3.8或更高版本")
        print(f"当前版本: Python {version.major}.{version.minor}.{version.micro}")
        return False
    print(f"✅ Python版本检查通过: {version.major}.{version.minor}.{version.micro}")
    return True

def check_pip():
    """检查pip是否可用"""
    try:
        subprocess.run([sys.executable, "-m", "pip", "--version"], 
                      check=True, capture_output=True)
        print("✅ pip检查通过")
        return True
    except subprocess.CalledProcessError:
        print("❌ 错误: pip不可用，请先安装pip")
        return False

def upgrade_pip():
    """升级pip到最新版本"""
    print("🔄 正在升级pip...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                      check=True)
        print("✅ pip升级成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"⚠️  pip升级失败: {e}")
        return False

def install_requirements():
    """安装requirements.txt中的依赖"""
    requirements_file = "requirements.txt"
    
    if not os.path.exists(requirements_file):
        print(f"❌ 错误: 找不到{requirements_file}文件")
        return False
    
    print(f"📦 正在安装{requirements_file}中的依赖...")
    
    try:
        # 使用pip安装依赖
        cmd = [sys.executable, "-m", "pip", "install", "-r", requirements_file]
        
        # 在中国大陆可以使用国内镜像源加速
        if input("是否使用清华大学镜像源加速安装？(y/n): ").lower() == 'y':
            cmd.extend(["-i", "https://pypi.tuna.tsinghua.edu.cn/simple"])
        
        subprocess.run(cmd, check=True)
        print("✅ 依赖安装成功")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖安装失败: {e}")
        return False

def install_windows_specific():
    """安装Windows特定的依赖"""
    if platform.system() != "Windows":
        return True
    
    print("🪟 检测到Windows系统，安装Windows特定依赖...")
    
    windows_packages = [
        "pywin32==306",
        "WMI==1.5.1"
    ]
    
    for package in windows_packages:
        try:
            print(f"安装 {package}...")
            subprocess.run([sys.executable, "-m", "pip", "install", package], 
                          check=True, capture_output=True)
            print(f"✅ {package} 安装成功")
        except subprocess.CalledProcessError as e:
            print(f"⚠️  {package} 安装失败: {e}")
    
    return True

def verify_installation():
    """验证关键包是否安装成功"""
    print("🔍 验证关键包安装...")
    
    key_packages = [
        "flask",
        "sqlalchemy", 
        "requests",
        "pydantic",
        "cryptography",
        "docx",
        "beautifulsoup4"
    ]
    
    failed_packages = []
    
    for package in key_packages:
        try:
            __import__(package.replace("-", "_"))
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package}")
            failed_packages.append(package)
    
    if failed_packages:
        print(f"\n⚠️  以下包安装失败: {', '.join(failed_packages)}")
        print("请手动安装这些包或检查错误信息")
        return False
    
    print("\n🎉 所有关键包验证通过！")
    return True

def main():
    """主函数"""
    print("=" * 50)
    print("🚀 早鸟论文系统 - Python依赖安装脚本")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 检查pip
    if not check_pip():
        sys.exit(1)
    
    # 升级pip
    upgrade_pip()
    
    # 安装依赖
    if not install_requirements():
        sys.exit(1)
    
    # 安装Windows特定依赖
    install_windows_specific()
    
    # 验证安装
    if verify_installation():
        print("\n🎉 依赖安装完成！现在可以运行项目了。")
        print("\n启动命令:")
        print("  python EarlyBird/server.py")
        print("  或")
        print("  python EarlyBird/web_server.py")
    else:
        print("\n⚠️  部分依赖安装可能有问题，请检查上面的错误信息")
        sys.exit(1)

if __name__ == "__main__":
    main()
