//定义了和服务器端交互的接口
//具体的提交格式在utils/request中进行了定义
//request.js里面实际上没有request的定义字样，实际上import的是export defaut的部分，也就是 export default service
import request from '@/utils/request'
import qs from 'qs'


export function generateThanks(params) {
    return request({
        url: '/api/talk/generateThanks',
        method: 'post',
        data: params,
        timeout: 500000,
    })
}

export function generateReference(params) {
    return request({
        url: '/api/talk/generateReference',
        method: 'post',
        data: params,
        timeout: 500000,
    })
}
export function exportOutline(params) {
    return request({
        url: '/api/talk/exportOutline',
        method: 'post',
        data: params,
    })
}

export function stopGenerateAll(params) {
    return request({
        url: '/api/talk/stopGenerateAll',
        method: 'post',
        data: params,
        timeout: 48000,
    })
}
export function generateAll(params) {
    return request({
        url: '/api/talk/generateAll',
        method: 'post',
        data: params,
        timeout: 480000,

    })
}
export function getTitle(params) {
    //将HTTP Request请求的 Response作为返回
    return request({
        url: '/api/talk/getTitle',
        method: 'post',
        data: params,
        timeout: 50000,

    })
}

export function getOutline(data) {
    //将HTTP Request请求的 Response作为返回
    return request({
        url: '/api/talk/getOutline',
        method: 'post',
        timeout: 500000,
        data
    })
}

export function getContent(data) {
    //将HTTP Request请求的 Response作为返回
    return request({
        url: '/api/talk/getContent',
        method: 'post',
        data
    })
}


export function select4ContentApi(data) {
    //将HTTP Request请求的 Response作为返回
    console.log('发送select4Content请求，数据:', JSON.stringify(data));
    
    // 确保outline字段是一个有效的对象
    if (!data.outline || typeof data.outline !== 'object') {
        console.error('提纲数据无效:', data.outline);
        data.outline = { title: data.title, subtitle: [] };
    }
    
    // 确保outline有title字段
    if (!data.outline.title) {
        console.log('提纲缺少title字段，使用论文标题');
        data.outline.title = data.title;
    }
    
    // 确保outline有subtitle字段且为数组
    if (!data.outline.subtitle || !Array.isArray(data.outline.subtitle)) {
        console.log('提纲缺少subtitle字段或非数组，创建空数组');
        data.outline.subtitle = [];
    }
    
    return request({
        url: '/api/talk/select4Content',
        method: 'post',
        data,
        timeout: 30000, // 增加超时时间
    }).catch(error => {
        console.error('select4Content请求失败:', error);
        
        // 处理特定错误
        if (error === '已有论文') {
            // 这个错误已经在request.js中处理过了，不需要再次显示错误提示
            return Promise.reject(error);
        }
        
        // 其他错误
        if (error.response && error.response.status === 400) {
            // 获取错误消息
            let errorMsg = '请求参数错误';
            
            if (error.response.data && error.response.data.message) {
                errorMsg = error.response.data.message;
            }
            
            // 根据错误消息提供更友好的提示
            if (errorMsg.includes('免费用户最多只能创建一篇论文')) {
                return Promise.reject('免费用户最多只能创建一篇论文，请先删除已有论文再继续');
            } else {
                return Promise.reject('您已经选择了一个提纲生成论文，请先完成或删除当前论文再选择新的提纲');
            }
        }
        
        throw error;
    });
}

export function generateSingleParagraph(params) {
    return request({
        url: '/api/talk/generateSingleParagraph',
        method: 'post',
        data: params,
        timeout: 80000,

    })
}

export function generateDigest(params) {
    return request({
        url: '/api/talk/generateDigest',
        method: 'post',
        data: params,
        timeout: 160000,

    })
}

// 获取用户选题历史
export function getTitleHistory(data = {}) {
    // 添加默认的分页参数
    const params = {
        pageNo: 1,
        pageSize: 10,
        ...data
    };
    
    return request({
        url: '/api/talk/getTitleHistory',
        method: 'post',
        data: params
    })
}

// 获取用户提纲历史
export function getOutlineHistory(data = {}) {
    // 添加默认的分页参数
    const params = {
        pageNo: 1,
        pageSize: 10,
        ...data
    };
    
    return request({
        url: '/api/talk/getOutlineHistory',
        method: 'post',
        data: params
    })
}

export function generateReport(params) {
    return request({
        url: '/api/talk/generateReport',
        method: 'post',
        data: params,
        timeout: 60000, // 60秒超时
    })
}

// 导出报告为Word格式
export function exportReportDocx(data) {
  return request({
    url: '/api/generate/exportReportDocx',
    method: 'post',
    data
  })
}

// 导出报告为HTML格式
export function exportReportHtml(data) {
  return request({
    url: '/api/generate/exportReportHtml',
    method: 'post',
    data
  })
}

// 保存报告历史记录
export function saveReportHistory(data) {
  return request({
    url: '/api/generate/saveReportHistory',
    method: 'post',
    data
  })
}

/**
 * 获取报告历史记录
 * @param {Object} data - 分页参数
 * @returns {Promise}
 */
export function getReportHistory(data = {}) {
  return request({
    url: '/api/talk/getReportHistory',
    method: 'post',
    data
  })
}

/**
 * 删除报告
 * @param {Object} data - 包含reportId
 * @returns {Promise}
 */
export function deleteReport(data) {
  return request({
    url: '/api/talk/deleteReport',
    method: 'post',
    data
  })
}

// 检查用户是否已有论文
export function checkExistingThesis() {
    return request({
        url: '/api/talk/checkExistingThesis',
        method: 'post',
        data: {}
    }).catch(error => {
        console.error('检查现有论文失败:', error);
        throw error;
    });
}

// 生成表格内容
export function generateTableContent(data) {
  return request({
    url: '/api/generate/tableContent',
    method: 'post',
    data: {
      content: data.content,  // 内容文本
      rows: data.rows || 0,   // 期望行数，0表示自动调整
      columns: data.columns || 0,  // 期望列数，0表示自动调整
      requirements: data.requirements || ''  // 生成要求
    }
  })
}