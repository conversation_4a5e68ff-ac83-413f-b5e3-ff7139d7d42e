<template>
  <div class="digest">
    <div class="digest_wrapper">
      <div class="digest_label">摘&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;要</div>
      <div class="digest_text" v-if="!isEditDialogOpen">
        <div class="text">
          {{ digestValue["digest"] == null ? "尚未生成" : digestValue["digest"] }}
        </div>
        <div class="keywords">关键字：{{ digestValue["keywords"] }}</div>
      </div>
      <div class="paragraph_one_edit" v-if="!isEditDialogOpen">
        <span @click="reGenDigest">重新生成摘要</span>
        <span @click="isEditDialogOpen = true">手动编辑</span>
      </div>
      <div v-if="isEditDialogOpen" class="digest_form">
        <el-form>
          <el-form-item label="摘要">
            <el-input type="textarea" v-model="digestEdited['digest']"></el-input>
          </el-form-item>
          <el-form-item label="关键字">
            <el-input type="textarea" v-model="digestEdited['keywords']"></el-input>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="save">保存内容</el-button>
            <el-button @click="isEditDialogOpen = false">放弃修改</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <div class="digest_wrapper">
      <div class="digest_label">ABSTRACT</div>
      <div class="digest_text" v-if="!isEditDialogOpen">
        <div class="text">
          {{ digestValue["digestEn"] == null ? "尚未生成" : digestValue["digestEn"] }}
        </div>
        <div class="keywords">Keywords ：{{ digestValue["keywordsEn"] }}</div>
      </div>
      <div v-if="isEditDialogOpen" class="digest_form">
        <el-form>
          <el-form-item label="英文摘要">
            <el-input type="textarea" v-model="digestEdited['digestEn']"></el-input>
          </el-form-item>
          <el-form-item label="英文关键字">
            <el-input type="textarea" v-model="digestEdited['keywordsEn']"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="save">保存内容</el-button>
            <el-button @click="isEditDialogOpen = false">放弃修改</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>
<script>
import { saveDigest } from "@/api/thesis.js";
import { generateDigest } from "@/api/generate.js";
export default {
  name: "Digest",
  props: {
    digestValue: {
      type: Object,
      default: {
        digest: "",
        digestEn: "",
        keywords: "",
        keywordsEn: "",
      },
    },
    thesisId: Number,
  },
  data() {
    return {
      digestEdited: this.digestValue,
      isEditDialogOpen: false,
    };
  },
  created() {
    // console.log(this.digestValue)
  },
  watch: {
    digestValue(newVal, oldVal) {
      this.digestEdited = newVal;
    },
  },
  methods: {
    openEditDialog() {},
    save() {
      let loadding = this.$loading({ text: "保存中，请稍等", target: ".digest" });
      saveDigest({
        ...this.digestEdited,
        thesisId: this.thesisId,
      })
        .then((res) => {
          this.isEditDialogOpen = false;
          loadding.close();
          this.$emit("onDigestUpdate");
        })
        .catch((res) => {
          loadding.close();
          this.$notify.error(res);
        });
    },
    reGenDigest() {
      let progress = 0;
      let loadingInstance = this.$loading({
        text: "正在生成中英文摘要，请稍候...",
        target: ".digest",
        customClass: "loading-with-percentage"
      });

      // 模拟进度更新
      const progressInterval = setInterval(() => {
        progress += Math.random() * 10;
        if (progress > 90) {
          progress = 90;
        }
        loadingInstance.setText(`正在生成中英文摘要，请稍候...<br><span class="loading-percentage">${Math.floor(progress)}%</span>`);
      }, 300);

      generateDigest({ thesisId: this.thesisId })
        .then((res) => {
          clearInterval(progressInterval);
          loadingInstance.setText(`正在生成中英文摘要，请稍候...<br><span class="loading-percentage">100%</span>`);
          setTimeout(() => {
            loadingInstance.close();
            this.$emit("onDigestUpdate");
          }, 200);
        })
        .catch((res) => {
          clearInterval(progressInterval);
          loadingInstance.close();
          this.$notify.error(res);
        });
    },
  },
};
</script>
<style lang="scss" scoped>
.digest {
  margin-top: 30px;
  font-size: 14px;
  color: #444;
  font-size: 16px;
  border-radius: 4px;

  .digest_wrapper {
    border: 1px solid red;
    background: #f6f6f6;
    border: 1px solid #f1f1f1;
    margin-bottom: 30px;
  }

  .digest_label {
    font-weight: bold;
    margin-bottom: 10px;
    font-size: 18px;
    padding: 10px;
    padding-top: 30px;
    text-align: center;
  }

  .digest_text {
    line-height: 30px;
    color: #444;
    padding: 10px 20px;
    white-space: pre-wrap;
    margin-bottom: 40px;

    .text {
      text-indent: 2rem;
    }

    .keywords {
      margin-top: 20px;
    }
  }

  .digest_form {
    font-size: 16px;
    line-height: 30px;
    margin: 10px 20px;
  }

  .paragraph_one_edit {
    color: #409eff;
    padding: 20px;

    span {
      border-radius: 6px;
      cursor: pointer;
      margin-right: 10px;
      background: #efefef;
      border: 1px solid #ccccccaa;
      font-style: normal;
      padding: 8px 16px;
      font-size: 12px;
      font-weight: normal;
    }

    span:hover {
      border: 1px solid #cccccc;
      background-color: #fff;
    }
  }
}

.digest:hover {
  .paragraph_one_edit {
    visibility: visible;
  }
}
</style>
