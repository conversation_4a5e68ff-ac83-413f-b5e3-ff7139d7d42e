2025-07-09 10:48:01  init_flask.py 27: INFO  {'CpuCount': 6, 'Mem': '32 GB'} [MainThread]
2025-07-09 10:48:01  web_server.py 32: INFO  Static directory: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\ui [MainThread]
2025-07-09 10:48:01  web_server.py 33: INFO  Static directory exists: True [MainThread]
2025-07-09 10:48:01  web_server.py 37: INFO  设置工作目录: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird [MainThread]
2025-07-09 10:48:01  EarlyBird.config.config 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-09 10:48:01  config.py 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-09 10:48:01  config.py 94: INFO  Database URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-09 10:48:03  EarlyBird.config.wechat_pay_config 49: WARNING  无法从数据库获取微信支付配置: No application found. Either work inside a view function or push an application context. See http://flask-sqlalchemy.pocoo.org/contexts/. [MainThread]
2025-07-09 10:48:03  wechat_pay_config.py 49: WARNING  无法从数据库获取微信支付配置: No application found. Either work inside a view function or push an application context. See http://flask-sqlalchemy.pocoo.org/contexts/. [MainThread]
2025-07-09 10:48:03  EarlyBird.config.wechat_pay_config 52: INFO  使用环境变量中的微信支付配置 [MainThread]
2025-07-09 10:48:03  wechat_pay_config.py 52: INFO  使用环境变量中的微信支付配置 [MainThread]
2025-07-09 10:48:03  EarlyBird.common.wechat_pay_v3 54: ERROR  加载商户私钥失败: [Errno 2] No such file or directory: 'certs/apiclient_key.pem' [MainThread]
2025-07-09 10:48:03  wechat_pay_v3.py 54: ERROR  加载商户私钥失败: [Errno 2] No such file or directory: 'certs/apiclient_key.pem' [MainThread]
2025-07-09 10:48:03  EarlyBird.api.pay.apis 31: ERROR  微信支付V3初始化失败: [Errno 2] No such file or directory: 'certs/apiclient_key.pem' [MainThread]
2025-07-09 10:48:03  apis.py 31: ERROR  微信支付V3初始化失败: [Errno 2] No such file or directory: 'certs/apiclient_key.pem' [MainThread]
2025-07-09 10:48:03  EarlyBird.api.admin.app 39: INFO  ✅ 后台管理系统API已注册 [MainThread]
2025-07-09 10:48:03  app.py 39: INFO  ✅ 后台管理系统API已注册 [MainThread]
2025-07-09 10:48:03  EarlyBird 40: INFO  确保数据目录存在: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\data [MainThread]
2025-07-09 10:48:03  server.py 40: INFO  确保数据目录存在: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\data [MainThread]
2025-07-09 10:48:03  EarlyBird 44: INFO  开始执行数据库迁移... [MainThread]
2025-07-09 10:48:03  server.py 44: INFO  开始执行数据库迁移... [MainThread]
2025-07-09 10:48:03  config.py 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-09 10:48:03  config.py 94: INFO  Database URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-09 10:48:03  EarlyBird 53: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-09 10:48:03  server.py 53: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-09 10:48:03  EarlyBird 97: WARNING  ChatLog表不存在，将在应用启动时自动创建 [MainThread]
2025-07-09 10:48:03  server.py 97: WARNING  ChatLog表不存在，将在应用启动时自动创建 [MainThread]
2025-07-09 10:48:03  add_uid_to_chatlog.py 20: INFO  开始执行ChatLog表迁移... [MainThread]
2025-07-09 10:48:03  add_uid_to_chatlog.py 24: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-09 10:48:03  add_uid_to_chatlog.py 38: WARNING  ChatLog表不存在，可能需要先创建表 [MainThread]
2025-07-09 10:48:03  task_api_server.py 31: INFO  TaskApiServer start [MainThread]
2025-07-09 10:48:03  task_paper_generator.py 38: INFO  论文生成任务处理线程初始化 [MainThread]
2025-07-09 10:48:03  task_paper_generator.py 72: INFO  论文生成任务处理线程启动 [Thread-2]
2025-07-09 10:48:03  task_api_server.py 47: INFO  TaskApiServer listen on 127.0.0.1:3302 [Thread-1]
2025-07-09 10:48:03  task_api_server.py 17: INFO  _ApiServerThread start [Thread-3]
2025-07-09 10:48:22  web_server.py 78: INFO  Serving request for path: admin/settings/payment [waitress-0]
2025-07-09 10:48:22  web_server.py 84: INFO  Serving index.html for path: admin/settings/payment [waitress-0]
2025-07-09 10:48:22  web_server.py 78: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-1]
2025-07-09 10:48:22  web_server.py 84: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-1]
2025-07-09 10:48:22  web_server.py 78: INFO  Serving request for path: js/chunk-vendors.7f82116b.js [waitress-2]
2025-07-09 10:48:22  web_server.py 81: INFO  Serving static file: js/chunk-vendors.7f82116b.js [waitress-2]
2025-07-09 10:48:22  web_server.py 78: INFO  Serving request for path: js/app.a0267689.js [waitress-3]
2025-07-09 10:48:22  web_server.py 81: INFO  Serving static file: js/app.a0267689.js [waitress-3]
2025-07-09 10:48:22  web_server.py 78: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-0]
2025-07-09 10:48:22  web_server.py 78: INFO  Serving request for path: css/app.7ae45238.css [waitress-1]
2025-07-09 10:48:22  web_server.py 81: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-0]
2025-07-09 10:48:22  web_server.py 81: INFO  Serving static file: css/app.7ae45238.css [waitress-1]
2025-07-09 10:48:22  web_server.py 78: INFO  Serving request for path: js/388.6189b2b6.js [waitress-2]
2025-07-09 10:48:22  web_server.py 81: INFO  Serving static file: js/388.6189b2b6.js [waitress-2]
2025-07-09 10:48:22  web_server.py 78: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-3]
2025-07-09 10:48:22  web_server.py 81: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-3]
2025-07-09 10:48:22  web_server.py 78: INFO  Serving request for path: fonts/element-icons.ff18efd1.woff [waitress-1]
2025-07-09 10:48:22  web_server.py 81: INFO  Serving static file: fonts/element-icons.ff18efd1.woff [waitress-1]
2025-07-09 10:48:22  web_server.py 78: INFO  Serving request for path: logo.png [waitress-2]
2025-07-09 10:48:22  web_server.py 81: INFO  Serving static file: logo.png [waitress-2]
2025-07-09 10:48:24  web_server.py 78: INFO  Serving request for path: admin/settings/payment [waitress-3]
2025-07-09 10:48:24  web_server.py 84: INFO  Serving index.html for path: admin/settings/payment [waitress-3]
2025-07-09 10:48:24  web_server.py 78: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-1]
2025-07-09 10:48:24  web_server.py 84: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-1]
2025-07-09 10:48:24  web_server.py 78: INFO  Serving request for path: js/chunk-vendors.7f82116b.js [waitress-0]
2025-07-09 10:48:24  web_server.py 81: INFO  Serving static file: js/chunk-vendors.7f82116b.js [waitress-0]
2025-07-09 10:48:24  web_server.py 78: INFO  Serving request for path: js/app.a0267689.js [waitress-2]
2025-07-09 10:48:24  web_server.py 81: INFO  Serving static file: js/app.a0267689.js [waitress-2]
2025-07-09 10:48:24  web_server.py 78: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-3]
2025-07-09 10:48:24  web_server.py 81: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-3]
2025-07-09 10:48:24  web_server.py 78: INFO  Serving request for path: css/app.7ae45238.css [waitress-1]
2025-07-09 10:48:24  web_server.py 81: INFO  Serving static file: css/app.7ae45238.css [waitress-1]
2025-07-09 10:48:24  web_server.py 78: INFO  Serving request for path: js/388.6189b2b6.js [waitress-0]
2025-07-09 10:48:24  web_server.py 81: INFO  Serving static file: js/388.6189b2b6.js [waitress-0]
2025-07-09 10:48:24  web_server.py 78: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-2]
2025-07-09 10:48:24  web_server.py 81: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-2]
2025-07-09 10:48:24  web_server.py 78: INFO  Serving request for path: logo.png [waitress-1]
2025-07-09 10:48:24  web_server.py 81: INFO  Serving static file: logo.png [waitress-1]
2025-07-09 10:48:24  web_server.py 78: INFO  Serving request for path: css/315.27b4d9c8.css [waitress-0]
2025-07-09 10:48:24  web_server.py 78: INFO  Serving request for path: js/315.f13759ae.js [waitress-2]
2025-07-09 10:48:24  web_server.py 81: INFO  Serving static file: css/315.27b4d9c8.css [waitress-0]
2025-07-09 10:48:24  web_server.py 81: INFO  Serving static file: js/315.f13759ae.js [waitress-2]
2025-07-09 10:48:26  web_server.py 78: INFO  Serving request for path: css/8.38fdf013.css [waitress-3]
2025-07-09 10:48:26  web_server.py 78: INFO  Serving request for path: js/8.a13b62ce.js [waitress-1]
2025-07-09 10:48:26  web_server.py 81: INFO  Serving static file: css/8.38fdf013.css [waitress-3]
2025-07-09 10:48:26  web_server.py 81: INFO  Serving static file: js/8.a13b62ce.js [waitress-1]
2025-07-09 10:51:43  init_flask.py 27: INFO  {'CpuCount': 6, 'Mem': '32 GB'} [MainThread]
2025-07-09 10:51:43  web_server.py 32: INFO  Static directory: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\ui [MainThread]
2025-07-09 10:51:43  web_server.py 33: INFO  Static directory exists: True [MainThread]
2025-07-09 10:51:43  web_server.py 37: INFO  设置工作目录: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird [MainThread]
2025-07-09 10:51:44  EarlyBird.config.config 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-09 10:51:44  config.py 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-09 10:51:44  config.py 94: INFO  Database URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-09 10:51:45  EarlyBird.config.wechat_pay_config 49: WARNING  无法从数据库获取微信支付配置: No application found. Either work inside a view function or push an application context. See http://flask-sqlalchemy.pocoo.org/contexts/. [MainThread]
2025-07-09 10:51:45  wechat_pay_config.py 49: WARNING  无法从数据库获取微信支付配置: No application found. Either work inside a view function or push an application context. See http://flask-sqlalchemy.pocoo.org/contexts/. [MainThread]
2025-07-09 10:51:45  EarlyBird.config.wechat_pay_config 52: INFO  使用环境变量中的微信支付配置 [MainThread]
2025-07-09 10:51:45  wechat_pay_config.py 52: INFO  使用环境变量中的微信支付配置 [MainThread]
2025-07-09 10:51:45  EarlyBird.common.wechat_pay_v3 54: ERROR  加载商户私钥失败: [Errno 2] No such file or directory: 'certs/apiclient_key.pem' [MainThread]
2025-07-09 10:51:45  wechat_pay_v3.py 54: ERROR  加载商户私钥失败: [Errno 2] No such file or directory: 'certs/apiclient_key.pem' [MainThread]
2025-07-09 10:51:45  EarlyBird.api.pay.apis 31: ERROR  微信支付V3初始化失败: [Errno 2] No such file or directory: 'certs/apiclient_key.pem' [MainThread]
2025-07-09 10:51:45  apis.py 31: ERROR  微信支付V3初始化失败: [Errno 2] No such file or directory: 'certs/apiclient_key.pem' [MainThread]
2025-07-09 10:51:45  EarlyBird.api.admin.app 39: INFO  ✅ 后台管理系统API已注册 [MainThread]
2025-07-09 10:51:45  app.py 39: INFO  ✅ 后台管理系统API已注册 [MainThread]
2025-07-09 10:51:45  EarlyBird 40: INFO  确保数据目录存在: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\data [MainThread]
2025-07-09 10:51:45  server.py 40: INFO  确保数据目录存在: d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\resources\data [MainThread]
2025-07-09 10:51:45  EarlyBird 44: INFO  开始执行数据库迁移... [MainThread]
2025-07-09 10:51:45  server.py 44: INFO  开始执行数据库迁移... [MainThread]
2025-07-09 10:51:45  config.py 21: INFO  load configFile at d:\Administrator\Documents\zaoniao-workspace\产品列表\AI论文\zaoniao1.0.6\EarlyBird\config/production.ini [MainThread]
2025-07-09 10:51:45  config.py 94: INFO  Database URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-09 10:51:45  EarlyBird 53: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-09 10:51:45  server.py 53: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-09 10:51:45  EarlyBird 97: WARNING  ChatLog表不存在，将在应用启动时自动创建 [MainThread]
2025-07-09 10:51:45  server.py 97: WARNING  ChatLog表不存在，将在应用启动时自动创建 [MainThread]
2025-07-09 10:51:45  add_uid_to_chatlog.py 20: INFO  开始执行ChatLog表迁移... [MainThread]
2025-07-09 10:51:45  add_uid_to_chatlog.py 24: INFO  数据库连接URI: mysql+pymysql://zndata:pFY87Fz75CysGtk6@localhost:3306/zndata?charset=utf8mb4 [MainThread]
2025-07-09 10:51:45  add_uid_to_chatlog.py 38: WARNING  ChatLog表不存在，可能需要先创建表 [MainThread]
2025-07-09 10:51:45  task_api_server.py 31: INFO  TaskApiServer start [MainThread]
2025-07-09 10:51:45  task_paper_generator.py 38: INFO  论文生成任务处理线程初始化 [MainThread]
2025-07-09 10:51:45  task_paper_generator.py 72: INFO  论文生成任务处理线程启动 [Thread-2]
2025-07-09 10:51:46  task_api_server.py 47: INFO  TaskApiServer listen on 127.0.0.1:3302 [Thread-1]
2025-07-09 10:51:46  task_api_server.py 17: INFO  _ApiServerThread start [Thread-3]
2025-07-09 10:51:46  web_server.py 78: INFO  Serving request for path: admin/settings/payment [waitress-0]
2025-07-09 10:51:46  web_server.py 84: INFO  Serving index.html for path: admin/settings/payment [waitress-0]
2025-07-09 10:51:46  web_server.py 78: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-1]
2025-07-09 10:51:46  web_server.py 84: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-1]
2025-07-09 10:51:46  web_server.py 78: INFO  Serving request for path: js/chunk-vendors.7f82116b.js [waitress-2]
2025-07-09 10:51:46  web_server.py 78: INFO  Serving request for path: js/app.5ad60535.js [waitress-3]
2025-07-09 10:51:46  web_server.py 81: INFO  Serving static file: js/chunk-vendors.7f82116b.js [waitress-2]
2025-07-09 10:51:46  web_server.py 81: INFO  Serving static file: js/app.5ad60535.js [waitress-3]
2025-07-09 10:51:46  web_server.py 78: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-0]
2025-07-09 10:51:46  web_server.py 78: INFO  Serving request for path: css/app.7ae45238.css [waitress-1]
2025-07-09 10:51:46  web_server.py 81: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-0]
2025-07-09 10:51:46  web_server.py 81: INFO  Serving static file: css/app.7ae45238.css [waitress-1]
2025-07-09 10:51:46  web_server.py 78: INFO  Serving request for path: js/388.6189b2b6.js [waitress-0]
2025-07-09 10:51:46  web_server.py 81: INFO  Serving static file: js/388.6189b2b6.js [waitress-0]
2025-07-09 10:51:46  web_server.py 78: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-3]
2025-07-09 10:51:46  web_server.py 81: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-3]
2025-07-09 10:51:46  web_server.py 78: INFO  Serving request for path: fonts/element-icons.ff18efd1.woff [waitress-1]
2025-07-09 10:51:46  web_server.py 81: INFO  Serving static file: fonts/element-icons.ff18efd1.woff [waitress-1]
2025-07-09 10:51:46  web_server.py 78: INFO  Serving request for path: logo.png [waitress-0]
2025-07-09 10:51:46  web_server.py 81: INFO  Serving static file: logo.png [waitress-0]
2025-07-09 10:51:49  web_server.py 78: INFO  Serving request for path: css/315.27b4d9c8.css [waitress-3]
2025-07-09 10:51:49  web_server.py 78: INFO  Serving request for path: js/315.f13759ae.js [waitress-1]
2025-07-09 10:51:49  web_server.py 81: INFO  Serving static file: css/315.27b4d9c8.css [waitress-3]
2025-07-09 10:51:49  web_server.py 81: INFO  Serving static file: js/315.f13759ae.js [waitress-1]
2025-07-09 10:51:51  web_server.py 78: INFO  Serving request for path: css/8.38fdf013.css [waitress-2]
2025-07-09 10:51:51  web_server.py 78: INFO  Serving request for path: js/8.a13b62ce.js [waitress-0]
2025-07-09 10:51:51  web_server.py 81: INFO  Serving static file: js/8.a13b62ce.js [waitress-0]
2025-07-09 10:51:51  web_server.py 81: INFO  Serving static file: css/8.38fdf013.css [waitress-2]
2025-07-09 10:55:32  web_server.py 78: INFO  Serving request for path: admin/settings/payment [waitress-3]
2025-07-09 10:55:32  web_server.py 84: INFO  Serving index.html for path: admin/settings/payment [waitress-3]
2025-07-09 10:55:32  web_server.py 78: INFO  Serving request for path: .well-known/appspecific/com.chrome.devtools.json [waitress-0]
2025-07-09 10:55:32  web_server.py 84: INFO  Serving index.html for path: .well-known/appspecific/com.chrome.devtools.json [waitress-0]
2025-07-09 10:55:32  web_server.py 78: INFO  Serving request for path: js/chunk-vendors.7f82116b.js [waitress-2]
2025-07-09 10:55:32  web_server.py 78: INFO  Serving request for path: js/app.5ad60535.js [waitress-1]
2025-07-09 10:55:32  web_server.py 81: INFO  Serving static file: js/app.5ad60535.js [waitress-1]
2025-07-09 10:55:32  web_server.py 81: INFO  Serving static file: js/chunk-vendors.7f82116b.js [waitress-2]
2025-07-09 10:55:32  web_server.py 78: INFO  Serving request for path: css/chunk-vendors.d5efd04f.css [waitress-3]
2025-07-09 10:55:32  web_server.py 78: INFO  Serving request for path: css/app.7ae45238.css [waitress-0]
2025-07-09 10:55:32  web_server.py 81: INFO  Serving static file: css/chunk-vendors.d5efd04f.css [waitress-3]
2025-07-09 10:55:32  web_server.py 81: INFO  Serving static file: css/app.7ae45238.css [waitress-0]
2025-07-09 10:55:32  web_server.py 78: INFO  Serving request for path: js/388.6189b2b6.js [waitress-1]
2025-07-09 10:55:32  web_server.py 81: INFO  Serving static file: js/388.6189b2b6.js [waitress-1]
2025-07-09 10:55:32  web_server.py 78: INFO  Serving request for path: img/logo.ce3a5bfd.png [waitress-3]
2025-07-09 10:55:32  web_server.py 81: INFO  Serving static file: img/logo.ce3a5bfd.png [waitress-3]
2025-07-09 10:55:32  web_server.py 78: INFO  Serving request for path: fonts/element-icons.ff18efd1.woff [waitress-0]
2025-07-09 10:55:32  web_server.py 81: INFO  Serving static file: fonts/element-icons.ff18efd1.woff [waitress-0]
2025-07-09 10:55:32  web_server.py 78: INFO  Serving request for path: logo.png [waitress-1]
2025-07-09 10:55:32  web_server.py 81: INFO  Serving static file: logo.png [waitress-1]
2025-07-09 10:55:34  web_server.py 78: INFO  Serving request for path: css/315.27b4d9c8.css [waitress-3]
2025-07-09 10:55:34  web_server.py 78: INFO  Serving request for path: js/315.f13759ae.js [waitress-2]
2025-07-09 10:55:34  web_server.py 81: INFO  Serving static file: css/315.27b4d9c8.css [waitress-3]
2025-07-09 10:55:34  web_server.py 81: INFO  Serving static file: js/315.f13759ae.js [waitress-2]
