<template>
  <div class="admin-thesis-stats">
    <el-card>
      <div slot="header">
        <span>论文统计</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="refreshData">
          刷新
        </el-button>
      </div>
      
      <div class="stats-content">
        <p>论文统计功能开发中...</p>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'AdminThesisStats',
  methods: {
    refreshData() {
      this.$message.info('刷新功能开发中...')
    }
  }
}
</script>

<style lang="scss" scoped>
.admin-thesis-stats {
  .stats-content {
    text-align: center;
    padding: 50px;
    color: #999;
  }
}
</style> 