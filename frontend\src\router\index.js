import router from './routers'
import store from '@/store'
import { Message } from 'element-ui'

// 不需要登录就可以访问的白名单路由
const whiteList = [
  '/help/guide', 
  '/paper/help', 
  '/aicg/index',
  '/user/login',
  '/user/register',
  '/admin/login'  // 添加管理员登录页面到白名单
]

// 标记是否已经初始化过用户状态
let userStateInitialized = false

router.beforeEach(async (to, from, next) => {
  // 如果是白名单路由，允许访问
  if (whiteList.some(path => to.path.startsWith(path))) {
    next()
    return
  }

  // 管理员路由特殊处理
  if (to.path.startsWith('/admin')) {
    try {
      // 检查管理员是否已登录
      const isAdminLoggedIn = store.getters['admin/isAdminLoggedIn']
      
      if (isAdminLoggedIn) {
        // 管理员已登录，允许访问
        next()
      } else {
        // 管理员未登录，跳转到管理员登录页面
        if (to.path !== '/admin/login') {
          Message.warning('请先登录管理员账号')
          next('/admin/login')
        } else {
          next()
        }
      }
    } catch (error) {
      console.error('管理员路由守卫检查失败:', error)
      if (to.path !== '/admin/login') {
        Message.error('检查管理员状态失败，请重新登录')
        next('/admin/login')
      } else {
        next()
      }
    }
    return
  }

  try {
    // 如果还没有初始化用户状态，先尝试恢复
    if (!userStateInitialized) {
      console.log('🔄 正在初始化用户状态...')
      const restoreSuccess = await store.dispatch('user/restoreUserState')
      userStateInitialized = true
      
      if (restoreSuccess) {
        console.log('✅ 用户状态初始化完成')
      } else {
        console.log('⚠️ 用户状态初始化失败，用户未登录')
      }
    }
    
    // 检查用户是否已登录
    const isLoggedIn = store.getters['user/isLoggedIn']
    
    if (isLoggedIn) {
      // 用户已登录，允许访问
      next()
    } else {
      // 用户未登录，跳转到登录页面
      if (to.path !== '/user/login') {
        Message.warning('请先登录')
        next('/user/login')
      } else {
        next()
      }
    }
  } catch (error) {
    console.error('路由守卫检查用户状态失败:', error)
    
    // 错误情况下清除所有缓存并跳转到登录页面
    try {
      await store.dispatch('user/clearAllCache')
    } catch (clearError) {
      console.error('清除缓存失败:', clearError)
    }
    
    if (to.path !== '/user/login') {
      Message.error('检查用户状态失败，请重新登录')
      next('/user/login')
    } else {
      next()
    }
  }
})

export default router 