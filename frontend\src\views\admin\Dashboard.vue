<template>
  <div class="dashboard">
    <!-- 欢迎横幅 -->
    <div class="welcome-banner">
      <div class="welcome-content">
        <div class="welcome-text">
          <h1>欢迎回来，{{ adminInfo?.username || '管理员' }}</h1>
          <p>今天是 {{ currentDate }}，祝您工作愉快！</p>
        </div>
        <div class="welcome-actions">
          <el-button type="primary" icon="el-icon-refresh" @click="refreshData">
            刷新数据
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="24" class="stats-cards">
      <el-col :xs="24" :sm="12" :md="6">
        <div class="stats-card user-card">
          <div class="stats-icon">
            <i class="el-icon-user"></i>
          </div>
          <div class="stats-content">
            <div class="stats-number">{{ overviewData?.overview?.total_users || 0 }}</div>
            <div class="stats-label">总用户数</div>
            <div class="stats-trend">
              <i class="el-icon-arrow-up"></i>
              今日新增: {{ overviewData?.today?.new_users || 0 }}
            </div>
          </div>
        </div>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <div class="stats-card thesis-card">
          <div class="stats-icon">
            <i class="el-icon-document"></i>
          </div>
          <div class="stats-content">
            <div class="stats-number">{{ overviewData?.overview?.total_thesis || 0 }}</div>
            <div class="stats-label">总论文数</div>
            <div class="stats-trend">
              <i class="el-icon-arrow-up"></i>
              今日新增: {{ overviewData?.today?.new_thesis || 0 }}
            </div>
          </div>
        </div>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <div class="stats-card vip-card">
          <div class="stats-icon">
            <i class="el-icon-star-on"></i>
          </div>
          <div class="stats-content">
            <div class="stats-number">{{ overviewData?.overview?.vip_users || 0 }}</div>
            <div class="stats-label">VIP用户</div>
            <div class="stats-trend">
              <i class="el-icon-warning"></i>
              过期: {{ overviewData?.overview?.expired_vip_users || 0 }}
            </div>
          </div>
        </div>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <div class="stats-card chat-card">
          <div class="stats-icon">
            <i class="el-icon-chat-dot-round"></i>
          </div>
          <div class="stats-content">
            <div class="stats-number">{{ overviewData?.overview?.total_chat_logs || 0 }}</div>
            <div class="stats-label">聊天记录</div>
            <div class="stats-trend">
              <i class="el-icon-arrow-up"></i>
              今日: {{ overviewData?.today?.chat_logs || 0 }}
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
    
    <!-- 图表区域 -->
    <el-row :gutter="24" class="charts-section">
      <el-col :xs="24" :lg="16">
        <div class="chart-card">
          <div class="chart-header">
            <h3>用户增长趋势</h3>
            <div class="chart-actions">
              <el-button size="small" type="text" @click="refreshData">
                <i class="el-icon-refresh"></i>
              </el-button>
            </div>
          </div>
          <div class="chart-container">
            <div v-if="userStats" class="chart-content">
              <div class="chart-item" v-for="(item, index) in userStats.daily_stats" :key="index">
                <div class="chart-bar">
                  <div 
                    class="chart-bar-fill" 
                    :style="{ height: getBarHeight(item.count, userStats.daily_stats) }"
                  ></div>
                </div>
                <div class="chart-label">{{ item.date }}</div>
                <div class="chart-value">{{ item.count }}</div>
              </div>
            </div>
            <div v-else class="chart-loading">
              <div class="loading-spinner"></div>
              <p>加载中...</p>
            </div>
          </div>
        </div>
      </el-col>
      
      <el-col :xs="24" :lg="8">
        <div class="chart-card">
          <div class="chart-header">
            <h3>VIP用户分布</h3>
          </div>
          <div class="chart-container">
            <div v-if="userStats" class="vip-distribution">
              <div class="vip-item" v-for="(item, index) in userStats.vip_level_stats" :key="index">
                <div class="vip-info">
                  <div class="vip-level">VIP {{ item.level }}</div>
                  <div class="vip-count">{{ item.count }}人</div>
                </div>
                <div class="vip-progress">
                  <div class="progress-bar">
                    <div 
                      class="progress-fill" 
                      :style="{ width: ((item.count / userStats.total_users) * 100) + '%' }"
                    ></div>
                  </div>
                  <div class="vip-percentage">
                    {{ ((item.count / userStats.total_users) * 100).toFixed(1) }}%
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="chart-loading">
              <div class="loading-spinner"></div>
              <p>加载中...</p>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
    
    <!-- 快速操作和系统信息 -->
    <el-row :gutter="24" class="bottom-section">
      <el-col :xs="24" :md="8">
        <div class="action-card">
          <div class="card-header">
            <h3>快速操作</h3>
          </div>
          <div class="action-grid">
            <div class="action-item" @click="$router.push('/admin/users')">
              <div class="action-icon">
                <i class="el-icon-user"></i>
              </div>
              <div class="action-text">用户管理</div>
            </div>
            <div class="action-item" @click="$router.push('/admin/thesis')">
              <div class="action-icon">
                <i class="el-icon-document"></i>
              </div>
              <div class="action-text">论文管理</div>
            </div>
            <div class="action-item" @click="$router.push('/admin/stats/overview')">
              <div class="action-icon">
                <i class="el-icon-s-data"></i>
              </div>
              <div class="action-text">详细统计</div>
            </div>
            <div class="action-item" @click="$router.push('/admin/settings')">
              <div class="action-icon">
                <i class="el-icon-setting"></i>
              </div>
              <div class="action-text">系统设置</div>
            </div>
          </div>
        </div>
      </el-col>
      
      <el-col :xs="24" :md="16">
        <div class="system-card">
          <div class="card-header">
            <h3>系统信息</h3>
          </div>
          <div class="system-grid">
            <div class="system-item">
              <div class="system-label">系统名称</div>
              <div class="system-value">早鸟论文后台管理系统</div>
            </div>
            <div class="system-item">
              <div class="system-label">当前管理员</div>
              <div class="system-value">{{ adminInfo?.username || '未知' }}</div>
            </div>
            <div class="system-item">
              <div class="system-label">最后登录</div>
              <div class="system-value">{{ adminInfo?.last_login_time || '未知' }}</div>
            </div>
            <div class="system-item">
              <div class="system-label">系统时间</div>
              <div class="system-value">{{ currentTime }}</div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex'

export default {
  name: 'AdminDashboard',
  data() {
    return {
      currentTime: '',
      currentDate: '',
      timer: null
    }
  },
  computed: {
    ...mapGetters('admin', ['overviewData', 'userStats', 'adminInfo'])
  },
  mounted() {
    // 延迟加载数据，避免登录后立即触发API请求
    this.$nextTick(() => {
      setTimeout(() => {
    this.loadData()
      }, 100)
    })
    this.startTimer()
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer)
    }
  },
  methods: {
    ...mapActions('admin', ['getOverview', 'getUserStats']),
    
    // 加载数据
    async loadData() {
      try {
        await Promise.all([
          this.getOverview(),
          this.getUserStats({ period: '7d' })
        ])
      } catch (error) {
        console.error('加载数据失败:', error)
        this.$message.error('加载数据失败，请稍后重试')
      }
    },
    
    // 刷新数据
    async refreshData() {
      this.$message.info('正在刷新数据...')
      await this.loadData()
      this.$message.success('数据刷新成功')
    },
    
    // 计算柱状图高度
    getBarHeight(count, data) {
      if (!data || data.length === 0) return '0%'
      const maxCount = Math.max(...data.map(item => item.count))
      return maxCount > 0 ? `${(count / maxCount) * 100}%` : '0%'
    },
    
    // 启动定时器
    startTimer() {
      this.updateTime()
      this.timer = setInterval(this.updateTime, 1000)
    },
    
    // 更新时间
    updateTime() {
      const now = new Date()
      this.currentTime = now.toLocaleString('zh-CN')
      this.currentDate = now.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        weekday: 'long'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.dashboard {
  padding: 24px;
  background: #f8fafc;
  min-height: calc(100vh - 60px);
  
  .welcome-banner {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    padding: 32px;
    margin-bottom: 32px;
    color: white;
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
    
    .welcome-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      
      .welcome-text {
        h1 {
          font-size: 28px;
          font-weight: 700;
          margin: 0 0 8px 0;
          color: white;
        }
        
        p {
          font-size: 16px;
          color: rgba(255, 255, 255, 0.9);
          margin: 0;
        }
      }
      
      .welcome-actions {
        .el-button {
          background: rgba(255, 255, 255, 0.2);
          border: 1px solid rgba(255, 255, 255, 0.3);
          color: white;
          padding: 12px 24px;
          border-radius: 8px;
          font-weight: 600;
          transition: all 0.3s;
          
          &:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
          }
        }
      }
    }
  }
  
  .stats-cards {
    margin-bottom: 32px;
    
    .stats-card {
      background: white;
      border-radius: 16px;
      padding: 24px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      border: 1px solid #f1f5f9;
      transition: all 0.3s;
      position: relative;
      overflow: hidden;
      
      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
      }
      
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
      }
      
      &.user-card::before {
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
      }
      
      &.thesis-card::before {
        background: linear-gradient(90deg, #f093fb 0%, #f5576c 100%);
      }
      
      &.vip-card::before {
        background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
      }
      
      &.chat-card::before {
        background: linear-gradient(90deg, #43e97b 0%, #38f9d7 100%);
      }
      
      .stats-icon {
        width: 60px;
        height: 60px;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 16px;
        
        i {
          font-size: 28px;
          color: white;
        }
      }
      
      .user-card .stats-icon {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }
      
      .thesis-card .stats-icon {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      }
      
      .vip-card .stats-icon {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      }
      
      .chat-card .stats-icon {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
      }
      
      .stats-content {
        .stats-number {
          font-size: 36px;
          font-weight: 800;
          color: #1e293b;
          line-height: 1;
          margin-bottom: 8px;
        }
        
        .stats-label {
          font-size: 16px;
          color: #64748b;
          font-weight: 600;
          margin-bottom: 12px;
        }
        
        .stats-trend {
          font-size: 14px;
          color: #10b981;
          font-weight: 600;
          display: flex;
          align-items: center;
          
          i {
            margin-right: 4px;
            font-size: 12px;
          }
        }
      }
    }
  }
  
  .charts-section {
    margin-bottom: 32px;
    
    .chart-card {
      background: white;
      border-radius: 16px;
      padding: 24px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      border: 1px solid #f1f5f9;
      height: 400px;
      
      .chart-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 24px;
        padding-bottom: 16px;
        border-bottom: 1px solid #f1f5f9;
        
        h3 {
          font-size: 20px;
          font-weight: 700;
          color: #1e293b;
          margin: 0;
        }
        
        .chart-actions {
          .el-button {
            color: #64748b;
            padding: 8px;
            border-radius: 8px;
            
            &:hover {
              background: #f1f5f9;
              color: #1e293b;
            }
          }
        }
      }
      
      .chart-container {
        height: calc(100% - 60px);
        
        .chart-content {
          height: 100%;
          display: flex;
          align-items: flex-end;
          justify-content: space-around;
          padding: 20px 0;
          
          .chart-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            flex: 1;
            margin: 0 8px;
            
            .chart-bar {
              width: 100%;
              height: 200px;
              background: #f8fafc;
              border-radius: 8px;
              position: relative;
              margin-bottom: 12px;
              overflow: hidden;
              
              .chart-bar-fill {
                position: absolute;
                bottom: 0;
                left: 0;
                right: 0;
                background: linear-gradient(to top, #667eea, #764ba2);
                border-radius: 8px;
                transition: height 0.6s ease;
                min-height: 4px;
              }
            }
            
            .chart-label {
              font-size: 12px;
              color: #64748b;
              font-weight: 600;
              margin-bottom: 4px;
            }
            
            .chart-value {
              font-size: 14px;
              font-weight: 700;
              color: #1e293b;
            }
          }
        }
        
        .vip-distribution {
          height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: space-around;
          padding: 20px 0;
          
          .vip-item {
            display: flex;
            align-items: center;
            padding: 16px 0;
            border-bottom: 1px solid #f1f5f9;
            
            &:last-child {
              border-bottom: none;
            }
            
            .vip-info {
              flex: 1;
              display: flex;
              align-items: center;
              justify-content: space-between;
              margin-right: 16px;
              
              .vip-level {
                font-weight: 700;
                color: #1e293b;
                font-size: 16px;
              }
              
              .vip-count {
                color: #667eea;
                font-weight: 700;
                font-size: 16px;
              }
            }
            
            .vip-progress {
              flex: 2;
              display: flex;
              align-items: center;
              
              .progress-bar {
                flex: 1;
                height: 8px;
                background: #f1f5f9;
                border-radius: 4px;
                margin-right: 12px;
                overflow: hidden;
                
                .progress-fill {
                  height: 100%;
                  background: linear-gradient(90deg, #667eea, #764ba2);
                  border-radius: 4px;
                  transition: width 0.6s ease;
                }
              }
              
              .vip-percentage {
                font-size: 14px;
                font-weight: 600;
                color: #64748b;
                min-width: 50px;
                text-align: right;
              }
            }
          }
        }
        
        .chart-loading {
          height: 100%;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          color: #64748b;
          
          .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f1f5f9;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 16px;
          }
          
          p {
            font-size: 16px;
            font-weight: 600;
            margin: 0;
          }
        }
      }
    }
  }
  
  .bottom-section {
    .action-card,
    .system-card {
      background: white;
      border-radius: 16px;
      padding: 24px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      border: 1px solid #f1f5f9;
      height: 300px;
      
      .card-header {
        margin-bottom: 24px;
        padding-bottom: 16px;
        border-bottom: 1px solid #f1f5f9;
        
        h3 {
          font-size: 20px;
          font-weight: 700;
          color: #1e293b;
          margin: 0;
        }
      }
    }
    
    .action-card {
      .action-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 16px;
        height: calc(100% - 60px);
        
        .action-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 24px 16px;
          background: #f8fafc;
          border-radius: 12px;
          cursor: pointer;
          transition: all 0.3s;
          border: 2px solid transparent;
          
          &:hover {
            background: #f1f5f9;
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.15);
          }
          
          .action-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 12px;
            
            i {
              font-size: 24px;
              color: white;
            }
          }
          
          .action-text {
            font-size: 14px;
            font-weight: 600;
            color: #1e293b;
            text-align: center;
          }
        }
      }
    }
    
    .system-card {
      .system-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        height: calc(100% - 60px);
        
        .system-item {
          padding: 20px;
          background: #f8fafc;
          border-radius: 12px;
          border: 1px solid #e2e8f0;
          
          .system-label {
            font-size: 14px;
            color: #64748b;
            font-weight: 600;
            margin-bottom: 8px;
          }
          
          .system-value {
            font-size: 16px;
            color: #1e293b;
            font-weight: 700;
            word-break: break-word;
          }
        }
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 响应式设计
@media (max-width: 768px) {
  .dashboard {
    padding: 16px;
    
    .welcome-banner {
      padding: 24px;
      margin-bottom: 24px;
      
      .welcome-content {
        flex-direction: column;
        align-items: flex-start;
        
        .welcome-text {
          margin-bottom: 20px;
          
          h1 {
            font-size: 24px;
          }
        }
      }
    }
    
    .stats-cards {
      margin-bottom: 24px;
      
      .stats-card {
        margin-bottom: 16px;
        padding: 20px;
        
        .stats-content {
          .stats-number {
            font-size: 28px;
          }
        }
      }
    }
    
    .charts-section {
      margin-bottom: 24px;
      
      .chart-card {
        margin-bottom: 16px;
        height: 350px;
        padding: 20px;
      }
    }
    
    .bottom-section {
      .action-card,
      .system-card {
        margin-bottom: 16px;
        height: auto;
        min-height: 250px;
      }
      
      .action-card {
        .action-grid {
          grid-template-columns: 1fr;
        }
      }
      
      .system-card {
        .system-grid {
          grid-template-columns: 1fr;
        }
      }
    }
  }
}
</style> 