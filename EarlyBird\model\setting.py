from werkzeug.security import generate_password_hash, check_password_hash

from EarlyBird.common.libs.BaseModel import BaseModel
from sqlalchemy import (
    BigInteger,
    Integer,
    String,
    Boolean,
    Column,
    Enum,
    SmallInteger,
    DateTime,
    Table,
    BIGINT,
    ForeignKey,
    MetaData,
    Text,
)
import json
import math
import logging
from datetime import datetime, timedelta
from . import TABLE_PREFIX


class Setting(BaseModel):
    """系统设置模型"""
    __tablename__ = f"{TABLE_PREFIX}setting"
    __table_args__ = {"comment": "系统设置表"}

    # 修复字段定义，与API使用保持一致
    category = Column(String(50), comment="设置分类", default="system")
    key = Column(String(100), comment="设置键", unique=True)
    value = Column(Text, comment="设置值")
    description = Column(String(255), comment="设置描述")

    # 保持向后兼容的属性
    @property
    def settingKey(self):
        return self.key

    @settingKey.setter
    def settingKey(self, value):
        self.key = value

    @property
    def settingValue(self):
        return self.value

    @settingValue.setter
    def settingValue(self, value):
        self.value = value

    def __str__(self):
        return json.dumps(self.to_dict())

    def save(self):
        """保存设置"""
        from EarlyBird.ExtendRegister.db_register import db
        try:
            db.session.add(self)
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            logging.error(f"保存设置失败: {str(e)}")
            return False
        
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'category': self.category or 'system',
            'key': self.key,
            'value': self.value,
            'description': self.description or '',
            'create_time': self.create_time.strftime('%Y-%m-%d %H:%M:%S') if hasattr(self, 'create_time') and self.create_time else None,
            'update_time': self.update_time.strftime('%Y-%m-%d %H:%M:%S') if hasattr(self, 'update_time') and self.update_time else None
        }
