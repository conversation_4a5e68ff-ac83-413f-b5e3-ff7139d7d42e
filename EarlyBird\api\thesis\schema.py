from pydantic import BaseModel, ValidationError, constr, Field
from typing import Optional, List, Dict, Any, Union
from EarlyBird.common import ai

# BaseModel是flask pydantic包提供的一个基础类，ParamOutline是这个BaseModel的继承类
# 继承Pydantic的好处在于 Pydantic可以很容易的处理  数据验证，序列化，数据格式转换如dictionary-json等操作。


class ParamThesisId(BaseModel):
    thesisId: int
    userId: Optional[int] = None


class ParamGenerateOutline(BaseModel):
    thesisId: int
    userId: Optional[int] = None


class ParamSaveSingleParagraph(BaseModel):
    thesisId: int
    paragraphId: str
    title: str
    text: str
    userId: Optional[int] = None


class ParamSaveDigest(BaseModel):
    thesisId: int
    digest: str
    digestEn: str
    keywords: str
    keywordsEn: str
    userId: Optional[int] = None


class SaveNewPara(BaseModel):
    thesisId: int
    baseParaId: str
    newTitle: str
    newContent: str
    addType: str  # next or child
    userId: Optional[int] = None


class MovePara(BaseModel):
    thesisId: int
    paraId: str
    moveType: str  # up or down
    userId: Optional[int] = None


class DeleteParagraph(BaseModel):
    thesisId: int
    paraId: str
    userId: Optional[int] = None


class SaveThesisProperty(BaseModel):
    thesisId: int
    content: str
    propName: Optional[str] = None
    userId: Optional[int] = None


class ParamPayDownload(BaseModel):
    thesisId: int
    paymentMethod: str  # 支付方式：alipay, wxpay, v990r等
    userId: Optional[int] = None


class ParamPaymentStatus(BaseModel):
    thesisId: int
    orderId: Optional[str] = None  # 支付订单号
    userId: Optional[int] = None


class ParamConfirmPayment(BaseModel):
    thesisId: int
    orderId: str  # 支付订单号
    userId: Optional[int] = None
