<!--  -->
<template>
    <div class='cover'>
        <div class="cover_uptitle">早鸟论文课程(设计)论文</div>
        <div class="cover_title">
            题目:<span>{{ title }}</span>
        </div>
        <div class="cover_meta">
            <div>学&nbsp;&nbsp;号：</div> <span>(下载后填写)</span>
        </div>
        <div class="cover_meta">
            <div>姓&nbsp;&nbsp;名：</div> <span>(下载后填写)</span>
        </div>
        <div class="cover_meta">
            <div>学&nbsp;&nbsp;院：</div> <span>(下载后填写)</span>
        </div>
        <div class="cover_meta">
            <div>专&nbsp;&nbsp;业：</div> <span>(下载后填写)</span>
        </div>
        <div class="cover_meta">
            <div>指导教师：</div> <span>(下载后填写)</span>
        </div>
        <div class="cover_school">学校名称</div>
        <div class="cover_major">专业名称</div>
        <div class="cover_student">学生姓名</div>
        <div class="cover_teacher">指导教师</div>
        <div class="cover_date">完成日期</div>
    </div>
</template>


<style scoped lang="scss">
.cover {
    text-align: center;
    border: 1px solid #ccc;
    padding: 200px 100px;


    .cover_uptitle {
        font-size: 32px;
        font-weight: bold;
        font-family: "黑体";

    }

    .cover_title {
        font-size: 24px;
        font-family: "黑体";
        font-weight: bold;
        margin-top: 20px;
        margin-bottom: 40px;
        padding:20px 40px;
    }

    .cover_meta {
        font-size: 20px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 20px;

        div {
            text-align: right;
            width: 120px;
        }

        span {
            width: 250px;
            display: inline-block;
            border-bottom: 1px solid #ccc;
            font-size: 16px;
            color: #ccc;
        }
    }
}
</style>

<script>

export default {
    props: {
        title: String,
    },
    components: {},
    data() {
        return {

        }
    },
    computed: {},
    watch: {},
    methods: {

    },
    created() {

    },
    mounted() {

    },
}
</script>