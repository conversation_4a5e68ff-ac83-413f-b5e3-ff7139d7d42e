<template>
  <div class="admin-user-stats">
    <el-card>
      <div slot="header">
        <span>用户统计</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="refreshData">
          刷新
        </el-button>
      </div>
      
      <div class="stats-content">
        <p>用户统计功能开发中...</p>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'AdminUserStats',
  methods: {
    refreshData() {
      this.$message.info('刷新功能开发中...')
    }
  }
}
</script>

<style lang="scss" scoped>
.admin-user-stats {
  .stats-content {
    text-align: center;
    padding: 50px;
    color: #999;
  }
}
</style> 